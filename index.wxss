/**index.wxss**/
.container {
  padding: 20rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 160rpx; /* 为底部固定区域留出空间 */
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.time-info {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  opacity: 0.9;
}

.meal-type {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.click-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  margin-bottom: 10rpx;
}

.click-status.can-click {
  background-color: rgba(255, 255, 255, 0.2);
  color: #e8f5e8;
}

.click-status.cannot-click {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffeaea;
}

.next-reset {
  font-size: 22rpx;
  opacity: 0.8;
}

.function-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  justify-content: center;
}

.function-btn {
  min-width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
  margin: 0;
  padding: 0 30rpx;
  transition: all 0.3s ease;
}

.export-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border: none;
}

.reset-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
}

.function-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
}

.instructions {
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.instruction-text {
  font-size: 26rpx;
  color: #856404;
  line-height: 1.4;
}

/* 访客就餐统计区域样式 */
.visitor-section {
  background-color: #e8f4fd;
  border: 1rpx solid #b3d9f2;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.visitor-header {
  margin-bottom: 15rpx;
}

.visitor-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976d2;
}

.visitor-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.visitor-btn {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  font-size: 32rpx !important;
  padding: 0 30rpx !important;
  margin: 0 !important;
  min-width: auto !important;
  width: auto !important;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
}

.visitor-btn::after {
  border: none !important;
}

.visitor-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.4);
}

.visitor-btn.disabled {
  opacity: 0.4 !important;
  background: #ccc !important;
  transform: none !important;
  box-shadow: none !important;
}

.visitor-count {
  font-size: 48rpx;
  font-weight: bold;
  color: #1976d2;
  background-color: white;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  border: 2rpx solid #1976d2;
  min-width: 100rpx;
  text-align: center;
}

.content {
  flex: 1;
  padding: 20rpx 0;
}

.department-header {
  margin: 30rpx 0 20rpx 0;
}

.department-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-left: 15rpx;
  border-left: 6rpx solid #007aff;
  position: relative;
}

.department-name::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #007aff, #0056b3);
  border-radius: 3rpx;
}

.people-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

/* 重写button组件的默认样式 - 增加1倍大小 */
.person-btn {
  display: inline-block !important;
  width: auto !important;
  min-width: auto !important;
  height: 140rpx !important;
  line-height: 140rpx !important;
  font-size: 56rpx !important;
  background-color: #f8f9fa !important;
  border: 2rpx solid #e9ecef !important;
  border-radius: 12rpx !important;
  color: #333 !important;
  margin: 0 !important;
  padding: 0 10rpx !important;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  white-space: nowrap;
  box-sizing: border-box;
}

/* 重写button的伪元素 */
.person-btn::after {
  border: none !important;
}

.person-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.person-btn.clicked {
  background-color: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
  animation: clickedPulse 0.3s ease;
}

.person-btn.disabled {
  opacity: 0.4 !important;
  background-color: #f0f0f0 !important;
  transform: none !important;
  box-shadow: none !important;
}

.person-btn:disabled {
  opacity: 0.4;
}

@keyframes clickedPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  padding: 20rpx !important;
  border-top: 1rpx solid #dee2e6 !important;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1) !important;
  z-index: 9999 !important;
  box-sizing: border-box !important;
}

.meal-info {
  font-size: 32rpx !important;
  font-weight: bold !important;
  color: #333 !important;
  text-align: center !important;
  display: block !important;
  margin-bottom: 8rpx !important;
}

.total-info {
  font-size: 24rpx !important;
  color: #666 !important;
  text-align: center !important;
  display: block !important;
}

/* 新入职员工功能区域样式 */
.new-employee-section {
  background-color: #f0f8ff;
  border: 1rpx solid #b3d9f2;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.new-employee-header {
  margin-bottom: 15rpx;
}

.new-employee-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976d2;
}

.new-employee-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}

.department-selector {
  flex: 1;
  background-color: white;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0;
  overflow: hidden;
}

.picker-display {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: white;
  border-radius: 8rpx;
  text-align: center;
  min-height: 40rpx;
  line-height: 40rpx;
  border: 1rpx solid #e0e0e0;
  position: relative;
}

.picker-display::after {
  content: '▼';
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 24rpx;
}

.new-employee-btn {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  margin: 0 !important;
  min-width: auto !important;
  width: auto !important;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
  white-space: nowrap;
}

.new-employee-btn::after {
  border: none !important;
}

.new-employee-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.4);
}

.new-employee-btn.disabled {
  opacity: 0.4 !important;
  background: #ccc !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 导出name.txt按钮区域 */
.export-name-section {
  margin-top: 15rpx;
  text-align: center;
}

.export-name-btn {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 70rpx !important;
  line-height: 70rpx !important;
  font-size: 26rpx !important;
  padding: 0 25rpx !important;
  margin: 0 !important;
  min-width: auto !important;
  width: auto !important;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
  white-space: nowrap;
}

.export-name-btn::after {
  border: none !important;
}

.export-name-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
}

/* 移除员工功能区域样式 */
.remove-employee-section {
  background-color: #fff5f5;
  border: 1rpx solid #ffcdd2;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  margin-top: 20rpx;
}

.remove-employee-header {
  margin-bottom: 15rpx;
}

.remove-employee-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #d32f2f;
}

.remove-employee-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.remove-selectors {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.remove-department-selector,
.remove-employee-selector {
  flex: 1;
  background-color: white;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.remove-employee-selector .picker-display.disabled {
  background-color: #f5f5f5;
  color: #999;
}

.remove-employee-btn {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  margin: 0 !important;
  min-width: auto !important;
  width: 100% !important;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(244, 67, 54, 0.3);
  white-space: nowrap;
}

.remove-employee-btn::after {
  border: none !important;
}

.remove-employee-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.4);
}

.remove-employee-btn.disabled {
  opacity: 0.4 !important;
  background: #ccc !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 导入数据功能区域样式 */
.import-data-section {
  background-color: #f3e5f5;
  border: 1rpx solid #ce93d8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  margin-top: 20rpx;
}

.import-data-header {
  margin-bottom: 15rpx;
}

.import-data-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #7b1fa2;
}

.import-data-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.import-description {
  background-color: white;
  border: 1rpx solid #e1bee7;
  border-radius: 8rpx;
  padding: 15rpx;
}

.import-desc-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.import-data-btn {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 80rpx !important;
  line-height: 80rpx !important;
  font-size: 28rpx !important;
  padding: 0 30rpx !important;
  margin: 0 !important;
  min-width: auto !important;
  width: 100% !important;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(156, 39, 176, 0.3);
  white-space: nowrap;
}

.import-data-btn::after {
  border: none !important;
}

.import-data-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(156, 39, 176, 0.4);
}