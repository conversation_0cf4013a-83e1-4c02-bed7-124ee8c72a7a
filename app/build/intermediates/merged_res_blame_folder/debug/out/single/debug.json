[{"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_point_edit1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_point_edit1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_in_bottom.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_slide_in_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_go_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_go_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_ywsb.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_ywsb.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_selector_disabled_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_selector_disabled_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_pmxj.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\pmxj.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_modelservice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_modelservice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_dsk.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\dsk.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_r.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\r.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_tree_ex.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\tree_ex.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_main.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_main.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_listservice_result.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\listservice_result.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_line.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_line.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_userdef_audio_background.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\userdef_audio_background.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e6.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e6.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_star_half_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_star_half_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_button.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_button.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_star_half_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_star_half_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_image_zyml.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\image_zyml.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_psk.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\psk.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_data_query_result.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\data_query_result.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_vsk.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\vsk.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_mode_bar.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_mode_bar.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_pressed_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_pressed_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_line_edit2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_line_edit2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_redlight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_redlight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchpen.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchpen.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_camera_mode_shutter_press.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\camera_mode_shutter_press.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_star_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_star_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_model3d_mianactivity.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\model3d_mianactivity.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_greendeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_greendeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_redlight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_redlight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_camera_snapshot.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\camera_snapshot.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_orangedeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_orangedeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_dialog_title_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_dialog_title_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_point_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_point_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_list_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_list_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_check_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sdk2_api.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sdk2_api.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_speech_end.mp3.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\bdspeech_speech_end.mp3"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_image_zyml2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\image_zyml2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_maptype1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\maptype1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_xspm.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_xspm.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_route.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\route.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketcheraser.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketcheraser.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_material_dark.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_primary_text_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_attribute_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_attribute_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_point_edit_1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_point_edit_1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-hdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_check_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_orientation_1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\orientation_1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_scrubber_primary_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_scrubber_primary_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_redlight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_redlight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketcha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketcha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_googlemap.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\googlemap.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_check_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_clear_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_clear_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_map.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_map.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_textfield_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_textfield_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_scrubber_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_scrubber_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_star_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_star_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_txt.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_txt.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_singlechoice_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\select_dialog_singlechoice_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_star_half_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_star_half_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_rating_star_on_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_rating_star_on_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_atti.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_atti.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_check_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_html.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\html.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_p.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\p.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_screen_pot.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_screen_pot.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_material_light.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_primary_text_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_txt.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_txt.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_gravel.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\gravel.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_mapview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_mapview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_reddeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_reddeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_content_include.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_screen_content_include.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_toolbar.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_screen_toolbar.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_gps_rectify.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_gps_rectify.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_section.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\section.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_camera_mode_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\camera_mode_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_tab_indicator_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_tab_indicator_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_switch_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_switch_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_maptype3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\maptype3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_secondary_text_material_light.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_secondary_text_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_recognition_start.mp3.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\bdspeech_recognition_start.mp3"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_editviewshaple.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\editviewshaple.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_recognizing.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_recognizing.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchtextpen.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchtextpen.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_media.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_media.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_toolbar_addpoint.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\toolbar_addpoint.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_star_half_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_star_half_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_search.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\search.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_menu_hardkey_panel_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_menu_hardkey_panel_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_menu_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_menu_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_apply.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\apply.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color-v23_abc_color_highlight_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color-v23\\abc_color_highlight_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_uponelevel.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_uponelevel.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_screen_pot_selected.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_screen_pot_selected.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_divider_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_divider_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_toolbar_edit_comm.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\toolbar_edit_comm.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_img_wj.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\img_wj.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_switch_thumb_material_light.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\switch_thumb_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_att_list_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_att_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_camera_save.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\camera_save.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_app_icon.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\app_icon.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_select_dialog_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_select_dialog_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_media_cancel_action.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_media_cancel_action.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_popup_background_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_popup_background_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_softupdate_progress.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\softupdate_progress.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bkshape.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bkshape.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_routesum.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\routesum.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_engpointlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\engpointlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_urlnavigation_itemicon_default.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\urlnavigation_itemicon_default.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_textgallery.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_textgallery.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geochemdict25w.db.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geochemdict25w.db"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_image_view.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\image_view.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_textfield_search_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_textfield_search_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_txt.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_txt.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_moreoverflow_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_moreoverflow_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_media_action.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_media_action.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_drawbitmap.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\drawbitmap.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_route.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_route.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_app_icon.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\app_icon.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_star_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_star_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_textfield_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_textfield_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_switch_to_on_mtrl_00001.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_switch_to_on_mtrl_00001.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_pressed_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_pressed_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_imagebutton.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_imagebutton.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_imageview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\imageview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_part_time.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_part_time.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_acticity_splitscreen.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\acticity_splitscreen.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_maintab_toolbar_bg.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\maintab_toolbar_bg.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e4.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e4.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_attribute_link.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_attribute_link.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_pressed_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_pressed_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_att_list_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_att_list_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_orientation1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_orientation1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_compass.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\compass.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dataquerylist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dataquerylist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_checkbox.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_list_menu_item_checkbox.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_activity_chooser_view_list_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_activity_chooser_view_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_common.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\common.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_left_deep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_left_deep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_menu_item_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_menu_item_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_toolbar_edit_comm.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_toolbar_edit_comm.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_txt.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_txt.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_menu_more.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\menu_more.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_selector_disabled_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_selector_disabled_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_menu_hardkey_panel_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_menu_hardkey_panel_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_userdef_audio_return.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\userdef_audio_return.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_model3d_layout_view02.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\model3d_layout_view02.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sys_config.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sys_config.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_pressed_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_pressed_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_star_half_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_star_half_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_selector_disabled_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_selector_disabled_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_reddeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_reddeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bigdatawebserviceview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bigdatawebserviceview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_left_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_left_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_att.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_att.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_icn_plus.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_icn_plus.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_geochem_mulsoil_layer_itemview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\geochem_mulsoil_layer_itemview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_lib.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_lib.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_home_btn_bg_d.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\home_btn_bg_d.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_scrubber_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_scrubber_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_proj.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_proj.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_check_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_moreoverflow_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_moreoverflow_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_multichoice_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\select_dialog_multichoice_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_longpressed_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_longpressed_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_star_half_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_star_half_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_switch_to_on_mtrl_00001.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_switch_to_on_mtrl_00001.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_seclib_edit1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_seclib_edit1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_delete_line.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_delete_line.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_sjjm.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_sjjm.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_aqfewqefwqefe.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\aqfewqefwqefe.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_load.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_load.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_draw_sketch.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\draw_sketch.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sectionmanage.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sectionmanage.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new_line1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new_line1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_listitem.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\listitem.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_titlebar_lightgray_bg.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\titlebar_lightgray_bg.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_att_link.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_att_link.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_popup_background_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_popup_background_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_switch_to_on_mtrl_00001.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_switch_to_on_mtrl_00001.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_section_att_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_section_att_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_folder.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_folder.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_icon.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_list_menu_item_icon.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_seclib_edit2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_seclib_edit2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxxhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_go_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_go_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_app_icon.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\app_icon.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_mapitem.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_mapitem.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_search_url_text.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_search_url_text.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_lines.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_lines.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_medialist_headerview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\medialist_headerview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_recognition_success.mp3.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\bdspeech_recognition_success.mp3"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_toolbar_edit_comm1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\toolbar_edit_comm1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_mapcatlog.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_mapcatlog.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_switch_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_switch_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_data_queryresult_popwindow.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\data_queryresult_popwindow.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_big_media_narrow.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_big_media_narrow.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_extdictionary.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_extdictionary.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_divider_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_divider_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_cab_background_top_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_cab_background_top_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_scrubber_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_scrubber_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_gpointlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\gpointlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_section_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_section_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_screen_pot.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_screen_pot.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_in_top.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_slide_in_top.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_recognizing_deep.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_recognizing_deep.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_gislibpath.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\gislibpath.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\xml_setting_online.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\xml\\setting_online.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_part_chronometer.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_part_chronometer.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_list_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_list_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bkframe.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bkframe.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sample.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sample.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_icon_4_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\icon_4_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp5.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp5.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_icon_5_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\icon_5_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_point.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\point.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_geochemistry.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_geochemistry.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_star_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_star_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_tab_indicator_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_tab_indicator_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_loc1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\loc1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_sjwj.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_sjwj.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_textfield_search_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_textfield_search_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_desc.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_desc.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_a321.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\a321.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_reddeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_reddeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_icon_1_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\icon_1_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_gravel.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_gravel.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_a3o.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\a3o.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_menu_hardkey_panel_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_menu_hardkey_panel_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp6.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp6.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_gravellist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_gravellist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_left_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_left_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new_note.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new_note.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ext_dlg_hisdataview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ext_dlg_hisdataview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sight_jd.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sight_jd.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_pressed_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_pressed_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_switch_thumb_material_dark.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\switch_thumb_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_info1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\info1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_compass1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\compass1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_free_p_edit1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_free_p_edit1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_switch_to_on_mtrl_00012.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_switch_to_on_mtrl_00012.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photocamera1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photocamera1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_voice_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_voice_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_infopopwindowvaluelist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\infopopwindowvaluelist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_attribute_link.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_attribute_link.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_copy_point.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_copy_point.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_star_half_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_star_half_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_main.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_main.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_free_p_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_free_p_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_screen_content.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_screen_content.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_home_btn_bg_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\home_btn_bg_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_digital_bg.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_digital_bg.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_switch_to_on_mtrl_00001.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_switch_to_on_mtrl_00001.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_yksb_result_activity.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\yksb_result_activity.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_textfield_search_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_textfield_search_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_wg_sh.jpg.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\wg_sh.jpg"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_net_web_imageview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\net_web_imageview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_compass.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_compass.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_popup_exit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_popup_exit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_editor.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_editor.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_bluetooth.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_bluetooth.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_cancel_button1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\cancel_button1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_notification_template_big_media.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\notification_template_big_media.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geocolor.gis.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geocolor.gis"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_orangedeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_orangedeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_bdsinfo.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_bdsinfo.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_orangedeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_orangedeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_scrubber_primary_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_scrubber_primary_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_info.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\info.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photo_desc.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photo_desc.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_menu_line_copy.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\menu_line_copy.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_myearthdialog.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\myearthdialog.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_pressed_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_pressed_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_menu_select1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\menu_select1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_move_line.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_move_line.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchsave.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchsave.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_menu_hardkey_panel_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_menu_hardkey_panel_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_menu_select2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\menu_select2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_move.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_move.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_thought.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_thought.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_top.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_top.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_star_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_star_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_cab_background_top_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_cab_background_top_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_f.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\f.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_attribute_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\attribute_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_tab_weixin_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\tab_weixin_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_guass_maptype.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_guass_maptype.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_selector_disabled_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_selector_disabled_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_rating_star_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_rating_star_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_divider_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_divider_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_orangelight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_orangelight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_selector_disabled_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_selector_disabled_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ab_share_pack_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ab_share_pack_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photolist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photolist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_focused_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_focused_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_loc.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\loc.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_bigdataquery.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_bigdataquery.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_yksb.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_yksb.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp7.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp7.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_setting_menu.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\setting_menu.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geochemdict5w.db.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geochemdict5w.db"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_greenlight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_greenlight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_video1.jpg.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\video1.jpg"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_da.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\da.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\xml_setting_all.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\xml\\setting_all.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_cancel.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\cancel.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_activity.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_activity.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_section_edit2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_section_edit2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_maptype.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_maptype.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_support_simple_spinner_dropdown_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\support_simple_spinner_dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_recievemsg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_recievemsg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_switch_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_switch_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_rating_star_on_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_rating_star_on_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_drawbitmapskecth.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\drawbitmapskecth.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_textfield_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_textfield_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_close_compass.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\close_compass.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_drawvectorsketch.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\drawvectorsketch.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_orientation.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\orientation.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_textfield_search_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_textfield_search_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_gpoint.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\gpoint.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_userdef_audio.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\userdef_audio.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_surveylist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\surveylist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_point_move.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_point_move.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_shrink_fade_out_from_bottom.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_shrink_fade_out_from_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_line_edit3.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_line_edit3.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_vscroll_view.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_vscroll_view.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_layer.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\layer.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_switch_button_left_checked.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\switch_button_left_checked.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_left_deep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_left_deep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_activity_chooser_view.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_activity_chooser_view.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_userdef_audio_stop.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\userdef_audio_stop.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_info.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_info.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_star_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_star_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_line_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_line_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_point_edit_2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_point_edit_2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_orientation.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_orientation.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_userpassword.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_userpassword.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_listservice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_listservice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_bddemo.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_bddemo.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_newsection.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\newsection.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchb.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchb.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_out_top.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_slide_out_top.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_search_view.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_search_view.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_logorgmap.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\logorgmap.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_section.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_section.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_droidsansfallback.ttf.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\droidsansfallback.ttf"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_greenlight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_greenlight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e7.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e7.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_screen_pot.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_screen_pot.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ao_bkcolor.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\ao_bkcolor.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_disable_only_material_light.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_primary_text_disable_only_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_point_del.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_point_del.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_greenlight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_greenlight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_popup_menu_item_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_popup_menu_item_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geotszf.db.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geotszf.db"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_maptype0.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\maptype0.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_primary_text_disable_only_material_dark.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_primary_text_disable_only_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_rating_star_on_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_rating_star_on_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_level.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_level.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_b.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\b.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-hdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_section_edit1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_section_edit1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geopoint.gis.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geopoint.gis"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_textfield_search_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_textfield_search_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_folder.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_folder.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_voice_rcd_hint.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\voice_rcd_hint.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_longpressed_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_longpressed_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_info.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\info.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_attribute_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_attribute_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_toolbar_info_query.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\toolbar_info_query.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sdview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sdview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_model3d_layout_view01.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\model3d_layout_view01.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_focused_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_focused_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_section_att_edit_for_inner_table.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_section_att_edit_for_inner_table.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photocamera.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photocamera.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_voice_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_voice_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_service_query.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_service_query.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_point_edit2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_point_edit2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_apply_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\apply_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_webbigdataservice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_webbigdataservice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_locationdlg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\locationdlg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_selector_disabled_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_selector_disabled_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_stopwav.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\stopwav.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_go_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_go_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_orangedeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_orangedeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_reddeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_reddeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_path.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\path.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_screen_pot_selected.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_screen_pot_selected.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_startup.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\startup.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_static_text.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_static_text.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_slide_out_bottom.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_slide_out_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e5.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e5.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_switch_to_on_mtrl_00012.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_switch_to_on_mtrl_00012.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_popup_background_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_popup_background_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_expanded_menu_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_expanded_menu_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_screen_pot.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_screen_pot.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_sleep.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_sleep.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_popup_enter.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_popup_enter.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new_section.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new_section.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_attribute_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_attribute_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_textfield_search_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_textfield_search_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_delete.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_delete.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sectionlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sectionlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_tab_indicator_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_tab_indicator_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_star_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_star_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_bar_title_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_bar_title_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_focused_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_focused_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_check_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bd_asr_popup_bg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bd_asr_popup_bg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_endinfo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\endinfo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_group.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_group.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-mdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_online_maptype.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_online_maptype.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_cab_background_top_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_cab_background_top_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ab_share_pack_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ab_share_pack_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_video.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\video.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_layout_baidu_voice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\layout_baidu_voice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_geopoint_desc.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_geopoint_desc.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_dictionary.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_dictionary.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_compass2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\compass2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_selector_disabled_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_selector_disabled_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_hydpointlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\hydpointlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_info2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\info2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_free_p_edit2.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_free_p_edit2.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_place.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_place.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_smartspace_map.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_smartspace_map.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_screen_pot_selected.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_screen_pot_selected.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_point_add.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_point_add.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_logo1.bmp.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\logo1.bmp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_apply_button1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\apply_button1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_copyroutedlg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\copyroutedlg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_modellist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_modellist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp4.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp4.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_a322.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\a322.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_on_disabled_holo_light.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\btn_radio_on_disabled_holo_light.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_data_query.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_data_query.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new_line2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new_line2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_clear_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_clear_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_textfield_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_textfield_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_secatt.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\secatt.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_listserviceroot.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_listserviceroot.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_hydpoint.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\hydpoint.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_switch_to_on_mtrl_00012.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_switch_to_on_mtrl_00012.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_maptype2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\maptype2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchlinewidth.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchlinewidth.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_html.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\html.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_recognition_cancel.mp3.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\bdspeech_recognition_cancel.mp3"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_ao_uponelevel.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\ao_uponelevel.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_videoview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\videoview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_a35.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\a35.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchcolor.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchcolor.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\xml_setting_offline.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\xml\\setting_offline.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dataqueryresult_headerview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dataqueryresult_headerview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_redlight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_redlight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_bdspeech_recognition_error.mp3.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\bdspeech_recognition_error.mp3"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_home_btn_bg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\home_btn_bg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_switch_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_switch_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_infolin.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_infolin.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_maptype3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\maptype3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_check_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_model3d_menu.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_model3d_menu.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_survey.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\survey.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_switch_to_on_mtrl_00012.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_switch_to_on_mtrl_00012.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_grow_fade_in_from_bottom.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_grow_fade_in_from_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_fossil.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\fossil.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ab_share_pack_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ab_share_pack_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_intab.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\intab.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_greenlight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_greenlight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_cab_background_top_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_cab_background_top_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_edit_base.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_edit_base.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_extphotocamera.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\extphotocamera.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_dict_edit.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_dict_edit.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_webview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\webview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_textfield_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_textfield_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_greendeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_greendeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_apply_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\apply_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_home_btn_bg_s.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\home_btn_bg_s.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_cancel_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\cancel_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_background_cache_hint_selector_material_dark.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_longpressed_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_longpressed_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_voice_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_voice_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_5.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_5.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_loc.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\loc.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_autocomplete.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_autocomplete.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-mdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_attribute_cell.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_attribute_cell.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_draw_sketch.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_draw_sketch.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_testlayout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\testlayout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_ptotosketch.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_ptotosketch.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_toolbar_edit_entity.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\toolbar_edit_entity.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_attilist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_attilist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_googleimage.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\googleimage.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_background_cache_hint_selector_material_light.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_background_cache_hint_selector_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_html_info_webview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\html_info_webview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_scrubber_control_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_star_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_star_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_buttoncircle.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\buttoncircle.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sketchlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sketchlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_sketchbk.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\sketchbk.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_tab_indicator_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_tab_indicator_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ao_titlebutton.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\ao_titlebutton.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_textfield_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_textfield_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_tree_ex.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\tree_ex.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_check_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_twegf.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\twegf.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_greendeep_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_greendeep_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_layerlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\layerlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_webserviceview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\webserviceview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_6.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_6.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_sendmsg.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\activity_sendmsg.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_geopoint_file_desc.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_geopoint_file_desc.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-hdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_menu_moreoverflow_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_menu_moreoverflow_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_alert_dialog_button_bar_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_alert_dialog_button_bar_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_rating_star_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_rating_star_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxxhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_share_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_share_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_textfield_search_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_textfield_search_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_scrubber_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_scrubber_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_check_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_copy_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_copy_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_pop_window.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\pop_window.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-mdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_geochemmul.db.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\geochemmul.db"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_clear_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_clear_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_maptype2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\maptype2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_list_pressed_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_list_pressed_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_flipper.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\flipper.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_rating_star_on_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_rating_star_on_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_tree_ec.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\tree_ec.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_popup_background_mtrl_mult.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_popup_background_mtrl_mult.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_alert_dialog_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_alert_dialog_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_textfield_search_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_textfield_search_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_orangelight_pressed.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_orangelight_pressed.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_draw_bitmap.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\draw_bitmap.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_tab_indicator_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_tab_indicator_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_lssc.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_lssc.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_mydialog.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\mydialog.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_geochem_mulsoil_layerlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\geochem_mulsoil_layerlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_icon_2_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\icon_2_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_cancel_button.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\cancel_button.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e2.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e2.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_close.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\close.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_orangelight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_orangelight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_scrubber_control_to_pressed_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_userdef_audio_rekam.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\userdef_audio_rekam.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_simple_list_item_for_listservicecontent.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\simple_list_item_for_listservicecontent.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_icon_3_n.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\icon_3_n.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_longpressed_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_longpressed_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_voice_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_voice_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_bar_view_list_nav_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_bar_view_list_nav_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\anim_abc_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\anim\\abc_fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_star_half_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_star_half_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_greendeep_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_greendeep_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_fossillist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\fossillist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_infopopwindow.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\infopopwindow.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_scrubber_primary_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_scrubber_primary_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_dlg_bigdataservice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\dlg_bigdataservice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_simple_overlay_action_mode.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_screen_simple_overlay_action_mode.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_new_line3.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_new_line3.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_yksb_camera.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\yksb_camera.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_sys_config.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_sys_config.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ab_share_pack_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ab_share_pack_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_focused_holo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_focused_holo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ext_dlg_listservice.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ext_dlg_listservice.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_radio.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_list_menu_item_radio.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_list_menu_item_layout.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_list_menu_item_layout.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_app_icon.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\app_icon.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\xml_setting_nlu.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\xml\\setting_nlu.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_search_dropdown_item_icons_2line.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_search_dropdown_item_icons_2line.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_list_selector_disabled_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_list_selector_disabled_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_list_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\list_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attributes_container.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attributes_container.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photo.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photo.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_maptype0.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\maptype0.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_clear_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_clear_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_attribute_seperator.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_attribute_seperator.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_btn_radio_on_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\btn_radio_on_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxxhdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_samplelist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\samplelist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_switch_track_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_switch_track_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_right_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_right_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_textfield_default_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_textfield_default_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_rating_star_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_rating_star_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_bottom_map1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\bottom_map1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bk_layoutshape.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bk_layoutshape.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_music1.jpg.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\music1.jpg"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_play1.jpg.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\play1.jpg"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_med.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\med.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_switch_to_on_mtrl_00001.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_switch_to_on_mtrl_00001.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_btn_rating_star_off_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_btn_rating_star_off_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_media_geochem_25w.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\media_geochem_25w.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_frame.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\frame.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_btn_orangelight_normal.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_btn_orangelight_normal.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-mdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-mdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_ab_back_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_ab_back_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_rocktype.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\rocktype.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_folder.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_folder.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_star_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_star_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_commit_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_logo.bmp.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\logo.bmp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_go_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_go_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_common_without_setting.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\common_without_setting.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_star_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_star_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_cunchu.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\cunchu.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_check_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sectionmain.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sectionmain.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dlg_gps_settings.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dlg_gps_settings.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_endinfo.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\endinfo.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_select_dialog_item_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\select_dialog_item_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_textfield_activated_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_textfield_activated_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_maptype1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\maptype1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_navigation.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\navigation.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_list_divider_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_list_divider_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_menu_moreoverflow_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_menu_moreoverflow_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_sketch.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\sketch.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_ao_dicedittype.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\ao_dicedittype.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_engpoint.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\engpoint.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_folder.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_folder.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_switch_to_on_mtrl_00012.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_switch_to_on_mtrl_00012.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\color_abc_secondary_text_material_dark.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\color\\abc_secondary_text_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_paste_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_paste_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_scrubber_primary_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_scrubber_primary_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_photo_desc_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\photo_desc_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_star_half_black_16dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_star_half_black_16dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_e1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\e1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-hdpi_abc_spinner_mtrl_am_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-hdpi\\abc_spinner_mtrl_am_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_att_geopoint_desclist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\att_geopoint_desclist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_scrubber_control_to_pressed_mtrl_005.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\raw_play.jpg.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\raw\\play.jpg"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldpi_ao_screen_pot_selected.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldpi\\ao_screen_pot_selected.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_ic_clear_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_ic_clear_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_menu_selectall_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_menu_selectall_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_btn_radio_to_on_mtrl_000.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_bdspeech_digital_deep_bg.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\bdspeech_digital_deep_bg.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_common_mini.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\common_mini.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_playwav.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\playwav.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ao_uponelevel.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ao_uponelevel.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_screen_simple.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_screen_simple.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_tree_ec.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\tree_ec.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_abc_ic_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\abc_ic_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_erase.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\erase.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_ic_4.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\ic_4.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_medialist_geochem_25w.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\medialist_geochem_25w.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_img_wjj.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\img_wjj.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_abc_list_pressed_holo_dark.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\abc_list_pressed_holo_dark.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_dlg_background.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\dlg_background.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_geochem_mulsoil_layer_headerview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\geochem_mulsoil_layer_headerview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_main.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\main.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-v23_abc_control_background_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-v23\\abc_control_background_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_image_zyml1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\image_zyml1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_abc_ic_voice_search_api_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\abc_ic_voice_search_api_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxxhdpi_abc_ic_star_half_black_36dp.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxxhdpi\\abc_ic_star_half_black_36dp.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_mode_close_item_material.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_mode_close_item_material.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xhdpi_ao_uponelevel.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xhdpi\\ao_uponelevel.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable_amp1.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable\\amp1.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_secattlist.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\secattlist.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_soundview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\soundview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_ic_menu_moreoverflow_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_ic_menu_moreoverflow_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-ldrtl-xxxhdpi_abc_ic_menu_cut_mtrl_alpha.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-ldrtl-xxxhdpi\\abc_ic_menu_cut_mtrl_alpha.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-hdpi_composer_with.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-hdpi\\composer_with.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_secwebview.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\secwebview.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_imageview1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\imageview1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-xxhdpi_abc_btn_radio_to_on_mtrl_015.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-xxhdpi\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_bottom_line_edit1.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\bottom_line_edit1.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\drawable-mdpi_a32.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\drawable-mdpi\\a32.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\res\\merged\\debug\\layout_abc_action_bar_up_container.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\layout\\abc_action_bar_up_container.xml"}]