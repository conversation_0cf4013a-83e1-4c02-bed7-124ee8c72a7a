{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-da\\values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-da\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,131,210,304,374,457,511,582,666,715,763,813,893,972,1039,1124,1198,1270,1357,1425", "endColumns": "72,77,92,68,81,52,69,82,47,46,48,78,77,65,83,72,70,85,66,70", "endOffsets": "125,204,298,368,451,505,576,660,709,757,807,887,966,1033,1118,1192,1264,1351,1419,1491"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,206,299,368,450,503,573,656,704,1684,1733,1812,1890,1956,2040,2113,2184,2270,2417", "endColumns": "72,77,92,68,81,52,69,82,47,46,48,78,77,65,83,72,70,85,66,70", "endOffsets": "123,201,294,363,445,498,568,651,699,746,1728,1807,1885,1951,2035,2108,2179,2265,2332,2483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,958,1051,1144,1238,1332,1425,1520,1618,1709,2757", "endColumns": "90,92,92,93,93,92,94,97,90,90,79", "endOffsets": "953,1046,1139,1233,1327,1420,1515,1613,1704,1795,2832"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "751,842,935,1028,1122,1216,1309,1404,1502,1593,2337", "endColumns": "90,92,92,93,93,92,94,97,90,90,79", "endOffsets": "837,930,1023,1117,1211,1304,1399,1497,1588,1679,2412"}}]}]}