{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-pt\\values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-pt\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,148,227,321,398,476,536,608,697,752,803,861,939,1022,1095,1174,1253,1334,1430,1501", "endColumns": "89,77,92,75,76,58,70,87,53,49,56,76,81,71,77,77,79,94,69,70", "endOffsets": "142,221,315,392,470,530,602,691,746,797,855,933,1016,1089,1168,1247,1328,1424,1495,1567"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,223,316,392,469,528,599,687,741,1729,1786,1863,1945,2017,2095,2173,2253,2348,2504", "endColumns": "89,77,92,75,76,58,70,87,53,49,56,76,81,71,77,77,79,94,69,70", "endOffsets": "140,218,311,387,464,523,594,682,736,786,1781,1858,1940,2012,2090,2168,2248,2343,2413,2570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,1003,1096,1191,1285,1385,1478,1573,1668,1759,2843", "endColumns": "90,92,94,93,99,92,94,94,90,90,85", "endOffsets": "998,1091,1186,1280,1380,1473,1568,1663,1754,1845,2924"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,882,975,1070,1164,1264,1357,1452,1547,1638,2418", "endColumns": "90,92,94,93,99,92,94,94,90,90,85", "endOffsets": "877,970,1065,1159,1259,1352,1447,1542,1633,1724,2499"}}]}]}