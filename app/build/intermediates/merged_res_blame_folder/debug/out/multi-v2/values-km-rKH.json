{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-km-rKH\\values-km-rKH.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-km-rKH\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,131,210,304,375,458,516,591,680,729,777,831,906,982,1053,1134,1213,1292,1386,1455", "endColumns": "72,77,92,69,81,56,73,87,47,46,52,73,74,69,79,77,77,92,67,70", "endOffsets": "125,204,298,369,452,510,585,674,723,771,825,900,976,1047,1128,1207,1286,1380,1449,1521"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,206,299,369,451,508,582,670,718,765,818,892,967,1037,1117,1195,1273,1366,1434", "endColumns": "72,77,92,69,81,56,73,87,47,46,52,73,74,69,79,77,77,92,67,70", "endOffsets": "123,201,294,364,446,503,577,665,713,760,813,887,962,1032,1112,1190,1268,1361,1429,1500"}}]}]}