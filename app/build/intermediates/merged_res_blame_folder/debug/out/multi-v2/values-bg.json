{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-bg\\values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "910,1001,1094,1189,1283,1383,1476,1571,1679,1770,2854", "endColumns": "90,92,94,93,99,92,94,107,90,90,83", "endOffsets": "996,1089,1184,1278,1378,1471,1566,1674,1765,1856,2933"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,882,975,1070,1164,1264,1357,1452,1560,1651,2429", "endColumns": "90,92,94,93,99,92,94,107,90,90,83", "endOffsets": "877,970,1065,1159,1259,1352,1447,1555,1646,1737,2508"}}, {"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-bg\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,143,225,324,402,478,535,611,703,754,803,857,942,1022,1093,1178,1256,1335,1429,1499", "endColumns": "84,80,97,76,74,55,74,90,49,47,52,83,78,69,83,76,77,92,68,70", "endOffsets": "137,219,318,396,472,529,605,697,748,797,851,936,1016,1087,1172,1250,1329,1423,1493,1565"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,140,221,319,396,471,527,602,693,743,1742,1795,1879,1958,2028,2112,2189,2267,2360,2513", "endColumns": "84,80,97,76,74,55,74,90,49,47,52,83,78,69,83,76,77,92,68,70", "endOffsets": "135,216,314,391,466,522,597,688,738,786,1790,1874,1953,2023,2107,2184,2262,2355,2424,2579"}}]}]}