{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-fa\\values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "884,977,1072,1164,1258,1360,1455,1552,1646,1739,2795", "endColumns": "92,94,91,93,101,94,96,93,92,89,81", "endOffsets": "972,1067,1159,1253,1355,1450,1547,1641,1734,1824,2872"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,868,963,1055,1149,1251,1346,1443,1537,1630,2393", "endColumns": "92,94,91,93,101,94,96,93,92,89,81", "endOffsets": "863,958,1050,1144,1246,1341,1438,1532,1625,1715,2470"}}, {"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-fa\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,138,219,316,390,472,527,601,687,738,787,839,922,996,1065,1146,1221,1301,1397,1469", "endColumns": "79,79,95,72,80,53,72,84,49,47,50,81,72,67,79,73,78,94,70,70", "endOffsets": "132,213,310,384,466,521,595,681,732,781,833,916,990,1059,1140,1215,1295,1391,1463,1535"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,215,311,384,465,519,592,677,727,1720,1771,1853,1926,1994,2074,2148,2227,2322,2475", "endColumns": "79,79,95,72,80,53,72,84,49,47,50,81,72,67,79,73,78,94,70,70", "endOffsets": "130,210,306,379,460,514,587,672,722,770,1766,1848,1921,1989,2069,2143,2222,2317,2388,2541"}}]}]}