{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-v16\\values-v16.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\d2fab01d17c089ea45014664a05aa8a0\\core-1.3.2\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}]}