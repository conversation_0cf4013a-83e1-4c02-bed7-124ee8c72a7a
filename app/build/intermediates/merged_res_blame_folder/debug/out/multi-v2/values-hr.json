{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-hr\\values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "888,979,1072,1167,1261,1361,1454,1549,1644,1735,2816", "endColumns": "90,92,94,93,99,92,94,94,90,90,84", "endOffsets": "974,1067,1162,1256,1356,1449,1544,1639,1730,1821,2896"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,866,959,1054,1148,1248,1341,1436,1531,1622,2398", "endColumns": "90,92,94,93,99,92,94,94,90,90,84", "endOffsets": "861,954,1049,1143,1243,1336,1431,1526,1617,1708,2478"}}, {"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-hr\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,133,212,306,374,455,512,587,677,733,787,844,919,1003,1080,1156,1241,1319,1413,1481", "endColumns": "74,77,92,66,79,55,73,88,54,52,55,73,82,75,74,83,76,92,66,70", "endOffsets": "127,206,300,368,449,506,581,671,727,781,838,913,997,1074,1150,1235,1313,1407,1475,1547"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,208,301,368,448,504,578,667,722,1713,1769,1843,1926,2002,2077,2161,2238,2331,2483", "endColumns": "74,77,92,66,79,55,73,88,54,52,55,73,82,75,74,83,76,92,66,70", "endOffsets": "125,203,296,363,443,499,573,662,717,770,1764,1838,1921,1997,2072,2156,2233,2326,2393,2549"}}]}]}