{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-fr-rCA\\values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-fr-rCA\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,139,218,312,398,480,538,625,726,781,832,899,980,1063,1137,1219,1298,1371,1464,1534", "endColumns": "80,77,92,84,80,56,85,99,53,49,65,79,81,72,80,77,71,91,68,73", "endOffsets": "133,212,306,392,474,532,619,720,775,826,893,974,1057,1131,1213,1292,1365,1458,1528,1603"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,214,307,392,473,530,616,716,770,1760,1826,1906,1988,2061,2142,2220,2292,2384,2540", "endColumns": "80,77,92,84,80,56,85,99,53,49,65,79,81,72,80,77,71,91,68,70", "endOffsets": "131,209,302,387,468,525,611,711,765,815,1821,1901,1983,2056,2137,2215,2287,2379,2448,2606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,1012,1105,1203,1298,1398,1491,1584,1679,1770,2855", "endColumns": "90,92,97,94,99,92,92,94,90,90,86", "endOffsets": "1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,2937"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,911,1004,1102,1197,1297,1390,1483,1578,1669,2453", "endColumns": "90,92,97,94,99,92,92,94,90,90,86", "endOffsets": "906,999,1097,1192,1292,1385,1478,1573,1664,1755,2535"}}]}]}