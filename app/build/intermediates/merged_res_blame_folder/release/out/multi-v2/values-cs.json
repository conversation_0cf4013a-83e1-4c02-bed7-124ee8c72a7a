{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-cs\\values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-cs\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,135,215,310,383,463,520,596,684,737,789,844,919,999,1069,1146,1228,1306,1399,1468", "endColumns": "76,78,93,71,78,55,74,86,51,50,53,73,78,68,75,80,76,91,67,70", "endOffsets": "129,209,304,377,457,514,590,678,731,783,838,913,993,1063,1140,1222,1300,1393,1462,1534"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,211,305,377,456,512,587,674,726,1711,1765,1839,1918,1987,2063,2144,2221,2313,2464", "endColumns": "76,78,93,71,78,55,74,86,51,50,53,73,78,68,75,80,76,91,67,70", "endOffsets": "127,206,300,372,451,507,582,669,721,772,1760,1834,1913,1982,2058,2139,2216,2308,2376,2530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,976,1069,1164,1258,1352,1445,1540,1637,1728,2798", "endColumns": "90,92,94,93,93,92,94,96,90,90,82", "endOffsets": "971,1064,1159,1253,1347,1440,1535,1632,1723,1814,2876"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "777,868,961,1056,1150,1244,1337,1432,1529,1620,2381", "endColumns": "90,92,94,93,93,92,94,96,90,90,82", "endOffsets": "863,956,1051,1145,1239,1332,1427,1524,1615,1706,2459"}}]}]}