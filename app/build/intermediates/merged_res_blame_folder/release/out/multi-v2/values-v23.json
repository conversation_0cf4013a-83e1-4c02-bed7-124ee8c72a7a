{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-v23\\values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-v23\\styles.xml", "from": {"startLines": "2,4,6,8,10,18,26,28,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,213,369,465,573,1203,1845,1971,2107,2235", "endLines": "3,5,7,9,17,25,27,29,31,33", "endColumns": "12,12,12,12,12,12,12,12,12,12", "endOffsets": "207,363,459,567,1197,1839,1965,2101,2229,2363"}, "to": {"startLines": "2,4,6,8,10,18,28,34,36,38", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,363,457,563,1185,2065,2413,2547,2673", "endLines": "3,5,7,9,17,25,29,35,37,39", "endColumns": "12,12,12,12,12,12,12,12,12,12", "endOffsets": "204,358,452,558,1180,1814,2184,2542,2668,2800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\204d56b624e751e7990ccfa8a11e8889\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "3035", "endLines": "46", "endColumns": "12", "endOffsets": "3180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "34,35,39,46", "startColumns": "4,4,4,4", "startOffsets": "2267,2386,2735,3294", "endLines": "34,35,42,49", "endColumns": "118,126,12,12", "endOffsets": "2381,2508,2954,3519"}, "to": {"startLines": "26,27,30,40", "startColumns": "4,4,4,4", "startOffsets": "1819,1938,2189,2805", "endLines": "26,27,33,43", "endColumns": "118,126,12,12", "endOffsets": "1933,2060,2408,3030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\e822c6e541770b57899944d7b2242c98\\material-1.2.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,320", "endLines": "5,9", "endColumns": "10,10", "endOffsets": "315,588"}, "to": {"startLines": "47,51", "startColumns": "4,4", "startOffsets": "3185,3450", "endLines": "50,54", "endColumns": "10,10", "endOffsets": "3445,3718"}}]}]}