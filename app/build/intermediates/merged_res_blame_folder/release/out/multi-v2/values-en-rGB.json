{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-en-rGB\\values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-en-rGB\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,132,211,305,376,455,510,581,667,716,763,816,890,965,1035,1111,1186,1261,1351,1422", "endColumns": "73,77,92,69,77,53,69,84,47,45,51,72,73,68,74,73,73,88,69,70", "endOffsets": "126,205,299,370,449,504,575,661,710,757,810,884,959,1029,1105,1180,1255,1345,1416,1488"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,207,300,370,448,502,572,657,705,1688,1740,1813,1887,1956,2031,2105,2179,2268,2421", "endColumns": "73,77,92,69,77,53,69,84,47,45,51,72,73,68,74,73,73,88,69,70", "endOffsets": "124,202,295,365,443,497,567,652,700,746,1735,1808,1882,1951,2026,2100,2174,2263,2333,2487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "869,960,1053,1148,1242,1342,1435,1530,1624,1715,2762", "endColumns": "90,92,94,93,99,92,94,93,90,90,82", "endOffsets": "955,1048,1143,1237,1337,1430,1525,1619,1710,1801,2840"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "751,842,935,1030,1124,1224,1317,1412,1506,1597,2338", "endColumns": "90,92,94,93,99,92,94,93,90,90,82", "endOffsets": "837,930,1025,1119,1219,1312,1407,1501,1592,1683,2416"}}]}]}