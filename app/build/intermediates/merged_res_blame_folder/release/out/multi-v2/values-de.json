{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-de\\values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "880,972,1066,1162,1263,1370,1470,1574,1672,1770,2832", "endColumns": "91,93,95,100,106,99,103,97,97,96,81", "endOffsets": "967,1061,1157,1258,1365,1465,1569,1667,1765,1862,2909"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,853,947,1043,1144,1251,1351,1455,1553,1651,2418", "endColumns": "91,93,95,100,106,99,103,97,97,96,81", "endOffsets": "848,942,1038,1139,1246,1346,1450,1548,1646,1743,2495"}}, {"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,133,212,306,375,458,515,591,677,726,773,826,908,982,1052,1134,1208,1286,1379,1452", "endColumns": "74,77,92,67,81,55,74,84,47,45,51,80,72,68,80,72,76,91,71,70", "endOffsets": "127,206,300,369,452,509,585,671,720,767,820,902,976,1046,1128,1202,1280,1373,1446,1518"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,208,301,369,451,507,582,667,715,1748,1800,1881,1954,2023,2104,2177,2254,2346,2500", "endColumns": "74,77,92,67,81,55,74,84,47,45,51,80,72,68,80,72,76,91,71,70", "endOffsets": "125,203,296,364,446,502,577,662,710,756,1795,1876,1949,2018,2099,2172,2249,2341,2413,2566"}}]}]}