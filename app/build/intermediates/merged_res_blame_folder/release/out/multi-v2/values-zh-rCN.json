{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-zh-rCN\\values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-zh-rCN\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,124,202,296,362,433,486,554,631,679,726,775,842,909,975,1043,1110,1179,1262,1327", "endColumns": "65,76,92,64,69,51,66,75,46,45,47,65,65,64,66,65,67,81,63,70", "endOffsets": "118,196,290,356,427,480,548,625,673,720,769,836,903,969,1037,1104,1173,1256,1321,1393"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,198,291,356,426,478,545,621,668,1647,1695,1761,1827,1892,1959,2025,2093,2175,2318", "endColumns": "65,76,92,64,69,51,66,75,46,45,47,65,65,64,66,65,67,81,63,70", "endOffsets": "116,193,286,351,421,473,540,616,663,709,1690,1756,1822,1887,1954,2020,2088,2170,2234,2384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,923,1016,1113,1209,1303,1396,1491,1583,1674,2662", "endColumns": "90,92,96,95,93,92,94,91,90,90,78", "endOffsets": "918,1011,1108,1204,1298,1391,1486,1578,1669,1760,2736"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,805,898,995,1091,1185,1278,1373,1465,1556,2239", "endColumns": "90,92,96,95,93,92,94,91,90,90,78", "endOffsets": "800,893,990,1086,1180,1273,1368,1460,1551,1642,2313"}}]}]}