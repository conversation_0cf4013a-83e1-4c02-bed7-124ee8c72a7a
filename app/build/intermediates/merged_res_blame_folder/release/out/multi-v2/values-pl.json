{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-pl\\values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-pl\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,144,223,317,390,469,526,606,696,746,794,847,927,1007,1077,1157,1240,1319,1413,1480", "endColumns": "85,77,92,71,77,55,78,88,48,46,51,78,78,68,78,81,77,92,65,70", "endOffsets": "138,217,311,384,463,520,600,690,740,788,841,921,1001,1071,1151,1234,1313,1407,1474,1546"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,219,312,384,462,518,597,686,735,1721,1773,1852,1931,2000,2079,2161,2239,2332,2481", "endColumns": "85,77,92,71,77,55,78,88,48,46,51,78,78,68,78,81,77,92,65,70", "endOffsets": "136,214,307,379,457,513,592,681,730,777,1768,1847,1926,1995,2074,2156,2234,2327,2393,2547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "897,988,1081,1176,1270,1371,1464,1559,1654,1745,2817", "endColumns": "90,92,94,93,100,92,94,94,90,90,82", "endOffsets": "983,1076,1171,1265,1366,1459,1554,1649,1740,1831,2895"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,873,966,1061,1155,1256,1349,1444,1539,1630,2398", "endColumns": "90,92,94,93,100,92,94,94,90,90,82", "endOffsets": "868,961,1056,1150,1251,1344,1439,1534,1625,1716,2476"}}]}]}