{"logs": [{"outputFile": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-ja\\values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\7-30\\test\\app\\src\\main\\res\\values-ja\\strings.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,125,203,295,359,435,488,557,636,685,732,781,854,924,990,1064,1131,1198,1280,1348", "endColumns": "66,76,90,62,74,51,67,77,47,45,47,71,68,64,72,65,65,80,66,70", "endOffsets": "119,197,289,353,429,482,551,630,679,726,775,848,918,984,1058,1125,1192,1274,1342,1414"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,22,23,24,25,26,27,28,29,30,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,199,290,353,428,480,548,626,674,1657,1705,1777,1846,1911,1984,2050,2116,2197,2343", "endColumns": "66,76,90,62,74,51,67,77,47,45,47,71,68,64,72,65,65,80,66,70", "endOffsets": "117,194,285,348,423,475,543,621,669,715,1700,1772,1841,1906,1979,2045,2111,2192,2259,2409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-2\\files-2.1\\20cb719c6e7f450c785cfe641873baec\\appcompat-1.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "841,932,1025,1120,1214,1314,1407,1502,1596,1687,2691", "endColumns": "90,92,94,93,99,92,94,93,90,90,78", "endOffsets": "927,1020,1115,1209,1309,1402,1497,1591,1682,1773,2765"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20,21,31", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "720,811,904,999,1093,1193,1286,1381,1475,1566,2264", "endColumns": "90,92,94,93,99,92,94,93,90,90,78", "endOffsets": "806,899,994,1088,1188,1281,1376,1470,1561,1652,2338"}}]}]}