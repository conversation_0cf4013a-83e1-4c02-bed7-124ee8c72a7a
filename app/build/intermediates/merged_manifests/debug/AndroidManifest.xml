<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.AoRGMap"
    android:versionCode="1"
    android:versionName="3.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="23" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <application
        android:name="com.AoRGMap.RGMapApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:hardwareAccelerated="true"
        android:icon="@drawable/app_icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.Light" >
        <uses-library android:name="android.test.runner" />

        <meta-data
            android:name="com.baidu.speech.APP_ID"
            android:value="10127572" />
        <meta-data
            android:name="com.baidu.speech.API_KEY"
            android:value="rZh0DLQKcqMzYjNSf2mwVByA" />
        <meta-data
            android:name="com.baidu.speech.SECRET_KEY"
            android:value="3137fd3bec47ff381c37505583a044c4" />

        <service
            android:name="com.baidu.speech.VoiceRecognitionService"
            android:exported="false" />

        <activity
            android:name="com.AoRGMap.AoRGMapActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:label="@string/app_name"
            android:screenOrientation="portrait" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.GisLibPathActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.StartupActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:label="@string/startup_title"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.MenuDemo"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.ColorSelectActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.SubnoSelectActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.NoteInfoActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.SubInfoActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.LineInfoActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.LayerManagerActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoGIS.ui.FileListActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoDevBase.ui.filebrowser.FileListActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoDevBase.ui.AttributeLinkActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoDevBase.config.MapParamActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoDevBase.ui.AoCompassActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SysConfigActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttGPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttRoutingActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttGBoundaryActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttAttitudeActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttFossilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttPhotoActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttOrecheckActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttFreeLineActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttSampleActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttSketchActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttGravelViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttGravelActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttAttiActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttRemainActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttEngPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttHydPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.EngPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.HydPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttLibEngPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttLibHydPointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.DrawSketchActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.DrawSecSketchActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.drawbitmap.DrawBitmapActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.SectionActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSectionActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSurveyActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSlayerActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSsampleActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSecattActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSphotoActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSfossilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSsketchActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.AttSgpointActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.PMDicListActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttSandSediActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoRockActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoSoilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoWaterActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25RockActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25SoilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25WaterActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25StreamActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttSandSediRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoRockRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoSoilRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoWaterRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25RockRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25SoilRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25WaterRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.Att25StreamRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttStremSediActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttSoilSediActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulsoilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulwaterActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulstremActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttStremSediRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttSoilSediRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulsoilRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulstremRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttMulwaterRActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemStream5wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSoil5wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemRock5wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemStream25wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSoil25wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemRock25wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulMedia25wActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSamNationalActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilLayActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilLayerActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSedimentActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulWaterActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.earth.MulActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.camera.CameraTakerActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.camera.CameraTaker2Activity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.OrientationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.ExtOrientationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.ViewVideo"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.ViewRouteActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.ImageViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.SoundViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.RouteSumActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.ViewSectionActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.AoRGMap.ViewSectionDocActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttRemainLinActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.pm.PmXjActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.MyImageView1Activity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.MySoundViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.MyVideoViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.net.WebServiceHtmlActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.net.WebServiceViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttGPointDescActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.MenuConfigActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.Util.ExtAudioRecorderActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.Util.ExtDrawBitmapActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.AoDlgRGMapActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.ExtLayer.AttLargeScaleActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.DataQueryConfigActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.ExtLayer.DataQueryListActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.BigDataQueryActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.ExtListFileServiceActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.SecExtListFileServiceActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.BigDataWebServiceViewActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.SmartSpaceServiceMapActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.ExtStorageActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttExtMediaDataActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.AttExtFileDataActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.InnerTableListActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.prb.InnerTableAttributeActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.HisDataListFileServiceActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SmartService.WebBigDataQueryActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.threeDmodel.DmodelActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.threeDmodel.Model3DMainActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.yksb.CameraActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.yksb.YksbReslutActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.SplitScreenActivity"
            android:configChanges="orientation|keyboardHidden|locale"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityMain"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.DeviceDefault.Light" >
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityOnline"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="在线识别"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityOffline"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="离线语法识别"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityNlu"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="语义解析（本地和在线）"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityAllRecog"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="全部识别功能"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.ActivityWakeUp"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="唤醒词"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.mini.ActivityMiniRecog"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="精简版识别"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.mini.ActivityMiniWakeUp"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="精简版唤醒词"
            android:screenOrientation="portrait" >
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.baidu.activity.setting.OnlineSetting"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="在线识别设置"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.DeviceDefault.Light" />
        <activity
            android:name="com.AoRGMap.baidu.activity.setting.OfflineSetting"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="离线语法设置"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.DeviceDefault.Light" />
        <activity
            android:name="com.AoRGMap.baidu.activity.setting.NluSetting"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="语义设置"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.DeviceDefault.Light" />
        <activity
            android:name="com.AoRGMap.baidu.activity.setting.AllSetting"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:label="全部识别设置"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.DeviceDefault.Light" />
        <activity android:name="com.AoDevBase.ui.AttributeExtActivity" />
    </application>

</manifest>