package org.kxml2.wap;

/* loaded from: classes.dex */
public interface Wbxml {
    public static final int END = 1;
    public static final int ENTITY = 2;
    public static final int EXT_0 = 192;
    public static final int EXT_1 = 193;
    public static final int EXT_2 = 194;
    public static final int EXT_I_0 = 64;
    public static final int EXT_I_1 = 65;
    public static final int EXT_I_2 = 66;
    public static final int EXT_T_0 = 128;
    public static final int EXT_T_1 = 129;
    public static final int EXT_T_2 = 130;
    public static final int LITERAL = 4;
    public static final int LITERAL_A = 132;
    public static final int LITERAL_AC = 196;
    public static final int LITERAL_C = 68;
    public static final int OPAQUE = 195;
    public static final int PI = 67;
    public static final int STR_I = 3;
    public static final int STR_T = 131;
    public static final int SWITCH_PAGE = 0;
}
