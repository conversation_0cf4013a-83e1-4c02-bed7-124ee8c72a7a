package org.apache.http.nio;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.WritableByteChannel;

/* loaded from: classes.dex */
public class ContentEncoderChannel implements WritableByteChannel {
    private final ContentEncoder contentEncoder;

    public ContentEncoderChannel(ContentEncoder contentEncoder) {
        this.contentEncoder = contentEncoder;
    }

    @Override // java.nio.channels.WritableByteChannel
    public int write(ByteBuffer src) throws IOException {
        return this.contentEncoder.write(src);
    }

    @Override // java.nio.channels.Channel, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
    }

    @Override // java.nio.channels.Channel
    public boolean isOpen() {
        return true;
    }
}
