package org.apache.http.nio;

import java.io.IOException;
import org.apache.http.HttpException;

/* loaded from: classes.dex */
public interface NHttpServerEventHandler {
    void closed(NHttpServerConnection nHttpServerConnection);

    void connected(NHttpServerConnection nHttpServerConnection) throws IOException, HttpException;

    void endOfInput(NHttpServerConnection nHttpServerConnection) throws IOException;

    void exception(NHttpServerConnection nHttpServerConnection, Exception exc);

    void inputReady(NHttpServerConnection nHttpServerConnection, ContentDecoder contentDecoder) throws IOException, HttpException;

    void outputReady(NHttpServerConnection nHttpServerConnection, ContentEncoder contentEncoder) throws IOException, HttpException;

    void requestReceived(NHttpServerConnection nHttpServerConnection) throws IOEx<PERSON>, HttpException;

    void responseReady(NHttpServerConnection nHttpServerConnection) throws IOException, HttpException;

    void timeout(NHttpServerConnection nHttpServerConnection) throws IOException;
}
