package org.apache.http.nio.entity;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.entity.AbstractHttpEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.ContentEncoder;
import org.apache.http.nio.IOControl;

@NotThreadSafe
/* loaded from: classes.dex */
public class NByteArrayEntity extends AbstractHttpEntity implements HttpAsyncContentProducer, ProducingNHttpEntity {
    private final byte[] b;
    private final ByteBuffer buf;

    @Deprecated
    protected final ByteBuffer buffer;

    @Deprecated
    protected final byte[] content;
    private final int len;
    private final int off;

    public NByteArrayEntity(byte[] b, ContentType contentType) {
        if (b == null) {
            throw new IllegalArgumentException("Source byte array may not be null");
        }
        this.b = b;
        this.off = 0;
        this.len = b.length;
        this.buf = ByteBuffer.wrap(b);
        this.content = b;
        this.buffer = this.buf;
        if (contentType != null) {
            setContentType(contentType.toString());
        }
    }

    public NByteArrayEntity(byte[] b, int off, int len, ContentType contentType) {
        if (b == null) {
            throw new IllegalArgumentException("Source byte array may not be null");
        }
        if (off < 0 || off > b.length || len < 0 || off + len < 0 || off + len > b.length) {
            throw new IndexOutOfBoundsException("off: " + off + " len: " + len + " b.length: " + b.length);
        }
        this.b = b;
        this.off = off;
        this.len = len;
        this.buf = ByteBuffer.wrap(b, off, len);
        this.content = b;
        this.buffer = this.buf;
        if (contentType != null) {
            setContentType(contentType.toString());
        }
    }

    public NByteArrayEntity(byte[] b) {
        this(b, null);
    }

    public NByteArrayEntity(byte[] b, int off, int len) {
        this(b, off, len, null);
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.buf.rewind();
    }

    @Override // org.apache.http.nio.entity.ProducingNHttpEntity
    public void finish() {
        close();
    }

    @Override // org.apache.http.nio.entity.HttpAsyncContentProducer
    public void produceContent(ContentEncoder encoder, IOControl ioctrl) throws IOException {
        encoder.write(this.buf);
        if (!this.buf.hasRemaining()) {
            encoder.complete();
        }
    }

    @Override // org.apache.http.HttpEntity
    public long getContentLength() {
        return this.len;
    }

    @Override // org.apache.http.HttpEntity
    public boolean isRepeatable() {
        return true;
    }

    @Override // org.apache.http.HttpEntity
    public boolean isStreaming() {
        return false;
    }

    @Override // org.apache.http.HttpEntity
    public InputStream getContent() {
        return new ByteArrayInputStream(this.b, this.off, this.len);
    }

    @Override // org.apache.http.HttpEntity
    public void writeTo(OutputStream outstream) throws IOException {
        if (outstream == null) {
            throw new IllegalArgumentException("Output stream may not be null");
        }
        outstream.write(this.b, this.off, this.len);
        outstream.flush();
    }
}
