package org.apache.http.nio.entity;

import java.io.IOException;
import java.io.InputStream;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.io.BufferInfo;
import org.apache.http.nio.util.ContentInputBuffer;

@NotThreadSafe
/* loaded from: classes.dex */
public class ContentInputStream extends InputStream {
    private final ContentInputBuffer buffer;

    public ContentInputStream(ContentInputBuffer buffer) {
        if (buffer == null) {
            throw new IllegalArgumentException("Input buffer may not be null");
        }
        this.buffer = buffer;
    }

    @Override // java.io.InputStream
    public int available() throws IOException {
        return this.buffer instanceof BufferInfo ? ((BufferInfo) this.buffer).length() : super.available();
    }

    @Override // java.io.InputStream
    public int read(byte[] b, int off, int len) throws IOException {
        return this.buffer.read(b, off, len);
    }

    @Override // java.io.InputStream
    public int read(byte[] b) throws IOException {
        if (b == null) {
            return 0;
        }
        return this.buffer.read(b, 0, b.length);
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        return this.buffer.read();
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
        byte[] tmp = new byte[1024];
        while (this.buffer.read(tmp, 0, tmp.length) >= 0) {
        }
        super.close();
    }
}
