package org.apache.http.nio.util;

import java.io.IOException;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.nio.ContentEncoder;

@NotThreadSafe
/* loaded from: classes.dex */
public class SimpleOutputBuffer extends ExpandableBuffer implements ContentOutputBuffer {
    private boolean endOfStream;

    public SimpleOutputBuffer(int buffersize, ByteBufferAllocator allocator) {
        super(buffersize, allocator);
        this.endOfStream = false;
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public int produceContent(ContentEncoder encoder) throws IOException {
        setOutputMode();
        int bytesWritten = encoder.write(this.buffer);
        if (!hasData() && this.endOfStream) {
            encoder.complete();
        }
        return bytesWritten;
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public void write(byte[] b, int off, int len) throws IOException {
        if (b != null && !this.endOfStream) {
            setInputMode();
            ensureCapacity(this.buffer.position() + len);
            this.buffer.put(b, off, len);
        }
    }

    public void write(byte[] b) throws IOException {
        if (b != null && !this.endOfStream) {
            write(b, 0, b.length);
        }
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public void write(int b) throws IOException {
        if (!this.endOfStream) {
            setInputMode();
            ensureCapacity(capacity() + 1);
            this.buffer.put((byte) b);
        }
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public void reset() {
        super.clear();
        this.endOfStream = false;
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public void flush() {
    }

    @Override // org.apache.http.nio.util.ContentOutputBuffer
    public void writeCompleted() {
        this.endOfStream = true;
    }

    public void shutdown() {
        this.endOfStream = true;
    }
}
