package org.apache.http.nio.protocol;

import org.apache.http.ConnectionReuseStrategy;
import org.apache.http.protocol.HttpContext;
import org.apache.http.protocol.HttpProcessor;

/* loaded from: classes.dex */
public interface HttpAsyncRequestExecutionHandler<T> extends HttpAsyncRequestProducer, HttpAsyncResponseConsumer<T> {
    ConnectionReuseStrategy getConnectionReuseStrategy();

    HttpContext getContext();

    HttpProcessor getHttpProcessor();
}
