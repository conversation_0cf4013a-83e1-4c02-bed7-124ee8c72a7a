package org.apache.http.nio.protocol;

import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.protocol.HttpContext;

/* loaded from: classes.dex */
class NullRequestHandler implements HttpAsyncRequestHandler<Object> {
    @Override // org.apache.http.nio.protocol.HttpAsyncRequestHandler
    public HttpAsyncRequestConsumer<Object> processRequest(HttpRequest request, HttpContext context) {
        return new NullRequestConsumer();
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncRequestHandler
    public void handle(Object obj, HttpAsyncExchange httpexchange, HttpContext context) {
        HttpResponse response = httpexchange.getResponse();
        response.setStatusCode(HttpStatus.SC_NOT_IMPLEMENTED);
        httpexchange.submitResponse(new ErrorResponseProducer(response, new NStringEntity("Service not implemented", ContentType.TEXT_PLAIN), true));
    }
}
