package org.apache.http.nio.protocol;

import java.io.Closeable;
import java.io.IOException;
import org.apache.http.HttpException;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.nio.ContentEncoder;
import org.apache.http.nio.IOControl;
import org.apache.http.protocol.HttpContext;

/* loaded from: classes.dex */
public interface HttpAsyncRequestProducer extends Closeable {
    void failed(Exception exc);

    HttpRequest generateRequest() throws IOException, HttpException;

    HttpHost getTarget();

    boolean isRepeatable();

    void produceContent(ContentEncoder contentEncoder, IOControl iOControl) throws IOException;

    void requestCompleted(HttpContext httpContext);

    void resetRequest() throws IOException;
}
