package org.apache.http.nio.protocol;

import java.io.Closeable;
import java.io.IOException;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.nio.ContentDecoder;
import org.apache.http.nio.IOControl;
import org.apache.http.protocol.HttpContext;

/* loaded from: classes.dex */
public interface HttpAsyncRequestConsumer<T> extends Closeable {
    void consumeContent(ContentDecoder contentDecoder, IOControl iOControl) throws IOException;

    void failed(Exception exc);

    Exception getException();

    T getResult();

    boolean isDone();

    void requestCompleted(HttpContext httpContext);

    void requestReceived(HttpRequest httpRequest) throws HttpException, IOException;
}
