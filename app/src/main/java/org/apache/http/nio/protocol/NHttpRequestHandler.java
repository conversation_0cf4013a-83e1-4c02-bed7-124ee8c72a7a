package org.apache.http.nio.protocol;

import java.io.IOException;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.nio.entity.ConsumingNHttpEntity;
import org.apache.http.protocol.HttpContext;

@Deprecated
/* loaded from: classes.dex */
public interface NHttpRequestHandler {
    ConsumingNHttpEntity entityRequest(HttpEntityEnclosingRequest httpEntityEnclosingRequest, HttpContext httpContext) throws HttpException, IOException;

    void handle(HttpRequest httpRequest, HttpResponse httpResponse, NHttpResponseTrigger nHttpResponseTrigger, HttpContext httpContext) throws HttpException, IOException;
}
