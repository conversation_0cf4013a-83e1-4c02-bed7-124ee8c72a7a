package org.apache.http.nio.protocol;

import java.io.IOException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpException;
import org.apache.http.HttpResponse;
import org.apache.http.annotation.ThreadSafe;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.ContentDecoder;
import org.apache.http.nio.IOControl;
import org.apache.http.protocol.HttpContext;

@ThreadSafe
/* loaded from: classes.dex */
public abstract class AbstractAsyncResponseConsumer<T> implements HttpAsyncResponseConsumer<T> {
    private volatile boolean completed;
    private volatile Exception ex;
    private volatile T result;

    protected abstract T buildResult(HttpContext httpContext) throws Exception;

    protected abstract void onContentReceived(ContentDecoder contentDecoder, IOControl iOControl) throws IOException;

    protected abstract void onEntityEnclosed(HttpEntity httpEntity, ContentType contentType) throws IOException;

    protected abstract void onResponseReceived(HttpResponse httpResponse) throws HttpException, IOException;

    protected abstract void releaseResources();

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public final synchronized void responseReceived(HttpResponse response) throws IOException, HttpException {
        onResponseReceived(response);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            ContentType contentType = ContentType.getOrDefault(entity);
            onEntityEnclosed(entity, contentType);
        }
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public final synchronized void consumeContent(ContentDecoder decoder, IOControl ioctrl) throws IOException {
        onContentReceived(decoder, ioctrl);
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public final synchronized void responseCompleted(HttpContext context) {
        if (!this.completed) {
            this.completed = true;
            try {
                try {
                    this.result = buildResult(context);
                } finally {
                    releaseResources();
                }
            } catch (Exception ex) {
                this.ex = ex;
                releaseResources();
            }
        }
    }

    @Override // org.apache.http.concurrent.Cancellable
    public final synchronized boolean cancel() {
        boolean z = true;
        synchronized (this) {
            if (this.completed) {
                z = false;
            } else {
                this.completed = true;
                releaseResources();
            }
        }
        return z;
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public final synchronized void failed(Exception ex) {
        if (!this.completed) {
            this.completed = true;
            this.ex = ex;
            releaseResources();
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public final synchronized void close() {
        if (!this.completed) {
            this.completed = true;
            releaseResources();
        }
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public Exception getException() {
        return this.ex;
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public T getResult() {
        return this.result;
    }

    @Override // org.apache.http.nio.protocol.HttpAsyncResponseConsumer
    public boolean isDone() {
        return this.completed;
    }
}
