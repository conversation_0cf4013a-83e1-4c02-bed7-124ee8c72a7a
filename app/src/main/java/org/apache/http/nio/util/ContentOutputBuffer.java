package org.apache.http.nio.util;

import java.io.IOException;
import org.apache.http.nio.ContentEncoder;

/* loaded from: classes.dex */
public interface ContentOutputBuffer {
    @Deprecated
    void flush() throws IOException;

    int produceContent(ContentEncoder contentEncoder) throws IOException;

    void reset();

    void write(int i) throws IOException;

    void write(byte[] bArr, int i, int i2) throws IOException;

    void writeCompleted() throws IOException;
}
