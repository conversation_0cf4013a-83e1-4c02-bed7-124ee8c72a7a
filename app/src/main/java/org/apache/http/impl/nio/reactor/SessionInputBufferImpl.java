package org.apache.http.impl.nio.reactor;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CoderResult;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.nio.reactor.SessionInputBuffer;
import org.apache.http.nio.util.ByteBufferAllocator;
import org.apache.http.nio.util.ExpandableBuffer;
import org.apache.http.nio.util.HeapByteBufferAllocator;
import org.apache.http.params.HttpParams;
import org.apache.http.params.HttpProtocolParams;
import org.apache.http.util.CharArrayBuffer;

@NotThreadSafe
/* loaded from: classes.dex */
public class SessionInputBufferImpl extends ExpandableBuffer implements SessionInputBuffer {
    private CharBuffer charbuffer;
    private CharsetDecoder chardecoder;
    private Charset charset;

    public SessionInputBufferImpl(int buffersize, int linebuffersize, ByteBufferAllocator allocator, HttpParams params) {
        super(buffersize, allocator);
        this.charbuffer = null;
        this.charset = null;
        this.chardecoder = null;
        this.charbuffer = CharBuffer.allocate(linebuffersize);
        this.charset = Charset.forName(HttpProtocolParams.getHttpElementCharset(params));
        this.chardecoder = this.charset.newDecoder();
        this.chardecoder.onMalformedInput(HttpProtocolParams.getMalformedInputAction(params));
        this.chardecoder.onUnmappableCharacter(HttpProtocolParams.getUnmappableInputAction(params));
    }

    public SessionInputBufferImpl(int buffersize, int linebuffersize, HttpParams params) {
        this(buffersize, linebuffersize, new HeapByteBufferAllocator(), params);
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int fill(ReadableByteChannel channel) throws IOException {
        if (channel == null) {
            throw new IllegalArgumentException("Channel may not be null");
        }
        setInputMode();
        if (!this.buffer.hasRemaining()) {
            expand();
        }
        int readNo = channel.read(this.buffer);
        return readNo;
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int read() {
        setOutputMode();
        return this.buffer.get() & 255;
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int read(ByteBuffer dst, int maxLen) {
        if (dst == null) {
            return 0;
        }
        setOutputMode();
        int len = Math.min(dst.remaining(), maxLen);
        int chunk = Math.min(this.buffer.remaining(), len);
        for (int i = 0; i < chunk; i++) {
            dst.put(this.buffer.get());
        }
        return chunk;
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int read(ByteBuffer dst) {
        if (dst == null) {
            return 0;
        }
        return read(dst, dst.remaining());
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int read(WritableByteChannel dst, int maxLen) throws IOException {
        if (dst == null) {
            return 0;
        }
        setOutputMode();
        if (this.buffer.remaining() > maxLen) {
            int oldLimit = this.buffer.limit();
            int newLimit = oldLimit - (this.buffer.remaining() - maxLen);
            this.buffer.limit(newLimit);
            int write = dst.write(this.buffer);
            this.buffer.limit(oldLimit);
            return write;
        }
        return dst.write(this.buffer);
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public int read(WritableByteChannel dst) throws IOException {
        if (dst == null) {
            return 0;
        }
        setOutputMode();
        return dst.write(this.buffer);
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public boolean readLine(CharArrayBuffer linebuffer, boolean endOfStream) throws CharacterCodingException {
        CoderResult result;
        setOutputMode();
        int pos = -1;
        boolean hasLine = false;
        int i = this.buffer.position();
        while (true) {
            if (i >= this.buffer.limit()) {
                break;
            }
            int b = this.buffer.get(i);
            if (b != 10) {
                i++;
            } else {
                hasLine = true;
                pos = i + 1;
                break;
            }
        }
        if (!hasLine) {
            if (endOfStream && this.buffer.hasRemaining()) {
                pos = this.buffer.limit();
            } else {
                return false;
            }
        }
        int origLimit = this.buffer.limit();
        this.buffer.limit(pos);
        int len = this.buffer.limit() - this.buffer.position();
        linebuffer.ensureCapacity(len);
        this.chardecoder.reset();
        do {
            result = this.chardecoder.decode(this.buffer, this.charbuffer, true);
            if (result.isError()) {
                result.throwException();
            }
            if (result.isOverflow()) {
                this.charbuffer.flip();
                linebuffer.append(this.charbuffer.array(), this.charbuffer.position(), this.charbuffer.remaining());
                this.charbuffer.clear();
            }
        } while (!result.isUnderflow());
        this.buffer.limit(origLimit);
        this.chardecoder.flush(this.charbuffer);
        this.charbuffer.flip();
        if (this.charbuffer.hasRemaining()) {
            linebuffer.append(this.charbuffer.array(), this.charbuffer.position(), this.charbuffer.remaining());
        }
        int l = linebuffer.length();
        if (l <= 0) {
            return true;
        }
        if (linebuffer.charAt(l - 1) == '\n') {
            l--;
            linebuffer.setLength(l);
        }
        if (l <= 0 || linebuffer.charAt(l - 1) != '\r') {
            return true;
        }
        linebuffer.setLength(l - 1);
        return true;
    }

    @Override // org.apache.http.nio.reactor.SessionInputBuffer
    public String readLine(boolean endOfStream) throws CharacterCodingException {
        CharArrayBuffer charbuffer = new CharArrayBuffer(64);
        boolean found = readLine(charbuffer, endOfStream);
        if (found) {
            return charbuffer.toString();
        }
        return null;
    }
}
