package org.apache.http.impl.nio;

import javax.net.ssl.SSLContext;
import org.apache.http.HttpRequestFactory;
import org.apache.http.annotation.Immutable;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.DefaultHttpRequestFactory;
import org.apache.http.nio.NHttpConnectionFactory;
import org.apache.http.nio.reactor.IOSession;
import org.apache.http.nio.reactor.ssl.SSLIOSession;
import org.apache.http.nio.reactor.ssl.SSLMode;
import org.apache.http.nio.reactor.ssl.SSLSetupHandler;
import org.apache.http.nio.util.ByteBufferAllocator;
import org.apache.http.nio.util.HeapByteBufferAllocator;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;

@Immutable
/* loaded from: classes.dex */
public class SSLNHttpServerConnectionFactory implements NHttpConnectionFactory<DefaultNHttpServerConnection> {
    private final ByteBufferAllocator allocator;
    private final HttpParams params;
    private final HttpRequestFactory requestFactory;
    private final SSLSetupHandler sslHandler;
    private final SSLContext sslcontext;

    public SSLNHttpServerConnectionFactory(SSLContext sslcontext, SSLSetupHandler sslHandler, HttpRequestFactory requestFactory, ByteBufferAllocator allocator, HttpParams params) {
        if (requestFactory == null) {
            throw new IllegalArgumentException("HTTP request factory may not be null");
        }
        if (allocator == null) {
            throw new IllegalArgumentException("Byte buffer allocator may not be null");
        }
        if (params == null) {
            throw new IllegalArgumentException("HTTP parameters may not be null");
        }
        this.sslcontext = sslcontext;
        this.sslHandler = sslHandler;
        this.requestFactory = requestFactory;
        this.allocator = allocator;
        this.params = params;
    }

    public SSLNHttpServerConnectionFactory(SSLContext sslcontext, SSLSetupHandler sslHandler, HttpParams params) {
        this(sslcontext, sslHandler, new DefaultHttpRequestFactory(), new HeapByteBufferAllocator(), params);
    }

    public SSLNHttpServerConnectionFactory(HttpParams params) {
        this(null, null, params);
    }

    private SSLContext getDefaultSSLContext() {
        try {
            SSLContext sslcontext = SSLContext.getInstance(SSLSocketFactory.TLS);
            sslcontext.init(null, null, null);
            return sslcontext;
        } catch (Exception ex) {
            throw new IllegalStateException("Failure initializing default SSL context", ex);
        }
    }

    protected DefaultNHttpServerConnection createConnection(IOSession session, HttpRequestFactory requestFactory, ByteBufferAllocator allocator, HttpParams params) {
        return new DefaultNHttpServerConnection(session, requestFactory, allocator, params);
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // org.apache.http.nio.NHttpConnectionFactory
    public DefaultNHttpServerConnection createConnection(IOSession session) {
        SSLContext sslcontext = this.sslcontext != null ? this.sslcontext : getDefaultSSLContext();
        SSLIOSession ssliosession = new SSLIOSession(session, SSLMode.SERVER, sslcontext, this.sslHandler);
        session.setAttribute(SSLIOSession.SESSION_KEY, ssliosession);
        DefaultNHttpServerConnection conn = createConnection(ssliosession, this.requestFactory, this.allocator, this.params);
        int timeout = HttpConnectionParams.getSoTimeout(this.params);
        conn.setSocketTimeout(timeout);
        return conn;
    }
}
