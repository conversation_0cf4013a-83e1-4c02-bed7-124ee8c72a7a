package org.apache.http.impl.nio.codecs;

import java.io.IOException;
import java.nio.channels.ReadableByteChannel;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpMessage;
import org.apache.http.ParseException;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.message.BasicLineParser;
import org.apache.http.message.LineParser;
import org.apache.http.nio.NHttpMessageParser;
import org.apache.http.nio.reactor.SessionInputBuffer;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.HttpParams;
import org.apache.http.util.CharArrayBuffer;

@NotThreadSafe
/* loaded from: classes.dex */
public abstract class AbstractMessageParser<T extends HttpMessage> implements NHttpMessageParser<T> {
    private static final int COMPLETED = 2;
    private static final int READ_HEADERS = 1;
    private static final int READ_HEAD_LINE = 0;
    private boolean endOfStream;
    private final List<CharArrayBuffer> headerBufs;
    private CharArrayBuffer lineBuf;
    protected final LineParser lineParser;
    private int maxHeaderCount;
    private int maxLineLen;
    private T message;
    private final SessionInputBuffer sessionBuffer;
    private int state;

    protected abstract T createMessage(CharArrayBuffer charArrayBuffer) throws HttpException, ParseException;

    public AbstractMessageParser(SessionInputBuffer buffer, LineParser parser, HttpParams params) {
        this.maxLineLen = -1;
        this.maxHeaderCount = -1;
        if (buffer == null) {
            throw new IllegalArgumentException("Session input buffer may not be null");
        }
        if (params == null) {
            throw new IllegalArgumentException("HTTP parameters may not be null");
        }
        this.sessionBuffer = buffer;
        this.state = 0;
        this.endOfStream = false;
        this.headerBufs = new ArrayList();
        this.maxLineLen = params.getIntParameter(CoreConnectionPNames.MAX_LINE_LENGTH, -1);
        this.maxHeaderCount = params.getIntParameter(CoreConnectionPNames.MAX_HEADER_COUNT, -1);
        this.lineParser = parser == null ? BasicLineParser.DEFAULT : parser;
    }

    @Override // org.apache.http.nio.NHttpMessageParser
    public void reset() {
        this.state = 0;
        this.endOfStream = false;
        this.headerBufs.clear();
        this.message = null;
    }

    @Override // org.apache.http.nio.NHttpMessageParser
    public int fillBuffer(ReadableByteChannel channel) throws IOException {
        int bytesRead = this.sessionBuffer.fill(channel);
        if (bytesRead == -1) {
            this.endOfStream = true;
        }
        return bytesRead;
    }

    private void parseHeadLine() throws HttpException, ParseException {
        this.message = createMessage(this.lineBuf);
    }

    private void parseHeader() throws IOException {
        char ch;
        CharArrayBuffer current = this.lineBuf;
        int count = this.headerBufs.size();
        if ((this.lineBuf.charAt(0) == ' ' || this.lineBuf.charAt(0) == '\t') && count > 0) {
            CharArrayBuffer previous = this.headerBufs.get(count - 1);
            int i = 0;
            while (i < current.length() && ((ch = current.charAt(i)) == ' ' || ch == '\t')) {
                i++;
            }
            if (this.maxLineLen > 0 && ((previous.length() + 1) + current.length()) - i > this.maxLineLen) {
                throw new IOException("Maximum line length limit exceeded");
            }
            previous.append(' ');
            previous.append(current, i, current.length() - i);
            return;
        }
        this.headerBufs.add(current);
        this.lineBuf = null;
    }

    /* JADX WARN: Removed duplicated region for block: B:54:0x004a  */
    /* JADX WARN: Removed duplicated region for block: B:68:0x00c2 A[RETURN, SYNTHETIC] */
    @Override // org.apache.http.nio.NHttpMessageParser
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public T parse() throws java.io.IOException, org.apache.http.HttpException {
        /*
            r9 = this;
            r8 = 2
        L1:
            int r5 = r9.state
            if (r5 == r8) goto L46
            org.apache.http.util.CharArrayBuffer r5 = r9.lineBuf
            if (r5 != 0) goto L3e
            org.apache.http.util.CharArrayBuffer r5 = new org.apache.http.util.CharArrayBuffer
            r6 = 64
            r5.<init>(r6)
            r9.lineBuf = r5
        L12:
            org.apache.http.nio.reactor.SessionInputBuffer r5 = r9.sessionBuffer
            org.apache.http.util.CharArrayBuffer r6 = r9.lineBuf
            boolean r7 = r9.endOfStream
            boolean r3 = r5.readLine(r6, r7)
            int r5 = r9.maxLineLen
            if (r5 <= 0) goto L44
            org.apache.http.util.CharArrayBuffer r5 = r9.lineBuf
            int r5 = r5.length()
            int r6 = r9.maxLineLen
            if (r5 > r6) goto L36
            if (r3 != 0) goto L44
            org.apache.http.nio.reactor.SessionInputBuffer r5 = r9.sessionBuffer
            int r5 = r5.length()
            int r6 = r9.maxLineLen
            if (r5 <= r6) goto L44
        L36:
            java.io.IOException r5 = new java.io.IOException
            java.lang.String r6 = "Maximum line length limit exceeded"
            r5.<init>(r6)
            throw r5
        L3e:
            org.apache.http.util.CharArrayBuffer r5 = r9.lineBuf
            r5.clear()
            goto L12
        L44:
            if (r3 != 0) goto L69
        L46:
            int r5 = r9.state
            if (r5 != r8) goto Lc2
            r2 = 0
        L4b:
            java.util.List<org.apache.http.util.CharArrayBuffer> r5 = r9.headerBufs
            int r5 = r5.size()
            if (r2 >= r5) goto Lbf
            java.util.List<org.apache.http.util.CharArrayBuffer> r5 = r9.headerBufs
            java.lang.Object r0 = r5.get(r2)
            org.apache.http.util.CharArrayBuffer r0 = (org.apache.http.util.CharArrayBuffer) r0
            T extends org.apache.http.HttpMessage r5 = r9.message     // Catch: org.apache.http.ParseException -> Lb4
            org.apache.http.message.LineParser r6 = r9.lineParser     // Catch: org.apache.http.ParseException -> Lb4
            org.apache.http.Header r6 = r6.parseHeader(r0)     // Catch: org.apache.http.ParseException -> Lb4
            r5.addHeader(r6)     // Catch: org.apache.http.ParseException -> Lb4
            int r2 = r2 + 1
            goto L4b
        L69:
            int r5 = r9.state
            switch(r5) {
                case 0: goto L7d;
                case 1: goto L8f;
                default: goto L6e;
            }
        L6e:
            boolean r5 = r9.endOfStream
            if (r5 == 0) goto L1
            org.apache.http.nio.reactor.SessionInputBuffer r5 = r9.sessionBuffer
            boolean r5 = r5.hasData()
            if (r5 != 0) goto L1
            r9.state = r8
            goto L1
        L7d:
            r9.parseHeadLine()     // Catch: org.apache.http.ParseException -> L84
            r5 = 1
            r9.state = r5
            goto L6e
        L84:
            r4 = move-exception
            org.apache.http.ProtocolException r5 = new org.apache.http.ProtocolException
            java.lang.String r6 = r4.getMessage()
            r5.<init>(r6, r4)
            throw r5
        L8f:
            org.apache.http.util.CharArrayBuffer r5 = r9.lineBuf
            int r5 = r5.length()
            if (r5 <= 0) goto Lb1
            int r5 = r9.maxHeaderCount
            if (r5 <= 0) goto Lad
            java.util.List<org.apache.http.util.CharArrayBuffer> r5 = r9.headerBufs
            int r5 = r5.size()
            int r6 = r9.maxHeaderCount
            if (r5 < r6) goto Lad
            java.io.IOException r5 = new java.io.IOException
            java.lang.String r6 = "Maximum header count exceeded"
            r5.<init>(r6)
            throw r5
        Lad:
            r9.parseHeader()
            goto L6e
        Lb1:
            r9.state = r8
            goto L6e
        Lb4:
            r1 = move-exception
            org.apache.http.ProtocolException r5 = new org.apache.http.ProtocolException
            java.lang.String r6 = r1.getMessage()
            r5.<init>(r6, r1)
            throw r5
        Lbf:
            T extends org.apache.http.HttpMessage r5 = r9.message
        Lc1:
            return r5
        Lc2:
            r5 = 0
            goto Lc1
        */
        throw new UnsupportedOperationException("Method not decompiled: org.apache.http.impl.nio.codecs.AbstractMessageParser.parse():org.apache.http.HttpMessage");
    }
}
