package org.apache.http.impl.nio;

import java.io.IOException;
import org.apache.http.HttpResponseFactory;
import org.apache.http.annotation.Immutable;
import org.apache.http.impl.DefaultHttpResponseFactory;
import org.apache.http.impl.nio.reactor.AbstractIODispatch;
import org.apache.http.nio.NHttpClientHandler;
import org.apache.http.nio.NHttpClientIOTarget;
import org.apache.http.nio.reactor.IOSession;
import org.apache.http.nio.util.ByteBufferAllocator;
import org.apache.http.nio.util.HeapByteBufferAllocator;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;

@Deprecated
@Immutable
/* loaded from: classes.dex */
public class DefaultClientIOEventDispatch extends AbstractIODispatch<NHttpClientIOTarget> {
    protected final ByteBufferAllocator allocator;
    protected final NHttpClientHandler handler;
    protected final HttpParams params;

    public DefaultClientIOEventDispatch(NHttpClientHandler handler, HttpParams params) {
        if (handler == null) {
            throw new IllegalArgumentException("HTTP client handler may not be null");
        }
        if (params == null) {
            throw new IllegalArgumentException("HTTP parameters may not be null");
        }
        this.allocator = createByteBufferAllocator();
        this.handler = handler;
        this.params = params;
    }

    protected ByteBufferAllocator createByteBufferAllocator() {
        return new HeapByteBufferAllocator();
    }

    protected HttpResponseFactory createHttpResponseFactory() {
        return new DefaultHttpResponseFactory();
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public NHttpClientIOTarget createConnection(IOSession session) {
        return new DefaultNHttpClientConnection(session, createHttpResponseFactory(), this.allocator, this.params);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onConnected(NHttpClientIOTarget conn) {
        int timeout = HttpConnectionParams.getSoTimeout(this.params);
        conn.setSocketTimeout(timeout);
        Object attachment = conn.getContext().getAttribute(IOSession.ATTACHMENT_KEY);
        this.handler.connected(conn, attachment);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onClosed(NHttpClientIOTarget conn) {
        this.handler.closed(conn);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onException(NHttpClientIOTarget conn, IOException ex) {
        this.handler.exception(conn, ex);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onInputReady(NHttpClientIOTarget conn) {
        conn.consumeInput(this.handler);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onOutputReady(NHttpClientIOTarget conn) {
        conn.produceOutput(this.handler);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // org.apache.http.impl.nio.reactor.AbstractIODispatch
    public void onTimeout(NHttpClientIOTarget conn) {
        this.handler.timeout(conn);
    }
}
