package org.apache.http.impl.client.cache.memcached;

/* loaded from: classes.dex */
public class PrefixKeyHashingScheme implements KeyHashingScheme {
    private KeyHashingScheme backingScheme;
    private String prefix;

    public PrefixKeyHashingScheme(String prefix, KeyHashingScheme backingScheme) {
        this.prefix = prefix;
        this.backingScheme = backingScheme;
    }

    @Override // org.apache.http.impl.client.cache.memcached.KeyHashingScheme
    public String hash(String storageKey) {
        return this.prefix + this.backingScheme.hash(storageKey);
    }
}
