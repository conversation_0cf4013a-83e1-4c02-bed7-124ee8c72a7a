package org.apache.http.impl.client;

import java.security.Principal;
import javax.net.ssl.SSLSession;
import org.apache.http.annotation.Immutable;
import org.apache.http.auth.AuthScheme;
import org.apache.http.auth.AuthState;
import org.apache.http.auth.Credentials;
import org.apache.http.client.UserTokenHandler;
import org.apache.http.client.protocol.ClientContext;
import org.apache.http.conn.HttpRoutedConnection;
import org.apache.http.protocol.HttpContext;

@Immutable
/* loaded from: classes.dex */
public class DefaultUserTokenHandler implements UserTokenHandler {
    @Override // org.apache.http.client.UserTokenHandler
    public Object getUserToken(HttpContext context) {
        SSLSession sslsession;
        Principal userPrincipal = null;
        AuthState targetAuthState = (AuthState) context.getAttribute(ClientContext.TARGET_AUTH_STATE);
        if (targetAuthState != null && (userPrincipal = getAuthPrincipal(targetAuthState)) == null) {
            AuthState proxyAuthState = (AuthState) context.getAttribute(ClientContext.PROXY_AUTH_STATE);
            userPrincipal = getAuthPrincipal(proxyAuthState);
        }
        if (userPrincipal == null) {
            HttpRoutedConnection conn = (HttpRoutedConnection) context.getAttribute("http.connection");
            if (conn.isOpen() && (sslsession = conn.getSSLSession()) != null) {
                Principal userPrincipal2 = sslsession.getLocalPrincipal();
                return userPrincipal2;
            }
            return userPrincipal;
        }
        return userPrincipal;
    }

    private static Principal getAuthPrincipal(AuthState authState) {
        Credentials creds;
        AuthScheme scheme = authState.getAuthScheme();
        if (scheme == null || !scheme.isComplete() || !scheme.isConnectionBased() || (creds = authState.getCredentials()) == null) {
            return null;
        }
        return creds.getUserPrincipal();
    }
}
