package org.apache.http.impl.client.cache.memcached;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import org.apache.http.client.cache.HttpCacheEntry;

/* loaded from: classes.dex */
public class MemcachedCacheEntryImpl implements MemcachedCacheEntry {
    private HttpCacheEntry httpCacheEntry;
    private String key;

    public MemcachedCacheEntryImpl(String key, HttpCacheEntry httpCacheEntry) {
        this.key = key;
        this.httpCacheEntry = httpCacheEntry;
    }

    public MemcachedCacheEntryImpl() {
    }

    @Override // org.apache.http.impl.client.cache.memcached.MemcachedCacheEntry
    public synchronized byte[] toByteArray() {
        ByteArrayOutputStream bos;
        bos = new ByteArrayOutputStream();
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(this.key);
            oos.writeObject(this.httpCacheEntry);
            oos.close();
        } catch (IOException ioe) {
            throw new MemcachedSerializationException(ioe);
        }
        return bos.toByteArray();
    }

    @Override // org.apache.http.impl.client.cache.memcached.MemcachedCacheEntry
    public synchronized String getStorageKey() {
        return this.key;
    }

    @Override // org.apache.http.impl.client.cache.memcached.MemcachedCacheEntry
    public synchronized HttpCacheEntry getHttpCacheEntry() {
        return this.httpCacheEntry;
    }

    @Override // org.apache.http.impl.client.cache.memcached.MemcachedCacheEntry
    public synchronized void set(byte[] bytes) {
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        try {
            ObjectInputStream ois = new ObjectInputStream(bis);
            String s = (String) ois.readObject();
            HttpCacheEntry entry = (HttpCacheEntry) ois.readObject();
            ois.close();
            bis.close();
            this.key = s;
            this.httpCacheEntry = entry;
        } catch (IOException ioe) {
            throw new MemcachedSerializationException(ioe);
        } catch (ClassNotFoundException cnfe) {
            throw new MemcachedSerializationException(cnfe);
        }
    }
}
