package org.apache.http.impl.nio.codecs;

import java.io.IOException;
import java.nio.channels.WritableByteChannel;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.impl.io.HttpTransportMetricsImpl;
import org.apache.http.nio.ContentEncoder;
import org.apache.http.nio.reactor.SessionOutputBuffer;

@NotThreadSafe
/* loaded from: classes.dex */
public abstract class AbstractContentEncoder implements ContentEncoder {
    protected final SessionOutputBuffer buffer;
    protected final WritableByteChannel channel;
    protected boolean completed;
    protected final HttpTransportMetricsImpl metrics;

    public AbstractContentEncoder(WritableByteChannel channel, SessionOutputBuffer buffer, HttpTransportMetricsImpl metrics) {
        if (channel == null) {
            throw new IllegalArgumentException("Channel may not be null");
        }
        if (buffer == null) {
            throw new IllegalArgumentException("Session input buffer may not be null");
        }
        if (metrics == null) {
            throw new IllegalArgumentException("Transport metrics may not be null");
        }
        this.buffer = buffer;
        this.channel = channel;
        this.metrics = metrics;
    }

    @Override // org.apache.http.nio.ContentEncoder
    public boolean isCompleted() {
        return this.completed;
    }

    @Override // org.apache.http.nio.ContentEncoder
    public void complete() throws IOException {
        this.completed = true;
    }

    protected void assertNotCompleted() {
        if (this.completed) {
            throw new IllegalStateException("Encoding process already completed");
        }
    }
}
