package org.apache.http.impl.nio.reactor;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.nio.channels.ByteChannel;
import java.nio.channels.Channel;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.annotation.ThreadSafe;
import org.apache.http.nio.reactor.IOSession;
import org.apache.http.nio.reactor.SessionBufferStatus;
import org.apache.http.nio.reactor.SocketAccessor;

@ThreadSafe
/* loaded from: classes.dex */
public class IOSessionImpl implements IOSession, SocketAccessor {
    private final Map<String, Object> attributes;
    private volatile SessionBufferStatus bufferStatus;
    private final ByteChannel channel;
    private volatile int currentEventMask;
    private final InterestOpsCallback interestOpsCallback;
    private final SelectionKey key;
    private long lastAccessTime;
    private long lastReadTime;
    private long lastWriteTime;
    private final SessionClosedCallback sessionClosedCallback;
    private volatile int socketTimeout;
    private final long startedTime;
    private volatile int status;

    public IOSessionImpl(SelectionKey key, InterestOpsCallback interestOpsCallback, SessionClosedCallback sessionClosedCallback) {
        if (key == null) {
            throw new IllegalArgumentException("Selection key may not be null");
        }
        this.key = key;
        this.channel = (ByteChannel) this.key.channel();
        this.interestOpsCallback = interestOpsCallback;
        this.sessionClosedCallback = sessionClosedCallback;
        this.attributes = Collections.synchronizedMap(new HashMap());
        this.currentEventMask = key.interestOps();
        this.socketTimeout = 0;
        this.status = 0;
        long now = System.currentTimeMillis();
        this.startedTime = now;
        this.lastReadTime = now;
        this.lastWriteTime = now;
        this.lastAccessTime = now;
    }

    public IOSessionImpl(SelectionKey key, SessionClosedCallback sessionClosedCallback) {
        this(key, null, sessionClosedCallback);
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public ByteChannel channel() {
        return this.channel;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public SocketAddress getLocalAddress() {
        Channel channel = this.channel;
        if (channel instanceof SocketChannel) {
            return ((SocketChannel) channel).socket().getLocalSocketAddress();
        }
        return null;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public SocketAddress getRemoteAddress() {
        Channel channel = this.channel;
        if (channel instanceof SocketChannel) {
            return ((SocketChannel) channel).socket().getRemoteSocketAddress();
        }
        return null;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized int getEventMask() {
        return this.interestOpsCallback != null ? this.currentEventMask : this.key.interestOps();
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized void setEventMask(int ops) {
        if (this.status != Integer.MAX_VALUE) {
            if (this.interestOpsCallback != null) {
                this.currentEventMask = ops;
                InterestOpEntry entry = new InterestOpEntry(this.key, this.currentEventMask);
                this.interestOpsCallback.addInterestOps(entry);
            } else {
                this.key.interestOps(ops);
            }
            this.key.selector().wakeup();
        }
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized void setEvent(int op) {
        if (this.status != Integer.MAX_VALUE) {
            if (this.interestOpsCallback != null) {
                this.currentEventMask |= op;
                InterestOpEntry entry = new InterestOpEntry(this.key, this.currentEventMask);
                this.interestOpsCallback.addInterestOps(entry);
            } else {
                int ops = this.key.interestOps();
                this.key.interestOps(ops | op);
            }
            this.key.selector().wakeup();
        }
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized void clearEvent(int op) {
        if (this.status != Integer.MAX_VALUE) {
            if (this.interestOpsCallback != null) {
                this.currentEventMask &= op ^ (-1);
                InterestOpEntry entry = new InterestOpEntry(this.key, this.currentEventMask);
                this.interestOpsCallback.addInterestOps(entry);
            } else {
                int ops = this.key.interestOps();
                this.key.interestOps((op ^ (-1)) & ops);
            }
            this.key.selector().wakeup();
        }
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public int getSocketTimeout() {
        return this.socketTimeout;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized void setSocketTimeout(int timeout) {
        this.socketTimeout = timeout;
        this.lastAccessTime = System.currentTimeMillis();
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public synchronized void close() {
        if (this.status != Integer.MAX_VALUE) {
            this.status = Integer.MAX_VALUE;
            this.key.cancel();
            try {
                this.key.channel().close();
            } catch (IOException e) {
            }
            if (this.sessionClosedCallback != null) {
                this.sessionClosedCallback.sessionClosed(this);
            }
            if (this.key.selector().isOpen()) {
                this.key.selector().wakeup();
            }
        }
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public int getStatus() {
        return this.status;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public boolean isClosed() {
        return this.status == Integer.MAX_VALUE;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public void shutdown() {
        close();
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public boolean hasBufferedInput() {
        SessionBufferStatus bufferStatus = this.bufferStatus;
        return bufferStatus != null && bufferStatus.hasBufferedInput();
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public boolean hasBufferedOutput() {
        SessionBufferStatus bufferStatus = this.bufferStatus;
        return bufferStatus != null && bufferStatus.hasBufferedOutput();
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public void setBufferStatus(SessionBufferStatus bufferStatus) {
        this.bufferStatus = bufferStatus;
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public Object getAttribute(String name) {
        return this.attributes.get(name);
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public Object removeAttribute(String name) {
        return this.attributes.remove(name);
    }

    @Override // org.apache.http.nio.reactor.IOSession
    public void setAttribute(String name, Object obj) {
        this.attributes.put(name, obj);
    }

    public synchronized long getStartedTime() {
        return this.startedTime;
    }

    public synchronized long getLastReadTime() {
        return this.lastReadTime;
    }

    public synchronized long getLastWriteTime() {
        return this.lastWriteTime;
    }

    public synchronized long getLastAccessTime() {
        return this.lastAccessTime;
    }

    synchronized void resetLastRead() {
        long now = System.currentTimeMillis();
        this.lastReadTime = now;
        this.lastAccessTime = now;
    }

    synchronized void resetLastWrite() {
        long now = System.currentTimeMillis();
        this.lastWriteTime = now;
        this.lastAccessTime = now;
    }

    private static void formatOps(StringBuilder buffer, int ops) {
        if ((ops & 1) > 0) {
            buffer.append('r');
        }
        if ((ops & 4) > 0) {
            buffer.append('w');
        }
        if ((ops & 16) > 0) {
            buffer.append('a');
        }
        if ((ops & 8) > 0) {
            buffer.append('c');
        }
    }

    private static void formatAddress(StringBuilder buffer, SocketAddress socketAddress) {
        if (socketAddress instanceof InetSocketAddress) {
            InetSocketAddress addr = (InetSocketAddress) socketAddress;
            buffer.append(addr.getAddress() != null ? addr.getAddress().getHostAddress() : addr.getAddress()).append(':').append(addr.getPort());
        } else {
            buffer.append(socketAddress);
        }
    }

    public synchronized String toString() {
        StringBuilder buffer;
        buffer = new StringBuilder();
        SocketAddress remoteAddress = getRemoteAddress();
        SocketAddress localAddress = getLocalAddress();
        if (remoteAddress != null && localAddress != null) {
            formatAddress(buffer, localAddress);
            buffer.append("<->");
            formatAddress(buffer, remoteAddress);
        }
        buffer.append("[");
        switch (this.status) {
            case 0:
                buffer.append("ACTIVE");
                break;
            case 1:
                buffer.append("CLOSING");
                break;
            case Integer.MAX_VALUE:
                buffer.append("CLOSED");
                break;
        }
        buffer.append("][");
        if (this.key.isValid()) {
            formatOps(buffer, this.interestOpsCallback != null ? this.currentEventMask : this.key.interestOps());
            buffer.append(":");
            formatOps(buffer, this.key.readyOps());
        }
        buffer.append("]");
        return buffer.toString();
    }

    @Override // org.apache.http.nio.reactor.SocketAccessor
    public Socket getSocket() {
        Channel channel = this.channel;
        if (channel instanceof SocketChannel) {
            return ((SocketChannel) channel).socket();
        }
        return null;
    }
}
