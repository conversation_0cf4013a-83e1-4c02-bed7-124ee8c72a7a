package org.apache.http.impl.nio;

import java.io.IOException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.HttpResponseFactory;
import org.apache.http.annotation.NotThreadSafe;
import org.apache.http.impl.nio.codecs.DefaultHttpRequestWriter;
import org.apache.http.impl.nio.codecs.DefaultHttpResponseParser;
import org.apache.http.nio.NHttpClientEventHandler;
import org.apache.http.nio.NHttpClientHandler;
import org.apache.http.nio.NHttpClientIOTarget;
import org.apache.http.nio.NHttpMessageParser;
import org.apache.http.nio.NHttpMessageWriter;
import org.apache.http.nio.reactor.IOSession;
import org.apache.http.nio.reactor.SessionInputBuffer;
import org.apache.http.nio.reactor.SessionOutputBuffer;
import org.apache.http.nio.util.ByteBufferAllocator;
import org.apache.http.params.HttpParams;

@NotThreadSafe
/* loaded from: classes.dex */
public class DefaultNHttpClientConnection extends NHttpConnectionBase implements NHttpClientIOTarget {
    protected final NHttpMessageWriter<HttpRequest> requestWriter;
    protected final NHttpMessageParser<HttpResponse> responseParser;

    public DefaultNHttpClientConnection(IOSession session, HttpResponseFactory responseFactory, ByteBufferAllocator allocator, HttpParams params) {
        super(session, allocator, params);
        if (responseFactory == null) {
            throw new IllegalArgumentException("Response factory may not be null");
        }
        this.responseParser = createResponseParser(this.inbuf, responseFactory, params);
        this.requestWriter = createRequestWriter(this.outbuf, params);
        this.hasBufferedInput = false;
        this.hasBufferedOutput = false;
        this.session.setBufferStatus(this);
    }

    protected NHttpMessageParser<HttpResponse> createResponseParser(SessionInputBuffer buffer, HttpResponseFactory responseFactory, HttpParams params) {
        return new DefaultHttpResponseParser(buffer, null, responseFactory, params);
    }

    protected NHttpMessageWriter<HttpRequest> createRequestWriter(SessionOutputBuffer buffer, HttpParams params) {
        return new DefaultHttpRequestWriter(buffer, null, params);
    }

    protected void onResponseReceived(HttpResponse response) {
    }

    protected void onRequestSubmitted(HttpRequest request) {
    }

    @Override // org.apache.http.nio.NHttpClientConnection
    public void resetInput() {
        this.response = null;
        this.contentDecoder = null;
        this.responseParser.reset();
    }

    @Override // org.apache.http.nio.NHttpClientConnection
    public void resetOutput() {
        this.request = null;
        this.contentEncoder = null;
        this.requestWriter.reset();
    }

    public void consumeInput(NHttpClientEventHandler handler) {
        int bytesRead;
        try {
            if (this.status != 0) {
                this.session.clearEvent(1);
                return;
            }
            if (this.response == null) {
                do {
                    bytesRead = this.responseParser.fillBuffer(this.session.channel());
                    if (bytesRead > 0) {
                        this.inTransportMetrics.incrementBytesTransferred(bytesRead);
                    }
                    this.response = this.responseParser.parse();
                    if (bytesRead <= 0) {
                        break;
                    }
                } while (this.response == null);
                if (this.response != null) {
                    if (this.response.getStatusLine().getStatusCode() >= 200) {
                        HttpEntity entity = prepareDecoder(this.response);
                        this.response.setEntity(entity);
                        this.connMetrics.incrementResponseCount();
                    }
                    onResponseReceived(this.response);
                    handler.responseReceived(this);
                    if (this.contentDecoder == null) {
                        resetInput();
                    }
                }
                if (bytesRead == -1) {
                    handler.endOfInput(this);
                }
            }
            if (this.contentDecoder != null && (this.session.getEventMask() & 1) > 0) {
                handler.inputReady(this, this.contentDecoder);
                if (this.contentDecoder.isCompleted()) {
                    resetInput();
                }
            }
        } catch (HttpException ex) {
            resetInput();
            handler.exception(this, ex);
        } catch (Exception ex2) {
            handler.exception(this, ex2);
        } finally {
            this.hasBufferedInput = this.inbuf.hasData();
        }
    }

    public void produceOutput(NHttpClientEventHandler handler) {
        int bytesWritten;
        try {
            if (this.outbuf.hasData() && (bytesWritten = this.outbuf.flush(this.session.channel())) > 0) {
                this.outTransportMetrics.incrementBytesTransferred(bytesWritten);
            }
            if (!this.outbuf.hasData()) {
                if (this.status == 1) {
                    this.session.close();
                    this.status = 2;
                    resetOutput();
                    return;
                }
                if (this.contentEncoder != null) {
                    handler.outputReady(this, this.contentEncoder);
                    if (this.contentEncoder.isCompleted()) {
                        resetOutput();
                    }
                }
                if (this.contentEncoder == null && !this.outbuf.hasData()) {
                    if (this.status == 1) {
                        this.session.close();
                        this.status = 2;
                    }
                    if (this.status != 2) {
                        this.session.clearEvent(4);
                        handler.requestReady(this);
                    }
                }
            }
        } catch (Exception ex) {
            handler.exception(this, ex);
        } finally {
            this.hasBufferedOutput = this.outbuf.hasData();
        }
    }

    @Override // org.apache.http.nio.NHttpClientConnection
    public void submitRequest(HttpRequest request) throws IOException, HttpException {
        if (request == null) {
            throw new IllegalArgumentException("HTTP request may not be null");
        }
        assertNotClosed();
        if (this.request != null) {
            throw new HttpException("Request already submitted");
        }
        onRequestSubmitted(request);
        this.requestWriter.write(request);
        this.hasBufferedOutput = this.outbuf.hasData();
        if ((request instanceof HttpEntityEnclosingRequest) && ((HttpEntityEnclosingRequest) request).getEntity() != null) {
            prepareEncoder(request);
            this.request = request;
        }
        this.connMetrics.incrementRequestCount();
        this.session.setEvent(4);
    }

    @Override // org.apache.http.nio.NHttpClientConnection
    public boolean isRequestSubmitted() {
        return this.request != null;
    }

    @Override // org.apache.http.nio.NHttpClientIOTarget
    public void consumeInput(NHttpClientHandler handler) {
        consumeInput(new NHttpClientEventHandlerAdaptor(handler));
    }

    @Override // org.apache.http.nio.NHttpClientIOTarget
    public void produceOutput(NHttpClientHandler handler) {
        produceOutput(new NHttpClientEventHandlerAdaptor(handler));
    }
}
