package com.baidu.tts.b.a.b;

import com.baidu.tts.aop.tts.TtsError;
import com.baidu.tts.b.a.b.e;
import com.baidu.tts.b.a.b.f;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import com.baidu.tts.f.n;
import com.baidu.tts.m.i;
import org.apache.http.HttpStatus;

/* compiled from: MixSynthesizer.java */
/* loaded from: classes.dex */
public class d extends a {
    private com.baidu.tts.m.b b;
    private TtsError f;
    private int g = 0;
    private int h = 0;
    private f c = new f();
    private e d = new e();
    private c e = new c();

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public TtsError a() {
        this.c.a(new com.baidu.tts.b.a.b() { // from class: com.baidu.tts.b.a.b.d.1
            @Override // com.baidu.tts.b.a.b
            public void a(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void c(com.baidu.tts.m.h hVar) {
                d.this.h = hVar.b();
                d.this.g = hVar.c();
                d.this.a(hVar);
            }

            @Override // com.baidu.tts.b.a.b
            public void b(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void e(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void d(com.baidu.tts.m.h hVar) {
            }
        });
        this.d.a(new com.baidu.tts.b.a.b() { // from class: com.baidu.tts.b.a.b.d.2
            @Override // com.baidu.tts.b.a.b
            public void a(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void c(com.baidu.tts.m.h hVar) {
                d.this.a(d.this.b(hVar));
            }

            @Override // com.baidu.tts.b.a.b
            public void b(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void e(com.baidu.tts.m.h hVar) {
            }

            @Override // com.baidu.tts.b.a.b
            public void d(com.baidu.tts.m.h hVar) {
            }
        });
        this.c.a();
        this.f = this.d.a();
        if (this.f == null) {
            return null;
        }
        return com.baidu.tts.h.a.c.a().b(n.MIX_ENGINE_OFFLINE_INIT_FAILURE);
    }

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public TtsError b() {
        this.c.b();
        this.d.b();
        this.e.a((com.baidu.tts.m.b) null);
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public <AllSynthesizerParams> void a(AllSynthesizerParams allsynthesizerparams) {
        this.b = (com.baidu.tts.m.b) allsynthesizerparams;
        f.b a = this.b.a();
        a.a(3);
        a.b(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        this.c.a((f) a);
        this.d.a((e) this.b.b());
        this.e.a(this.b);
    }

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public TtsError a(i iVar) throws InterruptedException {
        this.h = 0;
        this.g = 0;
        if (this.e.a()) {
            TtsError a = this.c.a(iVar);
            if (a != null) {
                LoggerProxy.d("MixSynthesizer", "online synthesize ttserror=" + a.getDetailMessage());
                iVar.b(iVar.c().substring(this.g));
                return this.d.a(iVar);
            }
            return a;
        }
        return this.d.a(iVar);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public com.baidu.tts.m.h b(com.baidu.tts.m.h hVar) {
        int i;
        int b = hVar.b();
        if (b >= 0) {
            i = b + this.h;
        } else {
            i = b - this.h;
        }
        hVar.b(i);
        hVar.d(hVar.c() + this.g);
        return hVar;
    }

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public int a(com.baidu.tts.m.e eVar) {
        return this.d.a(eVar);
    }

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public int b(com.baidu.tts.m.e eVar) {
        return this.d.b(eVar);
    }

    @Override // com.baidu.tts.b.a.b.a, com.baidu.tts.b.a.b.b
    public int a(com.baidu.tts.m.g gVar) {
        if (this.f == null) {
            return this.d.a(gVar);
        }
        String a = gVar.a();
        String b = gVar.b();
        e.b b2 = this.b.b();
        b2.d(a);
        b2.e(b);
        this.f = this.d.a();
        if (this.f == null) {
            return 0;
        }
        return this.f.getDetailCode();
    }

    @Override // com.baidu.tts.b.a.b.b
    public int a(com.baidu.tts.m.f fVar) {
        return this.d.a(fVar);
    }
}
