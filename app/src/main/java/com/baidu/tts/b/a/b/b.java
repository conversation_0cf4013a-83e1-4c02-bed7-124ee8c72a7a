package com.baidu.tts.b.a.b;

import com.baidu.tts.aop.tts.TtsError;
import com.baidu.tts.m.i;

/* compiled from: ISynthesizer.java */
/* loaded from: classes.dex */
public interface b {
    int a(com.baidu.tts.m.e eVar);

    int a(com.baidu.tts.m.f fVar);

    int a(com.baidu.tts.m.g gVar);

    TtsError a();

    TtsError a(i iVar) throws InterruptedException;

    void a(com.baidu.tts.b.a.b bVar);

    <T> void a(T t);

    int b(com.baidu.tts.m.e eVar);

    TtsError b();
}
