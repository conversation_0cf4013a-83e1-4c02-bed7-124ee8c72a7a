package com.baidu.tts.b.b.a;

/* compiled from: RunningPlayState.java */
/* loaded from: classes.dex */
public class g extends b {
    public g(f fVar) {
        super(fVar);
    }

    @Override // com.baidu.tts.b.b.a.b, com.baidu.tts.b.b.a.c
    public void a(com.baidu.tts.m.h hVar) {
        this.a.e(hVar);
    }

    @Override // com.baidu.tts.b.b.a.b, com.baidu.tts.j.b
    public void d() {
        this.a.w();
        a((b) this.a.s());
    }

    @Override // com.baidu.tts.b.b.a.b, com.baidu.tts.j.b
    public void e() {
        this.a.x();
        a((b) this.a.q());
    }

    @Override // com.baidu.tts.b.b.a.b, com.baidu.tts.j.b
    public void f() {
        this.a.x();
        this.a.y();
        a((b) this.a.p());
    }
}
