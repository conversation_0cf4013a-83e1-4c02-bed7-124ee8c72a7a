package com.baidu.tts.b.a.b;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import com.baidu.tts.b.a.b.f;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import com.baidu.tts.f.j;
import com.baidu.tts.f.l;

/* compiled from: MixStrategy.java */
/* loaded from: classes.dex */
public class c {
    private com.baidu.tts.m.b a;
    private j b;

    public void a(com.baidu.tts.m.b bVar) {
        this.a = bVar;
    }

    public boolean a() {
        try {
            Context h = com.baidu.tts.h.b.b.a().h();
            if (h != null) {
                if (b()) {
                    f.b a = this.a.a();
                    if (this.b.equals(j.HIGH_SPEED_SYNTHESIZE) || this.b.equals(j.HIGH_SPEED_SYNTHESIZE_WIFI)) {
                        a.c(l.FAST_SWITCH.b());
                    } else {
                        a.c(l.MIX_ONLINE_REQUEST_TIMEOUT.b());
                    }
                }
                NetworkInfo activeNetworkInfo = ((ConnectivityManager) h.getSystemService("connectivity")).getActiveNetworkInfo();
                if (activeNetworkInfo != null && activeNetworkInfo.isConnected()) {
                    int type = activeNetworkInfo.getType();
                    int subtype = activeNetworkInfo.getSubtype();
                    switch (this.b) {
                        case DEFAULT:
                        case HIGH_SPEED_SYNTHESIZE_WIFI:
                            if (c(type) || type == 9) {
                                return true;
                            }
                        case HIGH_SPEED_NETWORK:
                        case HIGH_SPEED_SYNTHESIZE:
                            if (c(type) || type == 9 || a(subtype)) {
                                return true;
                            }
                    }
                }
            }
        } catch (Exception e) {
            LoggerProxy.d("MixStrategy", e.toString());
        }
        return false;
    }

    private boolean b() {
        j jVar = null;
        try {
            jVar = this.a.c();
        } catch (Exception e) {
        }
        if (this.b == null) {
            if (jVar == null) {
                this.b = j.DEFAULT;
                return true;
            }
            this.b = jVar;
            return true;
        }
        if (jVar != null && !this.b.equals(jVar)) {
            this.b = jVar;
            return true;
        }
        return false;
    }

    private boolean a(int i) {
        return b(i) >= 2;
    }

    private int b(int i) {
        switch (i) {
            case 1:
            case 2:
            case 4:
            case 7:
            case 11:
                return 1;
            case 3:
            case 5:
            case 6:
            case 8:
            case 9:
            case 10:
            case 12:
            case 14:
            case 15:
                return 2;
            case 13:
                return 3;
            default:
                return 0;
        }
    }

    private boolean c(int i) {
        switch (i) {
            case 1:
                return true;
            default:
                return false;
        }
    }
}
