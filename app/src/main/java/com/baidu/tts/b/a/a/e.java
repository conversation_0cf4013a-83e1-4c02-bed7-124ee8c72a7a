package com.baidu.tts.b.a.a;

import com.baidu.tts.m.i;

/* compiled from: InitializedEngineState.java */
/* loaded from: classes.dex */
public class e extends b {
    public e(c cVar) {
        super(cVar);
    }

    @Override // com.baidu.tts.b.a.a.b
    public void a() {
        this.a.t();
        a((b) this.a.q());
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.j.b
    public void f() {
        this.a.x();
        a((b) this.a.o());
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.b.a.a.d
    public void a(i iVar) {
        a();
        if (this.a.a() != this) {
            this.a.a(iVar);
        }
    }
}
