package com.baidu.tts.b.a.a;

import com.baidu.tts.m.i;

/* compiled from: PauseEngineState.java */
/* loaded from: classes.dex */
public class f extends b {
    public f(c cVar) {
        super(cVar);
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.j.b
    public void c() {
        this.a.u();
        a((b) this.a.q());
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.j.b
    public void e() {
        this.a.w();
        a((b) this.a.p());
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.j.b
    public void f() {
        this.a.w();
        this.a.x();
        a((b) this.a.o());
    }

    @Override // com.baidu.tts.b.a.a.b, com.baidu.tts.b.a.a.d
    public void a(i iVar) {
        e();
        if (this.a.a() != this) {
            this.a.a(iVar);
        }
    }
}
