package com.baidu.tts.a.c;

import com.baidu.tts.aop.ttslistener.TtsListener;
import com.baidu.tts.m.e;
import com.baidu.tts.m.f;
import com.baidu.tts.m.g;
import com.baidu.tts.m.i;

/* compiled from: ITtsAdapter.java */
/* loaded from: classes.dex */
public interface a extends com.baidu.tts.j.b {
    int a(float f, float f2);

    int a(e eVar);

    int a(f fVar);

    int a(g gVar);

    com.baidu.tts.b.b.a.c a();

    void a(TtsListener ttsListener);

    void a(i iVar);

    int b(e eVar);

    void b(i iVar);
}
