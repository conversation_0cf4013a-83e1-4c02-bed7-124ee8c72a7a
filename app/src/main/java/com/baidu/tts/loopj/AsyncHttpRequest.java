package com.baidu.tts.loopj;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.AbstractHttpClient;
import org.apache.http.protocol.HttpContext;

/* loaded from: classes.dex */
public class AsyncHttpRequest implements Runnable {
    private boolean cancelIsNotified;
    private final AbstractHttpClient client;
    private final HttpContext context;
    private int executionCount;
    private final AtomicBoolean isCancelled = new AtomicBoolean();
    private volatile boolean isFinished;
    private boolean isRequestPreProcessed;
    private final HttpUriRequest request;
    private final ResponseHandlerInterface responseHandler;

    public AsyncHttpRequest(AbstractHttpClient client, HttpContext context, HttpUriRequest request, ResponseHandlerInterface responseHandler) {
        this.client = (AbstractHttpClient) Utils.notNull(client, "client");
        this.context = (HttpContext) Utils.notNull(context, "context");
        this.request = (HttpUriRequest) Utils.notNull(request, "request");
        this.responseHandler = (ResponseHandlerInterface) Utils.notNull(responseHandler, "responseHandler");
    }

    public void onPreProcessRequest(AsyncHttpRequest request) {
    }

    public void onPostProcessRequest(AsyncHttpRequest request) {
    }

    @Override // java.lang.Runnable
    public void run() {
        if (!isCancelled()) {
            if (!this.isRequestPreProcessed) {
                this.isRequestPreProcessed = true;
                onPreProcessRequest(this);
            }
            if (!isCancelled()) {
                this.responseHandler.sendStartMessage();
                if (!isCancelled()) {
                    try {
                        makeRequestWithRetries();
                    } catch (IOException e) {
                        if (!isCancelled()) {
                            this.responseHandler.sendFailureMessage(0, null, null, e);
                        } else {
                            AsyncHttpClient.log.e("AsyncHttpRequest", "makeRequestWithRetries returned error", e);
                        }
                    }
                    if (!isCancelled()) {
                        this.responseHandler.sendFinishMessage();
                        if (!isCancelled()) {
                            onPostProcessRequest(this);
                            this.isFinished = true;
                        }
                    }
                }
            }
        }
    }

    private void makeRequest() throws IOException {
        if (!isCancelled()) {
            if (this.request.getURI().getScheme() == null) {
                throw new MalformedURLException("No valid URI scheme was provided");
            }
            if (this.responseHandler instanceof RangeFileAsyncHttpResponseHandler) {
                ((RangeFileAsyncHttpResponseHandler) this.responseHandler).updateRequestHeaders(this.request);
            }
            HttpResponse execute = this.client.execute(this.request, this.context);
            if (!isCancelled()) {
                this.responseHandler.onPreProcessResponse(this.responseHandler, execute);
                if (!isCancelled()) {
                    this.responseHandler.sendResponseMessage(execute);
                    if (!isCancelled()) {
                        this.responseHandler.onPostProcessResponse(this.responseHandler, execute);
                    }
                }
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x0044 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0009 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void makeRequestWithRetries() throws java.io.IOException {
        /*
            r7 = this;
            r1 = 1
            r0 = 0
            org.apache.http.impl.client.AbstractHttpClient r2 = r7.client
            org.apache.http.client.HttpRequestRetryHandler r3 = r2.getHttpRequestRetryHandler()
            r2 = r1
        L9:
            if (r2 == 0) goto L73
            r7.makeRequest()     // Catch: java.net.UnknownHostException -> Lf java.lang.Exception -> L4c java.lang.NullPointerException -> L76 java.io.IOException -> La0
        Le:
            return
        Lf:
            r0 = move-exception
            java.io.IOException r2 = new java.io.IOException     // Catch: java.lang.Exception -> L4c
            java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch: java.lang.Exception -> L4c
            r4.<init>()     // Catch: java.lang.Exception -> L4c
            java.lang.String r5 = "UnknownHostException exception: "
            java.lang.StringBuilder r4 = r4.append(r5)     // Catch: java.lang.Exception -> L4c
            java.lang.String r5 = r0.getMessage()     // Catch: java.lang.Exception -> L4c
            java.lang.StringBuilder r4 = r4.append(r5)     // Catch: java.lang.Exception -> L4c
            java.lang.String r4 = r4.toString()     // Catch: java.lang.Exception -> L4c
            r2.<init>(r4)     // Catch: java.lang.Exception -> L4c
            int r4 = r7.executionCount     // Catch: java.lang.Exception -> L4c
            if (r4 < 0) goto L74
            int r4 = r7.executionCount     // Catch: java.lang.Exception -> L4c
            int r4 = r4 + 1
            r7.executionCount = r4     // Catch: java.lang.Exception -> L4c
            org.apache.http.protocol.HttpContext r5 = r7.context     // Catch: java.lang.Exception -> L4c
            boolean r0 = r3.retryRequest(r0, r4, r5)     // Catch: java.lang.Exception -> L4c
            if (r0 == 0) goto L74
            r0 = r1
        L3f:
            r6 = r2
            r2 = r0
            r0 = r6
        L42:
            if (r2 == 0) goto L9
            com.baidu.tts.loopj.ResponseHandlerInterface r4 = r7.responseHandler     // Catch: java.lang.Exception -> L4c
            int r5 = r7.executionCount     // Catch: java.lang.Exception -> L4c
            r4.sendRetryMessage(r5)     // Catch: java.lang.Exception -> L4c
            goto L9
        L4c:
            r0 = move-exception
            r1 = r0
            com.baidu.tts.loopj.LogInterface r0 = com.baidu.tts.loopj.AsyncHttpClient.log
            java.lang.String r2 = "AsyncHttpRequest"
            java.lang.String r3 = "Unhandled exception origin cause"
            r0.e(r2, r3, r1)
            java.io.IOException r0 = new java.io.IOException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "Unhandled exception: "
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r1 = r1.getMessage()
            java.lang.StringBuilder r1 = r2.append(r1)
            java.lang.String r1 = r1.toString()
            r0.<init>(r1)
        L73:
            throw r0
        L74:
            r0 = 0
            goto L3f
        L76:
            r2 = move-exception
            java.io.IOException r0 = new java.io.IOException     // Catch: java.lang.Exception -> L4c
            java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch: java.lang.Exception -> L4c
            r4.<init>()     // Catch: java.lang.Exception -> L4c
            java.lang.String r5 = "NPE in HttpClient: "
            java.lang.StringBuilder r4 = r4.append(r5)     // Catch: java.lang.Exception -> L4c
            java.lang.String r2 = r2.getMessage()     // Catch: java.lang.Exception -> L4c
            java.lang.StringBuilder r2 = r4.append(r2)     // Catch: java.lang.Exception -> L4c
            java.lang.String r2 = r2.toString()     // Catch: java.lang.Exception -> L4c
            r0.<init>(r2)     // Catch: java.lang.Exception -> L4c
            int r2 = r7.executionCount     // Catch: java.lang.Exception -> L4c
            int r2 = r2 + 1
            r7.executionCount = r2     // Catch: java.lang.Exception -> L4c
            org.apache.http.protocol.HttpContext r4 = r7.context     // Catch: java.lang.Exception -> L4c
            boolean r2 = r3.retryRequest(r0, r2, r4)     // Catch: java.lang.Exception -> L4c
            goto L42
        La0:
            r0 = move-exception
            boolean r2 = r7.isCancelled()     // Catch: java.lang.Exception -> L4c
            if (r2 != 0) goto Le
            int r2 = r7.executionCount     // Catch: java.lang.Exception -> L4c
            int r2 = r2 + 1
            r7.executionCount = r2     // Catch: java.lang.Exception -> L4c
            org.apache.http.protocol.HttpContext r4 = r7.context     // Catch: java.lang.Exception -> L4c
            boolean r2 = r3.retryRequest(r0, r2, r4)     // Catch: java.lang.Exception -> L4c
            goto L42
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.tts.loopj.AsyncHttpRequest.makeRequestWithRetries():void");
    }

    public boolean isCancelled() {
        boolean z = this.isCancelled.get();
        if (z) {
            sendCancelNotification();
        }
        return z;
    }

    private synchronized void sendCancelNotification() {
        if (!this.isFinished && this.isCancelled.get() && !this.cancelIsNotified) {
            this.cancelIsNotified = true;
            this.responseHandler.sendCancelMessage();
        }
    }

    public boolean isDone() {
        return isCancelled() || this.isFinished;
    }

    public boolean cancel(boolean mayInterruptIfRunning) {
        this.isCancelled.set(true);
        this.request.abort();
        return isCancelled();
    }

    public AsyncHttpRequest setRequestTag(Object TAG) {
        this.responseHandler.setTag(TAG);
        return this;
    }

    public Object getTag() {
        return this.responseHandler.getTag();
    }
}
