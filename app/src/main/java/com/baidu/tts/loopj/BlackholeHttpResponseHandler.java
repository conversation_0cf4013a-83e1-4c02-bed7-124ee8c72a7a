package com.baidu.tts.loopj;

import org.apache.http.Header;
import org.apache.http.HttpResponse;

/* loaded from: classes.dex */
public class BlackholeHttpResponseHandler extends AsyncHttpResponseHandler {
    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onSuccess(int statusCode, Header[] headers, byte[] responseBody) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onFailure(int statusCode, Header[] headers, byte[] responseBody, Throwable error) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onProgress(long bytesWritten, long totalSize) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onCancel() {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onFinish() {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler, com.baidu.tts.loopj.ResponseHandlerInterface
    public void onPostProcessResponse(ResponseHandlerInterface instance, HttpResponse response) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler, com.baidu.tts.loopj.ResponseHandlerInterface
    public void onPreProcessResponse(ResponseHandlerInterface instance, HttpResponse response) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onRetry(int retryNo) {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onStart() {
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onUserException(Throwable error) {
    }
}
