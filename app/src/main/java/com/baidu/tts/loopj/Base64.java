package com.baidu.tts.loopj;

import java.io.UnsupportedEncodingException;

/* loaded from: classes.dex */
public class Base64 {
    public static final int CRLF = 4;
    public static final int DEFAULT = 0;
    public static final int NO_CLOSE = 16;
    public static final int NO_PADDING = 1;
    public static final int NO_WRAP = 2;
    public static final int URL_SAFE = 8;

    static abstract class Coder {
        public int op;
        public byte[] output;

        public abstract int maxOutputSize(int i);

        public abstract boolean process(byte[] bArr, int i, int i2, boolean z);

        Coder() {
        }
    }

    public static byte[] decode(String str, int flags) {
        return decode(str.getBytes(), flags);
    }

    public static byte[] decode(byte[] input, int flags) {
        return decode(input, 0, input.length, flags);
    }

    public static byte[] decode(byte[] input, int offset, int len, int flags) {
        Decoder decoder = new Decoder(flags, new byte[(len * 3) / 4]);
        if (!decoder.process(input, offset, len, true)) {
            throw new IllegalArgumentException("bad base-64");
        }
        if (decoder.op == decoder.output.length) {
            return decoder.output;
        }
        byte[] bArr = new byte[decoder.op];
        System.arraycopy(decoder.output, 0, bArr, 0, decoder.op);
        return bArr;
    }

    static class Decoder extends Coder {
        private static final int[] DECODE = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -2, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
        private static final int[] DECODE_WEBSAFE = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -2, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, 63, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1};
        private static final int EQUALS = -2;
        private static final int SKIP = -1;
        private final int[] alphabet;
        private int state;
        private int value;

        public Decoder(int flags, byte[] output) {
            this.output = output;
            this.alphabet = (flags & 8) == 0 ? DECODE : DECODE_WEBSAFE;
            this.state = 0;
            this.value = 0;
        }

        @Override // com.baidu.tts.loopj.Base64.Coder
        public int maxOutputSize(int len) {
            return ((len * 3) / 4) + 10;
        }

        /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
        /* JADX WARN: Code restructure failed: missing block: B:17:0x005c, code lost:
        
            if (r13 != false) goto L57;
         */
        /* JADX WARN: Code restructure failed: missing block: B:18:0x005e, code lost:
        
            r9.state = r3;
            r9.value = r2;
            r9.op = r0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:19:?, code lost:
        
            return true;
         */
        /* JADX WARN: Code restructure failed: missing block: B:20:0x0104, code lost:
        
            switch(r3) {
                case 0: goto L58;
                case 1: goto L59;
                case 2: goto L60;
                case 3: goto L61;
                case 4: goto L62;
                default: goto L58;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:21:0x0107, code lost:
        
            r9.state = r3;
            r9.op = r0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:22:?, code lost:
        
            return true;
         */
        /* JADX WARN: Code restructure failed: missing block: B:23:0x010e, code lost:
        
            r9.state = 6;
         */
        /* JADX WARN: Code restructure failed: missing block: B:24:?, code lost:
        
            return false;
         */
        /* JADX WARN: Code restructure failed: missing block: B:25:0x0114, code lost:
        
            r4[r0] = (byte) (r2 >> 4);
            r0 = r0 + 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:26:0x011d, code lost:
        
            r1 = r0 + 1;
            r4[r0] = (byte) (r2 >> 10);
            r0 = r1 + 1;
            r4[r1] = (byte) (r2 >> 2);
         */
        /* JADX WARN: Code restructure failed: missing block: B:27:0x012c, code lost:
        
            r9.state = 6;
         */
        /* JADX WARN: Code restructure failed: missing block: B:28:?, code lost:
        
            return false;
         */
        @Override // com.baidu.tts.loopj.Base64.Coder
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct code enable 'Show inconsistent code' option in preferences
        */
        public boolean process(byte[] r10, int r11, int r12, boolean r13) {
            /*
                Method dump skipped, instructions count: 340
                To view this dump change 'Code comments level' option to 'DEBUG'
            */
            throw new UnsupportedOperationException("Method not decompiled: com.baidu.tts.loopj.Base64.Decoder.process(byte[], int, int, boolean):boolean");
        }
    }

    public static String encodeToString(byte[] input, int flags) {
        try {
            return new String(encode(input, flags), "US-ASCII");
        } catch (UnsupportedEncodingException e) {
            throw new AssertionError(e);
        }
    }

    public static String encodeToString(byte[] input, int offset, int len, int flags) {
        try {
            return new String(encode(input, offset, len, flags), "US-ASCII");
        } catch (UnsupportedEncodingException e) {
            throw new AssertionError(e);
        }
    }

    public static byte[] encode(byte[] input, int flags) {
        return encode(input, 0, input.length, flags);
    }

    public static byte[] encode(byte[] input, int offset, int len, int flags) {
        Encoder encoder = new Encoder(flags, null);
        int i = (len / 3) * 4;
        if (encoder.do_padding) {
            if (len % 3 > 0) {
                i += 4;
            }
        } else {
            switch (len % 3) {
                case 1:
                    i += 2;
                    break;
                case 2:
                    i += 3;
                    break;
            }
        }
        if (encoder.do_newline && len > 0) {
            i += (encoder.do_cr ? 2 : 1) * (((len - 1) / 57) + 1);
        }
        encoder.output = new byte[i];
        encoder.process(input, offset, len, true);
        return encoder.output;
    }

    static class Encoder extends Coder {
        private static final byte[] ENCODE = {65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47};
        private static final byte[] ENCODE_WEBSAFE = {65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 45, 95};
        public static final int LINE_GROUPS = 19;
        private final byte[] alphabet;
        private int count;
        public final boolean do_cr;
        public final boolean do_newline;
        public final boolean do_padding;
        private final byte[] tail;
        int tailLen;

        public Encoder(int flags, byte[] output) {
            this.output = output;
            this.do_padding = (flags & 1) == 0;
            this.do_newline = (flags & 2) == 0;
            this.do_cr = (flags & 4) != 0;
            this.alphabet = (flags & 8) == 0 ? ENCODE : ENCODE_WEBSAFE;
            this.tail = new byte[2];
            this.tailLen = 0;
            this.count = this.do_newline ? 19 : -1;
        }

        @Override // com.baidu.tts.loopj.Base64.Coder
        public int maxOutputSize(int len) {
            return ((len * 8) / 5) + 10;
        }

        /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
        @Override // com.baidu.tts.loopj.Base64.Coder
        public boolean process(byte[] input, int offset, int len, boolean finish) {
            int i;
            int i2;
            byte b;
            int i3;
            int i4;
            byte b2;
            int i5;
            byte b3;
            int i6;
            int i7;
            int i8;
            byte[] bArr = this.alphabet;
            byte[] bArr2 = this.output;
            int i9 = 0;
            int i10 = this.count;
            int len2 = len + offset;
            int i11 = -1;
            switch (this.tailLen) {
                case 0:
                    i = offset;
                    break;
                case 1:
                    if (offset + 2 <= len2) {
                        int i12 = offset + 1;
                        i11 = ((this.tail[0] & 255) << 16) | ((input[offset] & 255) << 8) | (input[i12] & 255);
                        this.tailLen = 0;
                        i = i12 + 1;
                        break;
                    }
                    i = offset;
                    break;
                case 2:
                    if (offset + 1 <= len2) {
                        i = offset + 1;
                        i11 = ((this.tail[0] & 255) << 16) | ((this.tail[1] & 255) << 8) | (input[offset] & 255);
                        this.tailLen = 0;
                        break;
                    }
                    i = offset;
                    break;
                default:
                    i = offset;
                    break;
            }
            if (i11 != -1) {
                bArr2[0] = bArr[(i11 >> 18) & 63];
                bArr2[1] = bArr[(i11 >> 12) & 63];
                bArr2[2] = bArr[(i11 >> 6) & 63];
                i9 = 4;
                bArr2[3] = bArr[i11 & 63];
                i10--;
                if (i10 == 0) {
                    if (this.do_cr) {
                        i8 = 5;
                        bArr2[4] = 13;
                    } else {
                        i8 = 4;
                    }
                    i9 = i8 + 1;
                    bArr2[i8] = 10;
                    i10 = 19;
                }
            }
            while (true) {
                int i13 = i10;
                int i14 = i9;
                if (i + 3 <= len2) {
                    int i15 = ((input[i] & 255) << 16) | ((input[i + 1] & 255) << 8) | (input[i + 2] & 255);
                    bArr2[i14] = bArr[(i15 >> 18) & 63];
                    bArr2[i14 + 1] = bArr[(i15 >> 12) & 63];
                    bArr2[i14 + 2] = bArr[(i15 >> 6) & 63];
                    bArr2[i14 + 3] = bArr[i15 & 63];
                    i += 3;
                    i9 = i14 + 4;
                    i10 = i13 - 1;
                    if (i10 == 0) {
                        if (this.do_cr) {
                            i7 = i9 + 1;
                            bArr2[i9] = 13;
                        } else {
                            i7 = i9;
                        }
                        i9 = i7 + 1;
                        bArr2[i7] = 10;
                        i10 = 19;
                    }
                } else {
                    if (finish) {
                        if (i - this.tailLen == len2 - 1) {
                            if (this.tailLen > 0) {
                                i6 = 1;
                                b3 = this.tail[0];
                            } else {
                                int i16 = i + 1;
                                b3 = input[i];
                                i6 = 0;
                            }
                            int i17 = (b3 & 255) << 4;
                            this.tailLen -= i6;
                            int i18 = i14 + 1;
                            bArr2[i14] = bArr[(i17 >> 6) & 63];
                            int i19 = i18 + 1;
                            bArr2[i18] = bArr[i17 & 63];
                            if (this.do_padding) {
                                int i20 = i19 + 1;
                                bArr2[i19] = 61;
                                i19 = i20 + 1;
                                bArr2[i20] = 61;
                            }
                            if (this.do_newline) {
                                if (this.do_cr) {
                                    bArr2[i19] = 13;
                                    i19++;
                                }
                                bArr2[i19] = 10;
                                i19++;
                            }
                            i14 = i19;
                        } else if (i - this.tailLen == len2 - 2) {
                            if (this.tailLen > 1) {
                                i4 = 1;
                                b = this.tail[0];
                                i3 = i;
                            } else {
                                b = input[i];
                                i3 = i + 1;
                                i4 = 0;
                            }
                            int i21 = (b & 255) << 10;
                            if (this.tailLen > 0) {
                                b2 = this.tail[i4];
                                i4++;
                            } else {
                                int i22 = i3 + 1;
                                b2 = input[i3];
                            }
                            int i23 = ((b2 & 255) << 2) | i21;
                            this.tailLen -= i4;
                            int i24 = i14 + 1;
                            bArr2[i14] = bArr[(i23 >> 12) & 63];
                            int i25 = i24 + 1;
                            bArr2[i24] = bArr[(i23 >> 6) & 63];
                            int i26 = i25 + 1;
                            bArr2[i25] = bArr[i23 & 63];
                            if (this.do_padding) {
                                i5 = i26 + 1;
                                bArr2[i26] = 61;
                            } else {
                                i5 = i26;
                            }
                            if (this.do_newline) {
                                if (this.do_cr) {
                                    bArr2[i5] = 13;
                                    i5++;
                                }
                                bArr2[i5] = 10;
                                i5++;
                            }
                            i14 = i5;
                        } else if (this.do_newline && i14 > 0 && i13 != 19) {
                            if (this.do_cr) {
                                i2 = i14 + 1;
                                bArr2[i14] = 13;
                            } else {
                                i2 = i14;
                            }
                            i14 = i2 + 1;
                            bArr2[i2] = 10;
                        }
                    } else if (i == len2 - 1) {
                        byte[] bArr3 = this.tail;
                        int i27 = this.tailLen;
                        this.tailLen = i27 + 1;
                        bArr3[i27] = input[i];
                    } else if (i == len2 - 2) {
                        byte[] bArr4 = this.tail;
                        int i28 = this.tailLen;
                        this.tailLen = i28 + 1;
                        bArr4[i28] = input[i];
                        byte[] bArr5 = this.tail;
                        int i29 = this.tailLen;
                        this.tailLen = i29 + 1;
                        bArr5[i29] = input[i + 1];
                    }
                    this.op = i14;
                    this.count = i13;
                    return true;
                }
            }
        }
    }

    private Base64() {
    }
}
