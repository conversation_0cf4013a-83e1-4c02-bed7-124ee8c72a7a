package com.baidu.tts.d.a;

import com.baidu.tts.aop.tts.TtsError;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import com.baidu.tts.client.model.ModelFileBags;
import com.baidu.tts.f.l;
import com.baidu.tts.f.n;
import com.baidu.tts.loopj.AsyncHttpClient;
import com.baidu.tts.loopj.SyncHttpClient;
import com.baidu.tts.tools.FileTools;
import com.baidu.tts.tools.StringTool;
import java.io.File;
import java.util.HashSet;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.http.Header;

/* compiled from: DownloadEngine.java */
/* loaded from: classes.dex */
public class b extends com.baidu.tts.j.a {
    private ThreadPoolExecutor h;
    private com.baidu.tts.l.a i;
    private i b = new i(this);
    private f c = new f(this);
    private d f = new d(this);
    private h g = new h(this);
    private volatile com.baidu.tts.d.a.a a = this.b;

    public b() {
        b();
    }

    public void a(com.baidu.tts.l.a aVar) {
        this.i = aVar;
    }

    public com.baidu.tts.d.a.a a() {
        return this.a;
    }

    public void a(com.baidu.tts.d.a.a aVar) {
        this.a = aVar;
    }

    public i o() {
        return this.b;
    }

    public f p() {
        return this.c;
    }

    public d q() {
        return this.f;
    }

    public h r() {
        return this.g;
    }

    @Override // com.baidu.tts.j.a
    protected TtsError g() {
        return this.a.b();
    }

    @Override // com.baidu.tts.j.a
    protected void h() {
        this.a.a();
    }

    @Override // com.baidu.tts.j.a
    protected void i() {
        this.a.c();
    }

    @Override // com.baidu.tts.j.a
    protected void j() {
        this.a.d();
    }

    @Override // com.baidu.tts.j.a
    protected void k() {
        this.a.e();
    }

    @Override // com.baidu.tts.j.a
    protected void l() {
        this.a.f();
    }

    @Override // com.baidu.tts.j.a
    public boolean m() {
        return this.a == this.g;
    }

    @Override // com.baidu.tts.j.a
    public boolean n() {
        return Thread.currentThread().isInterrupted() || this.a == this.c;
    }

    public e a(c cVar) {
        return this.a.a(cVar);
    }

    void s() {
        this.h = (ThreadPoolExecutor) Executors.newFixedThreadPool(5, new com.baidu.tts.g.a.a("downloadPoolThread"));
    }

    void t() {
        LoggerProxy.d("DownloadEngine", "enter stop");
        if (this.h != null) {
            if (!this.h.isShutdown()) {
                this.h.shutdownNow();
            }
            try {
                LoggerProxy.d("DownloadEngine", "before awaitTermination");
                LoggerProxy.d("DownloadEngine", "after awaitTermination isTermination=" + this.h.awaitTermination(l.DEFAULT.a(), TimeUnit.MILLISECONDS));
            } catch (InterruptedException e) {
            }
            this.h = null;
        }
        LoggerProxy.d("DownloadEngine", "end stop");
    }

    e b(c cVar) {
        a aVar = new a(cVar);
        cVar.c();
        LoggerProxy.d("DownloadEngine", "before submit");
        Future<Void> future = null;
        try {
            future = this.h.submit(aVar);
        } catch (Exception e) {
            LoggerProxy.d("DownloadEngine", "submit exception");
            cVar.a(com.baidu.tts.h.a.c.a().a(n.MODEL_FILE_DOWNLOAD_EXCEPTION, e));
        }
        e eVar = new e();
        eVar.a(future);
        eVar.a(aVar);
        return eVar;
    }

    /* compiled from: DownloadEngine.java */
    public class a implements Callable<Void> {
        private c b;
        private SyncHttpClient c;

        public a(c cVar) {
            this.b = cVar;
        }

        @Override // java.util.concurrent.Callable
        /* renamed from: a, reason: merged with bridge method [inline-methods] */
        public Void call() throws Exception {
            this.b.d();
            final String a = this.b.a();
            LoggerProxy.d("DownloadEngine", "DownloadWork start fileId=" + a);
            if (StringTool.isEmpty(a)) {
                this.b.a(com.baidu.tts.h.a.c.a().a(n.MODEL_REQUEST_ERROR, "fileId is null"));
            } else {
                HashSet hashSet = new HashSet();
                hashSet.add(a);
                ModelFileBags modelFileBags = b.this.i.a(hashSet).get();
                if (modelFileBags != null) {
                    String url = modelFileBags.getUrl(0);
                    if (url != null) {
                        if (url.startsWith("https")) {
                            this.c = new SyncHttpClient(true, 80, 443);
                        } else {
                            this.c = new SyncHttpClient();
                        }
                        this.c.setURLEncodingEnabled(false);
                        this.c.setTimeout(l.DEFAULT.b());
                        this.c.setMaxRetriesAndTimeout(5, AsyncHttpClient.DEFAULT_RETRY_SLEEP_TIME_MILLIS);
                        g gVar = new g(FileTools.getFile(this.b.b()), this.b) { // from class: com.baidu.tts.d.a.b.a.1
                            @Override // com.baidu.tts.d.a.g, com.baidu.tts.loopj.FileAsyncHttpResponseHandler
                            public void onFailure(int statusCode, Header[] headers, Throwable throwable, File file) {
                                LoggerProxy.d("DownloadEngine", "1isInterrupted=" + Thread.currentThread().isInterrupted());
                                if (b.this.C()) {
                                    super.onFailure(statusCode, headers, throwable, file);
                                }
                            }

                            @Override // com.baidu.tts.d.a.g, com.baidu.tts.loopj.FileAsyncHttpResponseHandler
                            public void onSuccess(int statusCode, Header[] headers, File file) {
                                LoggerProxy.d("DownloadEngine", "2isInterrupted=" + Thread.currentThread().isInterrupted() + "--fileId=" + a);
                                if (b.this.C()) {
                                    super.onSuccess(statusCode, headers, file);
                                }
                            }

                            @Override // com.baidu.tts.d.a.g, com.baidu.tts.loopj.AsyncHttpResponseHandler
                            public void onProgress(long bytesWritten, long totalSize) {
                                if (b.this.C()) {
                                    super.onProgress(bytesWritten, totalSize);
                                }
                            }
                        };
                        gVar.setUseSynchronousMode(true);
                        LoggerProxy.d("DownloadEngine", "before get fileId=" + a);
                        this.c.get(url, gVar);
                    } else {
                        this.b.a(com.baidu.tts.h.a.c.a().a(n.MODEL_REQUEST_ERROR, "url is null"));
                    }
                } else {
                    this.b.a(com.baidu.tts.h.a.c.a().a(n.MODEL_REQUEST_ERROR, "urlbags is null"));
                }
            }
            LoggerProxy.d("DownloadEngine", "DownloadWork end");
            return null;
        }

        public void b() {
            if (this.c != null) {
                this.c.stop();
            }
        }

        public c c() {
            return this.b;
        }
    }
}
