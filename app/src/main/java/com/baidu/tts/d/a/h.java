package com.baidu.tts.d.a;

/* compiled from: PauseDownloadState.java */
/* loaded from: classes.dex */
public class h extends a {
    public h(b bVar) {
        super(bVar);
    }

    @Override // com.baidu.tts.d.a.a, com.baidu.tts.j.b
    public void c() {
        a(this.a.q());
    }

    @Override // com.baidu.tts.d.a.a, com.baidu.tts.j.b
    public void e() {
        this.a.t();
        a(this.a.p());
    }

    @Override // com.baidu.tts.d.a.a, com.baidu.tts.j.b
    public void f() {
        e();
        if (this.a.a() != this) {
            this.a.f();
        }
    }

    @Override // com.baidu.tts.d.a.a
    public e a(c cVar) {
        c();
        if (this.a.a() != this) {
            return this.a.a(cVar);
        }
        return null;
    }
}
