package com.baidu.tts.d.a;

import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import com.baidu.tts.f.n;
import com.baidu.tts.loopj.RangeFileAsyncHttpResponseHandler;
import java.io.File;
import org.apache.http.Header;

/* compiled from: ModelFileResponseHandler.java */
/* loaded from: classes.dex */
public class g extends RangeFileAsyncHttpResponseHandler {
    private c a;

    public g(File file, c cVar) {
        super(file);
        this.a = cVar;
    }

    @Override // com.baidu.tts.loopj.FileAsyncHttpResponseHandler
    public void onFailure(int statusCode, Header[] headers, Throwable throwable, File file) {
        String str = null;
        if (throwable != null) {
            Throwable cause = throwable.getCause();
            if (cause == null) {
                str = throwable.getMessage();
            } else {
                str = cause.getMessage();
            }
        }
        LoggerProxy.d("ModelFileResponseHandler", "onFailure statuscode=" + statusCode + "--msg=" + str);
        this.a.a(com.baidu.tts.h.a.c.a().a(n.MODEL_REQUEST_ERROR, statusCode, "download failure", throwable));
    }

    @Override // com.baidu.tts.loopj.FileAsyncHttpResponseHandler
    public void onSuccess(int statusCode, Header[] headers, File file) {
        LoggerProxy.d("ModelFileResponseHandler", "onSuccess");
        this.a.e();
    }

    @Override // com.baidu.tts.loopj.AsyncHttpResponseHandler
    public void onProgress(long bytesWritten, long totalSize) {
        this.a.a(bytesWritten, totalSize);
    }
}
