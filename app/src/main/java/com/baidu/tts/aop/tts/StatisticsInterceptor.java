package com.baidu.tts.aop.tts;

import com.baidu.tts.aop.AInterceptor;
import com.baidu.tts.aop.AInterceptorHandler;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public class StatisticsInterceptor extends AInterceptor {
    @Override // com.baidu.tts.aop.AInterceptor
    protected void a() {
        this.a.add("speak");
        this.a.add("synthesize");
    }

    @Override // com.baidu.tts.aop.AInterceptor
    protected Object a(Object obj, Method method, Object[] objArr) {
        LoggerProxy.d("StatisticsInterceptor", "statistics");
        return AInterceptorHandler.DEFAULT;
    }
}
