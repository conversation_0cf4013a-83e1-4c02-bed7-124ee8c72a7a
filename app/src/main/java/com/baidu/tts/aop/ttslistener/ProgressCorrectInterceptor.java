package com.baidu.tts.aop.ttslistener;

import android.text.TextUtils;
import com.baidu.tts.aop.AInterceptor;
import com.baidu.tts.aop.AInterceptorHandler;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;
import com.baidu.tts.m.h;
import java.lang.reflect.Method;

/* loaded from: classes.dex */
public class ProgressCorrectInterceptor extends AInterceptor {
    @Override // com.baidu.tts.aop.AInterceptor
    protected void a() {
        this.a.add("onSynthesizeDataArrived");
        this.a.add("onPlayProgressUpdate");
    }

    @Override // com.baidu.tts.aop.AInterceptor
    protected Object a(Object obj, Method method, Object[] objArr) {
        h hVar = (h) objArr[0];
        if (hVar != null) {
            String b = hVar.e().b();
            if (!TextUtils.isEmpty(b)) {
                int length = b.length();
                int c = hVar.c();
                int i = c > length ? c - length : 0;
                LoggerProxy.d("ProgressCorrectInterceptor", "prefixLength=" + length + "--progress=" + c);
                h B = hVar.B();
                B.d(i);
                objArr[0] = B;
            }
        }
        return AInterceptorHandler.DEFAULT;
    }
}
