package com.baidu.tts.k;

import com.baidu.tts.k.a;
import com.baidu.tts.k.b;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Future;

/* compiled from: Memorizer.java */
/* loaded from: classes.dex */
public class c<A extends b<A, R>, R extends a> {
    private final ConcurrentMap<A, Future<R>> a = new ConcurrentHashMap();

    /* JADX WARN: Removed duplicated region for block: B:9:0x006e  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public R a(A r8) throws java.lang.Exception {
        /*
            r7 = this;
            r1 = 0
            com.baidu.tts.k.b r3 = r7.b(r8)
            if (r3 == 0) goto Lcf
            java.util.concurrent.ConcurrentMap<A extends com.baidu.tts.k.b<A, R>, java.util.concurrent.Future<R extends com.baidu.tts.k.a>> r0 = r7.a
            java.lang.Object r0 = r0.get(r3)
            java.util.concurrent.Future r0 = (java.util.concurrent.Future) r0
            r2 = r0
        L10:
            if (r2 == 0) goto Lcd
            java.lang.String r0 = "Memorizer"
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            java.lang.String r5 = "+ get f="
            java.lang.StringBuilder r4 = r4.append(r5)
            java.lang.StringBuilder r4 = r4.append(r2)
            java.lang.String r4 = r4.toString()
            com.baidu.tts.chainofresponsibility.logger.LoggerProxy.d(r0, r4)
            java.lang.Object r0 = r2.get()
            com.baidu.tts.k.a r0 = (com.baidu.tts.k.a) r0
            java.lang.String r4 = "Memorizer"
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            java.lang.String r6 = "- get f="
            java.lang.StringBuilder r5 = r5.append(r6)
            java.lang.StringBuilder r5 = r5.append(r2)
            java.lang.String r5 = r5.toString()
            com.baidu.tts.chainofresponsibility.logger.LoggerProxy.d(r4, r5)
            boolean r4 = r0.g()
            if (r4 != 0) goto Lcd
            java.lang.String r2 = "Memorizer"
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            java.lang.String r5 = "arg invalid r="
            java.lang.StringBuilder r4 = r4.append(r5)
            java.lang.StringBuilder r0 = r4.append(r0)
            java.lang.String r0 = r0.toString()
            com.baidu.tts.chainofresponsibility.logger.LoggerProxy.d(r2, r0)
            java.util.concurrent.ConcurrentMap<A extends com.baidu.tts.k.b<A, R>, java.util.concurrent.Future<R extends com.baidu.tts.k.a>> r0 = r7.a
            r0.remove(r3)
            r0 = r1
        L6c:
            if (r0 != 0) goto Lcb
            java.util.concurrent.FutureTask r1 = new java.util.concurrent.FutureTask
            r1.<init>(r8)
            java.util.concurrent.ConcurrentMap<A extends com.baidu.tts.k.b<A, R>, java.util.concurrent.Future<R extends com.baidu.tts.k.a>> r0 = r7.a
            java.lang.Object r0 = r0.putIfAbsent(r8, r1)
            java.util.concurrent.Future r0 = (java.util.concurrent.Future) r0
            if (r0 != 0) goto Lcb
            java.lang.String r0 = "Memorizer"
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "+ run f="
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.StringBuilder r2 = r2.append(r1)
            java.lang.String r2 = r2.toString()
            com.baidu.tts.chainofresponsibility.logger.LoggerProxy.d(r0, r2)
            r1.run()
            java.lang.String r0 = "Memorizer"
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = "- run f="
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.StringBuilder r2 = r2.append(r1)
            java.lang.String r2 = r2.toString()
            com.baidu.tts.chainofresponsibility.logger.LoggerProxy.d(r0, r2)
        Lb0:
            java.lang.Object r0 = r1.get()     // Catch: java.util.concurrent.ExecutionException -> Lb7 java.lang.Exception -> Lc4
            com.baidu.tts.k.a r0 = (com.baidu.tts.k.a) r0     // Catch: java.util.concurrent.ExecutionException -> Lb7 java.lang.Exception -> Lc4
            return r0
        Lb7:
            r0 = move-exception
            java.util.concurrent.ConcurrentMap<A extends com.baidu.tts.k.b<A, R>, java.util.concurrent.Future<R extends com.baidu.tts.k.a>> r2 = r7.a
            r2.remove(r8, r1)
            java.lang.Throwable r0 = r0.getCause()
            java.lang.Exception r0 = (java.lang.Exception) r0
            throw r0
        Lc4:
            r0 = move-exception
            java.util.concurrent.ConcurrentMap<A extends com.baidu.tts.k.b<A, R>, java.util.concurrent.Future<R extends com.baidu.tts.k.a>> r2 = r7.a
            r2.remove(r8, r1)
            throw r0
        Lcb:
            r1 = r0
            goto Lb0
        Lcd:
            r0 = r2
            goto L6c
        Lcf:
            r2 = r1
            goto L10
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.tts.k.c.a(com.baidu.tts.k.b):com.baidu.tts.k.a");
    }

    private A b(A a) {
        for (A a2 : this.a.keySet()) {
            if (a.compareTo(a2) == 0) {
                return a2;
            }
        }
        return null;
    }

    public void a() {
        if (this.a != null) {
            this.a.clear();
        }
    }
}
