package com.baidu.tts.f;

/* compiled from: AudioEncoderFormat.java */
/* loaded from: classes.dex */
public enum b {
    BV("0") { // from class: com.baidu.tts.f.b.1
        @Override // com.baidu.tts.f.b
        public c[] b() {
            return c.c();
        }
    },
    AMR("1") { // from class: com.baidu.tts.f.b.2
        @Override // com.baidu.tts.f.b
        public c[] b() {
            return c.d();
        }
    },
    OPUS("2") { // from class: com.baidu.tts.f.b.3
        @Override // com.baidu.tts.f.b
        public c[] b() {
            return c.e();
        }
    };

    private final String d;

    public abstract c[] b();

    b(String str) {
        this.d = str;
    }

    public String a() {
        return this.d;
    }

    public static b a(String str) {
        for (b bVar : values()) {
            if (bVar.a().equals(str)) {
                return bVar;
            }
        }
        return null;
    }
}
