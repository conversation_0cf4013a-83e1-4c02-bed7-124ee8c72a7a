package com.baidu.tts.o.a;

import android.content.Context;
import com.baidu.tts.aop.tts.ITts;
import com.baidu.tts.aop.ttslistener.TtsListener;
import com.baidu.tts.auth.AuthInfo;
import com.baidu.tts.f.g;
import com.baidu.tts.f.m;
import com.baidu.tts.m.j;

/* compiled from: ATtsState.java */
/* loaded from: classes.dex */
public abstract class a implements ITts {
    protected c a;

    public a(c cVar) {
        this.a = cVar;
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public void setTtsListener(TtsListener ttsListener) {
        this.a.a(ttsListener);
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public TtsListener getTtsListener() {
        return this.a.m();
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public void setContext(Context context) {
        this.a.a(context);
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public void setMode(m mode) {
        this.a.a(mode);
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public m getMode() {
        return this.a.n();
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public AuthInfo auth(m ttsEnum) {
        return this.a.b(ttsEnum);
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public int setParam(g key, String value) {
        return this.a.a(key, value);
    }

    @Override // com.baidu.tts.aop.tts.ITts
    public j getTtsParams() {
        return this.a.o();
    }

    public void a(a aVar) {
        this.a.a(aVar);
    }
}
