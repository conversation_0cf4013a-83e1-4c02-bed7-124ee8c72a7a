package com.baidu.tts.tools;

import java.io.UnsupportedEncodingException;

/* loaded from: classes.dex */
public final class Base64 {
    private static final byte[] a = {65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 43, 47};

    public static byte[] decode(byte[] paramArrayOfByte) {
        return decode(paramArrayOfByte, paramArrayOfByte.length);
    }

    public static byte[] decode(byte[] paramArrayOfByte, int paramInt) {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6 = (paramInt / 4) * 3;
        if (i6 == 0) {
            return new byte[0];
        }
        byte[] bArr = new byte[i6];
        int i7 = 0;
        while (true) {
            byte b = paramArrayOfByte[paramInt - 1];
            if (b != 10 && b != 13 && b != 32 && b != 9) {
                if (b != 61) {
                    break;
                }
                i7++;
            }
            paramInt--;
        }
        int i8 = 0;
        int i9 = 0;
        int i10 = 0;
        int i11 = 0;
        while (i8 < paramInt) {
            byte b2 = paramArrayOfByte[i8];
            if (b2 == 10 || b2 == 13 || b2 == 32 || b2 == 9) {
                i = i9;
                i2 = i11;
                i3 = i10;
            } else {
                if (b2 >= 65 && b2 <= 90) {
                    i4 = b2 - 65;
                } else if (b2 >= 97 && b2 <= 122) {
                    i4 = b2 - 71;
                } else if (b2 >= 48 && b2 <= 57) {
                    i4 = b2 + 4;
                } else if (b2 == 43) {
                    i4 = 62;
                } else if (b2 == 47) {
                    i4 = 63;
                } else {
                    return null;
                }
                int i12 = (i9 << 6) | ((byte) i4);
                if (i10 % 4 == 3) {
                    int i13 = i11 + 1;
                    bArr[i11] = (byte) ((16711680 & i12) >> 16);
                    int i14 = i13 + 1;
                    bArr[i13] = (byte) ((65280 & i12) >> 8);
                    i5 = i14 + 1;
                    bArr[i14] = (byte) (i12 & 255);
                } else {
                    i5 = i11;
                }
                i3 = i10 + 1;
                i2 = i5;
                i = i12;
            }
            i8++;
            i10 = i3;
            i11 = i2;
            i9 = i;
        }
        if (i7 > 0) {
            int i15 = i9 << (i7 * 6);
            int i16 = i11 + 1;
            bArr[i11] = (byte) ((16711680 & i15) >> 16);
            if (i7 == 1) {
                i11 = i16 + 1;
                bArr[i16] = (byte) ((65280 & i15) >> 8);
            } else {
                i11 = i16;
            }
        }
        byte[] bArr2 = new byte[i11];
        System.arraycopy(bArr, 0, bArr2, 0, i11);
        return bArr2;
    }

    public static String encode(byte[] paramArrayOfByte, String paramString) throws UnsupportedEncodingException {
        int i;
        int length = (paramArrayOfByte.length * 4) / 3;
        byte[] bArr = new byte[length + (length / 76) + 3];
        int length2 = paramArrayOfByte.length - (paramArrayOfByte.length % 3);
        int i2 = 0;
        int i3 = 0;
        for (int i4 = 0; i4 < length2; i4 += 3) {
            int i5 = i3 + 1;
            bArr[i3] = a[(paramArrayOfByte[i4] & 255) >> 2];
            int i6 = i5 + 1;
            bArr[i5] = a[((paramArrayOfByte[i4] & 3) << 4) | ((paramArrayOfByte[i4 + 1] & 255) >> 4)];
            int i7 = i6 + 1;
            bArr[i6] = a[((paramArrayOfByte[i4 + 1] & 15) << 2) | ((paramArrayOfByte[i4 + 2] & 255) >> 6)];
            int i8 = i7 + 1;
            bArr[i7] = a[paramArrayOfByte[i4 + 2] & 63];
            if ((i8 - i2) % 76 != 0 || i8 == 0) {
                i3 = i8;
            } else {
                i3 = i8 + 1;
                bArr[i8] = 10;
                i2++;
            }
        }
        switch (paramArrayOfByte.length % 3) {
            case 1:
                int i9 = i3 + 1;
                bArr[i3] = a[(paramArrayOfByte[length2] & 255) >> 2];
                int i10 = i9 + 1;
                bArr[i9] = a[(paramArrayOfByte[length2] & 3) << 4];
                int i11 = i10 + 1;
                bArr[i10] = 61;
                i = i11 + 1;
                bArr[i11] = 61;
                break;
            case 2:
                int i12 = i3 + 1;
                bArr[i3] = a[(paramArrayOfByte[length2] & 255) >> 2];
                int i13 = i12 + 1;
                bArr[i12] = a[((paramArrayOfByte[length2] & 3) << 4) | ((paramArrayOfByte[length2 + 1] & 255) >> 4)];
                int i14 = i13 + 1;
                bArr[i13] = a[(paramArrayOfByte[length2 + 1] & 15) << 2];
                i = i14 + 1;
                bArr[i14] = 61;
                break;
            default:
                i = i3;
                break;
        }
        return new String(bArr, 0, i, paramString);
    }
}
