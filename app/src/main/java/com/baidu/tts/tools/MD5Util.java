package com.baidu.tts.tools;

import android.annotation.SuppressLint;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@SuppressLint({"DefaultLocale"})
/* loaded from: classes.dex */
public class MD5Util {
    public static String toMd5(byte[] paramArrayOfByte, boolean paramBoolean) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(paramArrayOfByte);
            return toHexString(messageDigest.digest(), "", paramBoolean);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toHexString(byte[] paramArrayOfByte, String paramString, boolean paramBoolean) {
        StringBuilder sb = new StringBuilder();
        for (byte b : paramArrayOfByte) {
            String hexString = Integer.toHexString(b & 255);
            if (paramBoolean) {
                hexString = hexString.toUpperCase();
            }
            if (hexString.length() == 1) {
                sb.append("0");
            }
            sb.append(hexString).append(paramString);
        }
        return sb.toString();
    }
}
