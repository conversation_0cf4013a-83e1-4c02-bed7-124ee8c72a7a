package com.baidu.tts.tools;

import android.content.Context;
import android.text.TextUtils;
import com.baidu.tts.chainofresponsibility.logger.LoggerProxy;

/* loaded from: classes.dex */
public class GetCUID {
    private GetCUID() {
    }

    public static String getCUID(Context context) {
        String string = SharedPreferencesUtils.getString(context, "CUID", "");
        if (TextUtils.isEmpty(string)) {
            String cuid = CommonParam.getCUID(context);
            SharedPreferencesUtils.putString(context, "CUID", cuid);
            return cuid;
        }
        LoggerProxy.d("Device", "read deviceID:" + string);
        return string;
    }
}
