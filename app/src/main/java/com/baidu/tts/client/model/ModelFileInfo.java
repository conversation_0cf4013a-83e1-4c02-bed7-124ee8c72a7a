package com.baidu.tts.client.model;

import android.content.Context;
import com.baidu.tts.f.g;
import com.baidu.tts.tools.ResourceTools;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class ModelFileInfo {
    private String a;
    private String b;
    private String c;
    private String d;
    private String e;
    private String f;

    public String getServerid() {
        return this.a;
    }

    public void setServerid(String serverid) {
        this.a = serverid;
    }

    public String getLength() {
        return this.b;
    }

    public void setLength(String length) {
        this.b = length;
    }

    public String getMd5() {
        return this.c;
    }

    public void setMd5(String md5) {
        this.c = md5;
    }

    public String getName() {
        return this.d;
    }

    public void setName(String name) {
        this.d = name;
    }

    public String getAbsPath() {
        return this.e;
    }

    public void setAbsPath(String absPath) {
        this.e = absPath;
    }

    public String getUrl() {
        return this.f;
    }

    public void setUrl(String url) {
        this.f = url;
    }

    public void generateAbsPath(Context context) {
        this.e = ResourceTools.getModelFileAbsName(context, this.d);
    }

    public void setMap(Map<String, String> map) {
        if (map != null && !map.isEmpty()) {
            this.a = map.get(g.ID.b());
            this.b = map.get(g.LENGTH.b());
            this.c = map.get(g.MD5.b());
            this.d = map.get(g.NAME.b());
            this.e = map.get(g.ABS_PATH.b());
        }
    }

    public JSONObject toJson() {
        JSONObject jSONObject = new JSONObject();
        try {
            jSONObject.putOpt(g.ID.b(), this.a);
            jSONObject.putOpt(g.LENGTH.b(), this.b);
            jSONObject.putOpt(g.MD5.b(), this.c);
            jSONObject.putOpt(g.NAME.b(), this.d);
            jSONObject.putOpt(g.ABS_PATH.b(), this.e);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jSONObject;
    }

    public void parseJson(JSONObject jo) {
        this.a = jo.optString(g.ID.b());
        this.b = jo.optString(g.LENGTH.b());
        this.c = jo.optString(g.MD5.b());
        this.d = jo.optString(g.NAME.b());
        this.f = jo.optString(g.URL.b());
    }
}
