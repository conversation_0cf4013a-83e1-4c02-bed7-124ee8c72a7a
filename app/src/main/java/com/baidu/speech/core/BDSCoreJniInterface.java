package com.baidu.speech.core;

import android.util.Log;
import com.baidu.speech.core.BDSSDKLoader;
import java.lang.ref.WeakReference;
import java.util.HashMap;

/* loaded from: classes.dex */
public class BDSCoreJniInterface implements BDSSDKLoader.BDSSDKInterface {
    private static HashMap<String, WeakReference<BDSCoreJniInterface>> s_sdkInstances = new HashMap<>();
    private WeakReference<BDSSDKLoader.BDSCoreEventListener> m_observer;
    private String m_sdkHandle;

    private native void EchoMessage(BDSMessage bDSMessage, String str);

    private native int Post(BDSMessage bDSMessage, String str);

    private native void ReleaseInstance(String str);

    private static void addInstance(String str, BDSCoreJniInterface bDSCoreJniInterface) {
        WeakReference<BDSCoreJniInterface> weakReference = new WeakReference<>(bDSCoreJniInterface);
        synchronized (s_sdkInstances) {
            s_sdkInstances.put(str, weakReference);
        }
    }

    private static BDSCoreJniInterface findInstance(String str) {
        BDSCoreJniInterface bDSCoreJniInterface;
        synchronized (s_sdkInstances) {
            WeakReference<BDSCoreJniInterface> weakReference = s_sdkInstances.get(str);
            if (weakReference == null) {
                bDSCoreJniInterface = null;
            } else {
                bDSCoreJniInterface = weakReference.get();
                if (bDSCoreJniInterface == null) {
                    removeInstance(str);
                }
            }
        }
        return bDSCoreJniInterface;
    }

    public static BDSCoreJniInterface getNewSDK(String str) {
        String initCoreSDK = initCoreSDK(str);
        if (initCoreSDK == null || initCoreSDK.length() <= 0) {
            return null;
        }
        BDSCoreJniInterface bDSCoreJniInterface = new BDSCoreJniInterface();
        bDSCoreJniInterface.m_sdkHandle = initCoreSDK;
        addInstance(initCoreSDK, bDSCoreJniInterface);
        return bDSCoreJniInterface;
    }

    private static native String initCoreSDK(String str);

    private static void receiveCoreEvent(String str, BDSMessage bDSMessage) {
        BDSCoreJniInterface findInstance = findInstance(str);
        if (findInstance == null) {
            Log.e("core event", "Can't find instance for id " + str);
            return;
        }
        BDSSDKLoader.BDSCoreEventListener bDSCoreEventListener = findInstance.m_observer.get();
        if (bDSCoreEventListener != null) {
            bDSCoreEventListener.receiveCoreEvent(bDSMessage, findInstance);
        } else {
            Log.e("core event", "Listener is null for instance id " + str);
        }
    }

    private static void removeInstance(String str) {
        synchronized (s_sdkInstances) {
            s_sdkInstances.remove(str);
        }
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSSDKInterface
    public void EchoMessage(BDSMessage bDSMessage) {
        EchoMessage(bDSMessage, this.m_sdkHandle);
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSSDKInterface
    public boolean instanceInitialized() {
        return this.m_sdkHandle != null && this.m_sdkHandle.length() > 0;
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSSDKInterface
    public int postMessage(BDSMessage bDSMessage) {
        return Post(bDSMessage, this.m_sdkHandle);
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSSDKInterface
    public void release() {
        if (instanceInitialized()) {
            ReleaseInstance(this.m_sdkHandle);
        }
        removeInstance(this.m_sdkHandle);
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSSDKInterface
    public void setListener(BDSSDKLoader.BDSCoreEventListener bDSCoreEventListener) {
        this.m_observer = new WeakReference<>(bDSCoreEventListener);
    }
}
