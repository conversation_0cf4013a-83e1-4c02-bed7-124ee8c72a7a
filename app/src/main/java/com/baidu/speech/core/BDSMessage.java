package com.baidu.speech.core;

import com.baidu.speech.core.BDSParamBase;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/* loaded from: classes.dex */
public class BDSMessage {
    public long m_dataOffset;
    public byte[] m_messageData;
    public String m_messageName;
    public HashMap<String, BDSParamBase> m_messageParams;

    public String toString() {
        String str = this.m_messageName;
        Set<Map.Entry<String, BDSParamBase>> entrySet = this.m_messageParams.entrySet();
        String str2 = str + " messageParamsCount=" + this.m_messageParams.size() + " messageParams:{  ";
        Iterator<Map.Entry<String, BDSParamBase>> it = entrySet.iterator();
        while (true) {
            String str3 = str2;
            if (!it.hasNext()) {
                return str3 + "  } ";
            }
            Map.Entry<String, BDSParamBase> next = it.next();
            String key = next.getKey();
            str2 = key.endsWith("int") ? str3 + " (" + next.getKey() + " , " + ((BDSParamBase.BDSIntParam) next.getValue()).iValue + ") " : key.endsWith("string") ? str3 + " (" + next.getKey() + " , " + ((BDSParamBase.BDSObjectParam) next.getValue()).iValue + ") " : key.endsWith("float") ? str3 + " (" + next.getKey() + " , " + ((BDSParamBase.BDSFloatParam) next.getValue()).iValue + ") " : key.endsWith("bool") ? str3 + " (" + next.getKey() + " , " + ((BDSParamBase.BDSBooleanParam) next.getValue()).iValue + ") " : str3;
        }
    }
}
