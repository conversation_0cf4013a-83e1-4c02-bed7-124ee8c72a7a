package com.baidu.speech;

import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.RemoteException;
import android.speech.RecognitionService;
import android.util.AndroidRuntimeException;
import com.baidu.speech.asr.SpeechConstant;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public final class VoiceRecognitionService extends RecognitionService {
    public static final int EVENT_ENGINE_SWITCH = 12;
    private static final int EVENT_ERROR = 11;
    private static final int EVENT_THIRD_DATA = 12;
    public static final String VERSION_NAME = "3.2.0.100";
    private boolean internal;
    private EventManager mEventManagerAsr;
    private Bundle mFinalBundle;
    private boolean mLongSpeech;
    private MyListener mUsingListener;
    public static final String TAG = "VoiceRecognitionService";
    private static final Logger logger = Logger.getLogger(TAG);

    class MyListener implements EventListener {
        RecognitionService.Callback mListener;

        MyListener() {
        }

        private final void callbackOnEvent(RecognitionService.Callback callback, int i, Bundle bundle) {
            try {
                Field declaredField = callback.getClass().getDeclaredField("mListener");
                declaredField.setAccessible(true);
                Class.forName("android.speech.IRecognitionListener").getMethod("onEvent", Integer.TYPE, Bundle.class).invoke(declaredField.get(callback), Integer.valueOf(i), bundle);
            } catch (Exception e) {
                e.printStackTrace();
                VoiceRecognitionService.logger.log(Level.WARNING, "", (Throwable) e);
            }
        }

        @Override // com.baidu.speech.EventListener
        public void onEvent(String str, String str2, byte[] bArr, int i, int i2) {
            RecognitionService.Callback callback = this.mListener;
            if (callback == null) {
                return;
            }
            try {
                if (SpeechConstant.CALLBACK_EVENT_ASR_READY.equals(str)) {
                    callback.readyForSpeech(new Bundle());
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_BEGIN.equals(str)) {
                    callback.beginningOfSpeech();
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_AUDIO.equals(str)) {
                    callback.bufferReceived(bArr);
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_VOLUME.equals(str)) {
                    callback.rmsChanged((float) new JSONObject(str2).optDouble("volume"));
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_END.equals(str)) {
                    callback.endOfSpeech();
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_PARTIAL.equals(str)) {
                    JSONObject jSONObject = new JSONObject(str2);
                    String optString = jSONObject.optString("result_type");
                    Bundle fromJson = VoiceRecognitionService.fromJson(jSONObject);
                    if (optString != null && optString != "") {
                        if (optString.equals("partial_result")) {
                            callback.partialResults(fromJson);
                        } else if (optString.equals("final_result")) {
                            VoiceRecognitionService.this.mFinalBundle = fromJson;
                        } else if (optString.equals("third_result")) {
                            Bundle bundle = new Bundle();
                            bundle.putByteArray("third_data", bArr);
                            callbackOnEvent(callback, 12, bundle);
                        }
                    }
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_FINISH.equals(str)) {
                    JSONObject jSONObject2 = new JSONObject(str2);
                    int i3 = jSONObject2.getInt("error");
                    if (i3 != 0) {
                        callback.error(i3);
                        Bundle bundle2 = new Bundle();
                        bundle2.putInt("error", jSONObject2.getInt("sub_error"));
                        bundle2.putString("reason", jSONObject2.getString("desc"));
                        callbackOnEvent(callback, 11, bundle2);
                    } else if (!VoiceRecognitionService.this.mLongSpeech) {
                        callback.results(VoiceRecognitionService.this.mFinalBundle);
                        VoiceRecognitionService.this.mFinalBundle = null;
                    }
                } else if (SpeechConstant.CALLBACK_EVENT_ASR_LONG_SPEECH.equals(str)) {
                    callback.results(VoiceRecognitionService.this.mFinalBundle);
                    VoiceRecognitionService.this.mFinalBundle = null;
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            } catch (JSONException e2) {
                e2.printStackTrace();
            }
        }

        public void setCallbackListener(RecognitionService.Callback callback) {
            this.mListener = callback;
        }
    }

    private JSONObject convertIntentToJson(Intent intent) {
        HashMap hashMap = new HashMap();
        intent.getStringExtra("a");
        Bundle extras = intent.getExtras();
        for (String str : extras.keySet()) {
            Object obj = extras.get(str);
            if (str.equals("args") && (obj instanceof String)) {
                String[] split = ((String) obj).split("--");
                for (String str2 : split) {
                    int indexOf = str2.trim().indexOf(" ");
                    if (indexOf < 0) {
                        indexOf = str2.indexOf("\t");
                    }
                    if (indexOf < 0) {
                        indexOf = str2.indexOf("=");
                    }
                    if (indexOf > 0) {
                        hashMap.put(str2.substring(0, indexOf).trim(), str2.substring(indexOf + 1).trim());
                    }
                }
            } else {
                hashMap.put(str, obj);
            }
        }
        return new JSONObject(hashMap);
    }

    public static Bundle fromJson(JSONObject jSONObject) {
        Bundle bundle = new Bundle();
        Iterator<String> keys = jSONObject.keys();
        while (keys.hasNext()) {
            String next = keys.next();
            JSONArray optJSONArray = jSONObject.optJSONArray(next);
            String optString = jSONObject.optString(next);
            if (optJSONArray != null && optJSONArray.length() <= 0) {
                bundle.putStringArray(next, new String[0]);
            } else if (optJSONArray != null && optJSONArray.optString(0) != null) {
                ArrayList<String> arrayList = new ArrayList<>();
                for (int i = 0; i < optJSONArray.length(); i++) {
                    arrayList.add(optJSONArray.optString(i));
                }
                bundle.putStringArrayList(next, arrayList);
            } else if (optJSONArray != null && !Double.isNaN(optJSONArray.optDouble(0))) {
                double[] dArr = new double[optJSONArray.length()];
                for (int i2 = 0; i2 < optJSONArray.length(); i2++) {
                    dArr[i2] = optJSONArray.optDouble(i2);
                }
                bundle.putDoubleArray(next, dArr);
            } else if (optString != null) {
                bundle.putString(next, optString);
            }
        }
        return bundle;
    }

    public static String getSdkVersion() {
        return VERSION_NAME;
    }

    @Override // android.speech.RecognitionService
    protected void onCancel(RecognitionService.Callback callback) {
        this.mEventManagerAsr.send("asr.cancel", "{}", null, 0, 0);
    }

    @Override // android.app.Service
    public void onCreate() {
        super.onCreate();
        synchronized (VoiceRecognitionService.class) {
            if (this.mEventManagerAsr == null) {
                this.mEventManagerAsr = EventManagerFactory.create(getApplicationContext(), "asr");
                this.mUsingListener = new MyListener();
                this.mEventManagerAsr.registerListener(this.mUsingListener);
                SpeechConstant.PUBLIC_DECODER = false;
            }
        }
        logger.info(String.format("onCreate(), hashcode=%s", Integer.valueOf(hashCode())));
        try {
            Class.forName("com.baidu.android.voicedemo.SettingMore");
            this.internal = true;
        } catch (Exception e) {
        }
        logger.info("internal=" + this.internal);
        try {
            if (getPackageManager().getServiceInfo(new ComponentName(getPackageName(), getClass().getName()), 128).exported) {
                throw new AndroidRuntimeException(getClass().getName() + ", 'android:exported' should be false, please modify AndroidManifest.xml");
            }
        } catch (PackageManager.NameNotFoundException e2) {
            e2.printStackTrace();
        }
    }

    @Override // android.speech.RecognitionService, android.app.Service
    public void onDestroy() {
        this.mEventManagerAsr.send(SpeechConstant.ASR_KWS_UNLOAD_ENGINE, "{}", null, 0, 0);
        super.onDestroy();
    }

    @Override // android.speech.RecognitionService
    protected void onStartListening(Intent intent, RecognitionService.Callback callback) {
        if (!intent.hasExtra(SpeechConstant.AUDIO_MILLS)) {
            intent.putExtra(SpeechConstant.AUDIO_MILLS, System.currentTimeMillis());
        }
        this.mLongSpeech = intent.getIntExtra(SpeechConstant.VAD_ENDPOINT_TIMEOUT, -1) == 0;
        JSONObject convertIntentToJson = convertIntentToJson(intent);
        try {
            this.mUsingListener.setCallbackListener(callback);
            if (intent.getIntExtra(SpeechConstant.DECODER, 0) != 0) {
                this.mEventManagerAsr.send(SpeechConstant.ASR_KWS_LOAD_ENGINE, convertIntentToJson.toString(4), null, 0, 0);
            }
            this.mEventManagerAsr.send(SpeechConstant.ASR_START, convertIntentToJson.toString(4), null, 0, 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override // android.speech.RecognitionService
    protected void onStopListening(RecognitionService.Callback callback) {
        this.mEventManagerAsr.send(SpeechConstant.ASR_STOP, "{}", null, 0, 0);
    }
}
