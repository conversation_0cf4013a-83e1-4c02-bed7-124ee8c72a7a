package com.baidu.speech;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import com.baidu.speech.aidl.EventListener;
import com.baidu.speech.aidl.EventManagerFactory;
import com.baidu.speech.aidl.EventRecognitionService;
import com.baidu.speech.asr.EventManagerAsr;
import com.baidu.speech.asr.EventManagerSlot;
import com.baidu.speech.asr.EventManagerWp;
import com.baidu.speech.asr.SpeechConstant;
import com.baidu.speech.audio.MicrophoneServer;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class EventManagerFactory {
    private static final String TAG = "EventManagerFactory";
    private static boolean kwsLoaded = false;
    private static boolean asrUsing = false;
    private static boolean wpUsing = false;

    static class EventManagerRemote2Local implements EventManager {
        private Context context;
        private EventListener mLis;
        private String name;
        private com.baidu.speech.aidl.EventManager remoteEM;
        ExecutorService executor = Executors.newCachedThreadPool();
        final ServiceConnection conn = new ServiceConnection() { // from class: com.baidu.speech.EventManagerFactory.EventManagerRemote2Local.1
            @Override // android.content.ServiceConnection
            public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
                com.baidu.speech.aidl.EventManagerFactory asInterface = EventManagerFactory.Stub.asInterface(iBinder);
                try {
                    if (EventManagerRemote2Local.this.remoteEM == null) {
                        EventManagerRemote2Local.this.setRemoteEM(asInterface.create(EventManagerRemote2Local.this.name));
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override // android.content.ServiceConnection
            public void onServiceDisconnected(ComponentName componentName) {
                if (EventManagerFactory.kwsLoaded && EventManagerRemote2Local.this.mLis != null) {
                    EventManagerRemote2Local.this.mLis.onEvent(SpeechConstant.CALLBACK_EVENT_ASR_UNLOADED, null, null, 0, 0);
                }
                if (EventManagerFactory.asrUsing && EventManagerRemote2Local.this.mLis != null) {
                    EventManagerRemote2Local.this.mLis.onEvent(SpeechConstant.CALLBACK_EVENT_ASR_EXIT, null, null, 0, 0);
                }
                if (EventManagerFactory.wpUsing && EventManagerRemote2Local.this.mLis != null) {
                    EventManagerRemote2Local.this.mLis.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_STOPED, null, null, 0, 0);
                }
                EventManagerRemote2Local.this.remoteEM = null;
            }
        };

        /* renamed from: com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2, reason: invalid class name */
        class AnonymousClass2 implements Runnable {
            final /* synthetic */ String val$cmd;
            final /* synthetic */ byte[] val$data;
            final /* synthetic */ int val$length;
            final /* synthetic */ int val$offset;
            final /* synthetic */ String val$params;

            AnonymousClass2(String str, String str2, byte[] bArr, int i, int i2) {
                this.val$params = str;
                this.val$cmd = str2;
                this.val$data = bArr;
                this.val$offset = i;
                this.val$length = i2;
            }

            @Override // java.lang.Runnable
            public void run() {
                JSONObject jSONObject;
                String str;
                if (EventManagerRemote2Local.this.remoteEM == null) {
                    new Handler(Looper.getMainLooper()).postDelayed(this, 10L);
                    return;
                }
                String str2 = this.val$params;
                try {
                    if (SpeechConstant.ASR_START.equals(this.val$cmd) || SpeechConstant.WAKEUP_START.equals(this.val$cmd)) {
                        try {
                            jSONObject = new JSONObject(this.val$params);
                        } catch (Exception e) {
                            jSONObject = new JSONObject();
                        }
                        try {
                            String optString = jSONObject.optString(SpeechConstant.IN_FILE);
                            if (!jSONObject.has("audio.socketport") && !TextUtils.isEmpty(optString)) {
                                jSONObject.put("audio.socketport", MicrophoneServer.create(optString, jSONObject.has(SpeechConstant.AUDIO_SOURCE) ? jSONObject.optInt(SpeechConstant.AUDIO_SOURCE) : 1));
                                str2 = jSONObject.toString();
                            }
                            str = str2;
                        } catch (Exception e2) {
                            e2.printStackTrace();
                        }
                        EventManagerRemote2Local.this.remoteEM.registerListener(new EventListener.Stub() { // from class: com.baidu.speech.EventManagerFactory.EventManagerRemote2Local.2.1
                            /* JADX WARN: Removed duplicated region for block: B:19:0x002f  */
                            /* JADX WARN: Removed duplicated region for block: B:22:0x0069  */
                            @Override // com.baidu.speech.aidl.EventListener
                            /*
                                Code decompiled incorrectly, please refer to instructions dump.
                                To view partially-correct code enable 'Show inconsistent code' option in preferences
                            */
                            public void onEvent(final java.lang.String r10, final java.lang.String r11, final byte[] r12, final int r13, final int r14) throws android.os.RemoteException {
                                /*
                                    r9 = this;
                                    r2 = 0
                                    java.lang.String r0 = "asr.exit"
                                    boolean r0 = r0.equals(r10)
                                    if (r0 == 0) goto L48
                                    com.baidu.speech.EventManagerFactory.access$402(r2)
                                Lc:
                                    java.lang.String r0 = "wp.exit"
                                    boolean r0 = r0.equals(r10)
                                    if (r0 == 0) goto L80
                                    r3 = 0
                                    org.json.JSONObject r1 = new org.json.JSONObject     // Catch: org.json.JSONException -> L60
                                    if (r11 != 0) goto L1b
                                    java.lang.String r11 = "{}"
                                L1b:
                                    r1.<init>(r11)     // Catch: org.json.JSONException -> L60
                                    java.lang.String r0 = "_free"
                                    boolean r0 = r1.optBoolean(r0)     // Catch: org.json.JSONException -> L98
                                    java.lang.String r2 = "_free"
                                    r1.remove(r2)     // Catch: org.json.JSONException -> L9d
                                L29:
                                    java.lang.String r3 = r1.toString()
                                    if (r0 == 0) goto L69
                                    android.os.Handler r7 = new android.os.Handler
                                    android.os.Looper r0 = android.os.Looper.getMainLooper()
                                    r7.<init>(r0)
                                    com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$1 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$1
                                    r1 = r9
                                    r2 = r10
                                    r4 = r12
                                    r5 = r13
                                    r6 = r14
                                    r0.<init>()
                                    r2 = 200(0xc8, double:9.9E-322)
                                    r7.postDelayed(r0, r2)
                                L47:
                                    return
                                L48:
                                    java.lang.String r0 = "wp.exit"
                                    boolean r0 = r0.equals(r10)
                                    if (r0 == 0) goto L54
                                    com.baidu.speech.EventManagerFactory.access$502(r2)
                                    goto Lc
                                L54:
                                    java.lang.String r0 = "asr.unloaded"
                                    boolean r0 = r0.equals(r10)
                                    if (r0 == 0) goto Lc
                                    com.baidu.speech.EventManagerFactory.access$202(r2)
                                    goto Lc
                                L60:
                                    r0 = move-exception
                                    r1 = r3
                                    r8 = r2
                                    r2 = r0
                                    r0 = r8
                                L65:
                                    r2.printStackTrace()
                                    goto L29
                                L69:
                                    android.os.Handler r7 = new android.os.Handler
                                    android.os.Looper r0 = android.os.Looper.getMainLooper()
                                    r7.<init>(r0)
                                    com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$2 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$2
                                    r1 = r9
                                    r2 = r10
                                    r4 = r12
                                    r5 = r13
                                    r6 = r14
                                    r0.<init>()
                                    r7.post(r0)
                                    goto L47
                                L80:
                                    android.os.Handler r7 = new android.os.Handler
                                    android.os.Looper r0 = android.os.Looper.getMainLooper()
                                    r7.<init>(r0)
                                    com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$3 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$3
                                    r1 = r9
                                    r2 = r10
                                    r3 = r11
                                    r4 = r12
                                    r5 = r13
                                    r6 = r14
                                    r0.<init>()
                                    r7.post(r0)
                                    goto L47
                                L98:
                                    r0 = move-exception
                                    r8 = r0
                                    r0 = r2
                                    r2 = r8
                                    goto L65
                                L9d:
                                    r2 = move-exception
                                    goto L65
                                */
                                throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.EventManagerFactory.EventManagerRemote2Local.AnonymousClass2.AnonymousClass1.onEvent(java.lang.String, java.lang.String, byte[], int, int):void");
                            }
                        });
                        EventManagerRemote2Local.this.remoteEM.send(this.val$cmd, str, this.val$data, this.val$offset, this.val$length);
                        return;
                    }
                    EventManagerRemote2Local.this.remoteEM.registerListener(new EventListener.Stub() { // from class: com.baidu.speech.EventManagerFactory.EventManagerRemote2Local.2.1
                        @Override // com.baidu.speech.aidl.EventListener
                        public void onEvent(String str3, String str4, byte[] bArr, int i, int i2) throws RemoteException {
                            /*
                                this = this;
                                r2 = 0
                                java.lang.String r0 = "asr.exit"
                                boolean r0 = r0.equals(r10)
                                if (r0 == 0) goto L48
                                com.baidu.speech.EventManagerFactory.access$402(r2)
                            Lc:
                                java.lang.String r0 = "wp.exit"
                                boolean r0 = r0.equals(r10)
                                if (r0 == 0) goto L80
                                r3 = 0
                                org.json.JSONObject r1 = new org.json.JSONObject     // Catch: org.json.JSONException -> L60
                                if (r11 != 0) goto L1b
                                java.lang.String r11 = "{}"
                            L1b:
                                r1.<init>(r11)     // Catch: org.json.JSONException -> L60
                                java.lang.String r0 = "_free"
                                boolean r0 = r1.optBoolean(r0)     // Catch: org.json.JSONException -> L98
                                java.lang.String r2 = "_free"
                                r1.remove(r2)     // Catch: org.json.JSONException -> L9d
                            L29:
                                java.lang.String r3 = r1.toString()
                                if (r0 == 0) goto L69
                                android.os.Handler r7 = new android.os.Handler
                                android.os.Looper r0 = android.os.Looper.getMainLooper()
                                r7.<init>(r0)
                                com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$1 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$1
                                r1 = r9
                                r2 = r10
                                r4 = r12
                                r5 = r13
                                r6 = r14
                                r0.<init>()
                                r2 = 200(0xc8, double:9.9E-322)
                                r7.postDelayed(r0, r2)
                            L47:
                                return
                            L48:
                                java.lang.String r0 = "wp.exit"
                                boolean r0 = r0.equals(r10)
                                if (r0 == 0) goto L54
                                com.baidu.speech.EventManagerFactory.access$502(r2)
                                goto Lc
                            L54:
                                java.lang.String r0 = "asr.unloaded"
                                boolean r0 = r0.equals(r10)
                                if (r0 == 0) goto Lc
                                com.baidu.speech.EventManagerFactory.access$202(r2)
                                goto Lc
                            L60:
                                r0 = move-exception
                                r1 = r3
                                r8 = r2
                                r2 = r0
                                r0 = r8
                            L65:
                                r2.printStackTrace()
                                goto L29
                            L69:
                                android.os.Handler r7 = new android.os.Handler
                                android.os.Looper r0 = android.os.Looper.getMainLooper()
                                r7.<init>(r0)
                                com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$2 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$2
                                r1 = r9
                                r2 = r10
                                r4 = r12
                                r5 = r13
                                r6 = r14
                                r0.<init>()
                                r7.post(r0)
                                goto L47
                            L80:
                                android.os.Handler r7 = new android.os.Handler
                                android.os.Looper r0 = android.os.Looper.getMainLooper()
                                r7.<init>(r0)
                                com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$3 r0 = new com.baidu.speech.EventManagerFactory$EventManagerRemote2Local$2$1$3
                                r1 = r9
                                r2 = r10
                                r3 = r11
                                r4 = r12
                                r5 = r13
                                r6 = r14
                                r0.<init>()
                                r7.post(r0)
                                goto L47
                            L98:
                                r0 = move-exception
                                r8 = r0
                                r0 = r2
                                r2 = r8
                                goto L65
                            L9d:
                                r2 = move-exception
                                goto L65
                            */
                            throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.EventManagerFactory.EventManagerRemote2Local.AnonymousClass2.AnonymousClass1.onEvent(java.lang.String, java.lang.String, byte[], int, int):void");
                        }
                    });
                    EventManagerRemote2Local.this.remoteEM.send(this.val$cmd, str, this.val$data, this.val$offset, this.val$length);
                    return;
                } catch (RemoteException e3) {
                    e3.printStackTrace();
                    EventManagerRemote2Local.this.remoteEM = null;
                    return;
                }
                str = str2;
            }
        }

        EventManagerRemote2Local(Context context, String str) {
            this.context = context;
            this.name = str;
        }

        @Override // com.baidu.speech.EventManager
        public void registerListener(EventListener eventListener) {
            this.mLis = eventListener;
        }

        @Override // com.baidu.speech.EventManager
        public void send(String str, String str2, byte[] bArr, int i, int i2) {
            this.context.bindService(new Intent(this.context, (Class<?>) EventRecognitionService.class), this.conn, 1);
            byte[] bArr2 = bArr == null ? new byte[0] : bArr;
            if (SpeechConstant.ASR_START.equals(str) || SpeechConstant.ASR_KWS_LOAD_ENGINE.equals(str)) {
                boolean unused = EventManagerFactory.asrUsing = true;
            } else if (SpeechConstant.WAKEUP_START.equals(str)) {
                boolean unused2 = EventManagerFactory.wpUsing = true;
            } else if (SpeechConstant.ASR_KWS_LOAD_ENGINE.equals(str)) {
                boolean unused3 = EventManagerFactory.kwsLoaded = true;
            }
            new Handler(Looper.getMainLooper()).postDelayed(new AnonymousClass2(str2, str, bArr2, i, i2), 0L);
        }

        public void setRemoteEM(com.baidu.speech.aidl.EventManager eventManager) {
            this.remoteEM = eventManager;
        }

        @Override // com.baidu.speech.EventManager
        public void unregisterListener(EventListener eventListener) {
            this.mLis = null;
        }
    }

    public static final EventManager create(Context context, String str) {
        return create(context, str, false);
    }

    public static final EventManager create(Context context, String str, boolean z) {
        if (context == null || str == null || str.equals("")) {
            return null;
        }
        Context applicationContext = context.getApplicationContext();
        if (z) {
            return new EventManagerRemote2Local(applicationContext, str);
        }
        if (str.equals("asr")) {
            return new EventManagerAsr(applicationContext);
        }
        if (str.equals("wp")) {
            return new EventManagerWp(applicationContext);
        }
        if (str.equals("slot")) {
            return new EventManagerSlot(applicationContext);
        }
        return null;
    }
}
