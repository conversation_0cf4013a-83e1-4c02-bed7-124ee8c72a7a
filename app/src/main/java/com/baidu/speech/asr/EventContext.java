package com.baidu.speech.asr;

import android.content.Context;
import android.content.ContextWrapper;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.http.client.methods.HttpPost;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public final class EventContext extends ContextWrapper {
    private static final String TAG = "EventContext";
    private static final Logger logger = Logger.getLogger(TAG);

    private static class SmartLogger {
        private static final String TAG = "baidu_speech";
        private static final Logger logger = Logger.getLogger(TAG);

        static {
            logger.setLevel(Level.OFF);
        }

        private SmartLogger() {
        }

        public static <T> T wrap(Object obj, String[] strArr) {
            return (T) wrap(TAG, obj, strArr);
        }

        public static <T> T wrap(String str, final Object obj, final String... strArr) {
            final boolean isLoggable = Log.isLoggable(TAG, 3);
            if (Log.isLoggable(TAG, 3)) {
                logger.setLevel(Level.ALL);
            }
            ArrayList arrayList = new ArrayList();
            Class<?> cls = obj.getClass();
            do {
                arrayList.addAll(Arrays.asList(cls.getInterfaces()));
                cls = cls.getSuperclass();
            } while (cls != Object.class);
            return (T) Proxy.newProxyInstance(obj.getClass().getClassLoader(), (Class[]) arrayList.toArray(new Class[0]), new InvocationHandler() { // from class: com.baidu.speech.asr.EventContext.SmartLogger.1
                @Override // java.lang.reflect.InvocationHandler
                public Object invoke(Object obj2, Method method, Object[] objArr) throws Throwable {
                    Object invoke = method.invoke(obj, objArr);
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append(((strArr == null || strArr.length <= 0) ? obj.getClass().getName() : strArr[0]) + "@" + Integer.toHexString(obj.hashCode()));
                    stringBuffer.append("." + method.getName() + "(");
                    if (objArr != null) {
                        for (Object obj3 : objArr) {
                            String str2 = obj3 + "";
                            if (!isLoggable) {
                                String replaceAll = str2.replaceAll("[\r\n]]", "");
                                str2 = replaceAll.substring(0, Math.min(50, replaceAll.length()));
                            }
                            stringBuffer.append(str2 + ", ");
                        }
                    }
                    stringBuffer.append(") : " + invoke);
                    SmartLogger.logger.info(stringBuffer.toString());
                    return invoke;
                }
            });
        }
    }

    public EventContext(Context context) {
        super(context);
    }

    public static short[] byteToShortArray(byte[] bArr, int i, int i2) {
        if (i2 == 0) {
            return new short[0];
        }
        ByteBuffer allocate = ByteBuffer.allocate(i2);
        allocate.order(ByteOrder.nativeOrder());
        allocate.put(bArr, i, i2);
        allocate.clear();
        short[] sArr = new short[i2 / 2];
        allocate.asShortBuffer().get(sArr);
        return sArr;
    }

    public static long computePower(short[] sArr, int i) {
        if (sArr == null) {
            return 0L;
        }
        System.currentTimeMillis();
        int min = Math.min(i / 2, 512);
        if (min <= 0) {
            return 0L;
        }
        long j = 0;
        for (int i2 = 0; i2 < min; i2++) {
            j += sArr[i2 * 2] * sArr[i2 * 2];
        }
        return (long) Math.sqrt(j / min);
    }

    public long computePower(byte[] bArr, int i) {
        short[] sArr = new short[i / 2];
        for (int i2 = 0; i2 < sArr.length; i2++) {
            sArr[i2] = (short) ((bArr[(i2 * 2) + 1] << 8) | (bArr[(i2 * 2) + 0] & 255));
        }
        return computePower(sArr, sArr.length);
    }

    public SharedPreferences getSdkSharedPreferences() {
        return super.getSharedPreferences("bds", 0);
    }

    public String httpRequest(String str, Map<String, String> map, byte[] bArr, boolean z) throws Exception {
        HttpURLConnection httpURLConnection = null;
        try {
            try {
                if (Log.isLoggable(TAG, 3) || logger.isLoggable(Level.ALL)) {
                    logger.info("cur time: " + (System.currentTimeMillis() % 1000000) + ", http req: " + str);
                }
                HttpURLConnection httpURLConnection2 = (HttpURLConnection) new URL(str).openConnection();
                try {
                    httpURLConnection2.setConnectTimeout(8000);
                    httpURLConnection2.setReadTimeout(8000);
                    httpURLConnection2.setInstanceFollowRedirects(false);
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        httpURLConnection2.setRequestProperty(entry.getKey(), entry.getValue());
                    }
                    if (bArr != null || z) {
                        httpURLConnection2.setRequestMethod(HttpPost.METHOD_NAME);
                    }
                    httpURLConnection2.connect();
                    if (bArr != null) {
                        httpURLConnection2.getOutputStream().write(bArr);
                    }
                    String next = new Scanner(httpURLConnection2.getInputStream()).useDelimiter("\\A").next();
                    if (Log.isLoggable(TAG, 3) || logger.isLoggable(Level.ALL)) {
                        logger.info("http res: " + next);
                    }
                    if (httpURLConnection2 != null) {
                        httpURLConnection2.disconnect();
                    }
                    return next;
                } catch (Exception e) {
                    e = e;
                    if (Log.isLoggable(TAG, 3) || logger.isLoggable(Level.ALL)) {
                        logger.log(Level.WARNING, "", (Throwable) e);
                    }
                    throw e;
                } catch (Throwable th) {
                    httpURLConnection = httpURLConnection2;
                    th = th;
                    if (httpURLConnection != null) {
                        httpURLConnection.disconnect();
                    }
                    throw th;
                }
            } catch (Exception e2) {
                e = e2;
            }
        } catch (Throwable th2) {
            th = th2;
        }
    }

    public String join(List<String> list, String str) {
        StringBuilder sb = new StringBuilder();
        boolean z = true;
        for (String str2 : list) {
            if (z) {
                z = false;
            } else {
                sb.append(str);
            }
            sb.append(str2);
        }
        return sb.toString();
    }

    public byte[] loadBytesFromUri(String str) throws IOException {
        InputStream inputStream = null;
        try {
            if (!str.contains("://")) {
                str = "file://" + str;
            }
            Matcher matcher = Pattern.compile("(.*?)://(.*)").matcher(str);
            if (matcher.find()) {
                String group = matcher.group(1);
                String group2 = matcher.group(2);
                if (group.equalsIgnoreCase("file")) {
                    inputStream = new FileInputStream(group2);
                } else if (group.equalsIgnoreCase("asset") || group.equalsIgnoreCase("assets")) {
                    inputStream = getClass().getResourceAsStream("/assets" + (group2.startsWith("/") ? "" : "/") + group2);
                } else if (group.equalsIgnoreCase("res")) {
                    inputStream = getClass().getResourceAsStream(group2);
                }
            }
            if (inputStream == null) {
                throw new IOException("bad data source");
            }
            byte[] bArr = new byte[1024];
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            while (true) {
                int read = inputStream.read(bArr, 0, bArr.length);
                if (-1 == read) {
                    break;
                }
                byteArrayOutputStream.write(bArr, 0, read);
            }
            return byteArrayOutputStream.toByteArray();
        } finally {
            if (0 != 0) {
                inputStream.close();
            }
        }
    }

    public JSONObject loadJsonFromUri(String str) {
        return loadJsonFromUri(str, false, false);
    }

    public JSONObject loadJsonFromUri(String str, boolean z, boolean z2) {
        try {
            return loadJsonFromUriOrThrow(str, z, z2);
        } catch (Exception e) {
            return null;
        }
    }

    public JSONObject loadJsonFromUriOrThrow(String str) throws IOException, JSONException {
        return loadJsonFromUriOrThrow(str, false, false);
    }

    public JSONObject loadJsonFromUriOrThrow(String str, boolean z, boolean z2) throws IOException, JSONException {
        String loadStringFromUri = loadStringFromUri(str, z);
        if (z2) {
            loadStringFromUri = URLDecoder.decode(loadStringFromUri, "UTF-8");
        }
        return new JSONObject(loadStringFromUri);
    }

    public String loadStringFromUri(String str) throws IOException {
        return loadStringFromUri(str, false);
    }

    public String loadStringFromUri(String str, boolean z) throws IOException {
        byte[] loadBytesFromUri = loadBytesFromUri(str);
        return z ? new String(Base64.decode(loadBytesFromUri, 0), "UTF-8") : new String(loadBytesFromUri, "UTF-8");
    }

    public <T> T loggerIt(Object obj, String... strArr) {
        return (T) SmartLogger.wrap(obj, strArr);
    }

    public Object searchItemFromJson(JSONObject jSONObject, String str) throws JSONException {
        Object searchItemFromJson;
        if (jSONObject != null && !TextUtils.isEmpty(str)) {
            Iterator<String> keys = jSONObject.keys();
            while (keys.hasNext()) {
                String next = keys.next();
                if (next.equals(str)) {
                    return jSONObject.get(str);
                }
                Object obj = jSONObject.get(next);
                if ((obj instanceof JSONObject) && (searchItemFromJson = searchItemFromJson((JSONObject) obj, str)) != null) {
                    return searchItemFromJson;
                }
            }
            return null;
        }
        return null;
    }
}
