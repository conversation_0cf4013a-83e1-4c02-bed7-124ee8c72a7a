package com.baidu.speech.asr;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import com.baidu.speech.EventListener;
import com.baidu.speech.core.BDSErrorDescription;
import com.baidu.speech.core.BDSMessage;
import com.baidu.speech.core.BDSParamBase;
import com.baidu.speech.core.BDSSDKLoader;
import com.baidu.speech.utils.AsrError;
import com.baidu.speech.utils.Policy;
import java.io.File;
import java.util.HashMap;
import java.util.Vector;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class WakeUpControl implements BDSSDKLoader.BDSCoreEventListener {
    private static final int EWakeupEngineWorkStatusError = 6;
    private static final int EWakeupEngineWorkStatusLoaded = 3;
    private static final int EWakeupEngineWorkStatusNewData = 7;
    private static final int EWakeupEngineWorkStatusReadyed = 1;
    private static final int EWakeupEngineWorkStatusStarted = 0;
    private static final int EWakeupEngineWorkStatusStopped = 2;
    private static final int EWakeupEngineWorkStatusTriggered = 5;
    private static final int EWakeupEngineWorkStatusUnLoaded = 4;
    private Context context;
    private boolean mFeedBackAudio;
    private EventListener mListener;
    private JSONObject mParams;
    private BDSSDKLoader.BDSSDKInterface m_Wakeupcore;
    private static String WAK_CMD_CONFIG = "wak.config";
    private static String WAK_CMD_START = "wak.start";
    private static String WAK_CMD_DATA = "wak.data";
    private static String WAK_CMD_STOP = "wak.stop";
    private static String WAK_CMD_LOAD_ENGINE = "wak.load";
    private static String WAK_CMD_UNLOAD_ENGINE = "wak.unload";
    private static String ASR_PARAM_KEY_PLATFORM = "asr_param_key_platform.string";
    private static String ASR_PARAM_KEY_SDK_VERSION = "asr_param_key_sdk_version.string";
    private static String ASR_PARAM_KEY_OFFLINE_APP_CODE = "offline_param_key_app_code.string";
    private static String MIC_PARAM_KEY_AUDIO_FILE_PATH = "mic_audio_file_path.string";
    private static String OFFLINE_PARAM_KEY_LICENSE_FILE_PATH = "offline_param_key_license_filepath.string";
    private static String WP_PARAM_KEY_WAKEUP_WORDS = "wakeup_param_key_words.vector<string>";
    private static String WP_PARAM_KEY_WAKEUP_DAT_FILE_PATH = "wakeup_param_key_dat_filepath.string";
    private static String WP_PARAM_KEY_WAKEUP_MODE = "wakeup_param_key_mode.int";
    private static String WP_PARAM_KEY_WAKEUP_WORDS_FILE_PATH = "wakeup_param_key_words_filepath.string";
    private static String COMMON_PARAM_KEY_DEBUG_LOG_LEVEL = "common_param_key_debug_log_level.int";
    private static String WP_PARAM_KEY_WAKEUP_ACCEPT_AUDIO_DATA = "wakeup_param_key_accept_audio_data.bool";
    private static String MIC_PARAM_KEY_SOCKET_PORT = "mic_param_key_socket_port.int";
    private String outFile = null;
    private boolean mIsWorking = false;

    enum DebugLogLevel {
        EVRDebugLogLevelOff,
        EVRDebugLogLevelFatal,
        EVRDebugLogLevelError,
        EVRDebugLogLevelWarning,
        EVRDebugLogLevelInformation,
        EVRDebugLogLevelDebug,
        EVRDebugLogLevelTrace
    }

    public WakeUpControl(Context context) throws Exception {
        this.context = context;
        try {
            BDSSDKLoader.loadLibraries();
            try {
                this.m_Wakeupcore = BDSSDKLoader.getSDKObjectForSDKType("WakeupCore", context);
                if (this.m_Wakeupcore == null) {
                    throw new Exception("ASR core support is not linked in package");
                }
                if (!this.m_Wakeupcore.instanceInitialized()) {
                    throw new Exception("Failed initialize ASR Core native layer");
                }
                this.m_Wakeupcore.setListener(this);
            } catch (Throwable th) {
                th.printStackTrace();
                throw new Exception("Can't found ASR Core native method");
            }
        } catch (Throwable th2) {
            throw new Exception(generateErrorResult(AsrError.ERROR_CLIENT_UNABLE_LOAD_LIBRARY));
        }
    }

    private void asrCallBack(BDSMessage bDSMessage, EventListener eventListener) {
        if (bDSMessage.m_messageName.equals(SpeechConstant.WAKEUP_CALLBACK_NAME)) {
            switch (((BDSParamBase.BDSIntParam) bDSMessage.m_messageParams.get(SpeechConstant.CALLBACK_WAK_STATUS)).iValue) {
                case 0:
                    eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_STARTED, null, null, 0, 0);
                    break;
                case 1:
                    eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_READY, null, null, 0, 0);
                    break;
                case 2:
                    this.mIsWorking = false;
                    eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_STOPED, null, null, 0, 0);
                    break;
                case 5:
                    String str = (String) ((BDSParamBase.BDSObjectParam) bDSMessage.m_messageParams.get(SpeechConstant.CALLBACK_WAK_RESULT)).iValue;
                    HashMap hashMap = new HashMap();
                    hashMap.put("word", str);
                    hashMap.put("errorCode", 0);
                    hashMap.put("errorDesc", "wakup success");
                    eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_SUCCESS, new JSONObject(hashMap).toString(), null, 0, 0);
                    break;
                case 6:
                    this.mIsWorking = false;
                    int i = ((BDSParamBase.BDSIntParam) bDSMessage.m_messageParams.get(SpeechConstant.CALLBACK_ERROR_DOMAIN)).iValue;
                    String str2 = "";
                    try {
                        str2 = generateErrorResult(i, ((BDSParamBase.BDSIntParam) bDSMessage.m_messageParams.get(SpeechConstant.CALLBACK_ERROR_CODE)).iValue);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_ERROR, str2, null, 0, 0);
                    break;
                case 7:
                    byte[] bArr = bDSMessage.m_messageData;
                    if (this.mFeedBackAudio && bArr != null) {
                        eventListener.onEvent(SpeechConstant.CALLBACK_EVENT_WAKEUP_AUDIO, null, bArr, 0, bArr.length);
                    }
                    saveOutFile(bArr);
                    break;
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:36:0x003e A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void clearOutFile() {
        /*
            r3 = this;
            java.lang.String r0 = r3.outFile
            if (r0 == 0) goto L24
            java.lang.String r0 = r3.outFile
            java.lang.String r1 = ""
            boolean r0 = r0.equals(r1)
            if (r0 != 0) goto L24
            r2 = 0
            java.io.FileOutputStream r1 = new java.io.FileOutputStream     // Catch: java.io.IOException -> L2a java.lang.Throwable -> L3a
            java.lang.String r0 = r3.outFile     // Catch: java.io.IOException -> L2a java.lang.Throwable -> L3a
            r1.<init>(r0)     // Catch: java.io.IOException -> L2a java.lang.Throwable -> L3a
            java.lang.String r0 = ""
            byte[] r0 = r0.getBytes()     // Catch: java.lang.Throwable -> L47 java.io.IOException -> L49
            r1.write(r0)     // Catch: java.lang.Throwable -> L47 java.io.IOException -> L49
            if (r1 == 0) goto L24
            r1.close()     // Catch: java.io.IOException -> L25
        L24:
            return
        L25:
            r0 = move-exception
            r0.printStackTrace()
            goto L24
        L2a:
            r0 = move-exception
            r1 = r2
        L2c:
            r0.printStackTrace()     // Catch: java.lang.Throwable -> L47
            if (r1 == 0) goto L24
            r1.close()     // Catch: java.io.IOException -> L35
            goto L24
        L35:
            r0 = move-exception
            r0.printStackTrace()
            goto L24
        L3a:
            r0 = move-exception
            r1 = r2
        L3c:
            if (r1 == 0) goto L41
            r1.close()     // Catch: java.io.IOException -> L42
        L41:
            throw r0
        L42:
            r1 = move-exception
            r1.printStackTrace()
            goto L41
        L47:
            r0 = move-exception
            goto L3c
        L49:
            r0 = move-exception
            goto L2c
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.asr.WakeUpControl.clearOutFile():void");
    }

    private String generateErrorResult(int i) {
        String descFromCode = AsrError.getDescFromCode(i);
        int i2 = i / 1000;
        JSONObject jSONObject = new JSONObject();
        try {
            jSONObject.put("error", i2);
            jSONObject.put("desc", descFromCode);
            jSONObject.put("sub_error", i);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jSONObject.toString();
    }

    private String generateErrorResult(int i, int i2) {
        if (AsrError.getDescFromCode(i2) == null) {
            if (i == 38) {
                if (1 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_ENGINE_EXCEPTION;
                } else if (2 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_NO_LICENSE;
                } else if (3 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_INVALID_LICENSE;
                } else if (4 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_EXCEPTION;
                } else if (5 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_MODEL_EXCEPTION;
                } else if (6 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_ENGINE_INITIAL_FAIL;
                } else if (7 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_MEM_ALLOC_FAIL;
                } else if (8 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_ENGINE_RESET_FAIL;
                } else if (9 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_ENGINE_FREE_FAIL;
                } else if (10 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_ENGINE_NOT_SUPPORT;
                } else if (11 == i2) {
                    i2 = AsrError.ERROR_WAKEUP_RECOGNIZE_FAIL;
                }
            }
            i2 = -1;
        }
        return generateErrorResult(i2);
    }

    private File getDiskCacheDir(Context context) {
        return context.getCacheDir();
    }

    private BDSErrorDescription initWp(BDSErrorDescription bDSErrorDescription, JSONObject jSONObject) {
        String str = null;
        try {
            ApplicationInfo applicationInfo = this.context.getPackageManager().getApplicationInfo(this.context.getPackageName(), 128);
            str = applicationInfo.metaData == null ? null : applicationInfo.metaData.getInt("com.baidu.speech.APP_ID") + "";
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        String optString = jSONObject.optString(SpeechConstant.APP_ID, str);
        JSONArray optJSONArray = jSONObject.optJSONArray(SpeechConstant.WP_WORDS);
        Vector vector = new Vector();
        if (optJSONArray != null) {
            for (int i = 0; i < optJSONArray.length(); i++) {
                try {
                    vector.add(optJSONArray.getString(i));
                } catch (JSONException e2) {
                    e2.printStackTrace();
                }
            }
        }
        String optString2 = jSONObject.optString(SpeechConstant.WP_DAT_FILEPATH, jSONObject.optString("wp.res-file", jSONObject.optString("res-file", String.format("%s/%s", this.context.getApplicationInfo().nativeLibraryDir, "libbd_easr_s1_merge_normal_20151216.dat.so"))));
        String optString3 = jSONObject.optString("wp.kws-file", jSONObject.optString(SpeechConstant.WP_WORDS_FILE));
        String optString4 = jSONObject.optString(SpeechConstant.IN_FILE);
        String optString5 = jSONObject.optString("decoder-offline.license-file-path", jSONObject.optString("license-file-path", jSONObject.optString("license")));
        int optInt = jSONObject.optInt(SpeechConstant.LOG_LEVEL, -1);
        this.mFeedBackAudio = jSONObject.optBoolean(SpeechConstant.ACCEPT_AUDIO_DATA, false);
        this.outFile = jSONObject.optString(SpeechConstant.OUT_FILE);
        BDSMessage bDSMessage = new BDSMessage();
        bDSMessage.m_messageName = WAK_CMD_CONFIG;
        bDSMessage.m_messageParams = new HashMap<>();
        bDSMessage.m_messageParams.put(ASR_PARAM_KEY_OFFLINE_APP_CODE, BDSParamBase.objectParam(optString, "java.lang.String"));
        bDSMessage.m_messageParams.put(WP_PARAM_KEY_WAKEUP_DAT_FILE_PATH, BDSParamBase.objectParam(loadSourceFromUri(optString2), "java.lang.String"));
        bDSMessage.m_messageParams.put(WP_PARAM_KEY_WAKEUP_WORDS, BDSParamBase.objectParam(vector, "java.util.Vector;"));
        bDSMessage.m_messageParams.put(WP_PARAM_KEY_WAKEUP_WORDS_FILE_PATH, BDSParamBase.objectParam(loadSourceFromUri(optString3), "java.lang.String"));
        bDSMessage.m_messageParams.put(OFFLINE_PARAM_KEY_LICENSE_FILE_PATH, BDSParamBase.objectParam(loadSourceFromUri(optString5), "java.lang.String"));
        bDSMessage.m_messageParams.put(MIC_PARAM_KEY_AUDIO_FILE_PATH, BDSParamBase.objectParam(optString4, "java.lang.String"));
        bDSMessage.m_messageParams.put(MIC_PARAM_KEY_SOCKET_PORT, BDSParamBase.intParam(jSONObject.optInt("audio.socketport")));
        if (optInt != -1) {
            bDSMessage.m_messageParams.put(COMMON_PARAM_KEY_DEBUG_LOG_LEVEL, BDSParamBase.intParam(optInt));
        }
        bDSMessage.m_messageParams.put(WP_PARAM_KEY_WAKEUP_ACCEPT_AUDIO_DATA, BDSParamBase.boolParam(this.mFeedBackAudio));
        boolean optBoolean = jSONObject.optBoolean(SpeechConstant.ACCEPT_AUDIO_VOLUME, true);
        if (!optBoolean) {
            bDSMessage.m_messageParams.put("mic_accept_audio_volume.bool", BDSParamBase.boolParam(optBoolean));
        }
        int optInt2 = jSONObject.optInt("wp.mode", 0);
        if (optInt2 > 0) {
            bDSMessage.m_messageParams.put(WP_PARAM_KEY_WAKEUP_MODE, BDSParamBase.intParam(optInt2));
        }
        bDSMessage.m_messageParams.put(ASR_PARAM_KEY_PLATFORM, BDSParamBase.objectParam("Android", "java.lang.String"));
        bDSMessage.m_messageParams.put(ASR_PARAM_KEY_SDK_VERSION, BDSParamBase.objectParam("C++ ASR core", "java.lang.String"));
        try {
            int postMessage = this.m_Wakeupcore.postMessage(bDSMessage);
            if (postMessage == 0) {
                return bDSErrorDescription;
            }
            BDSErrorDescription bDSErrorDescription2 = new BDSErrorDescription();
            bDSErrorDescription2.errorCode = -2;
            bDSErrorDescription2.errorDomain = 1;
            bDSErrorDescription2.errorDescription = "JNI: readyParamsWpStart Call to Native layer returned error! err( " + postMessage + " )";
            return bDSErrorDescription2;
        } catch (Throwable th) {
            th.printStackTrace();
            BDSErrorDescription bDSErrorDescription3 = new BDSErrorDescription();
            bDSErrorDescription3.errorCode = -2;
            bDSErrorDescription3.errorDomain = 1;
            bDSErrorDescription3.errorDescription = "JNI: readyParamsWpStart Call to Native layer returned error! err";
            return bDSErrorDescription3;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:65:0x012c A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:70:0x0127 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private java.lang.String loadSourceFromUri(java.lang.String r8) {
        /*
            Method dump skipped, instructions count: 328
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.asr.WakeUpControl.loadSourceFromUri(java.lang.String):java.lang.String");
    }

    private BDSErrorDescription postEvent(BDSErrorDescription bDSErrorDescription, String str) {
        BDSMessage bDSMessage = new BDSMessage();
        if (str.contains("wp")) {
            bDSMessage.m_messageName = str.replace("wp", "wak");
        } else {
            bDSMessage.m_messageName = str;
        }
        bDSMessage.m_messageParams = new HashMap<>();
        this.mParams.optString(SpeechConstant.APP_NAME, Policy.app(this.context));
        bDSMessage.m_messageParams.put(ASR_PARAM_KEY_PLATFORM, BDSParamBase.objectParam("Android", "java.lang.String"));
        bDSMessage.m_messageParams.put(ASR_PARAM_KEY_SDK_VERSION, BDSParamBase.objectParam("C++ ASR core", "java.lang.String"));
        try {
            if (this.m_Wakeupcore.postMessage(bDSMessage) == 0) {
                return bDSErrorDescription;
            }
            BDSErrorDescription bDSErrorDescription2 = new BDSErrorDescription();
            bDSErrorDescription2.errorCode = -2;
            bDSErrorDescription2.errorDomain = 1;
            bDSErrorDescription2.errorDescription = "JNI: readyParamsAsrStart Call to Native layer returned error! err";
            return bDSErrorDescription2;
        } catch (Throwable th) {
            th.printStackTrace();
            BDSErrorDescription bDSErrorDescription3 = new BDSErrorDescription();
            bDSErrorDescription3.errorCode = -2;
            bDSErrorDescription3.errorDomain = 1;
            bDSErrorDescription3.errorDescription = "JNI: readyParamsWpStart Call to Native layer returned error! err";
            return bDSErrorDescription3;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:35:0x003a A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v4, types: [java.io.OutputStream] */
    /* JADX WARN: Type inference failed for: r1v6, types: [java.io.FileOutputStream, java.io.OutputStream] */
    /* JADX WARN: Type inference failed for: r2v0 */
    /* JADX WARN: Type inference failed for: r2v1, types: [java.io.OutputStream] */
    /* JADX WARN: Type inference failed for: r2v2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void saveOutFile(byte[] r5) {
        /*
            r4 = this;
            java.lang.String r0 = r4.outFile
            if (r0 == 0) goto L21
            java.lang.String r0 = r4.outFile
            java.lang.String r1 = ""
            boolean r0 = r0.equals(r1)
            if (r0 != 0) goto L21
            if (r5 == 0) goto L21
            r2 = 0
            java.io.FileOutputStream r1 = new java.io.FileOutputStream     // Catch: java.io.IOException -> L27 java.lang.Throwable -> L37
            java.lang.String r0 = r4.outFile     // Catch: java.io.IOException -> L27 java.lang.Throwable -> L37
            r3 = 1
            r1.<init>(r0, r3)     // Catch: java.io.IOException -> L27 java.lang.Throwable -> L37
            r1.write(r5)     // Catch: java.lang.Throwable -> L43 java.io.IOException -> L46
            if (r1 == 0) goto L21
            r1.close()     // Catch: java.io.IOException -> L22
        L21:
            return
        L22:
            r0 = move-exception
            r0.printStackTrace()
            goto L21
        L27:
            r0 = move-exception
            r1 = r2
        L29:
            r0.printStackTrace()     // Catch: java.lang.Throwable -> L43
            if (r1 == 0) goto L21
            r1.close()     // Catch: java.io.IOException -> L32
            goto L21
        L32:
            r0 = move-exception
            r0.printStackTrace()
            goto L21
        L37:
            r0 = move-exception
        L38:
            if (r2 == 0) goto L3d
            r2.close()     // Catch: java.io.IOException -> L3e
        L3d:
            throw r0
        L3e:
            r1 = move-exception
            r1.printStackTrace()
            goto L3d
        L43:
            r0 = move-exception
            r2 = r1
            goto L38
        L46:
            r0 = move-exception
            goto L29
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.asr.WakeUpControl.saveOutFile(byte[]):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0044  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x00a4  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public com.baidu.speech.core.BDSErrorDescription postEvent(java.lang.String r6, java.lang.String r7) {
        /*
            r5 = this;
            r3 = -1
            r2 = 1
            r0 = 0
            com.baidu.speech.core.BDSSDKLoader$BDSSDKInterface r1 = r5.m_Wakeupcore     // Catch: java.lang.Throwable -> L19
            boolean r1 = r1.instanceInitialized()     // Catch: java.lang.Throwable -> L19
            if (r1 != 0) goto L2b
            com.baidu.speech.core.BDSErrorDescription r0 = new com.baidu.speech.core.BDSErrorDescription
            r0.<init>()
            r0.errorCode = r3
            r0.errorDomain = r2
            java.lang.String r1 = "JNI: ASR Core native layer is not initialized!"
            r0.errorDescription = r1
        L18:
            return r0
        L19:
            r0 = move-exception
            r0.printStackTrace()
            com.baidu.speech.core.BDSErrorDescription r0 = new com.baidu.speech.core.BDSErrorDescription
            r0.<init>()
            r0.errorCode = r3
            r0.errorDomain = r2
            java.lang.String r1 = "JNI: ASR Core native layer is not initialized!"
            r0.errorDescription = r1
            goto L18
        L2b:
            if (r7 == 0) goto L35
            java.lang.String r1 = ""
            boolean r1 = r7.equals(r1)     // Catch: org.json.JSONException -> L93
            if (r1 == 0) goto L8b
        L35:
            org.json.JSONObject r1 = new org.json.JSONObject     // Catch: org.json.JSONException -> L93
            r1.<init>()     // Catch: org.json.JSONException -> L93
            r5.mParams = r1     // Catch: org.json.JSONException -> L93
        L3c:
            java.lang.String r1 = "wp.start"
            boolean r1 = r6.equals(r1)
            if (r1 == 0) goto La4
            boolean r1 = r5.mIsWorking
            if (r1 != 0) goto L18
            r5.mIsWorking = r2
            org.json.JSONObject r1 = r5.mParams     // Catch: java.lang.Exception -> L9f
            java.lang.String r3 = "audio.socketport"
            boolean r1 = r1.has(r3)     // Catch: java.lang.Exception -> L9f
            if (r1 != 0) goto L79
            org.json.JSONObject r1 = r5.mParams     // Catch: java.lang.Exception -> L9f
            java.lang.String r3 = "infile"
            java.lang.String r3 = r1.optString(r3)     // Catch: java.lang.Exception -> L9f
            org.json.JSONObject r1 = r5.mParams     // Catch: java.lang.Exception -> L9f
            java.lang.String r4 = "audio.source"
            boolean r1 = r1.has(r4)     // Catch: java.lang.Exception -> L9f
            if (r1 == 0) goto Lb2
            org.json.JSONObject r1 = r5.mParams     // Catch: java.lang.Exception -> L9f
            java.lang.String r2 = "audio.source"
            int r1 = r1.optInt(r2)     // Catch: java.lang.Exception -> L9f
        L6e:
            int r1 = com.baidu.speech.audio.MicrophoneServer.create(r3, r1)     // Catch: java.lang.Exception -> L9f
            org.json.JSONObject r2 = r5.mParams     // Catch: java.lang.Exception -> L9f
            java.lang.String r3 = "audio.socketport"
            r2.put(r3, r1)     // Catch: java.lang.Exception -> L9f
        L79:
            org.json.JSONObject r1 = r5.mParams
            r5.initWp(r0, r1)
            java.lang.String r1 = com.baidu.speech.asr.WakeUpControl.WAK_CMD_LOAD_ENGINE
            r5.postEvent(r0, r1)
            r5.clearOutFile()
        L86:
            com.baidu.speech.core.BDSErrorDescription r0 = r5.postEvent(r0, r6)
            goto L18
        L8b:
            org.json.JSONObject r1 = new org.json.JSONObject     // Catch: org.json.JSONException -> L93
            r1.<init>(r7)     // Catch: org.json.JSONException -> L93
            r5.mParams = r1     // Catch: org.json.JSONException -> L93
            goto L3c
        L93:
            r1 = move-exception
            r1.printStackTrace()
            org.json.JSONObject r1 = new org.json.JSONObject
            r1.<init>()
            r5.mParams = r1
            goto L3c
        L9f:
            r1 = move-exception
            r1.printStackTrace()
            goto L79
        La4:
            java.lang.String r1 = "wp.stop"
            boolean r1 = r6.equals(r1)
            if (r1 == 0) goto L86
            java.lang.String r1 = com.baidu.speech.asr.WakeUpControl.WAK_CMD_UNLOAD_ENGINE
            r5.postEvent(r0, r1)
            goto L86
        Lb2:
            r1 = r2
            goto L6e
        */
        throw new UnsupportedOperationException("Method not decompiled: com.baidu.speech.asr.WakeUpControl.postEvent(java.lang.String, java.lang.String):com.baidu.speech.core.BDSErrorDescription");
    }

    @Override // com.baidu.speech.core.BDSSDKLoader.BDSCoreEventListener
    public void receiveCoreEvent(BDSMessage bDSMessage, BDSSDKLoader.BDSSDKInterface bDSSDKInterface) {
        if (this.mListener == null || bDSMessage == null) {
            return;
        }
        asrCallBack(bDSMessage, this.mListener);
    }

    public void setListener(EventListener eventListener) {
        this.mListener = eventListener;
    }
}
