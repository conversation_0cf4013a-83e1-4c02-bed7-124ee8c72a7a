package com.AoDevBase.compass;

/* loaded from: classes.dex */
public class BDMessage {
    private byte[] address;
    private byte[] message;

    public byte[] getMessage() {
        return this.message;
    }

    public void setMessage(byte[] message) {
        this.message = message;
    }

    public byte[] getAddress() {
        return this.address;
    }

    public void setAddress(byte[] address) {
        this.address = address;
    }
}
