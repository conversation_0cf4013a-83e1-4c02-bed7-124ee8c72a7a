package com.AoDevBase.compass;

import android.app.ActivityManager;
import android.bluetooth.BluetoothDevice;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.util.Log;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/* loaded from: classes.dex */
public class BDManager {
    public static final String SERVICE_NAME = "com.AoDevBase.compass.BluetoothGpsService";
    public static final int STATELLITE_ALL_ABLE = 3;
    public static final int STATELLITE_ALL_UNABLE = 1;
    public static final int STATELLITE_MESSAGE_ABLE = 2;
    public static final int STATELLITE_UNABLE_GET = -1;
    private static final String TAG = "BDLocationManager";
    private BDManager instance;
    private Context mContext;
    private static boolean bDebug = true;
    private static BDListener bdTimeListener = null;
    private static BDListener bdMessageListener = null;
    private static BDListener bdStateListener = null;
    private static BDShortcutListener bdShortcutListener = null;
    private static Intent mServiceIntent = null;
    private static BGServiceBinder serviceBinder = null;
    public static BluetoothGpsService mService = null;
    private static ServiceConnection serverConnection = new ServiceConnection() { // from class: com.AoDevBase.compass.BDManager.1
        @Override // android.content.ServiceConnection
        public void onServiceConnected(ComponentName name, IBinder service) {
            BGServiceBinder unused = BDManager.serviceBinder = (BGServiceBinder) service;
            if (BDManager.serviceBinder != null) {
                BDManager.mService = BDManager.serviceBinder.getService();
            }
        }

        @Override // android.content.ServiceConnection
        public void onServiceDisconnected(ComponentName name) {
            if (BDManager.mService != null) {
                BDManager.mService.stopConnect();
            }
            BGServiceBinder unused = BDManager.serviceBinder = null;
            BDManager.mService = null;
        }
    };

    private BDManager() {
        this.instance = null;
        this.mContext = null;
    }

    public BDManager(Context context) {
        this.instance = null;
        this.mContext = null;
        if (this.mContext == null && !context.equals(this.mContext)) {
            this.mContext = context;
            if (!isServiceRunning()) {
                mServiceIntent = new Intent(this.mContext, (Class<?>) BluetoothGpsService.class);
                this.mContext.startService(mServiceIntent);
            }
            boolean bBind = this.mContext.bindService(new Intent(SERVICE_NAME), serverConnection, 1);
            if (!bBind && bDebug) {
                Log.e(TAG, "bind service failed!");
            }
        }
    }

    public final boolean connectToBDS(BluetoothDevice device) {
        if (bDebug && mService == null) {
            Log.d(TAG, "gpsService == null");
        }
        String mAddress = device.getAddress();
        return mService.connect(mAddress);
    }

    public final void disConnectToBDS() {
        if (mService != null) {
            mService.stopConnect();
        }
    }

    public int requestLocationUpdates(int iEmergency, int minTime, BDListener listener) {
        if (listener == null) {
            throw new IllegalArgumentException("listener == null");
        }
        if (minTime < 0) {
            minTime = 0;
        }
        if (iEmergency != 1) {
            iEmergency = 0;
        }
        if (mService != null) {
            return mService.setGpsLocaitonParams(iEmergency, minTime, listener);
        }
        return -1;
    }

    public int requestGpsLocationUpdates(BDListener listener) {
        if (listener == null) {
            throw new IllegalArgumentException("listener == null");
        }
        if (mService != null) {
            return mService.setGpsLocaitonListener(listener);
        }
        return -1;
    }

    public void unBindService() {
        this.mContext.unbindService(serverConnection);
    }

    public BDLocation getLastKnownLocation() {
        return mService.getLastKnownLocation();
    }

    public long getLastKnownLocationTime() {
        return mService.getLastKnownLocationTime();
    }

    public int sendMessage(String msg, String cardNum) {
        if (msg == null || msg.equals("") || cardNum == null || cardNum.equals("")) {
            return -1;
        }
        if (!isServiceRunning() || mService == null) {
            return 0;
        }
        String[] str = cardNum.split(";");
        for (String str2 : str) {
            String[] temp = str2.split("-");
            String singleNum = temp[0];
            if (mService.sendMessage(2, msg, singleNum, "郭浩", bdMessageListener) != 0) {
                return 0;
            }
        }
        return 1;
    }

    public int queryMessage() {
        if (mService != null) {
            return mService.queryMessage();
        }
        return -1;
    }

    public String getCurrentCardNum() {
        if (mService != null) {
            return mService.getCurrentCardNum();
        }
        return null;
    }

    public void setBDStateListener(BDListener listener) {
        bdStateListener = listener;
        if (mService != null) {
            mService.setBDStateListener(listener);
        }
    }

    public void setBDMessageListener(BDListener listener) {
        bdMessageListener = listener;
        if (mService != null) {
            mService.setBDMessageListener(listener);
        }
    }

    public void setBDTimeListener(BDListener listener) {
        bdTimeListener = listener;
        if (mService != null) {
            mService.setBDTimeListener(listener);
        }
    }

    public static BDShortcutListener getBDShortcutListener() {
        return bdShortcutListener;
    }

    public void setBDShortCutListener(BDShortcutListener listener) {
        bdShortcutListener = listener;
        if (mService != null) {
            mService.setBDShortCutListener(listener);
        }
    }

    public static BDListener getBDMessageListener() {
        return bdMessageListener;
    }

    public static BDListener getBDStateListener() {
        return bdStateListener;
    }

    public static BDListener getBDTimeListener() {
        return bdTimeListener;
    }

    public boolean isServiceRunning() {
        boolean isRunning = false;
        ActivityManager activityManager = (ActivityManager) this.mContext.getSystemService("activity");
        List<ActivityManager.RunningServiceInfo> serviceList = activityManager.getRunningServices(Integer.MAX_VALUE);
        if (serviceList.size() < 0) {
            return false;
        }
        int i = 0;
        while (true) {
            if (i >= serviceList.size()) {
                break;
            }
            if (!serviceList.get(i).service.getClassName().equals(SERVICE_NAME)) {
                i++;
            } else {
                isRunning = true;
                break;
            }
        }
        return isRunning;
    }

    protected void finalize() throws Throwable {
        super.finalize();
    }

    public void stopBDService() {
        if (mServiceIntent != null) {
            this.mContext.stopService(mServiceIntent);
        }
        this.mContext.unbindService(serverConnection);
    }

    public static String getSystemTime() {
        Calendar myDate = Calendar.getInstance();
        Date date = myDate.getTime();
        SimpleDateFormat dFormat = new SimpleDateFormat("yyyy-MM-dd   HH:mm:ss ");
        return dFormat.format(date);
    }

    public String[] getLatestMsg() {
        if (mService != null) {
            return mService.getLatestMessage();
        }
        return null;
    }

    public int sendSetShortCutMsg(BDShortCutInfo info) {
        if (mService != null) {
            return mService.sendSetShortCutMsg(info);
        }
        return -1;
    }

    public int getLeftTime() {
        if (mService != null) {
            return mService.getLeftTime();
        }
        return -1;
    }

    public int sendGetShortCutMsg(short type) {
        if (mService != null) {
            return mService.sendGetShortCutMsg(type);
        }
        return -1;
    }
}
