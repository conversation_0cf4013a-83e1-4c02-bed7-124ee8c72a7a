package com.AoDevBase.compass.ui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;
import com.AoDevBase.R;
import com.AoDevBase.compass.BDManager;

/* loaded from: classes.dex */
public class BDDemoActivity extends Activity {
    public static BDManager bdManager = null;
    private Button bd_BluetoothBtn;
    private Button bd_InfoBtn;
    private Button bd_SendMsgBtn;
    private Button bd_receiveMsgBtn;

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bddemo);
        bdManager = new BDManager(getApplicationContext());
        this.bd_BluetoothBtn = (Button) findViewById(R.id.bluetoothBtn);
        this.bd_InfoBtn = (Button) findViewById(R.id.bdinfoBtn);
        this.bd_SendMsgBtn = (Button) findViewById(R.id.sendmsgBtn);
        this.bd_receiveMsgBtn = (Button) findViewById(R.id.receivemsgBtn);
        this.bd_BluetoothBtn.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.compass.ui.BDDemoActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent connectIntent = new Intent(BDDemoActivity.this, (Class<?>) BDBluetoothActivity.class);
                BDDemoActivity.this.startActivity(connectIntent);
            }
        });
        this.bd_InfoBtn.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.compass.ui.BDDemoActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent bdinfoIntent = new Intent(BDDemoActivity.this, (Class<?>) BDInfoActivity.class);
                BDDemoActivity.this.startActivity(bdinfoIntent);
            }
        });
        this.bd_SendMsgBtn.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.compass.ui.BDDemoActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent sendIntent = new Intent(BDDemoActivity.this, (Class<?>) BDSendMsgActivity.class);
                BDDemoActivity.this.startActivity(sendIntent);
            }
        });
        this.bd_receiveMsgBtn.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.compass.ui.BDDemoActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (BDDemoActivity.bdManager.getLatestMsg() != null) {
                    String cardNum = BDDemoActivity.bdManager.getLatestMsg()[0];
                    String content = BDDemoActivity.bdManager.getLatestMsg()[1];
                    Toast.makeText(BDDemoActivity.this, "来自" + cardNum + "的短消息;内容为：" + content, 0).show();
                    return;
                }
                Toast.makeText(BDDemoActivity.this, "未收到任何短消息", 0).show();
            }
        });
    }
}
