package com.AoDevBase.ui;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.widget.ImageView;

/* loaded from: classes.dex */
public class CustomImageView extends ImageView {
    private int mColor;
    private Context mContext;
    private Paint mPaint;
    private float mX;
    private float mY;

    public CustomImageView(Context context) {
        super(context);
        this.mContext = null;
        this.mPaint = null;
        this.mX = 40.0f;
        this.mY = 40.0f;
        this.mColor = -7829368;
        this.mContext = context;
    }

    public CustomImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = null;
        this.mPaint = null;
        this.mX = 40.0f;
        this.mY = 40.0f;
        this.mColor = -7829368;
        this.mContext = context;
    }

    @Override // android.widget.ImageView, android.view.View
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        this.mPaint = new Paint();
        this.mPaint.setColor(this.mColor);
        canvas.drawCircle(this.mX, this.mY, 10.0f, this.mPaint);
    }

    public void setPntPos(float x, float y, int color) {
        this.mX = x;
        this.mY = y;
        this.mColor = color;
    }
}
