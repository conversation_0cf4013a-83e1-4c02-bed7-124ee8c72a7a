package com.AoDevBase.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.Scroller;
import com.AoDevBase.R;

/* loaded from: classes.dex */
public class ScrollScreen extends ViewGroup {
    private static final int DEFAULT_SCREEN = 0;
    private static final int SNAP_VELOCITY = 600;
    private static final int TOUCH_STATE_REST = 0;
    private static final int TOUCH_STATE_SCROLLING = 1;
    private ScreenIndicator indicator;
    private boolean mAllowDrag;
    private int mCurrentScreen;
    private int mDefaultScreen;
    private boolean mFirstLayout;
    private float mLastMotionX;
    private int mMaximumVelocity;
    private OnScreenChangeListener mOnScreenChangeListener;
    private Scroller mScroller;
    private int mTouchSlop;
    private int mTouchState;
    private VelocityTracker mVelocityTracker;

    public interface OnScreenChangeListener {
        void onScreenChanged(int i);
    }

    public interface ScreenContentFactory {
        View createScreenContent(int i);
    }

    public interface ScreenIndicator {
        void addIndicator();

        void setCurrentScreen(int i);
    }

    public boolean isAllowDrag() {
        return this.mAllowDrag;
    }

    public void setAllowDrag(boolean mAllowDrag) {
        this.mAllowDrag = mAllowDrag;
    }

    public ScrollScreen(Context context) {
        super(context);
        this.mFirstLayout = true;
        this.mTouchState = 0;
        this.mAllowDrag = true;
        this.mDefaultScreen = 0;
        initScrollScreen();
    }

    public ScrollScreen(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mFirstLayout = true;
        this.mTouchState = 0;
        this.mAllowDrag = true;
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ao_ScrollScreen);
        this.mDefaultScreen = a.getInt(R.styleable.ao_ScrollScreen_default_screen, 0);
        a.recycle();
        initScrollScreen();
    }

    private void initScrollScreen() {
        Context context = getContext();
        this.mScroller = new Scroller(context);
        ViewConfiguration configuration = ViewConfiguration.get(getContext());
        this.mTouchSlop = configuration.getScaledTouchSlop();
        this.mMaximumVelocity = configuration.getScaledMaximumFlingVelocity();
        this.mCurrentScreen = this.mDefaultScreen;
    }

    @Override // android.view.View
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int width = View.MeasureSpec.getSize(widthMeasureSpec);
        int widthMode = View.MeasureSpec.getMode(widthMeasureSpec);
        if (widthMode != 1073741824) {
            throw new IllegalStateException("Workspace can only be used in EXACTLY mode.");
        }
        int heightMode = View.MeasureSpec.getMode(heightMeasureSpec);
        if (heightMode != 1073741824) {
            throw new IllegalStateException("Workspace can only be used in EXACTLY mode.");
        }
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            getChildAt(i).measure(widthMeasureSpec, heightMeasureSpec);
        }
        if (this.mFirstLayout) {
            setHorizontalScrollBarEnabled(false);
            scrollTo(this.mCurrentScreen * width, 0);
            setHorizontalScrollBarEnabled(true);
            this.mFirstLayout = false;
            if (this.indicator != null) {
                this.indicator.setCurrentScreen(this.mCurrentScreen);
            }
        }
    }

    @Override // android.view.ViewGroup, android.view.View
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int childLeft = 0;
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View child = getChildAt(i);
            if (child.getVisibility() != 8) {
                int childWidth = child.getMeasuredWidth();
                child.layout(childLeft, 0, childLeft + childWidth, child.getMeasuredHeight());
                childLeft += childWidth;
            }
        }
    }

    @Override // android.view.View
    public void computeScroll() {
        if (this.mScroller.computeScrollOffset()) {
            scrollTo(this.mScroller.getCurrX(), this.mScroller.getCurrY());
            postInvalidate();
        }
    }

    @Override // android.view.ViewGroup
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!this.mAllowDrag) {
            return false;
        }
        int action = ev.getAction();
        if (action == 2 && this.mTouchState != 0) {
            return true;
        }
        if (this.mVelocityTracker == null) {
            this.mVelocityTracker = VelocityTracker.obtain();
        }
        this.mVelocityTracker.addMovement(ev);
        switch (action) {
            case 0:
                this.mLastMotionX = ev.getX();
                this.mTouchState = this.mScroller.isFinished() ? 0 : 1;
                break;
            case 1:
            case 3:
                this.mTouchState = 0;
                if (this.mVelocityTracker != null) {
                    this.mVelocityTracker.recycle();
                    this.mVelocityTracker = null;
                    break;
                }
                break;
            case 2:
                float x = ev.getX();
                int xDiff = (int) Math.abs(x - this.mLastMotionX);
                int touchSlop = this.mTouchSlop;
                boolean xMoved = xDiff > touchSlop;
                if (xMoved) {
                    this.mTouchState = 1;
                    this.mLastMotionX = x;
                    break;
                }
                break;
        }
        return this.mTouchState != 0;
    }

    @Override // android.view.View
    public boolean onTouchEvent(MotionEvent ev) {
        if (this.mVelocityTracker == null) {
            this.mVelocityTracker = VelocityTracker.obtain();
        }
        this.mVelocityTracker.addMovement(ev);
        int action = ev.getAction();
        switch (action & 255) {
            case 0:
                if (!this.mScroller.isFinished()) {
                    this.mScroller.abortAnimation();
                }
                this.mLastMotionX = ev.getX();
                break;
            case 1:
                VelocityTracker velocityTracker = this.mVelocityTracker;
                velocityTracker.computeCurrentVelocity(1000, this.mMaximumVelocity);
                int velocityX = (int) velocityTracker.getXVelocity();
                if (velocityX > SNAP_VELOCITY && this.mCurrentScreen > 0) {
                    snapToScreen(this.mCurrentScreen - 1);
                } else if (velocityX < -600 && this.mCurrentScreen < getChildCount() - 1) {
                    snapToScreen(this.mCurrentScreen + 1);
                } else {
                    snapToDestination();
                }
                if (this.mVelocityTracker != null) {
                    this.mVelocityTracker.recycle();
                    this.mVelocityTracker = null;
                }
                this.mTouchState = 0;
                break;
            case 2:
                float x = ev.getX();
                float deltaX = this.mLastMotionX - x;
                this.mLastMotionX = x;
                scrollBy((int) deltaX, 0);
                break;
            case 3:
                this.mTouchState = 0;
                break;
        }
        return true;
    }

    public void snapToDestination() {
        int screenWidth = getWidth();
        int destScreen = (getScrollX() + (screenWidth / 2)) / screenWidth;
        snapToScreen(destScreen);
    }

    public void snapToScreen(int whichScreen) {
        int whichScreen2 = Math.max(0, Math.min(whichScreen, getChildCount() - 1));
        if (getScrollX() != getWidth() * whichScreen2) {
            int delta = (getWidth() * whichScreen2) - getScrollX();
            this.mScroller.startScroll(getScrollX(), 0, delta, 0, Math.abs(delta) * 2);
            this.mCurrentScreen = whichScreen2;
            invalidate();
            if (this.indicator != null) {
                this.indicator.setCurrentScreen(this.mCurrentScreen);
            }
            invokeOnScreenChangeListener();
        }
    }

    public void addScreen(View view) {
        addView(view, new ViewGroup.LayoutParams(-1, -1));
        if (this.indicator != null) {
            this.indicator.addIndicator();
        }
    }

    public void addScreen(int count, ScreenContentFactory contentFactory) {
        for (int i = 0; i < count; i++) {
            addScreen(contentFactory.createScreenContent(i));
        }
    }

    public void setScreenIndicator(ScreenIndicator indicator) {
        this.indicator = indicator;
    }

    public ScreenIndicator getScreenIndicator() {
        return this.indicator;
    }

    public int getCurrentScreen() {
        return this.mCurrentScreen;
    }

    public void setToScreen(int whichScreen) {
        int whichScreen2 = Math.max(0, Math.min(whichScreen, getChildCount() - 1));
        this.mCurrentScreen = whichScreen2;
        scrollTo(getWidth() * whichScreen2, 0);
        if (this.indicator != null) {
            this.indicator.setCurrentScreen(this.mCurrentScreen);
        }
    }

    public void setOnScreenChangedListener(OnScreenChangeListener l) {
        this.mOnScreenChangeListener = l;
    }

    private void invokeOnScreenChangeListener() {
        if (this.mOnScreenChangeListener != null) {
            this.mOnScreenChangeListener.onScreenChanged(getCurrentScreen());
        }
    }
}
