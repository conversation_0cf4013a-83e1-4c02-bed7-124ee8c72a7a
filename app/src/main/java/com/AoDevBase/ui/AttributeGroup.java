package com.AoDevBase.ui;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.ui.AttributeItem;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class AttributeGroup {
    private ViewGroup innerView;
    private List<AttributeItem> items = new ArrayList();
    private Object tag;

    public static class AttributeGroupParams {
        public Object tag;
        public String title;
    }

    public int getItemCount() {
        return this.items.size();
    }

    public Object getTag() {
        return this.tag;
    }

    public void setTag(Object tag) {
        this.tag = tag;
    }

    public ViewGroup getInnerView() {
        return this.innerView;
    }

    public AttributeGroup(Context context, AttributeGroupParams params, ViewGroup parent) {
        this.innerView = AttributeUIBase.createGroup(params, context, parent);
        this.tag = params.tag;
    }

    protected void attachItem(AttributeItem item) {
        if (item.attachedGroup != null) {
            throw new IllegalStateException("cannot attach a item twice");
        }
        this.items.add(item);
        this.innerView.addView(item.getInnerView());
        item.attachedGroup = this;
    }

    public AttributeItem addItem(AttributeItem.AttributeItemParams param) {
        if (this.items.size() > 0) {
            addSeperator();
        }
        AttributeItem item = new AttributeItem(this, param);
        attachItem(item);
        return item;
    }

    protected void addSeperator() {
        View lastSep = AttributeUIBase.createSeperator(this.innerView.getContext(), this.innerView);
        this.innerView.addView(lastSep);
    }

    public void addItems(AttributeItem.AttributeItemParams[] params) {
        View lastSep = null;
        for (AttributeItem.AttributeItemParams item : params) {
            addItem(item);
            lastSep = AttributeUIBase.createSeperator(this.innerView.getContext(), this.innerView);
            this.innerView.addView(lastSep);
        }
        this.innerView.removeView(lastSep);
    }
}
