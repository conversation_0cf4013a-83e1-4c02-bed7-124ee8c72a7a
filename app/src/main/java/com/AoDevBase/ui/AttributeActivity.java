package com.AoDevBase.ui;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.Toast;
import com.AoDevBase.R;
import com.AoDevBase.dialog.DictionaryDialog;
import com.AoDevBase.ui.ScrollScreen;
import com.AoGIS.AoGISApplication;
import com.AoGIS.database.GdbFieldInfo;
import com.AoGIS.database.WorkArea;
import com.AoGIS.ui.AoGISUIActivity;
import com.AoGIS.util.GdbAttributesMap;
import com.AoGIS.util.StringUtil;
import com.AoGIS.util.dict.DictionaryManager;
import com.AoGIS.util.dict.FileDictionaryProvider;
import java.util.ArrayList;

/* loaded from: classes.dex */
public abstract class AttributeActivity extends AoGISUIActivity {
    public static final String PARAM_BOOL_IS_NEW_ATTRIBUTE = "isNew";
    public static final String PARAM_INT_GEOMETRY_ID = "gid";
    public static final String PARAM_INT_WORKAREA_IN_APP_DATA = "WorkArea";
    public static final String PARAM_STRING_DICT_PATH = "DictPath";
    private String DictPath;
    private WorkArea area;
    GdbAttributesMap<String, Object> data;
    GdbFieldInfo[] fields;
    private int gid;
    private int inappId;
    private boolean isNew;
    private FrameLayout mExtendView;
    private ScrollScreen screen;
    private ScreenListener mInnerListener = new ScreenListener();
    private int m_curViewIndex = 0;
    private ArrayList<View> mContextViews = new ArrayList<>();
    Toast toast = null;

    @Override // com.AoGIS.ui.AoGISUIActivity
    public abstract void onClickCancel();

    public abstract void onClickSave();

    public abstract void onInitializeViews(ContextViewManager contextViewManager);

    @Override // android.app.Activity
    protected void onDestroy() {
        if (this.area != null) {
            this.area.SaveArea();
        }
        finalizeData();
        super.onDestroy();
    }

    private class ScreenListener implements ScrollScreen.ScreenContentFactory, ScrollScreen.OnScreenChangeListener {
        private ScreenListener() {
        }

        @Override // com.AoDevBase.ui.ScrollScreen.ScreenContentFactory
        public View createScreenContent(int index) {
            if (index == 0) {
                return (View) AttributeActivity.this.mContextViews.get(0);
            }
            if (index == 1) {
                return AttributeActivity.this.mExtendView;
            }
            throw new RuntimeException();
        }

        @Override // com.AoDevBase.ui.ScrollScreen.OnScreenChangeListener
        public void onScreenChanged(int index) {
            if (index != 0) {
                AttributeActivity.this.m_btnCancel.setVisibility(4);
                AttributeActivity.this.m_btnOK.setText(R.string.ao_btnReturn);
            } else {
                AttributeActivity.this.m_btnCancel.setVisibility(0);
                AttributeActivity.this.m_btnOK.setText(R.string.ao_btnOK);
            }
        }
    }

    private void ensureScrollScreen() {
        if (this.screen == null) {
            setContentView(R.layout.ao_screen_content);
        }
    }

    public ScrollScreen getScrollScreen() {
        ensureScrollScreen();
        return this.screen;
    }

    @Override // com.AoGIS.ui.AoGISUIActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        initData();
        this.screen = new ScrollScreen(this);
        this.mBottomLayout = this.screen;
        super.onCreate(savedInstanceState);
        if (this.screen == null) {
            throw new RuntimeException("Your content must have a ScrollScreen whose id com.AoRGMap.ui.attribute is '.R.id.scroll_screen'");
        }
        this.mExtendView = new FrameLayout(this);
        this.m_btnOK.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.ui.AttributeActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (AttributeActivity.this.getScrollScreen().getCurrentScreen() != 0) {
                    AttributeActivity.this.getScrollScreen().snapToScreen(0);
                } else {
                    AttributeActivity.this.onClickOK();
                }
            }
        });
        this.m_btnCancel.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.ui.AttributeActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (AttributeActivity.this.getScrollScreen().getCurrentScreen() != 0) {
                    AttributeActivity.this.getScrollScreen().snapToScreen(0);
                } else {
                    AttributeActivity.this.onClickCancel();
                }
            }
        });
        onInitializeViews(new ContextViewManager());
        ScrollScreen scrollScreen = getScrollScreen();
        scrollScreen.addScreen(2, this.mInnerListener);
        scrollScreen.setOnScreenChangedListener(this.mInnerListener);
        scrollScreen.setAllowDrag(false);
        this.screen.setFocusable(true);
        this.screen.setFocusableInTouchMode(true);
        this.screen.requestFocus();
    }

    public void showDictionaryDialog(String dictName, String title, EditText editor, boolean bInsert, boolean bReplace, String sperator, int levelLimit, String defaultString) {
        String[] dicts = dictName.split("[,/.|]");
        DictionaryDialog dlg = new DictionaryDialog(this, new FileDictionaryProvider(this.DictPath, dicts));
        dlg.showListDialog(title, editor, bInsert, bReplace, sperator, levelLimit, defaultString);
    }

    public void showDictionaryDialog(String dictName, AttributeItem item) {
        EditText editor = item.getEditorEditText();
        String title = item.getLabelText();
        ItemDictInfo info = item.getPropDict();
        if (info != null && editor != null) {
            showDictionaryDialog(dictName, title, editor, info.bInsert, info.bReplace, info.seperator, info.levelLimit, info.defaultString);
        }
    }

    public void ensureShowView(int index) {
        if (this.m_curViewIndex != index) {
            if (index == 0) {
                getScrollScreen().snapToScreen(0);
            } else if (index > 0) {
                this.mExtendView.removeAllViews();
                this.mExtendView.addView(this.mContextViews.get(index));
                getScrollScreen().snapToScreen(1);
            }
        }
    }

    public class ContextViewManager {
        private ContextViewManager() {
        }

        public ViewGroup addStandardAttributeView() {
            ViewGroup viewGroup = (ViewGroup) LayoutInflater.from(AttributeActivity.this).inflate(R.layout.ao_attributes_container, (ViewGroup) AttributeActivity.this.getScrollScreen(), false);
            AttributeActivity.this.mContextViews.add(viewGroup);
            ViewGroup container = (ViewGroup) viewGroup.findViewById(R.id.ao_attributes_container_view);
            return container;
        }

        public void addView(View v) {
            AttributeActivity.this.mContextViews.add(v);
        }
    }

    public boolean updateAttributeMap(Iterable<AttributeItem> list) {
        String key;
        for (AttributeItem item : list) {
            Object name = item.getAttributeName();
            if (name != null) {
                if (name instanceof Number) {
                    key = getResources().getString(((Number) name).intValue());
                } else {
                    key = name.toString();
                }
                String value = item.getItemData().toString();
                this.data.put(key, value);
            }
        }
        return true;
    }

    protected void MakeDictButtton(Iterable<AttributeItem> items) {
        View.OnClickListener listener = new View.OnClickListener() { // from class: com.AoDevBase.ui.AttributeActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttributeItem item = AttributeItem.fromButtons(v);
                AttributeActivity.this.showDictionaryDialog(item.getPropDictName(), item);
            }
        };
        for (AttributeItem item : items) {
            String prop_dict = StringUtil.Trim(item.getLabelText(), ": ");
            if (DictionaryManager.isAvailable(this.DictPath, prop_dict) && item.getPropDictName() == null) {
                item.setPropDictName(prop_dict);
            }
            String obj = item.getPropDictName();
            if (obj != null) {
                item.addButton(new AttributeButton(listener, null));
            }
        }
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        if (this.area != null) {
            this.area.SaveArea();
        }
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == 4 && event.getRepeatCount() == 0 && getScrollScreen().getCurrentScreen() != 0) {
            getScrollScreen().snapToScreen(0);
            return true;
        }
        if (keyCode == 4) {
            if (this.toast == null) {
                this.toast = Toast.makeText(this, R.string.ao_attribute_back_toast, 0);
            } else {
                this.toast.cancel();
            }
            this.toast.setText(R.string.ao_attribute_back_toast);
            this.toast.show();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public GdbAttributesMap<String, Object> getAttributeMap() {
        return this.data;
    }

    public WorkArea getWorkArea() {
        return this.area;
    }

    public boolean isNewFlag() {
        return this.isNew;
    }

    public int getGeometryId() {
        return this.gid;
    }

    private void initData() {
        Bundle bundle = getIntent().getExtras();
        this.inappId = bundle.getInt("WorkArea");
        this.area = (WorkArea) AoGISApplication.getCurrentApp().getInAppTaskData(this.inappId);
        this.isNew = bundle.getBoolean("isNew");
        this.gid = bundle.getInt("gid");
        this.DictPath = bundle.getString("DictPath");
        if (!this.isNew) {
            this.data = this.area.getNamedAttributeStrings(this.gid);
        } else {
            this.data = new GdbAttributesMap<>();
        }
        this.fields = this.area.getAttributeSchemaManager().getFieldInfoClones();
    }

    private void finalizeData() {
        AoGISApplication.getCurrentApp().removeInAppTaskData(this.inappId);
    }

    public String getData(String name) {
        Object obj = this.data.get(name);
        if (obj != null) {
            return obj.toString();
        }
        return null;
    }

    public String getData(int string_res_id) {
        String str = getResources().getString(string_res_id);
        return getData(str);
    }

    public String getData(AttributeItem item) {
        Object obj = item.getAttributeName();
        if (obj != null) {
            if (obj instanceof Number) {
                return getData(((Number) obj).intValue());
            }
            return getData(obj.toString());
        }
        return null;
    }
}
