package com.AoDevBase.util;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Button;
import android.widget.RelativeLayout;
import com.AoDevBase.R;
import com.AoGIS.AoGISApplication;
import com.AoGIS.base.GeoRect;
import com.AoGIS.base.PointType;
import com.AoGIS.database.AoMap;
import com.AoGIS.database.WorkArea;
import com.AoGIS.edit.DelEditListener;
import com.AoGIS.edit.LineEditListener;
import com.AoGIS.edit.PointEditListener;
import com.AoGIS.edit.TouchDelEditListener;
import com.AoGIS.edit.TouchInfoEditListener;
import com.AoGIS.edit.TouchLineEditListener;
import com.AoGIS.edit.TouchPointEditListener;
import com.AoGIS.geometry.GeoClassType;
import com.AoGIS.render.AoView;
import com.AoGIS.render.BaseTouchListener;
import com.AoGIS.ui.LayerManagerActivity;
import com.AoGIS.util.DisplayHelper;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class DrawBaseActivity extends Activity {
    DisplayMetrics dm;
    private static int widthH = 0;
    private static int heightH = 0;
    private static int widthP = 0;
    private static int heightP = 0;
    protected AoMap m_Map = null;
    private AoView m_MapView = null;
    private final int MENU_SKETCH_FILE = 1;
    private final int MENU_SKETCH_FILE_SAVE = 2;
    private final int MENU_SKETCH_FILE_LYRMGR = 3;
    private final int MENU_SKETCH_FILE_RESETVIEW = 4;
    private final int MENU_SKETCH_FILE_EXIT = 5;
    private final int MENU_SKETCH_FILE_LAYERRESTORE = 6;
    private final int MENU_SKETCH_POINT = 21;
    private final int MENU_SKETCH_POINT_NEWSYM = 22;
    private final int MENU_SKETCH_POINT_NEWNOTE = 23;
    private final int MENU_SKETCH_POINT_DELETE = 24;
    private final int MENU_SKETCH_POINT_MOVE = 25;
    private final int MENU_SKETCH_POINT_COPY = 26;
    private final int MENU_SKETCH_POINT_INFO = 27;
    private final int MENU_SKETCH_LINE = 51;
    private final int MENU_SKETCH_LINE_NEW1 = 52;
    private final int MENU_SKETCH_LINE_NEW2 = 53;
    private final int MENU_SKETCH_LINE_NEW3 = 54;
    private final int MENU_SKETCH_LINE_DELETE = 55;
    private final int MENU_SKETCH_LINE_NODEEDIT = 56;
    private final int MENU_SKETCH_LINE_MOVE = 57;
    private final int MENU_SKETCH_LINE_COPY = 58;
    private final int MENU_SKETCH_LINE_INFO = 59;
    private RelativeLayout m_mainRelativeLayout = null;

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.ao_draw_sketch);
        this.m_mainRelativeLayout = (RelativeLayout) findViewById(R.id.ao_mainRelativeLayout);
        this.m_MapView = (AoView) findViewById(R.id.ao_main_view);
        this.m_MapView.setMap(this.m_Map);
        ViewTreeObserver vto = this.m_MapView.getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() { // from class: com.AoDevBase.util.DrawBaseActivity.1
            @Override // android.view.ViewTreeObserver.OnGlobalLayoutListener
            public void onGlobalLayout() {
                DrawBaseActivity.this.m_MapView.resetView();
                DrawBaseActivity.this.m_MapView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        });
        this.dm = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(this.dm);
        widthP = this.dm.widthPixels;
        heightP = this.dm.heightPixels;
    }

    @Override // android.app.Activity, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        switch (newConfig.orientation) {
            case 1:
                double[] d = this.m_MapView.getDrawParams();
                this.dm = new DisplayMetrics();
                getWindowManager().getDefaultDisplay().getMetrics(this.dm);
                widthP = this.dm.widthPixels;
                heightP = this.dm.heightPixels;
                double lx2 = widthH / d[2];
                double ly2 = heightH / d[2];
                double x2 = d[0] + (lx2 / 2.0d);
                double y2 = d[1] + (ly2 / 2.0d);
                double px = x2 - ((widthP / 2) / d[2]);
                double py = y2 - ((heightP / 2) / d[2]);
                this.m_MapView.setDrawParams(px, py, d[2]);
                break;
            case 2:
                double[] d2 = this.m_MapView.getDrawParams();
                this.dm = new DisplayMetrics();
                getWindowManager().getDefaultDisplay().getMetrics(this.dm);
                widthH = this.dm.widthPixels;
                heightH = this.dm.heightPixels;
                double lx1 = widthP / d2[2];
                double ly1 = heightP / d2[2];
                double x1 = d2[0] + (lx1 / 2.0d);
                double y1 = d2[1] + (ly1 / 2.0d);
                double hx = x1 - ((widthH / 2) / d2[2]);
                double hy = y1 - ((heightH / 2) / d2[2]);
                this.m_MapView.setDrawParams(hx, hy, d2[2]);
                break;
        }
    }

    @Override // android.app.Activity
    protected void onDestroy() {
        this.m_Map.closeMap();
        super.onDestroy();
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        SubMenu filemenu = menu.addSubMenu(0, 1, 0, R.string.ao_menu_main_file);
        filemenu.setHeaderTitle(R.string.ao_menu_main_file);
        filemenu.add(0, 2, 0, R.string.ao_menu_main_file_save);
        filemenu.add(0, 3, 0, R.string.ao_menu_main_file_lyrmgr);
        filemenu.add(0, 4, 0, R.string.ao_menu_main_file_resetview);
        filemenu.add(0, 6, 0, R.string.ao_menu_main_file_layerrestore);
        filemenu.add(0, 5, 0, R.string.ao_menu_sketch_exit);
        SubMenu ptmenu = menu.addSubMenu(0, 21, 0, R.string.ao_menu_sketch_point);
        ptmenu.setHeaderTitle(R.string.ao_menu_sketch_point);
        ptmenu.add(0, 22, 0, R.string.ao_menu_sketch_point_newsym);
        ptmenu.add(0, 23, 0, R.string.ao_menu_sketch_point_newnote);
        ptmenu.add(0, 24, 0, R.string.ao_menu_sketch_point_del);
        ptmenu.add(0, 25, 0, R.string.ao_menu_sketch_point_move);
        ptmenu.add(0, 26, 0, R.string.ao_menu_sketch_point_copy);
        ptmenu.add(0, 27, 0, R.string.ao_menu_sketch_point_info);
        SubMenu lnmenu = menu.addSubMenu(0, 51, 0, R.string.ao_menu_sketch_line);
        lnmenu.setHeaderTitle(R.string.ao_menu_sketch_line);
        lnmenu.add(0, 52, 0, R.string.ao_menu_sketch_line_new1);
        lnmenu.add(0, 53, 0, R.string.ao_menu_sketch_line_new2);
        lnmenu.add(0, 54, 0, R.string.ao_menu_sketch_line_new3);
        lnmenu.add(0, 55, 0, R.string.ao_menu_sketch_line_del);
        lnmenu.add(0, 56, 0, R.string.ao_menu_sketch_line_node);
        lnmenu.add(0, 57, 0, R.string.ao_menu_sketch_line_move);
        lnmenu.add(0, 58, 0, R.string.ao_menu_sketch_line_copy);
        lnmenu.add(0, 59, 0, R.string.ao_menu_sketch_line_info);
        super.onCreateOptionsMenu(menu);
        return true;
    }

    private boolean setEditLayout() {
        LayoutInflater inflater = getLayoutInflater();
        ViewGroup toolbar = (ViewGroup) inflater.inflate(R.layout.ao_toolbar_edit_comm, (ViewGroup) this.m_mainRelativeLayout, false);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(-2, -2);
        params.topMargin = (int) DisplayHelper.dip2px(this, 10.0f);
        params.leftMargin = (int) DisplayHelper.dip2px(this, 10.0f);
        params.addRule(9, -1);
        toolbar.setLayoutParams(params);
        this.m_mainRelativeLayout.addView(toolbar);
        Button btn = new Button(this);
        btn.setText(R.string.ao_btnOK);
        btn.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.util.DrawBaseActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawBaseActivity.this.m_MapView.getTouchListener().onEditOk();
            }
        });
        toolbar.addView(btn, new ViewGroup.LayoutParams(-2, -2));
        Button btn2 = new Button(this);
        btn2.setText(R.string.ao_btnCancel);
        btn2.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.util.DrawBaseActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawBaseActivity.this.m_MapView.getTouchListener().onEditCancel();
            }
        });
        toolbar.addView(btn2, new ViewGroup.LayoutParams(-2, -2));
        Button btn3 = new Button(this);
        btn3.setText(R.string.ao_btnReturn);
        btn3.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.util.DrawBaseActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                View toolbar2 = DrawBaseActivity.this.findViewById(R.id.ao_toolbar_edit_comm);
                DrawBaseActivity.this.m_mainRelativeLayout.removeView(toolbar2);
                DrawBaseActivity.this.m_MapView.setZoomTouchListener();
            }
        });
        toolbar.addView(btn3, new ViewGroup.LayoutParams(-2, -2));
        BaseTouchListener listener = this.m_MapView.getTouchListener();
        String[] strs = listener.getExtendFunctions();
        for (int i = 0; i < strs.length; i++) {
            Button btn4 = new Button(this);
            btn4.setText(strs[i]);
            btn4.setTag(Integer.valueOf(i));
            btn4.setOnClickListener(new View.OnClickListener() { // from class: com.AoDevBase.util.DrawBaseActivity.5
                @Override // android.view.View.OnClickListener
                public void onClick(View v) {
                    DrawBaseActivity.this.m_MapView.getTouchListener().callFunction(((Integer) v.getTag()).intValue(), null);
                }
            });
            toolbar.addView(btn4, new ViewGroup.LayoutParams(-2, -2));
        }
        return true;
    }

    private boolean prepareEdit(GeoClassType type) {
        return setEditLayout();
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case 2:
                this.m_Map.saveMapAndAreas();
                break;
            case 3:
                Bundle bundle = new Bundle();
                bundle.putInt(LayerManagerActivity.BUNDLE_MAP, AoGISApplication.getCurrentApp().addInAppTaskData(this.m_Map));
                Intent intent = new Intent(this, (Class<?>) LayerManagerActivity.class);
                intent.putExtras(bundle);
                startActivityForResult(intent, 1006);
                break;
            case 4:
                this.m_MapView.resetView();
                break;
            case 5:
                finish();
                break;
            case 6:
                ArrayList<String> list = new ArrayList<>(20);
                short sNum = this.m_Map.getWorkAreaNum();
                for (short i = 0; i < sNum; i = (short) (i + 1)) {
                    list.add(this.m_Map.getItemName(i));
                }
                String[] waNames = (String[]) list.toArray(new String[list.size()]);
                AlertDialog.Builder builder = new AlertDialog.Builder(this).setTitle(R.string.ao_menu_main_file_layerrestore);
                builder.setItems(waNames, new DialogInterface.OnClickListener() { // from class: com.AoDevBase.util.DrawBaseActivity.6
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        GeoRect rect;
                        WorkArea wa = DrawBaseActivity.this.m_Map.getItemWorkAreaMustEdit((short) which);
                        if (wa != null && (rect = wa.getRect()) != null) {
                            DrawBaseActivity.this.m_MapView.resetView(rect);
                        }
                    }
                });
                builder.create().show();
                break;
            case 22:
                this.m_MapView.setTouchListener(new PointEditListener(this.m_MapView, PointEditListener.MODE_NEW));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 23:
                PointEditListener listener = new PointEditListener(this.m_MapView, PointEditListener.MODE_NEW, PointType.Note);
                this.m_MapView.setTouchListener(listener);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 24:
                this.m_MapView.setTouchListener(new DelEditListener(this.m_MapView, GeoClassType.POINT));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 25:
                this.m_MapView.setTouchListener(new TouchPointEditListener(this.m_MapView, TouchPointEditListener.MODE_MODIFY));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 26:
                this.m_MapView.setTouchListener(new TouchPointEditListener(this.m_MapView, TouchPointEditListener.MODE_COPY));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 27:
                this.m_MapView.setTouchListener(new TouchInfoEditListener(this.m_MapView, GeoClassType.POINT, null));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.POINT);
                break;
            case 52:
                LineEditListener listener2 = new TouchLineEditListener(this.m_MapView, 0);
                this.m_MapView.setTouchListener(listener2);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 53:
                LineEditListener listener3 = new TouchLineEditListener(this.m_MapView, 0);
                listener3.setCurve(true);
                this.m_MapView.setTouchListener(listener3);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 54:
                LineEditListener listener4 = new TouchLineEditListener(this.m_MapView, 0);
                listener4.setCurveInDB(true);
                this.m_MapView.setTouchListener(listener4);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 55:
                this.m_MapView.setTouchListener(new TouchDelEditListener(this.m_MapView, GeoClassType.LINE, null));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 56:
                LineEditListener listener5 = new TouchLineEditListener(this.m_MapView, 1);
                this.m_MapView.setTouchListener(listener5);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 57:
                LineEditListener listener6 = new TouchLineEditListener(this.m_MapView, 2);
                this.m_MapView.setTouchListener(listener6);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 58:
                LineEditListener listener7 = new TouchLineEditListener(this.m_MapView, 3);
                this.m_MapView.setTouchListener(listener7);
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
            case 59:
                this.m_MapView.setTouchListener(new TouchInfoEditListener(this.m_MapView, GeoClassType.LINE, null));
                this.m_mainRelativeLayout.removeView(findViewById(R.id.ao_toolbar_edit_comm));
                prepareEdit(GeoClassType.LINE);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1006 && resultCode == 1) {
            this.m_MapView.updateView();
        }
        if (this.m_MapView.getTouchListener() != null) {
            this.m_MapView.getTouchListener().onActivityResult(requestCode, resultCode, data);
        }
    }
}
