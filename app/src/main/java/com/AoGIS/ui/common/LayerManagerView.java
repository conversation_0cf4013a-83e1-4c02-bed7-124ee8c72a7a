package com.AoGIS.ui.common;

import android.R;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.support.v4.internal.view.SupportMenu;
import android.support.v4.view.ViewCompat;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import android.widget.Toast;
import com.AoGIS.AoGISApplication;
import com.AoGIS.database.AoMap;
import com.AoGIS.geometry.GeoClassType;
import org.apache.http.protocol.HTTP;

/* loaded from: classes.dex */
public class LayerManagerView extends RelativeLayout {
    private String[] LAYER_STATE;
    private short iWorkAreaNum;
    private Context mContext;
    private AoMap m_Map;
    private TableLayout table;
    public static int TABLEROW_ID = 0;
    public static short MOVE_PRJ_ID = -1;
    public static int CONTROL_WORKAREA_ID = 0;

    public short getiWorkAreaNum() {
        return this.iWorkAreaNum;
    }

    private LayerManagerView(Context context) {
        super(context);
        this.mContext = null;
        this.table = null;
        this.LAYER_STATE = null;
        this.m_Map = null;
        this.iWorkAreaNum = (short) 0;
    }

    public LayerManagerView(Context context, AoMap m_Map) {
        super(context);
        this.mContext = null;
        this.table = null;
        this.LAYER_STATE = null;
        this.m_Map = null;
        this.iWorkAreaNum = (short) 0;
        this.mContext = context;
        this.m_Map = m_Map;
        initParams();
        this.LAYER_STATE = new String[4];
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            this.LAYER_STATE[0] = "关闭";
            this.LAYER_STATE[1] = "打开";
            this.LAYER_STATE[2] = "编辑";
            this.LAYER_STATE[3] = "当前编辑";
        } else {
            this.LAYER_STATE[0] = HTTP.CONN_CLOSE;
            this.LAYER_STATE[1] = "Open";
            this.LAYER_STATE[2] = "Edit";
            this.LAYER_STATE[3] = "Cur Edit";
        }
        setLayoutParams(new RelativeLayout.LayoutParams(-1, -1));
        addView(addLayout());
    }

    private void initParams() {
        TABLEROW_ID = 10000;
        this.iWorkAreaNum = (short) 0;
        MOVE_PRJ_ID = (short) -1;
        CONTROL_WORKAREA_ID = 19999;
    }

    private View addLayout() {
        LinearLayout linearLayout = new LinearLayout(this.mContext);
        linearLayout.setLayoutParams(AoWidgetGlobal.generateLinearParams(-1, -1, 0, null));
        linearLayout.setOrientation(1);
        TextView blank = new TextView(this.mContext);
        linearLayout.addView(blank, AoWidgetGlobal.generateLinearParams(-1, -2, 2, null));
        linearLayout.addView(generateMainLayout(), AoWidgetGlobal.generateLinearParams(-1, -2, 98, null));
        return linearLayout;
    }

    private View generateMainLayout() {
        LinearLayout layout = new LinearLayout(this.mContext);
        layout.setLayoutParams(AoWidgetGlobal.generateLinearParams(-1, -1, 0, null));
        TextView leftTv = new TextView(this.mContext);
        TextView rightTv = new TextView(this.mContext);
        layout.addView(leftTv, AoWidgetGlobal.generateLinearParams(0, -2, 2, null));
        layout.addView(generateTableLayout(), AoWidgetGlobal.generateLinearParams(0, -2, 96, null));
        layout.addView(rightTv, AoWidgetGlobal.generateLinearParams(0, -2, 2, null));
        return layout;
    }

    private View generateTableLayout() {
        HorizontalScrollView hs = new HorizontalScrollView(this.mContext);
        LinearLayout layout = new LinearLayout(this.mContext);
        this.table = new TableLayout(this.mContext);
        this.table.setLayoutParams(AoWidgetGlobal.generateTableParams(-1, -2, 0, null));
        this.table.setStretchAllColumns(false);
        this.table.addView(addTitleRow());
        this.iWorkAreaNum = this.m_Map.getWorkAreaNum();
        for (short i = 0; i < this.iWorkAreaNum; i = (short) (i + 1)) {
            addRow(i, this.m_Map.getItemName(i), this.m_Map.getItemType(i), this.m_Map.getItemState(i), this.m_Map.getItemPathName(i));
        }
        layout.addView(this.table);
        hs.addView(layout);
        return hs;
    }

    private View addTitleRow() {
        String strLabel;
        String strLabel2;
        String strLabel3;
        String strLabel4;
        TableRow row = new TableRow(this.mContext);
        row.setBackgroundColor(AoWidgetGlobal.ROW_BACKCOLOR);
        row.setLayoutParams(AoWidgetGlobal.generateTableParams(-1, -2, 0, null));
        row.setGravity(16);
        TextView checkTv = new TextView(this.mContext);
        AoWidgetGlobal.setTextViewStyle(checkTv, "", ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
        row.addView(checkTv, AoWidgetGlobal.generateTableRowParams(-2, -2, 0, new int[]{1, 1, 1, 1}));
        checkTv.setPadding(10, 10, 10, 10);
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel = "名称";
        } else {
            strLabel = "Name";
        }
        TextView nameTv = new TextView(this.mContext);
        AoWidgetGlobal.setTextViewStyle(nameTv, strLabel, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
        row.addView(nameTv, AoWidgetGlobal.generateTableRowParams(-2, -2, 0, new int[]{1, 1, 1, 1}));
        nameTv.setPadding(10, 10, 10, 10);
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel2 = "类型";
        } else {
            strLabel2 = "Type";
        }
        TextView typeTv = new TextView(this.mContext);
        AoWidgetGlobal.setTextViewStyle(typeTv, strLabel2, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
        row.addView(typeTv, AoWidgetGlobal.generateTableRowParams(-2, -2, 0, new int[]{1, 1, 1, 1}));
        typeTv.setPadding(10, 10, 10, 10);
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel3 = "状态";
        } else {
            strLabel3 = "Station";
        }
        TextView stateSp = new TextView(this.mContext);
        AoWidgetGlobal.setTextViewStyle(stateSp, strLabel3, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
        row.addView(stateSp, AoWidgetGlobal.generateTableRowParams(-2, -2, 0, new int[]{1, 1, 1, 1}));
        stateSp.setPadding(10, 10, 10, 10);
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel4 = "路径";
        } else {
            strLabel4 = "Path";
        }
        TextView pathTv = new TextView(this.mContext);
        AoWidgetGlobal.setTextViewStyle(pathTv, strLabel4, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
        row.addView(pathTv, AoWidgetGlobal.generateTableRowParams(-2, -2, 0, new int[]{1, 1, 1, 1}));
        pathTv.setPadding(10, 10, 10, 10);
        return row;
    }

    public void addRow(short id, String name, GeoClassType type, AoMap.GeoItemState state, String path) {
        LayerTableRow row = new LayerTableRow(this.mContext);
        row.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.common.LayerManagerView.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (LayerManagerView.MOVE_PRJ_ID >= 0 && LayerManagerView.MOVE_PRJ_ID < LayerManagerView.this.table.getChildCount() - 1) {
                    LayerTableRow selectRow = (LayerTableRow) LayerManagerView.this.table.findViewById(LayerManagerView.MOVE_PRJ_ID + 10000);
                    selectRow.setBackgroundColor(AoWidgetGlobal.ROW_BACKCOLOR);
                }
                LayerTableRow ltRow = (LayerTableRow) v;
                ltRow.setBackgroundColor(SupportMenu.CATEGORY_MASK);
                ltRow.invalidate();
                LayerManagerView.MOVE_PRJ_ID = (short) (((short) ltRow.getId()) - 10000);
            }
        });
        if (row.setLayerRowParams(id, name, type, state, path) != 0) {
            this.table.addView(row, id + 1);
        }
    }

    public TableLayout getTable() {
        return this.table;
    }

    public class LayerTableRow extends TableRow {
        private ArrayAdapter<String> adapter;
        private CheckBox cb;
        private LinearLayout cbLayout;
        private String fileName;
        private int initSpFlag;
        private Button nameBt;
        private TextView pathTv;
        private Context rContext;
        private GeoClassType rowLayerType;
        private Spinner stateSp;
        private ArrayAdapter<String> tifAdapter;
        private TextView typeTv;

        static /* synthetic */ int access$510(LayerTableRow x0) {
            int i = x0.initSpFlag;
            x0.initSpFlag = i - 1;
            return i;
        }

        public LinearLayout getCbLayout() {
            return this.cbLayout;
        }

        public String getFileName() {
            return this.fileName;
        }

        public LayerTableRow(Context context) {
            super(context);
            this.rContext = null;
            this.cb = null;
            this.nameBt = null;
            this.stateSp = null;
            this.pathTv = null;
            this.typeTv = null;
            this.rowLayerType = null;
            this.adapter = null;
            this.tifAdapter = null;
            this.cbLayout = null;
            this.initSpFlag = 1;
            this.fileName = null;
            this.rContext = context;
            this.cb = new CheckBox(this.rContext);
            this.nameBt = new Button(this.rContext);
            this.typeTv = new TextView(this.rContext);
            this.stateSp = new Spinner(this.rContext);
            this.pathTv = new TextView(this.rContext);
            setParams();
            generateAdapter();
        }

        private void setParams() {
            setBackgroundColor(AoWidgetGlobal.ROW_BACKCOLOR);
            setLayoutParams(AoWidgetGlobal.generateTableParams(-1, -2, 0, null));
            int i = LayerManagerView.TABLEROW_ID;
            LayerManagerView.TABLEROW_ID = i + 1;
            setId(i);
        }

        private void generateAdapter() {
            this.adapter = new ArrayAdapter<>(this.rContext, R.layout.simple_spinner_item, LayerManagerView.this.LAYER_STATE);
            this.adapter.setDropDownViewResource(R.layout.simple_spinner_dropdown_item);
            this.tifAdapter = new ArrayAdapter<>(this.rContext, R.layout.simple_spinner_item);
            this.tifAdapter.add(LayerManagerView.this.LAYER_STATE[0]);
            this.tifAdapter.add(LayerManagerView.this.LAYER_STATE[1]);
            this.tifAdapter.setDropDownViewResource(R.layout.simple_spinner_dropdown_item);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public int setLayerRowParams(short id, String name, GeoClassType type, AoMap.GeoItemState state, String path) {
            setListener(id);
            if (id > 32867) {
                Toast.makeText(this.rContext, "图层过多", 0).show();
                return 0;
            }
            String strType = "";
            if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                switch (type) {
                    case LINE:
                        strType = "线";
                        break;
                    case POINT:
                        strType = "点";
                        break;
                    case POLYGON:
                        strType = "面";
                        break;
                    case IMAGE:
                        strType = "影像";
                        break;
                }
            } else {
                switch (type) {
                    case LINE:
                        strType = "Line";
                        break;
                    case POINT:
                        strType = "Point";
                        break;
                    case POLYGON:
                        strType = "Polygon";
                        break;
                    case IMAGE:
                        strType = "Image";
                        break;
                }
            }
            this.rowLayerType = type;
            this.cbLayout = new LinearLayout(this.rContext);
            this.cbLayout.setBackgroundColor(AoWidgetGlobal.SUBWIDGET_BACKCOLOR);
            this.cbLayout.setGravity(17);
            this.cbLayout.addView(this.cb);
            if (this.rowLayerType == GeoClassType.IMAGE) {
                this.cb.setClickable(false);
            }
            LinearLayout nameLayout = new LinearLayout(this.rContext);
            nameLayout.setBackgroundColor(AoWidgetGlobal.SUBWIDGET_BACKCOLOR);
            nameLayout.setGravity(17);
            this.nameBt.setTextColor(ViewCompat.MEASURED_STATE_MASK);
            this.nameBt.setTextSize(2, 17.0f);
            this.nameBt.setGravity(17);
            this.nameBt.setText(name);
            nameLayout.addView(this.nameBt, AoWidgetGlobal.generateLinearParams(-1, -2, 0, null));
            AoWidgetGlobal.setTextViewStyle(this.typeTv, strType, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
            this.typeTv.setPadding(10, 0, 10, 0);
            LinearLayout stateLayout = new LinearLayout(this.rContext);
            stateLayout.setBackgroundColor(AoWidgetGlobal.SUBWIDGET_BACKCOLOR);
            stateLayout.setGravity(17);
            this.stateSp.setVisibility(0);
            stateLayout.addView(this.stateSp, AoWidgetGlobal.generateLinearParams(-1, -2, 0, null));
            this.initSpFlag = 1;
            if (this.rowLayerType == GeoClassType.IMAGE) {
                this.stateSp.setAdapter((SpinnerAdapter) this.tifAdapter);
                switch (state) {
                    case CLOSE:
                        this.stateSp.setSelection(0, true);
                        break;
                    case OPEN:
                        this.stateSp.setSelection(1, true);
                        break;
                }
            } else {
                this.stateSp.setAdapter((SpinnerAdapter) this.adapter);
                switch (state) {
                    case CLOSE:
                        this.stateSp.setSelection(0, true);
                        break;
                    case OPEN:
                        this.stateSp.setSelection(1, true);
                        break;
                    case EDIT:
                        this.stateSp.setSelection(2, true);
                        break;
                    case CUREDIT:
                        this.stateSp.setSelection(3, true);
                        break;
                }
            }
            AoWidgetGlobal.setTextViewStyle(this.pathTv, path, ViewCompat.MEASURED_STATE_MASK, 17, AoWidgetGlobal.SUBWIDGET_BACKCOLOR, 17);
            this.pathTv.setPadding(10, 0, 20, 0);
            addView(this.cbLayout, AoWidgetGlobal.generateTableRowParams(-2, -1, 0, new int[]{1, 1, 1, 1}));
            addView(nameLayout, AoWidgetGlobal.generateTableRowParams(-1, -1, 0, new int[]{1, 1, 1, 1}));
            addView(this.typeTv, AoWidgetGlobal.generateTableRowParams(-2, -1, 0, new int[]{1, 1, 1, 1}));
            addView(stateLayout, AoWidgetGlobal.generateTableRowParams(-2, -1, 0, new int[]{1, 1, 1, 1}));
            addView(this.pathTv, AoWidgetGlobal.generateTableRowParams(-2, -1, 0, new int[]{1, 1, 1, 1}));
            return 1;
        }

        public void setListener(final short id) {
            this.nameBt.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.common.LayerManagerView.LayerTableRow.1
                @Override // android.view.View.OnClickListener
                public void onClick(View v) {
                    String fNote;
                    String fNote2;
                    AoMap.ItemNote itemNote = LayerManagerView.this.m_Map.getItemNoteInfo(id);
                    String noteField = itemNote.strNoteField;
                    float noteHeight = itemNote.fNoteHeight;
                    if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                        if (itemNote.ifNote) {
                            fNote2 = "是";
                        } else {
                            fNote2 = "否";
                        }
                        int noteColor = itemNote.iNoteColor;
                        new AlertDialog.Builder(LayerManagerView.this.mContext).setTitle("自动标注").setItems(new String[]{"标注字段名称：" + noteField, "是否标注：" + fNote2, "标注字高：" + noteHeight, "标注颜色：" + noteColor}, (DialogInterface.OnClickListener) null).setNegativeButton("确定", (DialogInterface.OnClickListener) null).show();
                        return;
                    }
                    if (itemNote.ifNote) {
                        fNote = "Yes";
                    } else {
                        fNote = "No";
                    }
                    int noteColor2 = itemNote.iNoteColor;
                    new AlertDialog.Builder(LayerManagerView.this.mContext).setTitle("Automatic Label").setItems(new String[]{"Label Field：" + noteField, "Is Label：" + fNote, "Label Height：" + noteHeight, "Label Color：" + noteColor2}, (DialogInterface.OnClickListener) null).setNegativeButton("OK", (DialogInterface.OnClickListener) null).show();
                }
            });
            if (this.rowLayerType == GeoClassType.IMAGE) {
                this.stateSp.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() { // from class: com.AoGIS.ui.common.LayerManagerView.LayerTableRow.2
                    @Override // android.widget.AdapterView.OnItemSelectedListener
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id_) {
                        switch (position) {
                            case 0:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    LayerManagerView.this.m_Map.setItemState(id, AoMap.GeoItemState.CLOSE);
                                    break;
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                    break;
                                }
                            case 1:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    LayerManagerView.this.m_Map.setItemState(id, AoMap.GeoItemState.OPEN);
                                    break;
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                    break;
                                }
                        }
                    }

                    @Override // android.widget.AdapterView.OnItemSelectedListener
                    public void onNothingSelected(AdapterView<?> arg0) {
                    }
                });
            } else {
                this.cb.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.common.LayerManagerView.LayerTableRow.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v) {
                        CheckBox checkBox = (CheckBox) v;
                        if (checkBox.isChecked()) {
                            LayerTableRow.this.stateSp.setSelection(3);
                        } else {
                            LayerTableRow.this.stateSp.setSelection(2);
                        }
                    }
                });
                this.stateSp.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() { // from class: com.AoGIS.ui.common.LayerManagerView.LayerTableRow.4
                    @Override // android.widget.AdapterView.OnItemSelectedListener
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id_) {
                        switch (position) {
                            case 0:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    LayerManagerView.this.m_Map.setItemState(id, AoMap.GeoItemState.CLOSE);
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                }
                                if (LayerTableRow.this.cb.isChecked()) {
                                    LayerTableRow.this.cb.setChecked(false);
                                    break;
                                }
                                break;
                            case 1:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    LayerManagerView.this.m_Map.setItemState(id, AoMap.GeoItemState.OPEN);
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                }
                                if (LayerTableRow.this.cb.isChecked()) {
                                    LayerTableRow.this.cb.setChecked(false);
                                    break;
                                }
                                break;
                            case 2:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    LayerManagerView.this.m_Map.setItemState(id, AoMap.GeoItemState.EDIT);
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                }
                                if (LayerTableRow.this.cb.isChecked()) {
                                    LayerTableRow.this.cb.setChecked(false);
                                    break;
                                }
                                break;
                            case 3:
                                if (LayerTableRow.this.initSpFlag == 0) {
                                    int curId = LayerManagerView.this.m_Map.getCurEditIndex(LayerTableRow.this.rowLayerType);
                                    Log.i("the id of the current workarea is ", String.valueOf(curId));
                                    if (curId >= 0 && curId <= LayerManagerView.this.iWorkAreaNum) {
                                        if (LayerManagerView.this.table != null) {
                                            LayerTableRow tr = (LayerTableRow) LayerManagerView.this.table.findViewById(curId + 10000);
                                            tr.stateSp.setSelection(2, true);
                                            tr.cb.setChecked(false);
                                        }
                                    }
                                    LayerManagerView.this.m_Map.setCurEdit(id);
                                } else {
                                    LayerTableRow.access$510(LayerTableRow.this);
                                }
                                if (!LayerTableRow.this.cb.isChecked()) {
                                    LayerTableRow.this.cb.setChecked(true);
                                    break;
                                }
                                break;
                        }
                    }

                    @Override // android.widget.AdapterView.OnItemSelectedListener
                    public void onNothingSelected(AdapterView<?> arg0) {
                    }
                });
            }
        }

        public CheckBox getCheckBox() {
            return this.cb;
        }
    }
}
