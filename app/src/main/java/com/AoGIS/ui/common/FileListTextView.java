package com.AoGIS.ui.common;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

/* loaded from: classes.dex */
public class FileListTextView extends LinearLayout {
    private ImageView mIcon;
    private TextView mText;
    private TextView passText;

    public FileListTextView(Context context, FileListText fileListText) {
        super(context);
        setOrientation(0);
        this.mIcon = new ImageView(context);
        this.mIcon.setLayoutParams(new LinearLayout.LayoutParams(-2, -2));
        this.mIcon.setImageDrawable(fileListText.getIcon());
        this.mIcon.setPadding(0, 2, 5, 0);
        addView(this.mIcon);
        LinearLayout textLayout = new LinearLayout(context);
        textLayout.setLayoutParams(new LinearLayout.LayoutParams(-2, -2));
        textLayout.setOrientation(1);
        this.mText = new TextView(context);
        this.mText.setLayoutParams(new LinearLayout.LayoutParams(-2, -2));
        this.mText.setText(fileListText.getText());
        textLayout.addView(this.mText);
        this.passText = new TextView(context);
        this.passText.setLayoutParams(new LinearLayout.LayoutParams(-2, -2));
        this.passText.setText(fileListText.getPassText());
        textLayout.addView(this.passText);
        addView(textLayout);
    }

    public void setText(String words) {
        this.mText.setText(words);
    }

    public void setIcon(Drawable bullet) {
        this.mIcon.setImageDrawable(bullet);
    }

    public void setPassText(String text) {
        this.passText.setText(text);
    }
}
