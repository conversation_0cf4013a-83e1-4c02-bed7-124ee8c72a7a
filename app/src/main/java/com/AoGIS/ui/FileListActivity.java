package com.AoGIS.ui;

import android.R;
import android.app.AlertDialog;
import android.app.ListActivity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Environment;
import android.view.ContextMenu;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;
import com.AoGIS.AoGISApplication;
import com.AoGIS.ui.common.FileListAdapter;
import com.AoGIS.ui.common.FileListText;
import java.io.BufferedInputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/* loaded from: classes.dex */
public class FileListActivity extends ListActivity {
    private static final int Add_ID = 3;
    public static final String FILE_ENDS = "file_ends";
    public static final String INITIAL_DIRECTORY = "initial_directory";
    public static final String IS_DIRECTORY = "isDirectory";
    private static final int Main_ID = 4;
    public static final String STR_RETURN = "back";
    private static final int Select_ID = 2;
    private List<FileListText> directoryEntries = new ArrayList();
    private String[] fileEnds = null;
    private boolean isDirectory = false;
    private String selectedFile = "/";
    private File currentDirectory = null;
    private String rtParentDirectory = "..";
    private String passText = "";
    private File rootPath = Environment.getExternalStorageDirectory();
    String fileName = null;
    EditText et = null;

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        this.fileEnds = intent.getStringArrayExtra("file_ends");
        this.isDirectory = intent.getBooleanExtra("isDirectory", this.isDirectory);
        this.selectedFile = intent.getStringExtra("initial_directory");
        this.currentDirectory = new File(this.selectedFile);
        browseTo(this.currentDirectory);
        getListView().setOnCreateContextMenuListener(this);
        getListView().setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() { // from class: com.AoGIS.ui.FileListActivity.1
            @Override // android.widget.AdapterView.OnItemLongClickListener
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                String selectedFileString = ((FileListText) FileListActivity.this.directoryEntries.get(position)).getText();
                if (selectedFileString.equals(FileListActivity.this.rtParentDirectory)) {
                    return true;
                }
                File file = new File(FileListActivity.this.currentDirectory.getAbsolutePath() + "/" + selectedFileString);
                return (file.isDirectory() && FileListActivity.this.isDirectory) ? false : true;
            }
        });
    }

    @Override // android.app.ListActivity
    protected void onListItemClick(ListView l, View v, int position, long id) {
        String selectedFileString = this.directoryEntries.get(position).getText();
        if (selectedFileString.equals(this.rtParentDirectory)) {
            upOneLevel();
        } else {
            browseTo(new File(this.currentDirectory.getAbsolutePath() + "/" + selectedFileString));
        }
    }

    private void browseTo(File aDirectory) {
        String strCurPath = aDirectory.getAbsolutePath();
        if (aDirectory.isDirectory() && aDirectory.canRead()) {
            setTitle(strCurPath);
            this.currentDirectory = aDirectory;
            showFiles(aDirectory.listFiles());
        } else {
            if (!aDirectory.isDirectory()) {
                if (!this.isDirectory) {
                    this.selectedFile = aDirectory.getAbsolutePath();
                    Intent intent = new Intent();
                    intent.putExtra("back", this.selectedFile);
                    setResult(1, intent);
                    finish();
                    return;
                }
                return;
            }
            if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                Toast.makeText(this, "无访问权限", 0).show();
            } else {
                Toast.makeText(this, "No Access", 0).show();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showFiles(File[] files) {
        int currentPathStringLenght;
        Drawable currentIcon;
        this.directoryEntries.clear();
        if (this.currentDirectory.getParent() != null) {
            Drawable uponelevel = null;
            try {
                BufferedInputStream bis = new BufferedInputStream(getAssets().open("uponelevel.png"));
                Bitmap bm = BitmapFactory.decodeStream(bis);
                Drawable uponelevel2 = new BitmapDrawable(getResources(), bm);
                uponelevel = uponelevel2;
            } catch (Exception e) {
                e.printStackTrace();
            }
            this.directoryEntries.add(new FileListText(this.rtParentDirectory, uponelevel, ""));
        }
        if (this.currentDirectory.getAbsolutePath().equals("/")) {
            currentPathStringLenght = this.currentDirectory.getAbsolutePath().length();
        } else {
            currentPathStringLenght = this.currentDirectory.getAbsolutePath().length() + 1;
        }
        int length = files.length;
        int i = 0;
        Drawable currentIcon2 = null;
        while (i < length) {
            File currentFile = files[i];
            if (currentFile.canRead()) {
                this.passText = "";
            } else if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                this.passText = "无访问权限";
            } else {
                this.passText = "No Access";
            }
            if (currentFile.isDirectory()) {
                try {
                    BufferedInputStream bis2 = new BufferedInputStream(getAssets().open("folder.png"));
                    Bitmap bm2 = BitmapFactory.decodeStream(bis2);
                    currentIcon = new BitmapDrawable(getResources(), bm2);
                } catch (Exception e2) {
                    e2.printStackTrace();
                    currentIcon = currentIcon2;
                }
                this.directoryEntries.add(new FileListText(currentFile.getAbsolutePath().substring(currentPathStringLenght), currentIcon, this.passText));
            } else if (checkEnds(currentFile.getName(), this.fileEnds)) {
                try {
                    BufferedInputStream bis3 = new BufferedInputStream(getAssets().open("file.png"));
                    Bitmap bm3 = BitmapFactory.decodeStream(bis3);
                    currentIcon = new BitmapDrawable(getResources(), bm3);
                } catch (Exception e3) {
                    e3.printStackTrace();
                    currentIcon = currentIcon2;
                }
                this.directoryEntries.add(new FileListText(currentFile.getAbsolutePath().substring(currentPathStringLenght), currentIcon, ""));
            } else {
                currentIcon = currentIcon2;
            }
            i++;
            currentIcon2 = currentIcon;
        }
        Collections.sort(this.directoryEntries);
        FileListAdapter adapter = new FileListAdapter(this);
        adapter.setListItems(this.directoryEntries);
        setListAdapter(adapter);
    }

    private boolean checkEnds(String fileName, String[] fileEndings) {
        for (String strEnd : fileEndings) {
            if (fileName.endsWith(strEnd)) {
                return true;
            }
        }
        return false;
    }

    private void upOneLevel() {
        if (this.currentDirectory.getParent() != null) {
            browseTo(this.currentDirectory.getParentFile());
        }
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == 4) {
            if (this.currentDirectory.getAbsolutePath().equalsIgnoreCase("/")) {
                finish();
            } else {
                browseTo(this.currentDirectory.getParentFile());
            }
            return true;
        }
        return false;
    }

    public String getSelectFilePath() {
        return this.selectedFile;
    }

    @Override // android.app.Activity, android.view.View.OnCreateContextMenuListener
    public void onCreateContextMenu(ContextMenu menu, View v, ContextMenu.ContextMenuInfo menuInfo) {
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            menu.setHeaderTitle("选择文件夹");
            menu.add(0, 2, 0, "选择");
        } else {
            menu.setHeaderTitle("Select directory");
            menu.add(0, 2, 0, "Select");
        }
        super.onCreateContextMenu(menu, v, menuInfo);
    }

    @Override // android.app.Activity
    public boolean onContextItemSelected(MenuItem item) {
        AdapterView.AdapterContextMenuInfo info;
        try {
            info = (AdapterView.AdapterContextMenuInfo) item.getMenuInfo();
        } catch (ClassCastException e) {
        }
        switch (item.getItemId()) {
            case 2:
                this.selectedFile = this.directoryEntries.get(info.position).getText();
                Intent intent = new Intent();
                intent.putExtra("back", this.currentDirectory.getAbsolutePath() + "/" + this.selectedFile);
                setResult(1, intent);
                finish();
            default:
                return false;
        }
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            menu.add(0, 3, 0, "创建文件夹").setIcon(R.drawable.ic_menu_add);
            menu.add(0, 4, 0, "返回主目录").setIcon(R.drawable.ic_menu_save);
        } else {
            menu.add(0, 3, 0, "Create Directory").setIcon(R.drawable.ic_menu_add);
            menu.add(0, 4, 0, "Return to Main").setIcon(R.drawable.ic_menu_save);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        switch (item.getItemId()) {
            case 3:
                this.et = new EditText(this);
                if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                    new AlertDialog.Builder(this).setTitle("创建文件夹").setMessage("请输入新的文件夹名称").setIcon(R.drawable.ic_dialog_info).setView(this.et).setNegativeButton("取消", new DialogInterface.OnClickListener() { // from class: com.AoGIS.ui.FileListActivity.3
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                        }
                    }).setPositiveButton("确定", new DialogInterface.OnClickListener() { // from class: com.AoGIS.ui.FileListActivity.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                            FileListActivity.this.fileName = FileListActivity.this.et.getText().toString();
                            File dir = new File(FileListActivity.this.currentDirectory + "/" + FileListActivity.this.fileName);
                            dir.mkdir();
                            FileListActivity.this.showFiles(FileListActivity.this.currentDirectory.listFiles());
                        }
                    }).show();
                    break;
                } else {
                    new AlertDialog.Builder(this).setTitle("Create Directory").setMessage("Please input the name").setIcon(R.drawable.ic_dialog_info).setView(this.et).setNegativeButton("Cancel", new DialogInterface.OnClickListener() { // from class: com.AoGIS.ui.FileListActivity.5
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                        }
                    }).setPositiveButton("OK", new DialogInterface.OnClickListener() { // from class: com.AoGIS.ui.FileListActivity.4
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                            FileListActivity.this.fileName = FileListActivity.this.et.getText().toString();
                            File dir = new File(FileListActivity.this.currentDirectory + "/" + FileListActivity.this.fileName);
                            dir.mkdir();
                            FileListActivity.this.showFiles(FileListActivity.this.currentDirectory.listFiles());
                        }
                    }).show();
                    break;
                }
            case 4:
                browseTo(this.rootPath);
                break;
        }
        return super.onMenuItemSelected(featureId, item);
    }
}
