package com.AoGIS.ui;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.AoGIS.AoGISApplication;
import com.AoGIS.ui.common.SubnoSelectView;

/* loaded from: classes.dex */
public class SubnoSelectActivity extends AoGISUIActivity {
    public static final String BUNDLE_SUBNO = "subno";
    private int mSelSubno = 0;
    private SubnoSelectView mSubnoView = null;
    private LinearLayout mContainer = null;
    private TextView mIndicator = null;

    @Override // android.app.Activity
    protected void onDestroy() {
        super.onDestroy();
    }

    protected void refreshIndicator() {
        String text;
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            text = String.format("第%d页(%d-%d)", Integer.valueOf(this.mSubnoView.getCurPage() + 1), Integer.valueOf(this.mSubnoView.getPageStartNo()), Integer.valueOf(this.mSubnoView.getPageEndNo()));
        } else {
            text = String.format("Page%d(%d-%d)", Integer.valueOf(this.mSubnoView.getCurPage() + 1), Integer.valueOf(this.mSubnoView.getPageStartNo()), Integer.valueOf(this.mSubnoView.getPageEndNo()));
        }
        this.mIndicator.setText(text);
    }

    @Override // com.AoGIS.ui.AoGISUIActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        String strTitle;
        String strLabel;
        String strLabel2;
        String strLabel3;
        String strLabel4;
        Bundle bundle = getIntent().getExtras();
        this.mSelSubno = bundle.getInt(BUNDLE_SUBNO);
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strTitle = String.format("符号点号:%d", Integer.valueOf(this.mSelSubno));
        } else {
            strTitle = String.format("Symbol:%d", Integer.valueOf(this.mSelSubno));
        }
        setTitle(strTitle);
        this.mContainer = new LinearLayout(this);
        this.mContainer.setOrientation(1);
        this.mBottomLayout = this.mContainer;
        this.mSubnoView = new SubnoSelectView(this);
        this.mSubnoView.setSelectedColor(this.mSelSubno);
        this.mSubnoView.setPageListener(new SubnoSelectView.OnPageChangedListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.1
            @Override // com.AoGIS.ui.common.SubnoSelectView.OnPageChangedListener
            public void OnPageChanged(int p) {
                SubnoSelectActivity.this.refreshIndicator();
            }
        });
        this.mContainer.addView(this.mSubnoView, new LinearLayout.LayoutParams(-1, -2, 1.0f));
        this.mSubnoView.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String strTitleInfo;
                SubnoSelectActivity.this.mSelSubno = ((SubnoSelectView) v).getSelectedSubno();
                if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                    strTitleInfo = String.format("符号点号:%d", Integer.valueOf(SubnoSelectActivity.this.mSelSubno));
                } else {
                    strTitleInfo = String.format("Symbol:%d", Integer.valueOf(SubnoSelectActivity.this.mSelSubno));
                }
                SubnoSelectActivity.this.setTitleDynamic(strTitleInfo);
            }
        });
        LinearLayout mBottomBar = new LinearLayout(this);
        this.mContainer.addView(mBottomBar, new LinearLayout.LayoutParams(-1, -2, 0.0f));
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel = "上一页";
        } else {
            strLabel = "PrePage";
        }
        Button btnPrePage = new Button(this);
        btnPrePage.setText(strLabel);
        btnPrePage.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int p = SubnoSelectActivity.this.mSubnoView.getCurPage() - 1;
                SubnoSelectActivity.this.mSubnoView.switchPage(p);
            }
        });
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel2 = "下一页";
        } else {
            strLabel2 = "NextPage";
        }
        Button btnNextPage = new Button(this);
        btnNextPage.setText(strLabel2);
        btnNextPage.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int p = SubnoSelectActivity.this.mSubnoView.getCurPage() + 1;
                SubnoSelectActivity.this.mSubnoView.switchPage(p);
            }
        });
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel3 = "上5页";
        } else {
            strLabel3 = "Pre 5P";
        }
        Button btnPre5Page = new Button(this);
        btnPre5Page.setText(strLabel3);
        btnPre5Page.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int p = SubnoSelectActivity.this.mSubnoView.getCurPage() - 5;
                SubnoSelectActivity.this.mSubnoView.switchPage(p);
            }
        });
        if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
            strLabel4 = "下5页";
        } else {
            strLabel4 = "Next 5P";
        }
        Button btnNext5Page = new Button(this);
        btnNext5Page.setText(strLabel4);
        btnNext5Page.setOnClickListener(new View.OnClickListener() { // from class: com.AoGIS.ui.SubnoSelectActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int p = SubnoSelectActivity.this.mSubnoView.getCurPage() + 5;
                SubnoSelectActivity.this.mSubnoView.switchPage(p);
            }
        });
        mBottomBar.addView(btnPrePage);
        mBottomBar.addView(btnNextPage);
        mBottomBar.addView(btnPre5Page);
        mBottomBar.addView(btnNext5Page);
        this.mIndicator = new TextView(this);
        this.mIndicator.setGravity(19);
        mBottomBar.addView(this.mIndicator, new ViewGroup.LayoutParams(-1, -1));
        mBottomBar.setOrientation(0);
        super.onCreate(savedInstanceState);
    }

    @Override // android.app.Activity
    protected void onStart() {
        super.onStart();
        this.mSubnoView.performClick();
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    protected void onClickOK() {
        Intent intent = new Intent();
        intent.putExtra(BUNDLE_SUBNO, this.mSelSubno);
        setResult(1, intent);
        finish();
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    protected void onClickCancel() {
        setResult(0, new Intent());
        finish();
    }
}
