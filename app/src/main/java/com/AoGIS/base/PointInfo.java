package com.AoGIS.base;

import com.AoGIS.GeoDBException;
import com.AoGIS.INative;
import com.AoGIS.geometry.GeoClassType;

/* loaded from: classes.dex */
public class PointInfo implements IGeometryInfo, INative {
    public final ArcInfo arcInfo;
    public final CircleInfo circleInfo;
    public final ImageInfo imageInfo;
    private boolean m_bSelfFinalize;
    private int m_native;
    public final NoteInfo noteInfo;
    public final SubInfo subInfo;
    public final TextInfo textInfo;

    private native byte[] getBytes(int i);

    private native int getIclr(int i);

    private native float getInfoDx(int i);

    private native float getInfoDy(int i);

    private native short getLayer(int i);

    private native int getLinNo(int i);

    private native byte getOvprnt(int i);

    private native void getRect(int i, int i2);

    private native int getRes0(int i);

    private native int getRes1(int i);

    private native byte getType(int i);

    private native int nativeCopyInit(int i);

    private native void nativeFinalize(int i);

    private native int nativeInit();

    private native void setBytes(int i, byte[] bArr);

    private native void setIclr(int i, int i2);

    private native void setInfoDx(int i, float f);

    private native void setInfoDy(int i, float f);

    private native void setLayer(int i, short s);

    private native void setLinNo(int i, int i2);

    private native void setOvprnt(int i, byte b);

    private native void setRect(int i, int i2);

    private native void setRes0(int i, int i2);

    private native void setRes1(int i, int i2);

    private native void setType(int i, byte b);

    public PointInfo() {
        this.noteInfo = new NoteInfo();
        this.textInfo = new TextInfo();
        this.subInfo = new SubInfo();
        this.circleInfo = new CircleInfo();
        this.arcInfo = new ArcInfo();
        this.imageInfo = new ImageInfo();
        this.m_bSelfFinalize = true;
        this.m_native = nativeInit();
    }

    private PointInfo(int Native) {
        this.noteInfo = new NoteInfo();
        this.textInfo = new TextInfo();
        this.subInfo = new SubInfo();
        this.circleInfo = new CircleInfo();
        this.arcInfo = new ArcInfo();
        this.imageInfo = new ImageInfo();
        this.m_bSelfFinalize = true;
        this.m_native = Native;
    }

    protected void finalize() throws Throwable {
        if (this.m_bSelfFinalize) {
            nativeFinalize(this.m_native);
        }
        super.finalize();
    }

    @Override // com.AoGIS.INative
    public int getNativeHandle() {
        return this.m_native;
    }

    public static PointInfo createFromNativeHandle(int Native) {
        PointInfo pinf = new PointInfo(Native);
        pinf.m_bSelfFinalize = false;
        return pinf;
    }

    @Override // com.AoGIS.base.IGeometryInfo
    /* renamed from: clone, reason: merged with bridge method [inline-methods] */
    public PointInfo m7clone() {
        PointInfo info = new PointInfo();
        info.m_native = nativeCopyInit(this.m_native);
        return info;
    }

    public void setType(PointType type) {
        setType(this.m_native, type.getNativeID());
    }

    public PointType getType() {
        byte type = getType(this.m_native);
        for (PointType t : PointType.values()) {
            if (type == t.getNativeID()) {
                return t;
            }
        }
        throw new GeoDBException("No Support For Native Point Type:" + ((int) type));
    }

    public void setOvprnt(byte ovprnt) {
        setOvprnt(this.m_native, ovprnt);
    }

    public byte getOvprnt() {
        return getOvprnt(this.m_native);
    }

    public void setInfoDx(byte infoDx) {
        setInfoDx(this.m_native, infoDx);
    }

    public float getInfoDx() {
        return getInfoDx(this.m_native);
    }

    public void setInfoDy(float infoDy) {
        setInfoDy(this.m_native, infoDy);
    }

    public float getInfoDy() {
        return getInfoDy(this.m_native);
    }

    public void setIclr(int iclr) {
        setIclr(this.m_native, iclr);
    }

    public int getIclr() {
        return getIclr(this.m_native);
    }

    public void setLinNo(int linNo) {
        setLinNo(this.m_native, linNo);
    }

    public int getLinNo() {
        return getLinNo(this.m_native);
    }

    public void setLayer(short layer) {
        setLayer(this.m_native, layer);
    }

    public short getLayer() {
        return getLayer(this.m_native);
    }

    public void setRes0(int res0) {
        setRes0(this.m_native, res0);
    }

    public int getRes0() {
        return getRes0(this.m_native);
    }

    public void setRes1(int res1) {
        setRes1(this.m_native, res1);
    }

    public int getRes1() {
        return getRes1(this.m_native);
    }

    public void setRect(GeoRect rect) {
        setRect(this.m_native, rect.getNativeHandle());
    }

    public GeoRect getRect() {
        GeoRect rect = new GeoRect();
        getRect(this.m_native, rect.getNativeHandle());
        return rect;
    }

    public void getRect(GeoRect rect) {
        if (rect == null) {
            throw new NullPointerException("Passed null pointer : PointInfo.getRect");
        }
        getRect(this.m_native, rect.getNativeHandle());
    }

    public class NoteInfo {
        private native float getAngle(int i);

        private native short getChnt(int i);

        private native float getHeight(int i);

        private native byte getHvpl(int i);

        private native short getIfnt(int i);

        private native byte getIfnx(int i);

        private native float getSpace(int i);

        private native float getWidth(int i);

        private native void setAngle(int i, float f);

        private native void setChnt(int i, short s);

        private native void setHeight(int i, float f);

        private native void setHvpl(int i, byte b);

        private native void setIfnt(int i, short s);

        private native void setIfnx(int i, byte b);

        private native void setSpace(int i, float f);

        private native void setWidth(int i, float f);

        private NoteInfo() {
        }

        public void setHeight(float height) {
            setHeight(PointInfo.this.m_native, height);
        }

        public float getHeight() {
            return getHeight(PointInfo.this.m_native);
        }

        public void setWidth(float width) {
            setWidth(PointInfo.this.m_native, width);
        }

        public float getWidth() {
            return getWidth(PointInfo.this.m_native);
        }

        public void setSpace(float space) {
            setSpace(PointInfo.this.m_native, space);
        }

        public float getSpace() {
            return getSpace(PointInfo.this.m_native);
        }

        public void setAngle(float angle) {
            setAngle(PointInfo.this.m_native, angle);
        }

        public float getAngle() {
            return getAngle(PointInfo.this.m_native);
        }

        public void setIfnt(short ifnt) {
            setIfnt(PointInfo.this.m_native, ifnt);
        }

        public short getIfnt() {
            return getIfnt(PointInfo.this.m_native);
        }

        public void setChnt(short chnt) {
            setChnt(PointInfo.this.m_native, chnt);
        }

        public short getChnt() {
            return getChnt(PointInfo.this.m_native);
        }

        public void setIfnx(byte ifnx) {
            setIfnx(PointInfo.this.m_native, ifnx);
        }

        public byte getIfnx() {
            return getIfnx(PointInfo.this.m_native);
        }

        public void setHvpl(byte hvpl) {
            setHvpl(PointInfo.this.m_native, hvpl);
        }

        public byte getHvpl() {
            return getHvpl(PointInfo.this.m_native);
        }
    }

    public class TextInfo {
        private native float getAngle(int i);

        private native short getChnt(int i);

        private native float getDx(int i);

        private native float getDy(int i);

        private native float getHeight(int i);

        private native byte getHvpl(int i);

        private native short getIfnt(int i);

        private native byte getIfnx(int i);

        private native float getLspace(int i);

        private native float getSpace(int i);

        private native float getWidth(int i);

        private native void setAngle(int i, float f);

        private native void setChnt(int i, short s);

        private native void setDx(int i, float f);

        private native void setDy(int i, float f);

        private native void setHeight(int i, float f);

        private native void setHvpl(int i, byte b);

        private native void setIfnt(int i, short s);

        private native void setIfnx(int i, byte b);

        private native void setLspace(int i, float f);

        private native void setSpace(int i, float f);

        private native void setWidth(int i, float f);

        private TextInfo() {
        }

        public void setHeight(float height) {
            setHeight(PointInfo.this.m_native, height);
        }

        public float getHeight() {
            return getHeight(PointInfo.this.m_native);
        }

        public void setWidth(float width) {
            setWidth(PointInfo.this.m_native, width);
        }

        public float getWidth() {
            return getWidth(PointInfo.this.m_native);
        }

        public void setSpace(float space) {
            setSpace(PointInfo.this.m_native, space);
        }

        public float getSpace() {
            return getSpace(PointInfo.this.m_native);
        }

        public void setAngle(float angle) {
            setAngle(PointInfo.this.m_native, angle);
        }

        public float getAngle() {
            return getAngle(PointInfo.this.m_native);
        }

        public void setIfnt(short ifnt) {
            setIfnt(PointInfo.this.m_native, ifnt);
        }

        public short getIfnt() {
            return getIfnt(PointInfo.this.m_native);
        }

        public void setChnt(short chnt) {
            setChnt(PointInfo.this.m_native, chnt);
        }

        public short getChnt() {
            return getChnt(PointInfo.this.m_native);
        }

        public void setIfnx(byte ifnx) {
            setIfnx(PointInfo.this.m_native, ifnx);
        }

        public byte getIfnx() {
            return getIfnx(PointInfo.this.m_native);
        }

        public void setLspace(float lspace) {
            setLspace(PointInfo.this.m_native, lspace);
        }

        public float getLspace() {
            return getLspace(PointInfo.this.m_native);
        }

        public void setDx(float dx) {
            setDx(PointInfo.this.m_native, dx);
        }

        public float getDx() {
            return getDx(PointInfo.this.m_native);
        }

        public void setDy(float dy) {
            setDy(PointInfo.this.m_native, dy);
        }

        public float getDy() {
            return getDy(PointInfo.this.m_native);
        }

        public void setHvpl(byte hvpl) {
            setHvpl(PointInfo.this.m_native, hvpl);
        }

        public byte getHvpl() {
            return getHvpl(PointInfo.this.m_native);
        }
    }

    public class SubInfo {
        private native float getAngle(int i);

        private native int getFclr(int i);

        private native float getHeight(int i);

        private native float getPenw(int i);

        private native int getSubno(int i);

        private native float getWidth(int i);

        private native void setAngle(int i, float f);

        private native void setFclr(int i, int i2);

        private native void setHeight(int i, float f);

        private native void setPenw(int i, float f);

        private native void setSubno(int i, int i2);

        private native void setWidth(int i, float f);

        private SubInfo() {
        }

        public void setSubno(int subno) {
            setSubno(PointInfo.this.m_native, subno);
        }

        public int getSubno() {
            return getSubno(PointInfo.this.m_native);
        }

        public void setHeight(float height) {
            setHeight(PointInfo.this.m_native, height);
        }

        public float getHeight() {
            return getHeight(PointInfo.this.m_native);
        }

        public void setWidth(float width) {
            setWidth(PointInfo.this.m_native, width);
        }

        public float getWidth() {
            return getWidth(PointInfo.this.m_native);
        }

        public void setAngle(float angle) {
            setAngle(PointInfo.this.m_native, angle);
        }

        public float getAngle() {
            return getAngle(PointInfo.this.m_native);
        }

        public void setPenw(float penw) {
            setPenw(PointInfo.this.m_native, penw);
        }

        public float getPenw() {
            return getPenw(PointInfo.this.m_native);
        }

        public void setFclr(int fclr) {
            setFclr(PointInfo.this.m_native, fclr);
        }

        public int getFclr() {
            return getFclr(PointInfo.this.m_native);
        }
    }

    public class CircleInfo {
        private native byte getFflag(int i);

        private native int getLcol(int i);

        private native float getPenw(int i);

        private native double getRadiu(int i);

        private native void setFflag(int i, byte b);

        private native void setLcol(int i, int i2);

        private native void setPenw(int i, float f);

        private native void setRadiu(int i, double d);

        private CircleInfo() {
        }

        public void setRadiu(double radiu) {
            setRadiu(PointInfo.this.m_native, radiu);
        }

        public double getRadiu() {
            return getRadiu(PointInfo.this.m_native);
        }

        public void setLcol(int lcol) {
            setLcol(PointInfo.this.m_native, lcol);
        }

        public int getLcol() {
            return getLcol(PointInfo.this.m_native);
        }

        public void setPenw(float penw) {
            setPenw(PointInfo.this.m_native, penw);
        }

        public float getPenw() {
            return getPenw(PointInfo.this.m_native);
        }

        public void setFflag(byte fflag) {
            setFflag(PointInfo.this.m_native, fflag);
        }

        public byte getFflag() {
            return getFflag(PointInfo.this.m_native);
        }
    }

    public class ArcInfo {
        private native float getBegang(int i);

        private native float getEndang(int i);

        private native float getPenw(int i);

        private native double getRadiu(int i);

        private native void setBegang(int i, float f);

        private native void setEndang(int i, float f);

        private native void setPenw(int i, float f);

        private native void setRadiu(int i, double d);

        private ArcInfo() {
        }

        public void setRadiu(double radiu) {
            setRadiu(PointInfo.this.m_native, radiu);
        }

        public double getRadiu() {
            return getRadiu(PointInfo.this.m_native);
        }

        public void setBegang(float begang) {
            setBegang(PointInfo.this.m_native, begang);
        }

        public float getBegang() {
            return getBegang(PointInfo.this.m_native);
        }

        public void setEndang(float endang) {
            setEndang(PointInfo.this.m_native, endang);
        }

        public float getEndang() {
            return getEndang(PointInfo.this.m_native);
        }

        public void setPenw(float penw) {
            setPenw(PointInfo.this.m_native, penw);
        }

        public float getPenw() {
            return getPenw(PointInfo.this.m_native);
        }
    }

    public class ImageInfo {
        private native float getAngle(int i);

        private native double getHeight(int i);

        private native double getWidth(int i);

        private native void setAngle(int i, float f);

        private native void setHeight(int i, double d);

        private native void setWidth(int i, double d);

        private ImageInfo() {
        }

        public void setWidth(double width) {
            setWidth(PointInfo.this.m_native, width);
        }

        public double getWidth() {
            return getWidth(PointInfo.this.m_native);
        }

        public void setHeight(double height) {
            setHeight(PointInfo.this.m_native, height);
        }

        public double getHeight() {
            return getHeight(PointInfo.this.m_native);
        }

        public void setAngle(float angle) {
            setAngle(PointInfo.this.m_native, angle);
        }

        public float getAngle() {
            return getAngle(PointInfo.this.m_native);
        }
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public GeoClassType getGeometryType() {
        return GeoClassType.POINT;
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public byte[] getBytes() {
        return getBytes(this.m_native);
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public void setBytes(byte[] bytes) {
        setBytes(this.m_native, bytes);
    }
}
