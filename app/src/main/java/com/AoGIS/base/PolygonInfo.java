package com.AoGIS.base;

import com.AoGIS.GeoDBException;
import com.AoGIS.INative;
import com.AoGIS.geometry.GeoClassType;

/* loaded from: classes.dex */
public class PolygonInfo implements INative, IGeometryInfo {
    private boolean m_bSelfFinalize;
    private int m_native;

    private native float getAngle(int i);

    private native short getBasLN(int i);

    private native byte[] getBytes(int i);

    private native int getClr(int i);

    private native byte getFmode(int i);

    private native short getLayer(int i);

    private native int getPatclr(int i);

    private native float getPathei(int i);

    private native short getPatno(int i);

    private native float getPatwid(int i);

    private native void getRect(int i, int i2);

    private native int getRes1(int i);

    private native int nativeCopyInit(int i);

    private native void nativeFinalize(int i);

    private native int nativeInit();

    private native void setAngle(int i, float f);

    private native void setBasLN(int i, short s);

    private native void setBytes(int i, byte[] bArr);

    private native void setClr(int i, int i2);

    private native void setFmode(int i, byte b);

    private native void setLayer(int i, short s);

    private native void setPatclr(int i, int i2);

    private native void setPathei(int i, float f);

    private native void setPatno(int i, short s);

    private native void setPatwid(int i, float f);

    private native void setRect(int i, int i2);

    private native void setRes1(int i, int i2);

    public PolygonInfo() {
        this.m_bSelfFinalize = true;
        this.m_native = nativeInit();
    }

    private PolygonInfo(int Native) {
        this.m_bSelfFinalize = true;
        this.m_native = Native;
    }

    protected void finalize() throws Throwable {
        if (this.m_bSelfFinalize) {
            nativeFinalize(this.m_native);
        }
        super.finalize();
    }

    @Override // com.AoGIS.INative
    public int getNativeHandle() {
        return this.m_native;
    }

    public static PolygonInfo createFromNativeHandle(int Native) {
        PolygonInfo rinf = new PolygonInfo(Native);
        rinf.m_bSelfFinalize = false;
        return rinf;
    }

    @Override // com.AoGIS.base.IGeometryInfo
    /* renamed from: clone, reason: merged with bridge method [inline-methods] */
    public PolygonInfo m8clone() {
        try {
            PolygonInfo info = (PolygonInfo) super.clone();
            info.m_native = nativeCopyInit(this.m_native);
            return info;
        } catch (CloneNotSupportedException e) {
            throw new GeoDBException("ERROR: Info clone Failed");
        }
    }

    public void setClr(int clr) {
        setClr(this.m_native, clr);
    }

    public int getClr() {
        return getClr(this.m_native);
    }

    public void setPatno(short patno) {
        setPatno(this.m_native, patno);
    }

    public short getPatno() {
        return getPatno(this.m_native);
    }

    public void setPathei(float pathei) {
        setPathei(this.m_native, pathei);
    }

    public float getPathei() {
        return getPathei(this.m_native);
    }

    public void setPatwid(float patwid) {
        setPatwid(this.m_native, patwid);
    }

    public float getPatwid() {
        return getPatwid(this.m_native);
    }

    public void setPatclr(int patclr) {
        setPatclr(this.m_native, patclr);
    }

    public int getPatclr() {
        return getPatclr(this.m_native);
    }

    public void setBasLN(short basLN) {
        setBasLN(this.m_native, basLN);
    }

    public short getBasLN() {
        return getBasLN(this.m_native);
    }

    public void setFmode(byte fmode) {
        setFmode(this.m_native, fmode);
    }

    public byte getFmode() {
        return getFmode(this.m_native);
    }

    public void setLayer(short layer) {
        setLayer(this.m_native, layer);
    }

    public short getLayer() {
        return getLayer(this.m_native);
    }

    public void setAngle(float angle) {
        setAngle(this.m_native, angle);
    }

    public float getAngle() {
        return getAngle(this.m_native);
    }

    public void setRes1(int res1) {
        setRes1(this.m_native, res1);
    }

    public int getRes1() {
        return getRes1(this.m_native);
    }

    public void setRect(GeoRect rect) {
        setRect(this.m_native, rect.getNativeHandle());
    }

    public GeoRect getRect() {
        GeoRect rect = new GeoRect();
        getRect(this.m_native, rect.getNativeHandle());
        return rect;
    }

    public void getRect(GeoRect rect) {
        if (rect == null) {
            throw new NullPointerException("Passed null pointer : PointInfo.getRect");
        }
        getRect(this.m_native, rect.getNativeHandle());
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public GeoClassType getGeometryType() {
        return GeoClassType.POLYGON;
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public byte[] getBytes() {
        return getBytes(this.m_native);
    }

    @Override // com.AoGIS.base.IGeometryInfo
    public void setBytes(byte[] bytes) {
        setBytes(this.m_native, bytes);
    }
}
