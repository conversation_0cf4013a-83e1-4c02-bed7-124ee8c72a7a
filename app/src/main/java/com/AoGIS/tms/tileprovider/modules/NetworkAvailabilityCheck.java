package com.AoGIS.tms.tileprovider.modules;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

/* loaded from: classes.dex */
public class NetworkAvailabilityCheck {
    private final ConnectivityManager mConnectionManager;

    public NetworkAvailabilityCheck(Context aContext) {
        this.mConnectionManager = (ConnectivityManager) aContext.getSystemService("connectivity");
    }

    public boolean getNetworkAvailable() {
        NetworkInfo networkInfo = this.mConnectionManager.getActiveNetworkInfo();
        return networkInfo != null && networkInfo.isAvailable();
    }
}
