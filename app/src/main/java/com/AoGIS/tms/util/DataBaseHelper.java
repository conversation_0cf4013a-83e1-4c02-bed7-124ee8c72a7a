package com.AoGIS.tms.util;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

/* loaded from: classes.dex */
public class DataBaseHelper extends SQLiteOpenHelper {
    private SQLiteDatabase db;
    private String mDBName;

    DataBaseHelper(Context c, String dbName) {
        super(c, dbName, (SQLiteDatabase.CursorFactory) null, 2);
        this.mDBName = "GeoMaps.db";
        this.mDBName = dbName;
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onCreate(SQLiteDatabase db) {
        this.db = db;
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
    }

    public String getDBName() {
        return this.mDBName;
    }
}
