package com.AoGIS.tms.util;

import android.content.Context;
import android.graphics.Canvas;
import android.os.Handler;
import com.AoGIS.tms.cache.CacheableBitmapDrawable;
import com.AoGIS.tms.tileprovider.MapTile;
import com.AoGIS.tms.tileprovider.MapTileLayerBase;
import com.AoGIS.tms.tileprovider.modules.MapTileModuleLayerBase;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public abstract class MoudleLayerLooper {
    protected List<CacheableBitmapDrawable> mBeingUsedDrawables = new ArrayList();
    protected Context mContext;

    public abstract boolean handleTile(Canvas canvas, MapTileModuleLayerBase mapTileModuleLayerBase, MapTile mapTile);

    public abstract void initializeLoop();

    public MoudleLayerLooper(Context context) {
        this.mContext = context;
    }

    public final int loop(<PERSON>vas pCanvas, MapTileLayerBase pTileProvider, MapTile pTile) {
        int iHasTileInMemory = 0;
        for (MapTileModuleLayerBase tileModuleLayer : pTileProvider.getModuleLayerList()) {
            if (!tileModuleLayer.getCacheKey().equals(pTile.getKey())) {
                pTile.setKey(tileModuleLayer.getCacheKey());
            }
            if (handleTile(pCanvas, tileModuleLayer, pTile)) {
                iHasTileInMemory++;
            }
        }
        finalizeLoop();
        return iHasTileInMemory;
    }

    public void finalizeLoop() {
        new Handler(this.mContext.getMainLooper()).postDelayed(new Runnable() { // from class: com.AoGIS.tms.util.MoudleLayerLooper.1
            @Override // java.lang.Runnable
            public void run() {
                for (CacheableBitmapDrawable drawable : MoudleLayerLooper.this.mBeingUsedDrawables) {
                    drawable.setBeingUsed(false);
                }
                MoudleLayerLooper.this.mBeingUsedDrawables.clear();
            }
        }, 1L);
    }
}
