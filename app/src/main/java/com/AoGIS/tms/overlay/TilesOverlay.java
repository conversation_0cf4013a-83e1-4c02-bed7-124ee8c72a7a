package com.AoGIS.tms.overlay;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.support.v4.internal.view.SupportMenu;
import android.util.Log;
import com.AoGIS.render.AoSurfaceView;
import com.AoGIS.tms.cache.CacheableBitmapDrawable;
import com.AoGIS.tms.math.TMSMath;
import com.AoGIS.tms.tileprovider.MapTile;
import com.AoGIS.tms.tileprovider.MapTileLayerBase;
import com.AoGIS.tms.tileprovider.modules.MapTileModuleLayerBase;
import com.AoGIS.tms.util.MoudleLayerLooper;
import com.AoGIS.tms.util.TileLooper;
import java.util.List;

/* loaded from: classes.dex */
public class TilesOverlay {
    private static final String TAG = "TilesOverlay";
    protected static Paint mDebugPaint = null;
    protected Context mContext;
    private double mCurMapXmax;
    private double mCurMapXmin;
    private double mCurMapYmax;
    private double mCurMapYmin;
    private int mCurTileXmax;
    private int mCurTileXmin;
    private int mCurTileYmax;
    private int mCurTileYmin;
    private int mCurZoomLevel;
    private MoudleLayerLooper mModuleLayerLooper;
    private int mNuberOfTiles;
    private double mResolution;
    private TileLooper mTileLooper;
    protected final MapTileLayerBase mTileProvider;
    private boolean isAnimating = false;
    private boolean mGridDispFlag = false;

    public TilesOverlay(Context context, MapTileLayerBase aTileProvider) {
        this.mNuberOfTiles = 0;
        this.mTileLooper = null;
        this.mModuleLayerLooper = null;
        if (aTileProvider == null) {
            throw new IllegalArgumentException("You must pass a valid tile provider to the tiles overlay.");
        }
        this.mTileProvider = aTileProvider;
        if (this.mGridDispFlag) {
            getDebugPaint();
        }
        this.mContext = context;
        this.mNuberOfTiles = 0;
        this.mTileLooper = new GeoTileLooper(context);
        this.mModuleLayerLooper = new GeoMoudleLayerLooper(context);
    }

    public static Paint getDebugPaint() {
        if (mDebugPaint == null) {
            mDebugPaint = new Paint();
            mDebugPaint.setAntiAlias(true);
            mDebugPaint.setFilterBitmap(true);
            mDebugPaint.setColor(SupportMenu.CATEGORY_MASK);
            mDebugPaint.setStyle(Paint.Style.STROKE);
        }
        return mDebugPaint;
    }

    public void setGridDispFlag(boolean bFlg) {
        this.mGridDispFlag = bFlg;
    }

    public boolean getGridDispFlag() {
        return this.mGridDispFlag;
    }

    public void onDetach(AoSurfaceView pMapView) {
        this.mTileProvider.detach();
    }

    public void draw(Canvas pCanvas, double dResolution, double dxmin, double dymin, double dxmax, double dymax) {
        TMSMath pTMSMath = this.mTileProvider.getTMSMath();
        if (pTMSMath != null) {
            this.mResolution = dResolution;
            this.mCurMapXmin = dxmin;
            this.mCurMapYmin = dymin;
            this.mCurMapXmax = dxmax;
            this.mCurMapYmax = dymax;
            int iZoom = pTMSMath.getZoomLevel(dResolution);
            int iMaxLevel = (int) this.mTileProvider.getMaximumZoomLevel();
            if (iZoom > iMaxLevel) {
                iZoom = iMaxLevel;
            }
            int[] TileXYmin = pTMSMath.getTileXYByLB(dxmin, dymin, iZoom);
            int[] TileXYmax = pTMSMath.getTileXYByLB(dxmax, dymax, iZoom);
            if (TileXYmin[0] < 0) {
                TileXYmin[0] = 0;
            }
            if (TileXYmin[1] < 0) {
                TileXYmin[1] = 0;
            }
            int iMaxTile = (int) Math.pow(2.0d, iZoom);
            if (TileXYmax[0] >= iMaxTile) {
                TileXYmax[0] = iMaxTile - 1;
            }
            if (TileXYmax[1] >= iMaxTile) {
                TileXYmax[1] = iMaxTile - 1;
            }
            if (TileXYmax[0] - TileXYmin[0] > 20 || TileXYmax[1] - TileXYmin[1] > 15) {
                this.mCurZoomLevel = -1;
                this.mCurTileYmax = -1;
                this.mCurTileXmax = -1;
                this.mCurTileYmin = -1;
                this.mCurTileXmin = -1;
                return;
            }
            this.mCurZoomLevel = iZoom;
            this.mCurTileXmin = TileXYmin[0];
            this.mCurTileYmin = TileXYmin[1];
            this.mCurTileXmax = TileXYmax[0];
            this.mCurTileYmax = TileXYmax[1];
            List<MapTileModuleLayerBase> pModuleLayerList = this.mTileProvider.getModuleLayerList();
            this.mNuberOfTiles = ((this.mCurTileXmax - this.mCurTileXmin) + 1) * ((this.mCurTileYmax - this.mCurTileYmin) + 1) * pModuleLayerList.size();
            this.mTileLooper.loop(pCanvas, pTMSMath, pModuleLayerList, this.mCurZoomLevel, this.mCurTileXmin, this.mCurTileYmin, this.mCurTileXmax, this.mCurTileYmax);
        }
    }

    public boolean drawTile(Canvas pCanvas, MapTile pTile) {
        if (pTile.getZ() != this.mCurZoomLevel || pTile.getX() < this.mCurTileXmin || pTile.getX() > this.mCurTileXmax || pTile.getY() < this.mCurTileYmin || pTile.getY() > this.mCurTileYmax || !isValideKey(pTile)) {
            return false;
        }
        this.mModuleLayerLooper.loop(pCanvas, this.mTileProvider, pTile);
        return true;
    }

    private boolean isValideKey(MapTile pTile) {
        for (MapTileModuleLayerBase tileModuleLayer : this.mTileProvider.getModuleLayerList()) {
            if (tileModuleLayer.getCacheKey().equals(pTile.getKey())) {
                return true;
            }
        }
        return false;
    }

    private class GeoTileLooper extends TileLooper {
        public GeoTileLooper(Context context) {
            super(context);
        }

        @Override // com.AoGIS.tms.util.TileLooper
        public void initializeLoop() {
        }

        @Override // com.AoGIS.tms.util.TileLooper
        public boolean handleTile(Canvas pCanvas, MapTileModuleLayerBase pModuleLayer, MapTile pTile) {
            boolean rtn;
            Rect rc = TilesOverlay.this.mTileProvider.getTMSMath().getTileDrawRect(pTile.getX(), pTile.getY(), pTile.getZ(), TilesOverlay.this.mCurMapXmin, TilesOverlay.this.mCurMapYmin, TilesOverlay.this.mCurMapXmax, TilesOverlay.this.mCurMapYmax, TilesOverlay.this.mResolution);
            Drawable drawable = TilesOverlay.this.mTileProvider.getMapTile(pTile, pModuleLayer, !TilesOverlay.this.isAnimating);
            boolean isReusable = drawable instanceof CacheableBitmapDrawable;
            Log.d(TilesOverlay.TAG, "handleTile tile should have been drawn to canvas tile = '" + pTile.getCacheKey());
            if (drawable == null) {
                TilesOverlay.this.mTileProvider.memoryCacheNeedsMoreMemory(TilesOverlay.this.mNuberOfTiles);
                rtn = false;
            } else {
                if (isReusable) {
                    this.mBeingUsedDrawables.add((CacheableBitmapDrawable) drawable);
                }
                drawable.setBounds(rc);
                drawable.draw(pCanvas);
                Log.d(TilesOverlay.TAG, "handleTile have been drawn to canvas tile = '" + pTile.getCacheKey());
                rtn = true;
            }
            if (TilesOverlay.this.mGridDispFlag) {
                pCanvas.drawText(pTile.toString(), rc.left + 1, rc.top + TilesOverlay.getDebugPaint().getTextSize(), TilesOverlay.getDebugPaint());
                pCanvas.drawRect(rc, TilesOverlay.getDebugPaint());
            }
            return rtn;
        }
    }

    private class GeoMoudleLayerLooper extends MoudleLayerLooper {
        public GeoMoudleLayerLooper(Context context) {
            super(context);
        }

        @Override // com.AoGIS.tms.util.MoudleLayerLooper
        public void initializeLoop() {
        }

        @Override // com.AoGIS.tms.util.MoudleLayerLooper
        public boolean handleTile(Canvas pCanvas, MapTileModuleLayerBase pModuleLayer, MapTile pTile) {
            boolean rtn;
            Rect rc = TilesOverlay.this.mTileProvider.getTMSMath().getTileDrawRect(pTile.getX(), pTile.getY(), pTile.getZ(), TilesOverlay.this.mCurMapXmin, TilesOverlay.this.mCurMapYmin, TilesOverlay.this.mCurMapXmax, TilesOverlay.this.mCurMapYmax, TilesOverlay.this.mResolution);
            Drawable drawable = TilesOverlay.this.mTileProvider.getTileFromMemoryCache(pTile);
            boolean isReusable = drawable instanceof CacheableBitmapDrawable;
            Log.d(TilesOverlay.TAG, "handTile drawtile should have been drawn to canvas tile = '" + pTile.getCacheKey());
            if (drawable != null) {
                if (isReusable) {
                    this.mBeingUsedDrawables.add((CacheableBitmapDrawable) drawable);
                }
                drawable.setBounds(rc);
                drawable.draw(pCanvas);
                Log.d(TilesOverlay.TAG, "handleTile have been drawn to canvas tile = '" + pTile.getCacheKey());
                rtn = true;
            } else {
                rtn = false;
            }
            if (TilesOverlay.this.mGridDispFlag) {
                pCanvas.drawText(pTile.toString(), rc.left + 1, rc.top + TilesOverlay.getDebugPaint().getTextSize(), TilesOverlay.getDebugPaint());
                pCanvas.drawRect(rc, TilesOverlay.getDebugPaint());
            }
            return rtn;
        }
    }
}
