package com.AoGIS.tms.math;

import com.AoGIS.tms.constants.GeoConstants;

/* loaded from: classes.dex */
public class LonLatMath extends TMSMath implements GeoConstants {
    private double m_dOriginShift = 180.0d;
    private double m_dInitialRes = 360.0d / mTileSize;

    @Override // com.AoGIS.tms.math.TMSMath
    public double getResolution(int izoom) {
        return this.m_dInitialRes / Math.pow(2.0d, izoom);
    }

    @Override // com.AoGIS.tms.math.TMSMath
    public int getZoomLevel(double dRes) {
        double dZoom = Math.log(this.m_dInitialRes / dRes) / Math.log(2.0d);
        return (int) (0.5d + dZoom);
    }

    @Override // com.AoGIS.tms.math.TMSMath
    public int[] getTileXYByLB(double dmx, double dmy, int izoom) {
        return MetersToTile(dmx, dmy, izoom);
    }

    @Override // com.AoGIS.tms.math.TMSMath
    public int getTileYByLT(int ity, int izoom) {
        return ((((int) Math.pow(2.0d, izoom)) / 2) - 1) - ity;
    }

    @Override // com.AoGIS.tms.math.TMSMath
    public double[] getTileRect(int itx, int ity, int izoom) {
        return TileBounds(itx, ity, izoom);
    }

    public double[] PixelsToMeters(double dpx, double dpy, int izoom) {
        double res = getResolution(izoom);
        double[] dPos = {(dpx * res) - this.m_dOriginShift, (dpy * res) - (this.m_dOriginShift / 2.0d)};
        return dPos;
    }

    public double[] MetersToPixels(double dmx, double dmy, int izoom) {
        double res = getResolution(izoom);
        double[] dPos = {(this.m_dOriginShift + dmx) / res, ((this.m_dOriginShift / 2.0d) + dmy) / res};
        return dPos;
    }

    public int[] PixelsToTile(double dpx, double dpy) {
        int[] iPos = {(int) Math.floor(dpx / mTileSize), (int) Math.floor(dpy / mTileSize)};
        return iPos;
    }

    public int[] MetersToTile(double dmx, double dmy, int izoom) {
        double[] dPos = MetersToPixels(dmx, dmy, izoom);
        return PixelsToTile(dPos[0], dPos[1]);
    }

    public double[] TileBounds(int itx, int ity, int izoom) {
        double[] dPosLB = PixelsToMeters(itx * mTileSize, ity * mTileSize, izoom);
        double[] dPosRT = PixelsToMeters((itx + 1) * mTileSize, (ity + 1) * mTileSize, izoom);
        double[] dRect = {dPosLB[0], dPosLB[1], dPosRT[0], dPosRT[1]};
        return dRect;
    }
}
