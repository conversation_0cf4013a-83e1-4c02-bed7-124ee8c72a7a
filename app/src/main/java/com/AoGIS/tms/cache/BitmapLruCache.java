package com.AoGIS.tms.cache;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Looper;
import android.os.Process;
import android.util.Log;
import com.jakewharton.disklrucache.DiskLruCache;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/* loaded from: classes.dex */
public class BitmapLruCache {
    static final int DISK_CACHE_FLUSH_DELAY_SECS = 5;
    private DiskLruCache mDiskCache;
    private HashMap<String, ReentrantLock> mDiskCacheEditLocks;
    private ScheduledThreadPoolExecutor mDiskCacheFlusherExecutor;
    private DiskCacheFlushRunnable mDiskCacheFlusherRunnable;
    private ScheduledFuture<?> mDiskCacheFuture;
    private BitmapMemoryLruCache mMemoryCache;
    private RecyclePolicy mRecyclePolicy;
    private Resources mResources;
    private File mTempDir;

    public interface InputStreamProvider {
        InputStream getInputStream();
    }

    public enum RecyclePolicy {
        DISABLED,
        PRE_HONEYCOMB_ONLY,
        ALWAYS;

        boolean canInBitmap() {
            switch (this) {
                case PRE_HONEYCOMB_ONLY:
                case DISABLED:
                    if (Build.VERSION.SDK_INT >= 11) {
                    }
                    break;
            }
            return false;
        }

        boolean canRecycle() {
            switch (this) {
                case PRE_HONEYCOMB_ONLY:
                    return Build.VERSION.SDK_INT < 11;
                case DISABLED:
                default:
                    return false;
                case ALWAYS:
                    return true;
            }
        }
    }

    private static void checkNotOnMainThread() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw new IllegalStateException("This method should not be called from the main/UI thread.");
        }
    }

    private static String transformUrlForDiskCacheKey(String url) {
        return Md5.encode(url);
    }

    BitmapLruCache(Context context) {
        if (context != null) {
            Context context2 = context.getApplicationContext();
            this.mTempDir = context2.getCacheDir();
            this.mResources = context2.getResources();
        }
    }

    public boolean contains(String url) {
        return containsInMemoryCache(url) || containsInDiskCache(url);
    }

    public boolean containsInDiskCache(String url) {
        if (this.mDiskCache == null) {
            return false;
        }
        checkNotOnMainThread();
        try {
            return this.mDiskCache.get(transformUrlForDiskCacheKey(url)) != null;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean containsInMemoryCache(String url) {
        return (this.mMemoryCache == null || this.mMemoryCache.get(url) == null) ? false : true;
    }

    public CacheableBitmapDrawable get(String url) {
        return get(url, null);
    }

    public CacheableBitmapDrawable get(String url, BitmapFactory.Options decodeOpts) {
        CacheableBitmapDrawable result = getFromMemoryCache(url);
        if (result == null) {
            return getFromDiskCache(url, decodeOpts);
        }
        return result;
    }

    public CacheableBitmapDrawable getFromDiskCache(String url, BitmapFactory.Options decodeOpts) {
        CacheableBitmapDrawable result = null;
        if (this.mDiskCache != null) {
            checkNotOnMainThread();
            try {
                String key = transformUrlForDiskCacheKey(url);
                result = decodeBitmapToDrawable(new SnapshotInputStreamProvider(key), url, decodeOpts);
                if (result != null) {
                    if (this.mMemoryCache != null) {
                        this.mMemoryCache.put(result);
                    }
                } else {
                    this.mDiskCache.remove(key);
                    scheduleDiskCacheFlush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public CacheableBitmapDrawable getFromMemoryCache(String url) {
        CacheableBitmapDrawable result = null;
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                result = this.mMemoryCache.get(url);
                if (result != null && !result.isBitmapValid()) {
                    this.mMemoryCache.remove(url);
                    result = null;
                }
            }
        }
        return result;
    }

    public Bitmap getBitmapFromRemoved(int width, int height) {
        Bitmap bitmapFromRemoved;
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                bitmapFromRemoved = this.mMemoryCache.getBitmapFromRemoved(width, height);
            }
            return bitmapFromRemoved;
        }
        return null;
    }

    public boolean isDiskCacheEnabled() {
        return this.mDiskCache != null;
    }

    public boolean isMemoryCacheEnabled() {
        return this.mMemoryCache != null;
    }

    public CacheableBitmapDrawable put(String url, Bitmap bitmap) {
        return put(url, bitmap, Bitmap.CompressFormat.PNG, 100);
    }

    public CacheableBitmapDrawable put(String url, Bitmap bitmap, Bitmap.CompressFormat compressFormat, int compressQuality) {
        Log.d("BitmapLruCache", "put" + url);
        CacheableBitmapDrawable d = new CacheableBitmapDrawable(url, this.mResources, bitmap, this.mRecyclePolicy, -1);
        putInMemoryCache(url, d, compressFormat, compressQuality);
        putInDiskCache(url, d, compressFormat, compressQuality);
        Log.d("BitmapLruCache", "put" + url + d);
        return d;
    }

    public CacheableBitmapDrawable putInMemoryCache(String url, Bitmap bitmap) {
        return putInMemoryCache(url, bitmap, Bitmap.CompressFormat.PNG, 100);
    }

    public CacheableBitmapDrawable putInMemoryCache(String url, CacheableBitmapDrawable drawable) {
        return putInMemoryCache(url, drawable, Bitmap.CompressFormat.PNG, 100);
    }

    public CacheableBitmapDrawable putInMemoryCache(String url, CacheableBitmapDrawable drawable, Bitmap.CompressFormat compressFormat, int compressQuality) {
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                this.mMemoryCache.put(drawable);
            }
        }
        return drawable;
    }

    public CacheableBitmapDrawable putInMemoryCache(String url, Bitmap bitmap, Bitmap.CompressFormat compressFormat, int compressQuality) {
        CacheableBitmapDrawable d = new CacheableBitmapDrawable(url, this.mResources, bitmap, this.mRecyclePolicy, -1);
        return putInMemoryCache(url, d, compressFormat, compressQuality);
    }

    public CacheableBitmapDrawable putInDiskCache(String url, Bitmap bitmap) {
        return putInDiskCache(url, bitmap, Bitmap.CompressFormat.PNG, 100);
    }

    public CacheableBitmapDrawable putInDiskCache(String url, CacheableBitmapDrawable drawable) {
        return putInDiskCache(url, drawable, Bitmap.CompressFormat.PNG, 100);
    }

    public CacheableBitmapDrawable putInDiskCache(String url, CacheableBitmapDrawable drawable, Bitmap.CompressFormat compressFormat, int compressQuality) {
        if (this.mDiskCache != null) {
            checkNotOnMainThread();
            String key = transformUrlForDiskCacheKey(url);
            ReentrantLock lock = getLockForDiskCacheEdit(key);
            lock.lock();
            OutputStream os = null;
            try {
                DiskLruCache.Editor editor = this.mDiskCache.edit(key);
                os = editor.newOutputStream(0);
                drawable.getBitmap().compress(compressFormat, compressQuality, os);
                os.flush();
                editor.commit();
            } catch (IOException e) {
                Log.e(Constants.LOG_TAG, "Error while writing to disk cache", e);
            } finally {
                IoUtils.closeStream(os);
                lock.unlock();
                scheduleDiskCacheFlush();
            }
        }
        return drawable;
    }

    public CacheableBitmapDrawable putInDiskCache(String url, Bitmap bitmap, Bitmap.CompressFormat compressFormat, int compressQuality) {
        CacheableBitmapDrawable d = new CacheableBitmapDrawable(url, this.mResources, bitmap, this.mRecyclePolicy, -1);
        return putInDiskCache(url, d, compressFormat, compressQuality);
    }

    public CacheableBitmapDrawable put(String url, InputStream inputStream) {
        return put(url, inputStream, (BitmapFactory.Options) null);
    }

    public CacheableBitmapDrawable put(String url, byte[] data, BitmapFactory.Options decodeOpts) {
        CacheableBitmapDrawable d;
        checkNotOnMainThread();
        if (this.mDiskCache != null || (d = decodeBitmapToDrawable(new ByteArrayInputStreamProvider(data), url, decodeOpts)) == null) {
            return put(url, new ByteArrayInputStream(data), decodeOpts);
        }
        if (this.mMemoryCache != null) {
            d.setCached(true);
            this.mMemoryCache.put(d.getUrl(), d);
            return d;
        }
        return d;
    }

    public CacheableBitmapDrawable put(String url, InputStream inputStream, BitmapFactory.Options decodeOpts) {
        CacheableBitmapDrawable d = null;
        if (inputStream != null) {
            checkNotOnMainThread();
            File tmpFile = null;
            try {
                tmpFile = File.createTempFile("bitmapcache_", null, this.mTempDir);
                IoUtils.copy(inputStream, tmpFile);
            } catch (IOException e) {
                Log.e(Constants.LOG_TAG, "Error writing to saving stream to temp file: " + url, e);
            }
            d = null;
            if (tmpFile != null) {
                d = decodeBitmapToDrawable(new FileInputStreamProvider(tmpFile), url, decodeOpts);
                if (d != null) {
                    if (this.mMemoryCache != null) {
                        d.setCached(true);
                        synchronized (this.mMemoryCache) {
                            this.mMemoryCache.put(d.getUrl(), d);
                        }
                    }
                    if (this.mDiskCache != null) {
                        String key = transformUrlForDiskCacheKey(url);
                        ReentrantLock lock = getLockForDiskCacheEdit(url);
                        lock.lock();
                        try {
                            DiskLruCache.Editor editor = this.mDiskCache.edit(key);
                            IoUtils.copy(tmpFile, editor.newOutputStream(0));
                            editor.commit();
                        } catch (IOException e2) {
                            Log.e(Constants.LOG_TAG, "Error writing to disk cache. URL: " + url, e2);
                        } finally {
                            lock.unlock();
                            scheduleDiskCacheFlush();
                        }
                    }
                }
                tmpFile.delete();
            }
        }
        return d;
    }

    public void remove(String url) {
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                this.mMemoryCache.remove(url);
            }
        }
        if (this.mDiskCache != null) {
            checkNotOnMainThread();
            try {
                this.mDiskCache.remove(transformUrlForDiskCacheKey(url));
                scheduleDiskCacheFlush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void removeFromMemoryCache(String url) {
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                this.mMemoryCache.remove(url);
            }
        }
    }

    public void removeFromDiskCache(String url) {
        if (this.mDiskCache != null) {
            checkNotOnMainThread();
            try {
                this.mDiskCache.remove(transformUrlForDiskCacheKey(url));
                scheduleDiskCacheFlush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void resizeMemoryForTiles(int numberOfTiles) {
        this.mMemoryCache.resizeMemoryForTiles(numberOfTiles);
    }

    public void trimMemory() {
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                this.mMemoryCache.trimMemory();
            }
        }
    }

    public void purgeMemoryCache() {
        if (this.mMemoryCache != null) {
            synchronized (this.mMemoryCache) {
                this.mMemoryCache.evictAll();
            }
        }
    }

    public void purgeDiskCache() {
        if (this.mDiskCache != null) {
            checkNotOnMainThread();
            try {
                this.mDiskCache.delete();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    synchronized void setDiskCache(DiskLruCache diskCache) {
        this.mDiskCache = diskCache;
        if (diskCache != null) {
            this.mDiskCacheEditLocks = new HashMap<>();
            this.mDiskCacheFlusherExecutor = new ScheduledThreadPoolExecutor(1);
            this.mDiskCacheFlusherRunnable = new DiskCacheFlushRunnable(diskCache);
        }
    }

    void setMemoryCache(BitmapMemoryLruCache memoryCache) {
        this.mMemoryCache = memoryCache;
        this.mRecyclePolicy = memoryCache.getRecyclePolicy();
    }

    private ReentrantLock getLockForDiskCacheEdit(String url) {
        ReentrantLock lock;
        synchronized (this.mDiskCacheEditLocks) {
            lock = this.mDiskCacheEditLocks.get(url);
            if (lock == null) {
                lock = new ReentrantLock();
                this.mDiskCacheEditLocks.put(url, lock);
            }
        }
        return lock;
    }

    private void scheduleDiskCacheFlush() {
        if (this.mDiskCacheFuture != null) {
            this.mDiskCacheFuture.cancel(false);
        }
        this.mDiskCacheFuture = this.mDiskCacheFlusherExecutor.schedule(this.mDiskCacheFlusherRunnable, 5L, TimeUnit.SECONDS);
    }

    public CacheableBitmapDrawable createCacheableBitmapDrawable(Bitmap bitmap, String url, int source) {
        if (bitmap != null) {
            return new CacheableBitmapDrawable(url, this.mResources, bitmap, this.mRecyclePolicy, source);
        }
        return null;
    }

    private CacheableBitmapDrawable decodeBitmapToDrawable(InputStreamProvider ip, String url, BitmapFactory.Options opts) {
        AtomicInteger source = new AtomicInteger(0);
        Bitmap result = decodeBitmap(ip, opts, source);
        return createCacheableBitmapDrawable(result, url, source.get());
    }

    public Bitmap decodeBitmap(InputStreamProvider ip, BitmapFactory.Options opts) {
        return decodeBitmap(ip, opts, null);
    }

    public Bitmap decodeBitmap(InputStreamProvider ip, BitmapFactory.Options opts, AtomicInteger source) {
        Bitmap bm = null;
        InputStream is = null;
        if (source != null) {
            source.set(0);
        }
        try {
            if (this.mRecyclePolicy.canInBitmap()) {
                if (opts == null) {
                    opts = new BitmapFactory.Options();
                }
                if (opts.inSampleSize <= 1) {
                    opts.inSampleSize = 1;
                    if (addInBitmapOptions(ip, opts) && source != null) {
                        source.set(1);
                    }
                }
            }
            is = ip.getInputStream();
            if (is == null && (ip instanceof ByteArrayInputStreamProvider)) {
                byte[] data = ((ByteArrayInputStreamProvider) ip).array;
                bm = BitmapFactory.decodeByteArray(data, 0, data.length, opts);
            } else {
                bm = BitmapFactory.decodeStream(is, null, opts);
            }
        } catch (Exception e) {
            Log.e(Constants.LOG_TAG, "Unable to decode stream", e);
        } finally {
            IoUtils.closeStream(is);
        }
        return bm;
    }

    private boolean addInBitmapOptions(InputStreamProvider ip, BitmapFactory.Options opts) {
        InputStream is = ip.getInputStream();
        opts.inJustDecodeBounds = true;
        if (is == null && (ip instanceof ByteArrayInputStreamProvider)) {
            byte[] data = ((ByteArrayInputStreamProvider) ip).array;
            BitmapFactory.decodeByteArray(data, 0, data.length, opts);
        } else {
            BitmapFactory.decodeStream(is, null, opts);
        }
        IoUtils.closeStream(is);
        opts.inJustDecodeBounds = false;
        opts.inMutable = true;
        synchronized (this.mMemoryCache) {
            Bitmap reusableBm = this.mMemoryCache.getBitmapFromRemoved(opts.outWidth, opts.outHeight);
            if (reusableBm == null) {
                return false;
            }
            if (Constants.DEBUG) {
                Log.i(Constants.LOG_TAG, "Using inBitmap");
            }
            SDK11.addInBitmapOption(opts, reusableBm);
            return true;
        }
    }

    public static final class Builder {
        static final int DEFAULT_DISK_CACHE_MAX_SIZE_MB = 10;
        static final float DEFAULT_MEMORY_CACHE_HEAP_PERCENTAGE = 12.5f;
        static final float DEFAULT_MEMORY_CACHE_HEAP_RATIO = 0.125f;
        static final int DEFAULT_MEM_CACHE_MAX_SIZE_MB = 3;
        static final RecyclePolicy DEFAULT_RECYCLE_POLICY = RecyclePolicy.PRE_HONEYCOMB_ONLY;
        static final float MAX_MEMORY_CACHE_HEAP_PERCENTAGE = 75.0f;
        static final float MAX_MEMORY_CACHE_HEAP_RATIO = 0.75f;
        static final int MEGABYTE = 1048576;
        private Context mContext;
        private boolean mDiskCacheEnabled;
        private File mDiskCacheLocation;
        private long mDiskCacheMaxSize;
        private boolean mMemoryCacheEnabled;
        private int mMemoryCacheMaxSize;
        private RecyclePolicy mRecyclePolicy;

        private static long getHeapSize() {
            return Runtime.getRuntime().maxMemory();
        }

        public Builder() {
            this(null);
        }

        public Builder(Context context) {
            this.mContext = context;
            this.mDiskCacheMaxSize = 10485760L;
            this.mMemoryCacheEnabled = true;
            this.mMemoryCacheMaxSize = 3145728;
            this.mRecyclePolicy = DEFAULT_RECYCLE_POLICY;
        }

        /* JADX WARN: Type inference failed for: r1v3, types: [com.AoGIS.tms.cache.BitmapLruCache$Builder$1] */
        public BitmapLruCache build() {
            final BitmapLruCache cache = new BitmapLruCache(this.mContext);
            if (isValidOptionsForMemoryCache()) {
                if (Constants.DEBUG) {
                    Log.d("BitmapLruCache.Builder", "Creating Memory Cache");
                }
                cache.setMemoryCache(new BitmapMemoryLruCache(this.mMemoryCacheMaxSize, this.mRecyclePolicy));
            }
            if (isValidOptionsForDiskCache()) {
                new AsyncTask<Void, Void, DiskLruCache>() { // from class: com.AoGIS.tms.cache.BitmapLruCache.Builder.1
                    /* JADX INFO: Access modifiers changed from: protected */
                    @Override // android.os.AsyncTask
                    public DiskLruCache doInBackground(Void... params) {
                        try {
                            return DiskLruCache.open(Builder.this.mDiskCacheLocation, 0, 1, Builder.this.mDiskCacheMaxSize);
                        } catch (IOException e) {
                            e.printStackTrace();
                            return null;
                        }
                    }

                    /* JADX INFO: Access modifiers changed from: protected */
                    @Override // android.os.AsyncTask
                    public void onPostExecute(DiskLruCache result) {
                        cache.setDiskCache(result);
                    }
                }.execute(new Void[0]);
            }
            return cache;
        }

        public Builder setDiskCacheEnabled(boolean enabled) {
            this.mDiskCacheEnabled = enabled;
            return this;
        }

        public Builder setDiskCacheLocation(File location) {
            this.mDiskCacheLocation = location;
            return this;
        }

        public Builder setDiskCacheMaxSize(long maxSize) {
            this.mDiskCacheMaxSize = maxSize;
            return this;
        }

        public Builder setMemoryCacheEnabled(boolean enabled) {
            this.mMemoryCacheEnabled = enabled;
            return this;
        }

        public Builder setMemoryCacheMaxSize(int size) {
            this.mMemoryCacheMaxSize = size;
            return this;
        }

        public Builder setMemoryCacheMaxSizeUsingHeapSize() {
            return setMemoryCacheMaxSizeUsingHeapSize(DEFAULT_MEMORY_CACHE_HEAP_RATIO);
        }

        public Builder setMemoryCacheMaxSizeUsingHeapSize(float percentageOfHeap) {
            int size = Math.round(getHeapSize() * Math.min(percentageOfHeap, MAX_MEMORY_CACHE_HEAP_RATIO));
            return setMemoryCacheMaxSize(size);
        }

        public Builder setRecyclePolicy(RecyclePolicy recyclePolicy) {
            if (recyclePolicy == null) {
                throw new IllegalArgumentException("The recycle policy can not be null");
            }
            this.mRecyclePolicy = recyclePolicy;
            return this;
        }

        private boolean isValidOptionsForDiskCache() {
            boolean valid = this.mDiskCacheEnabled;
            if (valid) {
                if (this.mDiskCacheLocation == null) {
                    Log.i(Constants.LOG_TAG, "Disk Cache has been enabled, but no location given. Please call setDiskCacheLocation(...)");
                    return false;
                }
                if (!this.mDiskCacheLocation.canWrite()) {
                    Log.i(Constants.LOG_TAG, "Disk Cache Location is not write-able, disabling disk caching.");
                    return false;
                }
                return valid;
            }
            return valid;
        }

        private boolean isValidOptionsForMemoryCache() {
            return this.mMemoryCacheEnabled && this.mMemoryCacheMaxSize > 0;
        }
    }

    static final class DiskCacheFlushRunnable implements Runnable {
        private final DiskLruCache mDiskCache;

        public DiskCacheFlushRunnable(DiskLruCache cache) {
            this.mDiskCache = cache;
        }

        @Override // java.lang.Runnable
        public void run() {
            Process.setThreadPriority(10);
            if (Constants.DEBUG) {
                Log.d(Constants.LOG_TAG, "Flushing Disk Cache");
            }
            try {
                this.mDiskCache.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static class FileInputStreamProvider implements InputStreamProvider {
        final File mFile;

        public FileInputStreamProvider(File file) {
            this.mFile = file;
        }

        @Override // com.AoGIS.tms.cache.BitmapLruCache.InputStreamProvider
        public InputStream getInputStream() {
            try {
                return new FileInputStream(this.mFile);
            } catch (FileNotFoundException e) {
                Log.e(Constants.LOG_TAG, "Could not decode file: " + this.mFile.getAbsolutePath(), e);
                return null;
            }
        }
    }

    public static class ByteArrayInputStreamProvider implements InputStreamProvider {
        final byte[] array;

        public ByteArrayInputStreamProvider(byte[] array) {
            this.array = array;
        }

        @Override // com.AoGIS.tms.cache.BitmapLruCache.InputStreamProvider
        public InputStream getInputStream() {
            return null;
        }
    }

    final class SnapshotInputStreamProvider implements InputStreamProvider {
        final String mKey;

        SnapshotInputStreamProvider(String key) {
            this.mKey = key;
        }

        @Override // com.AoGIS.tms.cache.BitmapLruCache.InputStreamProvider
        public InputStream getInputStream() {
            try {
                DiskLruCache.Snapshot snapshot = BitmapLruCache.this.mDiskCache.get(this.mKey);
                if (snapshot != null) {
                    return snapshot.getInputStream(0);
                }
            } catch (IOException e) {
                Log.e(Constants.LOG_TAG, "Could open disk cache for url: " + this.mKey, e);
            }
            return null;
        }
    }
}
