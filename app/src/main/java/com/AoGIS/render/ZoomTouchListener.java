package com.AoGIS.render;

import android.graphics.Matrix;
import android.graphics.PointF;
import android.view.MotionEvent;
import android.view.View;
import com.AoGIS.base.IAoView;

/* loaded from: classes.dex */
public class ZoomTouchListener extends BaseTouchListener {
    static final /* synthetic */ boolean $assertionsDisabled;
    private static final int DRAG = 1;
    private static final int NONE = 0;
    private static final int ZOOM = 2;
    private PointF mid;
    private int mode;
    private float oldDist;
    private Matrix savedMatrix;
    private PointF[] start;

    static {
        $assertionsDisabled = !ZoomTouchListener.class.desiredAssertionStatus();
    }

    public ZoomTouchListener(IAoView v) {
        super(v);
        this.mode = 0;
        this.savedMatrix = new Matrix();
        this.start = new PointF[2];
        this.mid = new PointF();
    }

    @Override // com.AoGIS.render.BaseTouchListener, android.view.View.OnTouchListener
    public boolean onTouch(View v, MotionEvent event) {
        if (!$assertionsDisabled && getView() != v) {
            throw new AssertionError();
        }
        IAoView GisView = getView();
        if (!GisView.getCanUpdateWindow()) {
            return false;
        }
        Matrix matrix = GisView.getMatrix();
        switch (event.getAction() & 255) {
            case 0:
                this.savedMatrix.set(matrix);
                this.start[0] = new PointF();
                this.start[0].set(event.getX(), event.getY());
                this.mode = 1;
                break;
            case 1:
                if (this.mode == 1 && (Math.abs(event.getX() - this.start[0].x) > 1.0f || Math.abs(event.getY() - this.start[0].y) > 1.0f)) {
                    PointF ptEnd = new PointF();
                    ptEnd.set(event.getX(), event.getY());
                    GisView.moveView(this.start[0], ptEnd);
                }
                this.mode = 0;
                break;
            case 2:
                if (this.mode != 1) {
                    if (this.mode == 2) {
                        float newDist = spacing(event);
                        if (Math.abs(newDist - this.oldDist) > 1.0f) {
                            if (newDist > 10.0f) {
                                matrix.set(this.savedMatrix);
                                float scale = newDist / this.oldDist;
                                matrix.postScale(scale, scale, this.mid.x, this.mid.y);
                            }
                            GisView.viewInvalidate();
                            break;
                        }
                    }
                } else if (Math.abs(event.getX() - this.start[0].x) > 1.0f || Math.abs(event.getY() - this.start[0].y) > 1.0f) {
                    matrix.set(this.savedMatrix);
                    matrix.postTranslate(event.getX() - this.start[0].x, event.getY() - this.start[0].y);
                    GisView.viewInvalidate();
                    break;
                }
                break;
            case 5:
                this.start[0] = new PointF();
                this.start[1] = new PointF();
                this.start[0].set(event.getX(0), event.getY(0));
                this.start[1].set(event.getX(1), event.getY(1));
                this.oldDist = spacing(event);
                if (this.oldDist > 10.0f) {
                    this.savedMatrix.set(matrix);
                    midPoint(this.mid, event);
                    this.mode = 2;
                    break;
                }
                break;
            case 6:
                if (this.mode == 2) {
                    float newDist2 = spacing(event);
                    if (Math.abs(newDist2 - this.oldDist) > 1.0f) {
                        float[] pts = {0.0f, GisView.getViewHeight()};
                        matrix.mapPoints(pts);
                        GisView.zoomView(pts[0], pts[1], newDist2 / this.oldDist);
                    }
                }
                this.mode = 0;
                break;
        }
        return super.onTouch(v, event);
    }

    private float spacing(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt((x * x) + (y * y));
    }

    private void midPoint(PointF point, MotionEvent event) {
        float x = event.getX(0) + event.getX(1);
        float y = event.getY(0) + event.getY(1);
        point.set(x / 2.0f, y / 2.0f);
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
    public void onLongPress(MotionEvent ev) {
        System.out.println("longpress");
        Matrix matrix = getView().getMatrix();
        matrix.reset();
        getView().resetView();
    }
}
