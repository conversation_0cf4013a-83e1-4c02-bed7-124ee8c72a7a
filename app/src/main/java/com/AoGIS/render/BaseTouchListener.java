package com.AoGIS.render;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import com.AoGIS.base.IAoView;

/* loaded from: classes.dex */
public class BaseTouchListener extends GestureDetector.SimpleOnGestureListener implements View.OnTouchListener {
    private IAoView mAoView;
    protected int mFlashCount = -1;
    protected GestureDetector mGestureDetector;

    public BaseTouchListener(IAoView v) {
        this.mAoView = null;
        this.mGestureDetector = null;
        this.mAoView = v;
        this.mGestureDetector = new GestureDetector(this.mAoView.getViewContext(), this);
    }

    public IAoView getView() {
        return this.mAoView;
    }

    public void setFlashCount(int iFlashCount) {
        this.mFlashCount = iFlashCount;
    }

    public int getFlashCount() {
        return this.mFlashCount;
    }

    public void onBitmapDraw(Bitmap bitmap) {
    }

    public void onCanvasDraw(Canvas canvas) {
    }

    public void clearDraw() {
    }

    public boolean onEditOk() {
        return true;
    }

    public boolean onEditCancel() {
        return true;
    }

    public String[] getExtendFunctions() {
        return new String[0];
    }

    public boolean callFunction(int i, Object param) {
        throw new RuntimeException("didn't implement callFunction to deal with extend functions");
    }

    public boolean onFlashTimeout(AoSelectPool pool) {
        return true;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
    }

    public void finalize() {
    }

    public boolean onTouch(View v, MotionEvent event) {
        return this.mGestureDetector.onTouchEvent(event);
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
    public boolean onDown(MotionEvent ev) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
    public boolean onSingleTapUp(MotionEvent ev) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnGestureListener
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
    public boolean onDoubleTap(MotionEvent e) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
    public boolean onDoubleTapEvent(MotionEvent e) {
        return true;
    }

    @Override // android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
    public boolean onSingleTapConfirmed(MotionEvent e) {
        return true;
    }
}
