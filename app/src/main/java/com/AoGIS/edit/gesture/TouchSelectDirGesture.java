package com.AoGIS.edit.gesture;

import android.content.Context;
import android.view.MotionEvent;
import com.AoGIS.database.WorkArea;
import com.AoGIS.edit.ComplexEditTouchListener;
import com.AoGIS.edit.ModifyEditListener;
import com.AoGIS.render.AoSelectPool;
import com.AoGIS.util.DisplayHelper;

/* loaded from: classes.dex */
public class TouchSelectDirGesture implements IFixedFingerGesture {
    private WorkArea mArea;
    private ModifyEditListener mListener;
    private double[] mPoint;
    private int[] mSelectedGid;

    public TouchSelectDirGesture(ModifyEditListener listener, WorkArea area, int[] selected, double[] point) {
        this.mSelectedGid = null;
        this.mPoint = null;
        this.mListener = listener;
        this.mArea = area;
        this.mSelectedGid = selected;
        this.mPoint = point;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public int getFingerNumber() {
        return 1;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doStart(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doTouchMove(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doEnd(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        Context context = this.mListener.getView().getViewContext();
        float pxSize = DisplayHelper.dip2px(context, 15.0f);
        AoSelectPool pool = this.mListener.getView().selectTopGraph(pointers[0].getCurrentX() - pxSize, pointers[0].getCurrentY() - pxSize, pointers[0].getCurrentX() + pxSize, pointers[0].getCurrentY() + pxSize, this.mArea);
        int gid = 0;
        if (pool != null && pool.getPoolEntityCount() > 0) {
            gid = pool.getEntityID(0);
        }
        if (this.mSelectedGid != null) {
            this.mSelectedGid[0] = gid;
        }
        this.mListener.onSelectOK();
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doCancel(MotionEvent event) {
        return false;
    }

    private double getNearestDistance(double[] points, double[] lp) {
        double curDist = Double.MAX_VALUE;
        int nCount = points.length / 2;
        for (int i = 0; i < nCount - 1; i++) {
            double x1 = points[i * 2];
            double y1 = points[(i * 2) + 1];
            double x2 = points[(i * 2) + 2];
            double y2 = points[(i * 2) + 3];
            double x3 = lp[0];
            double y3 = lp[1];
            double px = x2 - x1;
            double py = y2 - y1;
            double som = (px * px) + (py * py);
            double u = (((x3 - x1) * px) + ((y3 - y1) * py)) / som;
            if (u < 1.0d && u > 0.0d) {
                double x = x1 + (u * px);
                double y = y1 + (u * py);
                double dx = x - x3;
                double dy = y - y3;
                double dist = (dx * dx) + (dy * dy);
                if (curDist > dist) {
                    curDist = dist;
                }
            }
        }
        return curDist;
    }
}
