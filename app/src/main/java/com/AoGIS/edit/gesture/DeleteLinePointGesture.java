package com.AoGIS.edit.gesture;

import android.graphics.Matrix;
import android.view.MotionEvent;
import com.AoGIS.base.IAoView;
import com.AoGIS.edit.ComplexEditTouchListener;
import com.AoGIS.edit.ICacheLine;

/* loaded from: classes.dex */
public class DeleteLinePointGesture implements IFixedFingerGesture {
    protected ICacheLine m_line;
    protected double m_oriX;
    protected double m_oriY;
    protected IAoView m_view;
    protected Matrix m_matrix_step = new Matrix();
    protected boolean m_valid = false;
    protected int m_curPntIndex = 0;

    public DeleteLinePointGesture(IAoView v, ICacheLine line) {
        this.m_view = v;
        this.m_line = line;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public int getFingerNumber() {
        return 1;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doStart(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        this.m_valid = false;
        double[] lp = this.m_view.WCoordToMCoord(pointers[0].getCurrentX(), pointers[0].getCurrentY());
        int nCount = this.m_line.getPointCount();
        double[] points = this.m_line.getOriginalMCPoints();
        int minIndex = -1;
        double minDistanceSq = Double.MAX_VALUE;
        for (int i = 0; i < nCount; i++) {
            double dx = lp[0] - points[i * 2];
            double dy = lp[1] - points[(i * 2) + 1];
            double distSq = (dx * dx) + (dy * dy);
            if (distSq < minDistanceSq) {
                minIndex = i;
                minDistanceSq = distSq;
            }
        }
        if (minIndex < 0) {
            return false;
        }
        this.m_curPntIndex = minIndex;
        this.m_line.deletePoint(this.m_curPntIndex);
        this.m_line.setCurPoint(-1);
        this.m_valid = true;
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doTouchMove(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doEnd(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doCancel(MotionEvent event) {
        return false;
    }
}
