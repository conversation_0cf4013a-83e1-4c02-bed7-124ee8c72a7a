package com.AoGIS.edit.gesture;

import android.graphics.Matrix;
import android.graphics.PointF;
import android.view.MotionEvent;
import com.AoGIS.base.IAoView;
import com.AoGIS.edit.ComplexEditTouchListener;

/* loaded from: classes.dex */
public class ZoomGesture implements IFixedFingerGesture {
    private IAoView m_view;
    private Matrix m_matrix_step = new Matrix();
    private PointF m_mid = new PointF();
    private float m_dist_step = 0.0f;

    public IAoView getView() {
        return this.m_view;
    }

    public ZoomGesture(IAoView view) {
        this.m_view = null;
        if (view == null) {
            throw new NullPointerException("A non-null AoView is needed");
        }
        this.m_view = view;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public int getFingerNumber() {
        return 2;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doStart(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        this.m_matrix_step.set(this.m_view.getMatrix());
        pointers[0].makeStepState(event);
        pointers[1].makeStepState(event);
        this.m_mid.x = (pointers[0].getGestureStepX() + pointers[1].getGestureStepX()) / 2.0f;
        this.m_mid.y = (pointers[0].getGestureStepY() + pointers[1].getGestureStepY()) / 2.0f;
        float dx = pointers[0].getGestureStepX() - pointers[1].getGestureStepX();
        float dy = pointers[0].getGestureStepY() - pointers[1].getGestureStepY();
        this.m_dist_step = (float) Math.sqrt((dx * dx) + (dy * dy));
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doTouchMove(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        Matrix m = this.m_view.getMatrix();
        m.set(this.m_matrix_step);
        float dx = pointers[0].getCurrentX() - pointers[1].getCurrentX();
        float dy = pointers[0].getCurrentY() - pointers[1].getCurrentY();
        float newDist = (float) Math.sqrt((dx * dx) + (dy * dy));
        float scale = newDist / this.m_dist_step;
        m.postScale(scale, scale, this.m_mid.x, this.m_mid.y);
        this.m_view.viewInvalidate();
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doEnd(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        return false;
    }

    @Override // com.AoGIS.edit.gesture.IFixedFingerGesture
    public boolean doCancel(MotionEvent event) {
        this.m_view.getMatrix().set(this.m_matrix_step);
        return false;
    }
}
