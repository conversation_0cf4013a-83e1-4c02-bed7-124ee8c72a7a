package com.AoGIS.edit;

import android.graphics.Canvas;
import android.view.MotionEvent;
import android.view.View;
import com.AoGIS.base.IAoView;
import com.AoGIS.render.BaseTouchListener;
import java.util.ArrayList;
import java.util.Iterator;

/* loaded from: classes.dex */
public abstract class ComplexEditTouchListener extends BaseTouchListener {
    static final /* synthetic */ boolean $assertionsDisabled;
    public static final int MAX_POINTER_ID = 5;
    public static final boolean POINTER_DOWN = true;
    public static final boolean POINTER_UP = false;
    private ArrayList<PointerState> m_ClassAPointers;
    private ArrayList<PointerState> m_ClassBPointers;
    private PointerState[] m_Pointers;
    private ArrayList<TouchPadState> m_TouchPads;
    private boolean m_currentResult;

    public enum PadTouchMode {
        POINTER_ADD,
        POINTER_MOVE,
        POINTER_REMOVE,
        POINTER_END,
        POINTER_CANCEL,
        GENERAL_MOTION_EVENT
    }

    public interface SpecialTouchPad {
        boolean contains(float f, float f2);

        boolean onCanvasDraw(ComplexEditTouchListener complexEditTouchListener, Canvas canvas);

        boolean onTouch(ComplexEditTouchListener complexEditTouchListener, PointerState[] pointerStateArr, PointerState[] pointerStateArr2, MotionEvent motionEvent, PadTouchMode padTouchMode, PointerState pointerState);
    }

    public abstract boolean onGestureCancel(MotionEvent motionEvent);

    public abstract boolean onGestureEnd(PointerState pointerState, MotionEvent motionEvent);

    public abstract boolean onGestureFingerChanged(PointerState[] pointerStateArr, PointerState pointerState, boolean z, MotionEvent motionEvent);

    public abstract boolean onGestureMove(PointerState[] pointerStateArr, MotionEvent motionEvent);

    public abstract boolean onGestureStart(PointerState pointerState, MotionEvent motionEvent);

    static {
        $assertionsDisabled = !ComplexEditTouchListener.class.desiredAssertionStatus();
    }

    @Override // com.AoGIS.render.BaseTouchListener, android.view.GestureDetector.SimpleOnGestureListener, android.view.GestureDetector.OnDoubleTapListener
    public boolean onDoubleTapEvent(MotionEvent e) {
        return super.onDoubleTapEvent(e);
    }

    public boolean onTouchEx(PointerState[] classAPointers, PointerState[] classBPointers, MotionEvent event) {
        return false;
    }

    public static abstract class SimpleTouchPad implements SpecialTouchPad {
        private boolean m_Start = false;

        public abstract boolean onGestrueEnd(ComplexEditTouchListener complexEditTouchListener, PointerState pointerState, MotionEvent motionEvent);

        public abstract boolean onGestureCancel(MotionEvent motionEvent);

        public abstract boolean onGestureFingerChanged(ComplexEditTouchListener complexEditTouchListener, PointerState[] pointerStateArr, PointerState pointerState, boolean z, MotionEvent motionEvent);

        public abstract boolean onGestureMove(ComplexEditTouchListener complexEditTouchListener, PointerState[] pointerStateArr, MotionEvent motionEvent);

        public abstract boolean onGestureStart(ComplexEditTouchListener complexEditTouchListener, PointerState pointerState, MotionEvent motionEvent);

        @Override // com.AoGIS.edit.ComplexEditTouchListener.SpecialTouchPad
        public boolean onTouch(ComplexEditTouchListener sender, PointerState[] myPointers, PointerState[] allPointers, MotionEvent event, PadTouchMode mode, PointerState changed) {
            switch (mode) {
                case GENERAL_MOTION_EVENT:
                default:
                    return false;
                case POINTER_ADD:
                    if (!this.m_Start) {
                        this.m_Start = true;
                        return onGestureStart(sender, changed, event);
                    }
                    return onGestureFingerChanged(sender, myPointers, changed, true, event);
                case POINTER_CANCEL:
                    this.m_Start = false;
                    return onGestureCancel(event);
                case POINTER_END:
                    this.m_Start = false;
                    return onGestrueEnd(sender, changed, event);
                case POINTER_MOVE:
                    return onGestureMove(sender, myPointers, event);
                case POINTER_REMOVE:
                    return onGestureFingerChanged(sender, myPointers, changed, false, event);
            }
        }
    }

    public class PointerState {
        TouchPadState attachedPad = null;
        float currentX;
        float currentY;
        float gestureStepX;
        float gestureStepY;
        int index;
        float originalX;
        float originalY;
        int pointerId;
        long pressTime;

        public int getPointerId() {
            return this.pointerId;
        }

        public float getCurrentX() {
            return this.currentX;
        }

        public float getCurrentY() {
            return this.currentY;
        }

        protected PointerState(MotionEvent event, int pointerIndex) {
            this.originalX = 0.0f;
            this.originalY = 0.0f;
            this.gestureStepX = 0.0f;
            this.gestureStepY = 0.0f;
            this.currentX = 0.0f;
            this.currentY = 0.0f;
            this.pressTime = 0L;
            this.index = -1;
            this.pointerId = event.getPointerId(pointerIndex);
            this.originalX = event.getX(pointerIndex);
            this.originalY = event.getY(pointerIndex);
            this.pressTime = event.getEventTime();
            this.index = pointerIndex;
            this.gestureStepX = this.originalX;
            this.gestureStepY = this.originalY;
            this.currentX = this.originalX;
            this.currentY = this.originalY;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void makeRelations() {
            TouchPadState state = ComplexEditTouchListener.this.hitSpecialTouchPad(this.originalX, this.originalY);
            if (state != null) {
                this.attachedPad = state;
                state.pointers.add(this);
            }
            if (isClassA()) {
                ComplexEditTouchListener.this.m_ClassAPointers.add(this);
            } else {
                ComplexEditTouchListener.this.m_ClassBPointers.add(this);
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public void deleteRelations() {
            if (this.attachedPad != null) {
                this.attachedPad.pointers.remove(this);
            }
            if (isClassA()) {
                ComplexEditTouchListener.this.m_ClassAPointers.remove(this);
            } else {
                ComplexEditTouchListener.this.m_ClassBPointers.remove(this);
            }
        }

        public void updateProperties(MotionEvent event) {
            this.index = event.findPointerIndex(this.pointerId);
            this.currentX = event.getX(this.index);
            this.currentY = event.getY(this.index);
        }

        public boolean isClassA() {
            return this.attachedPad == null;
        }

        public boolean isClassB() {
            return this.attachedPad != null;
        }

        public void makeStepState(MotionEvent event) {
            this.gestureStepX = this.currentX;
            this.gestureStepY = this.currentY;
        }

        public float getGestureStepX() {
            return this.gestureStepX;
        }

        public float getGestureStepY() {
            return this.gestureStepY;
        }

        public long getPressTime() {
            return this.pressTime;
        }

        public TouchPadState getAttachedPad() {
            return this.attachedPad;
        }

        public float getOriginalX() {
            return this.originalX;
        }

        public float getOriginalY() {
            return this.originalY;
        }

        public float getIndex() {
            return this.index;
        }
    }

    public class TouchPadState {
        public final ArrayList<PointerState> pointers = new ArrayList<>(6);
        public final SpecialTouchPad touchPad;

        public TouchPadState(SpecialTouchPad pad) {
            this.touchPad = pad;
        }

        public PointerState[] getPointerArray() {
            return (PointerState[]) this.pointers.toArray(new PointerState[0]);
        }
    }

    public ComplexEditTouchListener(IAoView v) {
        super(v);
        this.m_TouchPads = new ArrayList<>(10);
        this.m_Pointers = new PointerState[6];
        this.m_ClassAPointers = new ArrayList<>(6);
        this.m_ClassBPointers = new ArrayList<>(6);
        this.m_currentResult = false;
        initPointers();
    }

    public void addSpecialTouchPad(SpecialTouchPad pad) {
        this.m_TouchPads.add(new TouchPadState(pad));
    }

    public void removeSpecialTouchPad(SpecialTouchPad pad) {
        int count = this.m_TouchPads.size();
        for (int i = 0; i < count; i++) {
            if (this.m_TouchPads.get(i).touchPad == pad) {
                TouchPadState state = this.m_TouchPads.remove(i);
                Iterator<PointerState> it = state.pointers.iterator();
                while (it.hasNext()) {
                    PointerState pt = it.next();
                    pt.deleteRelations();
                }
                return;
            }
        }
    }

    public void removeAllSpecialTouchPads() {
        int count = this.m_TouchPads.size();
        for (int i = 0; i < count; i++) {
            TouchPadState state = this.m_TouchPads.get(i);
            Iterator<PointerState> it = state.pointers.iterator();
            while (it.hasNext()) {
                PointerState pt = it.next();
                pt.deleteRelations();
            }
        }
        this.m_TouchPads.clear();
    }

    public PointerState[] getClassAStates() {
        return (PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]);
    }

    public PointerState[] getClassBStates() {
        return (PointerState[]) this.m_ClassBPointers.toArray(new PointerState[0]);
    }

    public boolean isInTouching() {
        return this.m_ClassAPointers.size() > 0 || this.m_ClassBPointers.size() > 0;
    }

    private void dispatchTouchPadEvent_Remove(MotionEvent event, PointerState point, TouchPadState touchPad) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState padStat = it.next();
            if (padStat == touchPad) {
                if (padStat.pointers.size() == 0) {
                    this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.POINTER_END, point) | this.m_currentResult;
                } else {
                    this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.POINTER_REMOVE, point) | this.m_currentResult;
                }
            } else {
                this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.GENERAL_MOTION_EVENT, point) | this.m_currentResult;
            }
        }
    }

    private void dispatchTouchPadEvent_Add(MotionEvent event, PointerState newPointer) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState padStat = it.next();
            if (padStat == newPointer.attachedPad) {
                this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.POINTER_ADD, newPointer) | this.m_currentResult;
            } else {
                this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.GENERAL_MOTION_EVENT, newPointer) | this.m_currentResult;
            }
        }
    }

    private void dispatchTouchPadEvent_Cancel(MotionEvent event) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState state = it.next();
            if (state.pointers != null && state.pointers.size() > 0) {
                this.m_currentResult = state.touchPad.onTouch(this, state.getPointerArray(), this.m_Pointers, event, PadTouchMode.POINTER_CANCEL, null) | this.m_currentResult;
            }
            state.pointers.clear();
        }
    }

    private void dispatchTouchPadEvent_Move(MotionEvent event) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState padStat = it.next();
            if (padStat.pointers.size() > 0) {
                this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.POINTER_MOVE, null) | this.m_currentResult;
            } else {
                this.m_currentResult = padStat.touchPad.onTouch(this, padStat.getPointerArray(), this.m_Pointers, event, PadTouchMode.GENERAL_MOTION_EVENT, null) | this.m_currentResult;
            }
        }
    }

    private void initPointers() {
        for (int i = 0; i < this.m_Pointers.length; i++) {
            this.m_Pointers[i] = null;
        }
        this.m_ClassAPointers.clear();
        this.m_ClassBPointers.clear();
    }

    private void registerPointer(MotionEvent event, int index) {
        int pointerId = event.getPointerId(index);
        PointerState newPointer = new PointerState(event, index);
        this.m_Pointers[pointerId] = newPointer;
        newPointer.makeRelations();
        if (!onTouchEx((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), (PointerState[]) this.m_ClassBPointers.toArray(new PointerState[0]), event)) {
            dispatchTouchPadEvent_Add(event, newPointer);
            if (newPointer.isClassA() && this.m_ClassAPointers.size() == 1 && !this.m_currentResult) {
                this.m_currentResult |= onGestureStart(newPointer, event);
            } else if (newPointer.isClassA() && !this.m_currentResult) {
                this.m_currentResult = onGestureFingerChanged((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), newPointer, true, event) | this.m_currentResult;
            }
        }
    }

    private void cancelPointers(MotionEvent event) {
        boolean result = onTouchEx((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), (PointerState[]) this.m_ClassBPointers.toArray(new PointerState[0]), event);
        if (!result) {
            dispatchTouchPadEvent_Cancel(event);
            if (this.m_ClassAPointers.size() > 0 && !this.m_currentResult) {
                this.m_currentResult |= onGestureCancel(event);
            }
        }
        initPointers();
    }

    private void releasePointer(MotionEvent event, int pointerId) {
        PointerState point = this.m_Pointers[pointerId];
        this.m_Pointers[pointerId] = null;
        boolean result = onTouchEx((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), (PointerState[]) this.m_ClassBPointers.toArray(new PointerState[0]), event);
        TouchPadState touchPad = point.getAttachedPad();
        point.deleteRelations();
        if (!result) {
            dispatchTouchPadEvent_Remove(event, point, touchPad);
            if (point.isClassA() && !this.m_currentResult) {
                if (this.m_ClassAPointers.size() > 0) {
                    this.m_currentResult = onGestureFingerChanged((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), point, false, event) | this.m_currentResult;
                } else {
                    this.m_currentResult |= onGestureEnd(point, event);
                }
            }
        }
    }

    private void updatePointers(MotionEvent event) {
        for (PointerState st : this.m_Pointers) {
            if (st != null) {
                st.updateProperties(event);
            }
        }
        boolean result = onTouchEx((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), (PointerState[]) this.m_ClassBPointers.toArray(new PointerState[0]), event);
        if (!result) {
            dispatchTouchPadEvent_Move(event);
            if (this.m_ClassAPointers.size() > 0 && !this.m_currentResult) {
                this.m_currentResult = onGestureMove((PointerState[]) this.m_ClassAPointers.toArray(new PointerState[0]), event) | this.m_currentResult;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public TouchPadState hitSpecialTouchPad(float x, float y) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState state = it.next();
            if (state.touchPad.contains(x, y)) {
                return state;
            }
        }
        return null;
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public void onCanvasDraw(Canvas canvas) {
        Iterator<TouchPadState> it = this.m_TouchPads.iterator();
        while (it.hasNext()) {
            TouchPadState state = it.next();
            if (state.touchPad != null) {
                state.touchPad.onCanvasDraw(this, canvas);
            }
        }
        super.onCanvasDraw(canvas);
    }

    @Override // com.AoGIS.render.BaseTouchListener, android.view.View.OnTouchListener
    public boolean onTouch(View v, MotionEvent event) {
        if (!$assertionsDisabled && getView() != v) {
            throw new AssertionError();
        }
        this.m_currentResult = false;
        switch (event.getActionMasked()) {
            case 0:
                registerPointer(event, 0);
                break;
            case 1:
                releasePointer(event, event.getPointerId(0));
                break;
            case 2:
                updatePointers(event);
                break;
            case 3:
                cancelPointers(event);
                break;
            case 4:
            default:
                updatePointers(event);
                break;
            case 5:
                int curIndex = event.getActionIndex();
                registerPointer(event, curIndex);
                break;
            case 6:
                int curIndex2 = event.getActionIndex();
                int id = event.getPointerId(curIndex2);
                releasePointer(event, id);
                break;
        }
        return super.onTouch(v, event);
    }
}
