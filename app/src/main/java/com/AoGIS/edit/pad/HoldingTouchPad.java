package com.AoGIS.edit.pad;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.support.v4.internal.view.SupportMenu;
import android.support.v4.view.ViewCompat;
import android.view.MotionEvent;
import com.AoGIS.AoGISApplication;
import com.AoGIS.base.IAoView;
import com.AoGIS.edit.ComplexEditTouchListener;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.RelativeRect;

/* loaded from: classes.dex */
public class HoldingTouchPad extends ComplexEditTouchListener.SimpleTouchPad {
    private static boolean bUseHoldingMode = false;
    private HoldingTouchPad[] m_arrExclusive;
    private RectF m_curRect;
    private RelativeRect m_rect;
    private IAoView m_view;
    boolean m_bPressing = false;
    boolean m_bHolding = false;
    String m_label = "";
    private HoldingTouchPadListener mListener = null;

    public interface HoldingTouchPadListener {
        void onPadStateChanged(boolean z, boolean z2, boolean z3, boolean z4, MotionEvent motionEvent);
    }

    public HoldingTouchPad(IAoView view, RelativeRect rect) {
        this.m_view = view;
        this.m_rect = rect;
    }

    public HoldingTouchPad(IAoView view, RelativeRect rect, HoldingTouchPad[] exclusive) {
        this.m_view = view;
        this.m_rect = rect;
        this.m_arrExclusive = exclusive;
    }

    public void setExclusivePads(HoldingTouchPad[] exclusive) {
        this.m_arrExclusive = exclusive;
    }

    public static void setEditMode(boolean bUseHold) {
        bUseHoldingMode = bUseHold;
    }

    public static boolean getEditMode() {
        return bUseHoldingMode;
    }

    public void setPadPressing() {
        if (!bUseHoldingMode) {
            this.m_bPressing = true;
            this.m_bHolding = true;
        }
    }

    public String getLabel() {
        return this.m_label;
    }

    public void setLabel(String m_label) {
        this.m_label = m_label;
    }

    public HoldingTouchPadListener getListener() {
        return this.mListener;
    }

    public void setListener(HoldingTouchPadListener mListener) {
        this.mListener = mListener;
    }

    public RelativeRect getRect() {
        return this.m_rect;
    }

    public void setRect(RelativeRect rect) {
        this.m_rect = rect;
        this.m_curRect = null;
    }

    public boolean isHolding() {
        return this.m_bHolding;
    }

    public boolean isPressing() {
        return this.m_bPressing;
    }

    private void setState(boolean bPressing, boolean bHolding, MotionEvent event, boolean forceUpdate) {
        boolean bChanged = false;
        boolean bPressingOld = this.m_bPressing;
        boolean bHoldingOld = this.m_bHolding;
        if (this.m_bPressing != bPressing) {
            this.m_bPressing = bPressing;
            bChanged = true;
        }
        if (this.m_bHolding != bHolding) {
            this.m_bHolding = bHolding;
            bChanged = true;
        }
        if (this.mListener != null) {
            if (bChanged || forceUpdate) {
                this.mListener.onPadStateChanged(bPressingOld, bPressing, bHoldingOld, bHolding, event);
            }
        }
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SimpleTouchPad
    public boolean onGestureStart(ComplexEditTouchListener sender, ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        if (bUseHoldingMode) {
            setState(this.m_curRect.contains(pointer.getCurrentX(), pointer.getCurrentY()), true, event, false);
            this.m_view.viewInvalidate();
        } else if (this.m_curRect.contains(pointer.getCurrentX(), pointer.getCurrentY())) {
            boolean newState = this.m_bHolding ? false : true;
            if (newState && this.m_arrExclusive != null) {
                for (HoldingTouchPad pad : this.m_arrExclusive) {
                    if (this != pad) {
                        pad.setState(false, false, event, false);
                    }
                }
            }
            setState(newState, newState, event, false);
            this.m_view.viewInvalidate();
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SimpleTouchPad
    public boolean onGestrueEnd(ComplexEditTouchListener sender, ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        if (bUseHoldingMode) {
            setState(false, false, event, false);
            this.m_view.viewInvalidate();
        } else {
            setState(this.m_bPressing, this.m_bHolding, event, true);
            this.m_view.viewInvalidate();
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SimpleTouchPad
    public boolean onGestureFingerChanged(ComplexEditTouchListener sender, ComplexEditTouchListener.PointerState[] pointers, ComplexEditTouchListener.PointerState changedOne, boolean isAdded, MotionEvent event) {
        if (bUseHoldingMode) {
            setState(this.m_curRect.contains(pointers[0].getCurrentX(), pointers[0].getCurrentY()), true, event, false);
            this.m_view.viewInvalidate();
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SimpleTouchPad
    public boolean onGestureMove(ComplexEditTouchListener sender, ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        if (bUseHoldingMode) {
            setState(this.m_curRect.contains(pointers[0].getCurrentX(), pointers[0].getCurrentY()), true, event, false);
            this.m_view.viewInvalidate();
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SimpleTouchPad
    public boolean onGestureCancel(MotionEvent event) {
        if (bUseHoldingMode) {
            setState(false, false, event, true);
            this.m_view.viewInvalidate();
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SpecialTouchPad
    public boolean onCanvasDraw(ComplexEditTouchListener sender, Canvas canvas) {
        float fScale;
        if (this.m_curRect == null) {
            float dipHeight = DisplayHelper.px2dip(this.m_view.getViewContext(), this.m_view.getViewHeight());
            float dipWidth = DisplayHelper.px2dip(this.m_view.getViewContext(), this.m_view.getViewWidth());
            this.m_curRect = this.m_rect.getRectF(dipHeight, dipWidth);
            this.m_curRect = DisplayHelper.dip2px(this.m_view.getViewContext(), this.m_curRect);
        }
        Paint paint = new Paint();
        int color1 = Color.argb(100, 255, 0, 0);
        if (this.m_bPressing) {
            paint.setColor(SupportMenu.CATEGORY_MASK);
        } else {
            paint.setColor(color1);
        }
        canvas.drawRect(this.m_curRect, paint);
        if (this.m_label != null && this.m_label.length() > 0) {
            if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                fScale = 0.6f;
            } else {
                fScale = 0.5f;
            }
            Paint paintText = new Paint();
            paintText.setColor(ViewCompat.MEASURED_STATE_MASK);
            paintText.setTextSize(this.m_curRect.width() * fScale);
            Rect bounds = new Rect();
            paintText.getTextBounds(this.m_label, 0, this.m_label.length(), bounds);
            float boxHei = this.m_curRect.height();
            float txtHei = bounds.height();
            float topY = this.m_curRect.top + ((boxHei - txtHei) / 2.0f);
            float baseY = topY + bounds.height();
            canvas.drawText(this.m_label, this.m_curRect.left + ((this.m_curRect.width() - bounds.width()) / 2.0f), baseY, paintText);
            return false;
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener.SpecialTouchPad
    public boolean contains(float x, float y) {
        return this.m_curRect.contains(x, y);
    }
}
