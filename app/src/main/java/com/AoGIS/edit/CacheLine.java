package com.AoGIS.edit;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.support.v4.internal.view.SupportMenu;
import com.AoGIS.base.GeoLine;
import com.AoGIS.base.IAoView;
import com.AoGIS.base.LineInfo;
import com.AoGIS.database.LineEntry;
import com.AoGIS.edit.info.DefaultInfo;
import com.AoGIS.render.AoRender;
import com.AoGIS.util.DisplayHelper;

/* loaded from: classes.dex */
public class CacheLine implements ICacheLine {
    protected static int INIT_POINT_COUNT = 64;
    private IAoView m_view;
    private double[] m_points = new double[INIT_POINT_COUNT * 2];
    private int m_pointCount = 0;
    private double[] m_curPoints = null;
    private boolean isCurveInDB = false;
    private int curPoint = -1;
    private LineInfo m_info = DefaultInfo.getDefLineInfo().m7clone();

    @Override // com.AoGIS.edit.ICacheLine
    public int getCurPoint() {
        return this.curPoint;
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void setCurPoint(int curPoint) {
        this.curPoint = curPoint;
    }

    public boolean isCurveInDB() {
        return this.isCurveInDB;
    }

    public void setCurveInDB(boolean isCurveLine) {
        this.isCurveInDB = isCurveLine;
    }

    CacheLine(IAoView view) {
        this.m_view = view;
    }

    public LineInfo getLineInfo() {
        return this.m_info;
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void setPoints(double[] points) {
        this.m_points = (double[]) points.clone();
        this.curPoint = -1;
        this.m_pointCount = this.m_points.length / 2;
        refreshCurvePoints();
    }

    public void setInfo(LineInfo info) {
        this.m_info = info;
    }

    public void refreshCurvePoints() {
        if (this.m_pointCount <= 2 || !this.isCurveInDB) {
            this.m_curPoints = new double[this.m_pointCount * 2];
            System.arraycopy(this.m_points, 0, this.m_curPoints, 0, this.m_pointCount * 2);
        } else {
            this.m_curPoints = this.m_view.smoothLine(this.m_points, this.m_pointCount);
        }
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void appendPoint(double x, double y) {
        if (this.m_pointCount * 2 == this.m_points.length) {
            double[] m_old = this.m_points;
            this.m_points = new double[m_old.length * 2];
            System.arraycopy(m_old, 0, this.m_points, 0, m_old.length);
        }
        this.m_points[this.m_pointCount * 2] = x;
        this.m_points[(this.m_pointCount * 2) + 1] = y;
        this.m_pointCount++;
        refreshCurvePoints();
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void insertPoint(int i, double x, double y) {
        if (this.m_pointCount * 2 == this.m_points.length) {
            double[] m_old = this.m_points;
            this.m_points = new double[m_old.length * 2];
            System.arraycopy(m_old, 0, this.m_points, 0, m_old.length);
        }
        System.arraycopy(this.m_points, i * 2, this.m_points, (i + 1) * 2, (this.m_pointCount - i) * 2);
        this.m_points[i * 2] = x;
        this.m_points[(i * 2) + 1] = y;
        this.m_pointCount++;
        refreshCurvePoints();
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void deletePoint(int i) {
        if (i >= 0 && i < this.m_pointCount) {
            System.arraycopy(this.m_points, (i + 1) * 2, this.m_points, i * 2, ((this.m_pointCount - i) - 1) * 2);
            this.m_pointCount--;
            refreshCurvePoints();
        }
    }

    public void backPoint() {
        if (this.m_pointCount > 0) {
            this.m_pointCount--;
            refreshCurvePoints();
        }
    }

    @Override // com.AoGIS.edit.ICacheLine
    public void modifyPoint(int i, double x, double y) {
        if (i < this.m_pointCount) {
            this.m_points[i * 2] = x;
            this.m_points[(i * 2) + 1] = y;
            refreshCurvePoints();
        }
    }

    @Override // com.AoGIS.edit.ICacheLine
    public double[] getOriginalMCPoints() {
        double[] points = new double[this.m_pointCount * 2];
        System.arraycopy(this.m_points, 0, points, 0, this.m_pointCount * 2);
        return points;
    }

    public double[] getActualMCPoints() {
        return (double[]) this.m_curPoints.clone();
    }

    @Override // com.AoGIS.edit.ICacheLine
    public int getPointCount() {
        return this.m_pointCount;
    }

    public boolean drawLine(Canvas canvas) {
        if (this.m_pointCount == 0) {
            return true;
        }
        if (this.m_pointCount >= 2) {
            Bitmap bitmap = Bitmap.createBitmap(this.m_view.getViewWidth(), this.m_view.getViewHeight(), Bitmap.Config.ARGB_8888);
            AoRender render = new AoRender(bitmap);
            render.setRenderSize(this.m_view.getViewWidth(), this.m_view.getViewHeight());
            double[] xy = this.m_view.WCoordToMCoord(0.0f, this.m_view.getViewHeight());
            render.setDrawParam(xy[0], xy[1], this.m_view.getScale());
            LineEntry entry = new LineEntry();
            entry.entity = new GeoLine(this.m_curPoints.length / 2);
            entry.info = getLineInfo();
            entry.entity.setPoints(this.m_curPoints, 0, 0, this.m_curPoints.length / 2);
            render.drawLine(entry, (short) 3);
            render.deleteRender();
            if (this.m_view.getRedraw()) {
                canvas.drawBitmap(bitmap, new Matrix(), new Paint());
            } else {
                canvas.drawBitmap(bitmap, this.m_view.getMatrix(), new Paint());
            }
        }
        if (this.curPoint < 0 || this.curPoint >= this.m_pointCount) {
            this.curPoint = -1;
        }
        Paint paint = new Paint();
        paint.setColor(SupportMenu.CATEGORY_MASK);
        float px = DisplayHelper.dip2px(this.m_view.getViewContext(), 5.0f);
        for (int i = 0; i < this.m_pointCount; i++) {
            float[] xy2 = this.m_view.MCoordToWCoord(this.m_points[i * 2], this.m_points[(i * 2) + 1]);
            if (!this.m_view.getRedraw()) {
                this.m_view.getMatrix().mapPoints(xy2);
            }
            if (i == this.curPoint || (this.curPoint == -1 && i == this.m_pointCount - 1)) {
                Paint paintx = new Paint();
                paintx.setColor(-16776961);
                canvas.drawLine(xy2[0] - px, xy2[1], xy2[0] + px, xy2[1], paintx);
                paintx.setColor(-16776961);
                canvas.drawLine(xy2[0], xy2[1] - px, xy2[0], xy2[1] + px, paintx);
            } else if (i == this.m_pointCount - 1) {
                canvas.drawLine(xy2[0] - px, xy2[1], xy2[0] + px, xy2[1], paint);
                canvas.drawLine(xy2[0], xy2[1] - px, xy2[0], xy2[1] + px, paint);
            } else {
                canvas.drawCircle(xy2[0], xy2[1], 4.0f, paint);
            }
        }
        return true;
    }
}
