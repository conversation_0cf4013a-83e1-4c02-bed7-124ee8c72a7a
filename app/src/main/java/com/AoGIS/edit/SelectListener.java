package com.AoGIS.edit;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.support.v4.internal.view.SupportMenu;
import android.view.MotionEvent;
import com.AoGIS.base.IAoView;
import com.AoGIS.edit.ComplexEditTouchListener;
import com.AoGIS.edit.gesture.IFixedFingerGesture;
import com.AoGIS.edit.gesture.SelectDragCrossGesture;
import com.AoGIS.edit.gesture.SelectDragGesture;
import com.AoGIS.edit.gesture.SelectZoomGesture;
import com.AoGIS.edit.pad.HoldingTouchPad;
import com.AoGIS.geometry.GeoClassType;
import com.AoGIS.render.AoSelectPool;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.RelativeRect;

/* loaded from: classes.dex */
public class SelectListener extends AoViewTouchListener implements HoldingTouchPad.HoldingTouchPadListener {
    float[] mMoveDis;
    private HoldingTouchPad mPad;
    private GeoClassType mType;
    double[] mXY;
    private boolean m_bBegDraw;
    private boolean m_bDrawRED;
    private int m_curFingerNum;
    private IFixedFingerGesture m_handler;

    public SelectListener(IAoView v, GeoClassType type) {
        super(v);
        this.m_curFingerNum = 0;
        this.mXY = new double[2];
        this.mMoveDis = new float[2];
        this.m_bBegDraw = false;
        this.m_bDrawRED = false;
        this.mType = type;
        this.mPad = new HoldingTouchPad(v, new RelativeRect(RelativeRect.VerticalBase.BOTTOM, RelativeRect.HorizontalBase.LEFT, 10, 10, 50, 50));
        addSpecialTouchPad(this.mPad);
        this.mPad.setListener(this);
        setFlashCount(2);
        getView().viewPost(new Runnable() { // from class: com.AoGIS.edit.SelectListener.1
            @Override // java.lang.Runnable
            public void run() {
                SelectListener.this.m_bDrawRED = !SelectListener.this.m_bDrawRED;
                SelectListener.this.getView().viewPostDelayed(this, 600L);
                SelectListener.this.getView().viewInvalidate();
            }
        });
        getView().viewInvalidate();
    }

    public void dealFingerChanged(int oldCount, int newCount, ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        this.m_curFingerNum = newCount;
        if (newCount != oldCount || newCount == 1) {
            if (oldCount < 2 || newCount < 2) {
                if (newCount == -1) {
                    if (this.m_handler != null) {
                        this.m_handler.doCancel(event);
                    }
                    this.m_handler = null;
                    this.m_curFingerNum = 0;
                    return;
                }
                if (this.m_handler != null) {
                    this.m_handler.doEnd(pointers, event);
                }
                this.mMoveDis[0] = 0.0f;
                this.mMoveDis[1] = 0.0f;
                switch (newCount) {
                    case 0:
                        this.m_handler = null;
                        break;
                    case 1:
                        if (this.mPad.isPressing()) {
                            this.m_handler = new SelectDragCrossGesture(getView(), this.mXY, this.m_bBegDraw);
                            if (!this.m_bBegDraw) {
                                this.m_bBegDraw = true;
                                break;
                            }
                        } else {
                            this.m_handler = new SelectDragGesture(getView(), this.mMoveDis);
                            break;
                        }
                        break;
                    case 2:
                        if (this.m_bBegDraw) {
                            float[] xy = getView().MCoordToWCoord(this.mXY[0], this.mXY[1]);
                            this.mMoveDis[0] = xy[0];
                            this.mMoveDis[1] = xy[1];
                        }
                        this.m_handler = new SelectZoomGesture(getView(), this.mMoveDis);
                        break;
                }
                if (this.m_handler != null) {
                    this.m_handler.doStart(pointers, event);
                }
            }
        }
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureStart(ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        dealFingerChanged(0, 1, new ComplexEditTouchListener.PointerState[]{pointer}, event);
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureEnd(ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        dealFingerChanged(1, 0, new ComplexEditTouchListener.PointerState[]{pointer}, event);
        getView().commitMatrix();
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureFingerChanged(ComplexEditTouchListener.PointerState[] pointers, ComplexEditTouchListener.PointerState changedOne, boolean isAdded, MotionEvent event) {
        int curFingers = pointers.length;
        int oldFingers = pointers.length - (isAdded ? 1 : -1);
        dealFingerChanged(oldFingers, curFingers, pointers, event);
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureMove(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        if (this.m_handler != null) {
            this.m_handler.doTouchMove(pointers, event);
            return false;
        }
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureCancel(MotionEvent event) {
        dealFingerChanged(-1, -1, null, event);
        return false;
    }

    @Override // com.AoGIS.edit.pad.HoldingTouchPad.HoldingTouchPadListener
    public void onPadStateChanged(boolean bPressingOld, boolean bPressing, boolean bHoldingOld, boolean bHolding, MotionEvent event) {
        dealFingerChanged(this.m_curFingerNum, this.m_curFingerNum, getClassAStates(), event);
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener, com.AoGIS.render.BaseTouchListener
    public void onCanvasDraw(Canvas canvas) {
        if (this.m_bBegDraw) {
            float px = DisplayHelper.dip2px(getView().getViewContext(), 25.0f);
            Paint paint = new Paint();
            if (this.m_bDrawRED) {
                paint.setColor(SupportMenu.CATEGORY_MASK);
            } else {
                paint.setColor(-16711936);
            }
            float[] xy = getView().MCoordToWCoord(this.mXY[0], this.mXY[1]);
            xy[0] = xy[0] + this.mMoveDis[0];
            xy[1] = xy[1] + this.mMoveDis[1];
            canvas.drawLine(xy[0] - px, xy[1], xy[0] + px, xy[1], paint);
            canvas.drawLine(xy[0], xy[1] - px, xy[0], xy[1] + px, paint);
        }
        super.onCanvasDraw(canvas);
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public boolean onEditOk() {
        boolean result = false;
        if (!this.m_bBegDraw) {
            return false;
        }
        AoSelectPool SelectPool = null;
        float[] xy = getView().MCoordToWCoord(this.mXY[0], this.mXY[1]);
        switch (this.mType) {
            case LINE:
                SelectPool = getView().selectLine(xy[0], xy[1]);
                break;
            case POINT:
                SelectPool = getView().selectPoint(xy[0], xy[1]);
                break;
            case POLYGON:
                SelectPool = getView().selectPolygon(xy[0], xy[1]);
                break;
        }
        if (SelectPool != null && SelectPool.getPoolEntityCount() > 0) {
            this.m_bBegDraw = false;
            getView().beginFlash(SelectPool);
            result = true;
        } else {
            System.out.println("select no entity");
        }
        return result;
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public boolean onEditCancel() {
        this.m_bBegDraw = false;
        return true;
    }
}
