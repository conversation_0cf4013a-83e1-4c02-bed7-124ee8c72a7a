package com.AoGIS.database;

import android.util.Log;
import com.AoGIS.GeoDBException;
import com.AoGIS.geometry.GeoClassType;
import java.lang.ref.WeakReference;
import java.util.Hashtable;

/* loaded from: classes.dex */
public class WorkSpace {
    static Hashtable<Short, WeakReference<WorkSpace>> nativeRefrences = new Hashtable<>();
    static int nativeWrapperCount = 0;
    final short m_ahInst;

    private native int nativeFinalizer(short s);

    private native short[] nativeGetAllAreas(short s);

    private native short nativeInit();

    public WorkSpace() {
        checkOpenCount();
        synchronized (nativeRefrences) {
            this.m_ahInst = nativeInit();
            if (this.m_ahInst <= 0) {
                throw new IllegalStateException("Create WorkSpace Failed.");
            }
            if (nativeRefrences.containsKey(Short.valueOf(this.m_ahInst))) {
                throw new GeoDBException("FATAL: WorkSpace meet Handle conflict!");
            }
            nativeRefrences.put(Short.valueOf(this.m_ahInst), new WeakReference<>(this));
            nativeWrapperCount++;
        }
    }

    private void checkOpenCount() {
        if (nativeWrapperCount >= 100) {
            System.gc();
            Log.w("AoGeoGIS-SDK", "WARN: More than 100 WorkSpace, trying telling GC to release some of them that were unreferenced");
        }
        if (nativeWrapperCount >= 128) {
            throw new GeoDBException("FATAL: Creating too many WorkSpace, more than maxWorkSpaceNum");
        }
    }

    private WorkSpace(short native_handle) {
        checkOpenCount();
        synchronized (nativeRefrences) {
            this.m_ahInst = native_handle;
            if (this.m_ahInst <= 0) {
                throw new IllegalStateException("Create WorkSpace Failed.");
            }
            if (nativeRefrences.containsKey(Short.valueOf(this.m_ahInst))) {
                throw new GeoDBException("FATAL: WorkSpace meet Handle conflict!");
            }
            nativeRefrences.put(Short.valueOf(this.m_ahInst), new WeakReference<>(this));
            nativeWrapperCount++;
        }
    }

    public static WorkSpace createFromNativeHandle(short handle) {
        WeakReference<WorkSpace> ws = nativeRefrences.get(Short.valueOf(handle));
        WorkSpace space = ws == null ? null : ws.get();
        if (space == null) {
            return new WorkSpace(handle);
        }
        return space;
    }

    protected void finalize() {
        synchronized (nativeRefrences) {
            System.out.println("finalize ~WorkSpace");
            nativeFinalizer(this.m_ahInst);
            nativeRefrences.remove(Short.valueOf(this.m_ahInst));
            nativeWrapperCount--;
        }
    }

    public WorkArea CreateWorkArea(String strFileName, GeoClassType type) throws GeoDBException {
        return WorkArea.CreateWorkArea(this, strFileName, type);
    }

    public WorkArea OpenWorkArea(String strFileName) throws GeoDBException {
        return WorkArea.OpenWorkArea(this, strFileName);
    }

    public WorkArea OpenWorkArea(short AreaHandle) throws GeoDBException {
        return WorkArea.CreateWorkArea(this, AreaHandle);
    }

    public WorkArea[] GetAllWorkAreas() {
        short[] ais = nativeGetAllAreas(this.m_ahInst);
        int iSize = ais.length;
        WorkArea[] arr = new WorkArea[iSize];
        for (int i = 0; i < iSize; i++) {
            arr[i] = WorkArea.CreateWorkArea(this, ais[i]);
        }
        return arr;
    }
}
