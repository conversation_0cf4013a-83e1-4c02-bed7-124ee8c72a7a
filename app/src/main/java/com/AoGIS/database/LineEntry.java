package com.AoGIS.database;

import com.AoGIS.base.GeoLine;
import com.AoGIS.base.LineInfo;
import com.AoGIS.geometry.GeoClassType;

/* loaded from: classes.dex */
public class LineEntry extends GeometryEntry {
    public GeoLine entity;
    public LineInfo info;

    public LineEntry() {
        this.entity = null;
        this.info = null;
    }

    public LineEntry(GeoLine entity, LineInfo info) {
        this.entity = null;
        this.info = null;
        this.entity = entity;
        this.info = info;
    }

    @Override // com.AoGIS.database.IGeometryEntry
    public GeoClassType getType() {
        return GeoClassType.LINE;
    }
}
