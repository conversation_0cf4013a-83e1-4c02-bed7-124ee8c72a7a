package com.AoGIS.database;

import com.AoGIS.base.INativeByteEnum;

/* loaded from: classes.dex */
public class WorkAreaParams {
    public double H;
    public byte TICtype;
    public byte TICunit;
    public double djd;
    public double dwd;
    public double dx;
    public double dy;
    public byte earthParam;
    public double h;
    public double infoRatex;
    public double infoRatey;
    public byte infoUnit;
    public double jd0;
    public double lat;
    public double lat1;
    public double lat2;
    public byte levelType;
    public double lon;
    public double lon1;
    public double lon2;
    public double maph;
    public double mapw;
    public byte projType;
    public double rate;
    public byte type;
    public byte unit;
    public double vRate;
    public byte vUnit;
    public double wd0;

    public enum CoordSysType implements INativeByteEnum {
        None((byte) 0),
        Geographic((byte) 1),
        Geodetic((byte) 2),
        Projection((byte) 3),
        Geocentric((byte) 4);

        private final byte m_typeId;

        CoordSysType(byte typeId) {
            this.m_typeId = typeId;
        }

        @Override // com.AoGIS.base.INativeByteEnum
        public byte getNativeID() {
            return this.m_typeId;
        }

        public static CoordSysType fromNativeID(byte id) {
            CoordSysType[] values = values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getNativeID() == id) {
                    return values[i];
                }
            }
            throw new IllegalStateException("Wrong CoordSysType ID from Native:" + ((int) id));
        }
    }

    public enum EarthType implements INativeByteEnum {
        None((byte) 0),
        Beijing54((byte) 1),
        Xian80((byte) 2),
        CGCS2000((byte) 3),
        IUGG1983((byte) 4),
        UserDefine((byte) 5),
        IUGG1967((byte) 6),
        WGS84((byte) 7),
        GRS80((byte) 8),
        WGS72((byte) 9),
        Australia1965((byte) 10),
        Hayford1910((byte) 11),
        KLK1880((byte) 12),
        KLK1866((byte) 13),
        BSER((byte) 14),
        IUGG1979((byte) 15);

        private final byte m_typeId;

        EarthType(byte typeId) {
            this.m_typeId = typeId;
        }

        @Override // com.AoGIS.base.INativeByteEnum
        public byte getNativeID() {
            return this.m_typeId;
        }

        public static EarthType fromNativeID(byte id) {
            EarthType[] values = values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getNativeID() == id) {
                    return values[i];
                }
            }
            throw new IllegalStateException("Wrong ProjectionType ID from Native:" + ((int) id));
        }
    }

    public enum ProjectionType implements INativeByteEnum {
        None((byte) 0),
        UTM((byte) 1),
        Albers((byte) 2),
        Lambert((byte) 3),
        Mercator((byte) 4),
        Gauss((byte) 5),
        Polyconic((byte) 6),
        EQ_Dist((byte) 7),
        Transverse_Mecator((byte) 8),
        StereoGraphic((byte) 9),
        Lambert_Azimuthal((byte) 10),
        Azimuthal_EQ((byte) 11),
        Gnomonic((byte) 12),
        Orthographic((byte) 13),
        General_VER_NS((byte) 14),
        Sinusoidal((byte) 15),
        Equirectangular((byte) 16),
        Miller_Cylindrical((byte) 17),
        V_D_Grinten_I((byte) 18),
        Oblique_Mercator((byte) 19),
        Polar_Srereographic((byte) 20),
        WebMercator((byte) 21);

        private final byte m_typeId;

        ProjectionType(byte typeId) {
            this.m_typeId = typeId;
        }

        @Override // com.AoGIS.base.INativeByteEnum
        public byte getNativeID() {
            return this.m_typeId;
        }

        public static ProjectionType fromNativeID(byte id) {
            ProjectionType[] values = values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getNativeID() == id) {
                    return values[i];
                }
            }
            throw new IllegalStateException("Wrong ProjectionType ID from Native:" + ((int) id));
        }
    }

    public enum LengthType implements INativeByteEnum {
        MM((byte) 0),
        MilliMeter((byte) 1),
        Meter((byte) 2),
        Second((byte) 3),
        Degree((byte) 4),
        DMS((byte) 5),
        Foot((byte) 6),
        Minute((byte) 7),
        Radian((byte) 8),
        GRAD((byte) 9),
        KiloMeter((byte) 10),
        DeciMeter((byte) 11),
        CentiMeter((byte) 12);

        private final byte m_typeId;

        LengthType(byte typeId) {
            this.m_typeId = typeId;
        }

        @Override // com.AoGIS.base.INativeByteEnum
        public byte getNativeID() {
            return this.m_typeId;
        }

        public static LengthType fromNativeID(byte id) {
            LengthType[] values = values();
            for (int i = 0; i < values.length; i++) {
                if (values[i].getNativeID() == id) {
                    return values[i];
                }
            }
            throw new IllegalStateException("Wrong LengthType ID from Native:" + ((int) id));
        }
    }

    public CoordSysType getCoordSysType() {
        return CoordSysType.fromNativeID(this.type);
    }

    public void setCoordSysType(CoordSysType etype) {
        this.type = etype.getNativeID();
    }

    public EarthType getEarthType() {
        return EarthType.fromNativeID(this.earthParam);
    }

    public void setEarthType(EarthType etype) {
        this.earthParam = etype.getNativeID();
    }

    public ProjectionType getProjectionType() {
        return ProjectionType.fromNativeID(this.projType);
    }

    public void setProjectionType(ProjectionType etype) {
        this.projType = etype.getNativeID();
    }

    public LengthType getCoordType() {
        return LengthType.fromNativeID(this.unit);
    }

    public void setCoordType(LengthType etype) {
        this.unit = etype.getNativeID();
    }
}
