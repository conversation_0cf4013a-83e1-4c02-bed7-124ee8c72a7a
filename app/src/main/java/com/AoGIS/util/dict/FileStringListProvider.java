package com.AoGIS.util.dict;

import com.AoGIS.AoGISApplication;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class FileStringListProvider implements IStringListProvider {
    private String filename;
    private ArrayList<String> list = new ArrayList<>(20);
    private boolean available = false;
    private boolean cached = false;

    public FileStringListProvider(String path, String filename) {
        this.filename = null;
        this.filename = path + "/" + filename + ".dic";
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public String[] getStringList() {
        if (!this.cached) {
            refresh();
        }
        return (String[]) this.list.toArray(new String[this.list.size()]);
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public void setStringList(String itemStrings) {
        try {
            File file = new File(this.filename);
            if (!file.exists()) {
                file.createNewFile();
            }
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(this.filename), Charset.forName("GBK")));
            writer.write(itemStrings);
            writer.close();
            this.cached = false;
            this.available = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public boolean isAvailable() {
        if (this.available) {
            return true;
        }
        try {
            FileReader r = new FileReader(this.filename);
            r.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public boolean refresh() {
        String strPrompt;
        ArrayList<String> newList = new ArrayList<>(30);
        boolean result = false;
        try {
            BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(this.filename), Charset.forName("GBK")));
            for (String line = reader.readLine(); line != null; line = reader.readLine()) {
                if (line.trim().length() > 0) {
                    newList.add(line);
                }
            }
            reader.close();
            this.list = newList;
            this.cached = true;
            result = true;
            this.available = true;
            return true;
        } catch (Exception e) {
            if (AoGISApplication.getAoGISLanguage() == AoGISApplication.AoGISLanguage.Chinese) {
                strPrompt = "找不到相应字典：";
            } else {
                strPrompt = "Can't find the dictionary: ";
            }
            newList.add(strPrompt + this.filename);
            if (!this.cached) {
                this.list = newList;
                return result;
            }
            return result;
        }
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public Object[] getValueList() {
        return getStringList();
    }

    @Override // com.AoGIS.util.dict.IStringListProvider
    public int getCount() {
        if (this.list != null) {
            return this.list.size();
        }
        return 0;
    }
}
