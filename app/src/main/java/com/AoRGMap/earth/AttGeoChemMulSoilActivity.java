package com.AoRGMap.earth;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.RGMapPreferences;
import com.AoRGMap.dao.DBOpenHelper;
import com.AoRGMap.prb.PointParams;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import org.apache.http.HttpHeaders;

/* loaded from: classes.dex */
public class AttGeoChemMulSoilActivity extends AttributeActivity {
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    double lpX = 0.0d;
    double lpY = 0.0d;
    private DBOpenHelper m_helper = null;
    private SQLiteDatabase m_db = null;
    int iSelFiled = 0;
    String[] fieldsStrings = null;
    String m_strDicDbpath = "/data/data/com.AoRGMap/AoGIS/geochemmul.db";
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    AttributeItem ItemMainID = null;
    AttributeItem ItemProvince = null;
    AttributeItem ItemColor = null;
    AttributeItem ItemLandForm = null;
    AttributeItem Itemdate = null;
    AttributeItem ItemSoilUse = null;
    AttributeItem ItemCause = null;
    AttributeItem ItemPollution = null;
    AttributeItem ItemErode = null;
    AttributeItem ItemSalinization = null;
    AttributeItem ItemSamCode = null;
    AttributeItem ItemStartDepth = null;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttGeoChemMulSoilActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strGeoChemMulsoil));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.m_helper = new DBOpenHelper(getBaseContext(), this.m_strDicDbpath);
        this.m_db = this.m_helper.getReadableDatabase();
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        if (!IsInputSamCode()) {
            Toast.makeText(this, "请输入样品号", 0).show();
            return;
        }
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        String strCodeString = this.ItemProvince.getEditorEditText().getText().toString();
        String strValueString = GetVauleByCode("Province", "Code", strCodeString, "Item");
        getAttributeMap().put("Province", strValueString);
        String strCodeString2 = this.ItemColor.getEditorEditText().getText().toString();
        String strValueString2 = GetVauleByCode("Color", "Code", strCodeString2, "Item");
        getAttributeMap().put("Color", strValueString2);
        String strCodeString3 = this.ItemPollution.getEditorEditText().getText().toString();
        String strValueString3 = GetVauleByCode("Pollution", "Code", strCodeString3, "Item");
        getAttributeMap().put("Pollution", strValueString3);
        String strCodeString4 = this.ItemErode.getEditorEditText().getText().toString();
        String strValueString4 = GetVauleByCode("Erode", "Code", strCodeString4, "Item");
        getAttributeMap().put("Erode", strValueString4);
        String strCodeString5 = this.ItemSalinization.getEditorEditText().getText().toString();
        String strValueString5 = GetVauleByCode("Salinization", "Code", strCodeString5, "Item");
        getAttributeMap().put("Salinization", strValueString5);
        String strCodeString6 = this.ItemCause.getEditorEditText().getText().toString();
        String strValueString6 = GetVauleByCode("Cause", "Code", strCodeString6, "Item");
        getAttributeMap().put("Cause", strValueString6);
        String strCodeString7 = this.ItemSoilUse.getEditorEditText().getText().toString();
        String strValueString7 = GetVauleByCode("SoilUse", "Code", strCodeString7, "Item");
        getAttributeMap().put("SoilUse", strValueString7);
        String strCodeString8 = this.ItemLandForm.getEditorEditText().getText().toString();
        String strValueString8 = GetVauleByCode("Landform", "Code", strCodeString8, "Item");
        getAttributeMap().put("Landform", strValueString8);
        area.setNamedAttributes(getGeometryId(), data);
        if (this.m_db.isOpen()) {
            this.m_db.close();
        }
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        if (this.m_db.isOpen()) {
            this.m_db.close();
        }
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_earth_5geosoil);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        AttributeItem item = AttributeUIHelper.createItem(group, "MainID", R.string.GEOCHEMMULSOIL_FIELD_MAINID, EditTextEditorInfo.textEditor(), map, "41");
        item.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_MAINID);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemMainID, titleString, "MainID", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item);
        this.ItemMainID = item;
        AttributeItem item2 = AttributeUIHelper.createItem(group, "MapCode", R.string.GEOCHEMMULSOIL_FIELD_MAPCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item2.setPropDictName(getResources().getString(R.string.dic_multfh));
        this.itemList.add(item2);
        this.itemList.add(AttributeUIHelper.createItem(group, RGMapPreferences.mMapName, R.string.GEOCHEMMULSOIL_FIELD_MAPNAME, EditTextEditorInfo.textEditor(), map, (String) null));
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, "RouteCode", R.string.GEOCHEMMULSOIL_FIELD_ROUTECODE, EditTextEditorInfo.textEditor(), map, defData));
        AttributeItem item3 = AttributeUIHelper.createItem(group, "SamCode", R.string.GEOCHEMMULSOIL_FIELD_SAMCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item3);
        this.ItemSamCode = item3;
        this.itemList.add(AttributeUIHelper.createItem(group, "SamOrgCode", R.string.GEOCHEMMULSOIL_FIELD_SAMORGCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item4 = AttributeUIHelper.createItem(group, "Province_C", R.string.GEOCHEMMULSOIL_FIELD_PROVINCE, EditTextEditorInfo.textEditor(), map, (String) null);
        item4.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_PROVINCE);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemProvince, titleString, "Province", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item4);
        this.ItemProvince = item4;
        String defData2 = String.valueOf(getX());
        AttributeItem item5 = AttributeUIHelper.createItem(group, "CoordX", R.string.GEOCHEMMULSOIL_FIELD_COORDX, EditTextEditorInfo.textEditor(), map, defData2);
        item5.getEditorEditText().setEnabled(false);
        this.itemList.add(item5);
        String defData3 = String.valueOf(getY());
        AttributeItem item6 = AttributeUIHelper.createItem(group, "CoordY", R.string.GEOCHEMMULSOIL_FIELD_COORDY, EditTextEditorInfo.textEditor(), map, defData3);
        item6.getEditorEditText().setEnabled(false);
        this.itemList.add(item6);
        this.itemList.add(AttributeUIHelper.createItem(group, "Altitude", R.string.GEOCHEMMULSOIL_FIELD_ALTITUDE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Component", R.string.GEOCHEMMULSOIL_FIELD_COMPONENT, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item7 = AttributeUIHelper.createItem(group, "Color_C", R.string.GEOCHEMMULSOIL_FIELD_COLOR, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_PROVINCE);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemColor, titleString, "Color", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item7);
        this.ItemColor = item7;
        this.itemList.add(AttributeUIHelper.createItem(group, HttpHeaders.DEPTH, R.string.GEOCHEMMULSOIL_FIELD_DEPTH, EditTextEditorInfo.decimalEditor(), map, (String) null));
        AttributeItem item8 = AttributeUIHelper.createItem(group, "Pollution_C", R.string.GEOCHEMMULSOIL_FIELD_POLLUTION, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_POLLUTION);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemPollution, titleString, "Pollution", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item8);
        this.ItemPollution = item8;
        AttributeItem item9 = AttributeUIHelper.createItem(group, "Erode_C", R.string.GEOCHEMMULSOIL_FIELD_ERODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item9.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_ERODE);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemErode, titleString, "Erode", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item9);
        this.ItemErode = item9;
        AttributeItem item10 = AttributeUIHelper.createItem(group, "Salinization_C", R.string.GEOCHEMMULSOIL_FIELD_SALINIZATION, EditTextEditorInfo.textEditor(), map, (String) null);
        item10.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_SALINIZATION);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemSalinization, titleString, "Salinization", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item10);
        this.ItemSalinization = item10;
        AttributeItem item11 = AttributeUIHelper.createItem(group, "Cause_C", R.string.GEOCHEMMULSOIL_FIELD_CAUSE, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.7
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_CAUSE);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemCause, titleString, "Cause", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item11);
        this.ItemCause = item11;
        AttributeItem item12 = AttributeUIHelper.createItem(group, "SoilUse_C", R.string.GEOCHEMMULSOIL_FIELD_SOILUSE, EditTextEditorInfo.textEditor(), map, (String) null);
        item12.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.8
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_SOILUSE);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemSoilUse, titleString, "SoilUse", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item12);
        this.ItemSoilUse = item12;
        AttributeItem item13 = AttributeUIHelper.createItem(group, "Landform_C", R.string.GEOCHEMMULSOIL_FIELD_LANDFORM, EditTextEditorInfo.textEditor(), map, (String) null);
        item13.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.9
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String titleString = AttGeoChemMulSoilActivity.this.getResources().getString(R.string.GEOCHEMMULSOIL_FIELD_LANDFORM);
                AttGeoChemMulSoilActivity.this.BtnClickFun(AttGeoChemMulSoilActivity.this.ItemLandForm, titleString, "Landform", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item13);
        this.ItemLandForm = item13;
        this.itemList.add(AttributeUIHelper.createItem(group, "GpsID", R.string.GEOCHEMMULSOIL_FIELD_GPSID, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "GpsFileID", R.string.GEOCHEMMULSOIL_FIELD_GPSFILEID, EditTextEditorInfo.textEditor(), map, "GPS.TA"));
        this.itemList.add(AttributeUIHelper.createItem(group, "UnitCode", R.string.GEOCHEMMULSOIL_FIELD_UNIT, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item14 = AttributeUIHelper.createItem(group, "StartDepth", R.string.GEOCHEMMULSOIL_FIELD_STARTDEPTH, EditTextEditorInfo.decimalEditor(), map, (String) null);
        item14.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.10
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String teString = AttGeoChemMulSoilActivity.this.ItemStartDepth.getEditorEditText().getText().toString();
                if (teString.equals("")) {
                    teString = "0.0";
                }
                double dstartDepth = Double.valueOf(teString).doubleValue();
                int istartDepth = (int) dstartDepth;
                String strSamCode = AttGeoChemMulSoilActivity.this.ItemSamCode.getEditorEditText().getText().toString();
                if (strSamCode.equals("")) {
                    Toast.makeText(AttGeoChemMulSoilActivity.this, "请输入样品号", 0).show();
                    return;
                }
                String strDbPath = AoRGMapActivity.getCurrentMapPath() + "/geochemmulsoillry.db";
                File file = new File(strDbPath);
                if (!file.exists()) {
                    try {
                        file.createNewFile();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                Intent intent = new Intent(AttGeoChemMulSoilActivity.this, (Class<?>) AttGeoChemMulSoilLayActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(AttGeoChemMulSoilLayActivity.GEOCHEMLAYER_DB_PATH, strDbPath);
                bundle.putString("GEOCHEMLAYERSAMID", strSamCode);
                bundle.putInt(AttGeoChemMulSoilLayActivity.GEOCHEMLAYER_SAM_STARTDEPTH, istartDepth);
                intent.putExtras(bundle);
                AttGeoChemMulSoilActivity.this.startActivity(intent);
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.ItemStartDepth = item14;
        this.itemList.add(item14);
        this.itemList.add(AttributeUIHelper.createItem(group, "BagCode", R.string.GEOCHEMMULSOIL_FIELD_BAGCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "GpsMapFile", R.string.GEOCHEMMULSOIL_FIELD_GPSMAPFILE, EditTextEditorInfo.textEditor(), map, PRBAreas.m_strGPS));
        this.itemList.add(AttributeUIHelper.createItem(group, "Description", R.string.GEOCHEMMULSOIL_FIELD_DESC, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeUIHelper.createItem(group, "Remark", R.string.GEOCHEMMULSOIL_FIELD_REMARK, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark1", R.string.GEOCHEMMULSOIL_FIELD_REMARK1, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark2", R.string.GEOCHEMMULSOIL_FIELD_REMARK2, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark3", R.string.GEOCHEMMULSOIL_FIELD_REMARK3, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark4", R.string.GEOCHEMMULSOIL_FIELD_REMARK4, EditTextEditorInfo.textEditor(), map, (String) null));
        final String strTemp = getResources().getString(R.string.RGMAP_PROMPT_SETTIME);
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        df.format(date);
        AttributeItem item15 = AttributeUIHelper.createItem(group, "SamDate", R.string.GEOCHEMMULSOIL_FIELD_SAMDATE, EditTextEditorInfo.textEditor(), map, (String) null);
        item15.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.11
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttGeoChemMulSoilActivity.this);
                dateDialog.showListDialog(strTemp, AttGeoChemMulSoilActivity.this.Itemdate.getEditorEditText());
            }
        }, null));
        this.itemList.add(item15);
        this.Itemdate = item15;
        AttributeItem item16 = AttributeUIHelper.createItem(group, "Recorder", R.string.GEOCHEMMULSOIL_FIELD_RECORDER, EditTextEditorInfo.textEditor(), map, (String) null);
        item16.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item16);
        AttributeItem item17 = AttributeUIHelper.createItem(group, "Sampler", R.string.GEOCHEMMULSOIL_FIELD_SAMPLER, EditTextEditorInfo.textEditor(), map, (String) null);
        item17.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item17);
        AttributeItem item18 = AttributeUIHelper.createItem(group, "Checker", R.string.GEOCHEMMULSOIL_FIELD_CHECKER, EditTextEditorInfo.textEditor(), map, (String) null);
        item18.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item18);
        container.addView(group.getInnerView());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void BtnClickFun(final AttributeItem v, String title, String tabname, String field1, String field2) {
        String strSQL = String.format("select * from %s", tabname);
        Cursor SectionSursor = this.m_db.rawQuery(strSQL, null);
        int isize = SectionSursor.getCount();
        this.fieldsStrings = new String[isize];
        int iFiled = 0;
        while (SectionSursor.moveToNext()) {
            int columnIndex = SectionSursor.getColumnIndex(field1);
            String szFieldValue = SectionSursor.getString(columnIndex);
            int columnIndex1 = SectionSursor.getColumnIndex(field2);
            String szFieldValue1 = SectionSursor.getString(columnIndex1);
            this.fieldsStrings[iFiled] = szFieldValue + ":" + szFieldValue1;
            iFiled++;
        }
        AlertDialog.Builder builder1 = new AlertDialog.Builder(this);
        builder1.setTitle(title);
        builder1.setSingleChoiceItems(this.fieldsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.12
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                AttGeoChemMulSoilActivity.this.iSelFiled = which;
            }
        });
        builder1.setPositiveButton(getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulSoilActivity.13
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                String codeString = AttGeoChemMulSoilActivity.this.fieldsStrings[AttGeoChemMulSoilActivity.this.iSelFiled];
                v.getEditorEditText().setText(codeString.split(":")[0]);
                AttGeoChemMulSoilActivity.this.iSelFiled = 0;
            }
        });
        builder1.show();
    }

    private String GetVauleByCode(String tabname, String field1, String value1, String field2) {
        String result = "";
        String strSQL = String.format("select * from %s where %s = '%s'", tabname, field1, value1);
        Cursor SectionSursor = this.m_db.rawQuery(strSQL, null);
        while (SectionSursor.moveToNext()) {
            int columnIndex1 = SectionSursor.getColumnIndex(field2);
            result = SectionSursor.getString(columnIndex1);
        }
        return result;
    }

    private boolean IsInputSamCode() {
        String Temp = this.ItemSamCode.getEditorEditText().getText().toString();
        return !Temp.equals("");
    }
}
