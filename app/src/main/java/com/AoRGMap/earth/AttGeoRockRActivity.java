package com.AoRGMap.earth;

import android.view.ViewGroup;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.base.GeoLine;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import java.util.ArrayList;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGeoRockRActivity extends AttributeActivity {
    private AttributeItem item_miaoshu;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();

    public AttGeoRockRActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strGeoRockR));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTE_CODE")) {
            String strCode = getAttributeMap().get("ROUTE_CODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        if (getAttributeMap().containsKey("KCANB_NAME")) {
            String strTemp = getAttributeMap().get("KCANB_NAME").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strTemp);
        }
        if (getAttributeMap().containsKey("KCANB_CODE")) {
            String strTemp2 = getAttributeMap().get("KCANB_CODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strTemp2);
        }
        if (getAttributeMap().containsKey("KTX_CODE")) {
            String strTemp3 = getAttributeMap().get("KTX_CODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strTemp3);
        }
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        WorkAreaParams mapInfo = getWorkArea().getWorkAreaParamsClone();
        double distance = 0.0d;
        double unit = 1.0d;
        if (mapInfo.getCoordType() != WorkAreaParams.LengthType.Meter) {
            unit = 1000.0d;
        }
        GeoLine line = (GeoLine) getWorkArea().getGeometry(getGeometryId());
        for (int i = 1; i < line.getPointCount(); i++) {
            double dx = line.getX(i - 1) - line.getX(i);
            double dy = line.getY(i - 1) - line.getY(i);
            distance += Math.sqrt((dx * dx) + (dy * dy));
        }
        double x1 = line.getX(0);
        double y1 = line.getY(0);
        double x2 = line.getX(line.getPointCount() - 1);
        double y2 = line.getY(line.getPointCount() - 1);
        double angle = 90.0d - ((180.0d * Math.atan2(y2 - y1, x2 - x1)) / 3.14159d);
        if (angle < 0.0d) {
            angle += 360.0d;
        }
        if (angle == 360.0d) {
        }
        double distance2 = (mapInfo.rate * distance) / unit;
        String sql = String.format("%s like '%s'", getResources().getString(R.string.RGMAP_FLDNAME_GEOPOINT), String.format("P%04d", Integer.valueOf(gstate.getStartPointNumber())));
        String attDis = getResources().getString(R.string.RGMAP_FLDNAME_RDISTANCE);
        int[] ids = getWorkArea().getGeometryIdListByAtt(sql);
        double sumL = 0.0d;
        if (ids != null) {
            for (int i2 : ids) {
                sumL += getWorkArea().Attributes.getDouble(i2, attDis);
            }
        }
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_r);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        EditTextEditorInfo decimalEditor = EditTextEditorInfo.decimalEditor();
        String defData = String.format("%.0f", Double.valueOf(sumL + distance2));
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_LENGTH, R.string.EARTH_ROUTE_LENGTH, decimalEditor, map, defData);
        this.itemList.add(item);
        String defData2 = gstate.getCurKcanbName();
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_KCANBNAME, R.string.EARTH_ROUTE_KCANBNAME, EditTextEditorInfo.textEditor(), map, defData2);
        this.itemList.add(item2);
        String defData3 = gstate.getCurKcanbCode();
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_KCANBCODE, R.string.EARTH_ROUTE_KCANBCODE, EditTextEditorInfo.textEditor(), map, defData3);
        this.itemList.add(item3);
        String defData4 = gstate.getCurKtxCode();
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_KTXCODE, R.string.EARTH_ROUTE_KTXCODE, EditTextEditorInfo.textEditor(), map, defData4);
        this.itemList.add(item4);
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_WORKPLACE, R.string.EARTH_ROUTE_WORKPLACE, EditTextEditorInfo.textEditor(), map, defData4);
        this.itemList.add(item5);
        String defData5 = gstate.GetCurrentRouteCode();
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_ROUTE_CODE, R.string.EARTH_ROUTE_ROUTE_CODE, EditTextEditorInfo.textEditor(), map, defData5);
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_TASK, R.string.EARTH_ROUTE_TASK, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_DATE, R.string.EARTH_ROUTE_DATE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_UNIT, R.string.EARTH_ROUTE_UNIT, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item9);
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_RECORDER, R.string.EARTH_ROUTE_RECORDER, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_SAMPLER, R.string.EARTH_ROUTE_SAMPLER, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item11);
        AttributeItem item12 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_FELLOW, R.string.EARTH_ROUTE_FELLOW, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item12);
        AttributeItem item13 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_ROUTE_REMARK, R.string.EARTH_ROUTE_REMARK, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item13);
        container.addView(group.getInnerView());
    }
}
