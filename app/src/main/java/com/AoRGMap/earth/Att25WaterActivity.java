package com.AoRGMap.earth;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.dialog.GPSDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.prb.PointParams;
import java.util.ArrayList;
import java.util.Map;

/* loaded from: classes.dex */
public class Att25WaterActivity extends AttributeActivity {
    private AttributeItem item_miaoshu;
    double lpX;
    double lpY;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private AttributeItem itemLon = null;
    private AttributeItem itemLat = null;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public Att25WaterActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_str25Water));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        if (isNewFlag()) {
            RGMapApplication.getCurrentApp().getCurrentGlobal().addStartPointNumber();
        }
        updateAttributeMap(this.itemList);
        String kX = getResources().getString(R.string.EARTH_FLDNAME_25ROCK_CHAHAB);
        String kY = getResources().getString(R.string.EARTH_FLDNAME_25ROCK_CHAHAA);
        getAttributeMap().put(kX, Double.valueOf(getX()));
        getAttributeMap().put(kY, Double.valueOf(getY()));
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTE_CODE")) {
            String strCode = getAttributeMap().get("ROUTE_CODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_p);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_ROUTE_CODE, R.string.EARTH_25WATER_ROUTE_CODE, EditTextEditorInfo.textEditor(), map, defData);
        this.itemList.add(item);
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTSXINX, R.string.EARTH_25WATER_HTSXINX, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item2);
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWRNM, R.string.EARTH_25WATER_HTWRNM, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item3);
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_QDAEC, R.string.EARTH_25WATER_QDAEC, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item4);
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_TKBEAL, R.string.EARTH_25WATER_TKBEAL, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_MAPCODE, R.string.EARTH_25WATER_MAPCODE, EditTextEditorInfo.textEditor(), map, defData);
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_ADMCODE, R.string.EARTH_25WATER_ADMCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_QDAJ, R.string.EARTH_25WATER_QDAJ, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_CHAHUN, R.string.EARTH_25WATER_CHAHUN, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item9);
        String strData = String.format("%f, %f", Double.valueOf(getX()), Double.valueOf(getY()));
        AttributeItem item10 = AttributeUIHelper.createItem(group, (String) null, R.string.gpoint_xy, EditTextEditorInfo.textEditor(), strData, getResources().getString(R.string.nodata));
        item10.getEditorEditText().setEnabled(false);
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_DDAEBA, R.string.EARTH_25WATER_DDAEBA, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item11);
        this.itemLon = item11;
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.Att25WaterActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                GlobalState GState = RGMapApplication.getCurrentApp().getCurrentGlobal();
                GPSDialog gpsDialog = new GPSDialog(Att25WaterActivity.this, GState.getWorkAreaParams(), GState.getGpsRectifyX(), GState.getGpsRectifyY());
                String strTemp = Att25WaterActivity.this.getResources().getString(R.string.RGMAP_PROMPT_GPSINFO);
                gpsDialog.showListDialog(strTemp, Att25WaterActivity.this.itemLon.getEditorEditText(), Att25WaterActivity.this.itemLat.getEditorEditText(), null, null, null);
            }
        }, null));
        AttributeItem item12 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_DDAEBB, R.string.EARTH_25WATER_DDAEBB, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item12);
        this.itemLat = item12;
        AttributeItem item13 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTDEM, R.string.EARTH_25WATER_HTDEM, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item13);
        AttributeItem item14 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_CHAHNV, R.string.EARTH_25WATER_CHAHNV, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item14);
        AttributeItem item15 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTTP, R.string.EARTH_25WATER_HTWTTP, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item15);
        AttributeItem item16 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTDP, R.string.EARTH_25WATER_HTWTDP, EditTextEditorInfo.decimalEditor(), map, (String) null);
        item16.setPropDictName(getResources().getString(R.string.dic_yanxing));
        this.itemList.add(item16);
        AttributeItem item17 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTWD, R.string.EARTH_25WATER_HTWTWD, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item17);
        AttributeItem item18 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTYS, R.string.EARTH_25WATER_HTWTYS, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item18);
        AttributeItem item19 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTSM, R.string.EARTH_25WATER_HTWTSM, EditTextEditorInfo.textEditor(), map, (String) null);
        item19.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item19);
        AttributeItem item20 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTWR, R.string.EARTH_25WATER_HTWTWR, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item20);
        AttributeItem item21 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWTPH, R.string.EARTH_25WATER_HTWTPH, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item21);
        AttributeItem item22 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTCYWZ, R.string.EARTH_25WATER_HTCYWZ, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item22);
        AttributeItem item23 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_YSEB, R.string.EARTH_25WATER_YSEB, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item23);
        AttributeItem item24 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTWYNO, R.string.EARTH_25WATER_HTWYNO, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item24);
        AttributeItem item25 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_25WATER_HTNOTE, R.string.EARTH_25WATER_HTNOTE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item25);
        container.addView(group.getInnerView());
    }
}
