package com.AoRGMap.earth;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoDlgRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.MyImageView1Activity;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.dao.GeoChemMulMedia25wDao;
import com.AoRGMap.pm.AttCommParams;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGeoChemMulMedia25wActivity extends AttributeDBActivity {
    public static final String ITEM_MAINID = "MainID";
    public static final String ITEM_SAMPLECODE = "SampleCode";
    public static final String MEDIA_DB_FLORD = "DbFLORD";
    public static final int RequestMap = 1;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private GeoChemMulMedia25wDao mSqlDB = null;
    private String mSampleCode = null;
    private String mMainID = null;
    private String mDbpath = null;
    private String mPath = null;
    private int iOpType = 0;
    private String tableName = "MediaBaseInfo";
    private AttributeItem mItem = null;

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void onInitializeViews(AttributeDBActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        this.mDbpath = bundle.getString(AttCommParams.PARAM_STRING_DB_PATH);
        this.mPath = bundle.getString("DbFLORD");
        this.mSampleCode = bundle.getString("SampleCode");
        this.mMainID = bundle.getString("MainID");
        this.iOpType = bundle.getInt(AttCommParams.PARAM_INT_SQLDB_OP_TYPE);
        this.mSqlDB = new GeoChemMulMedia25wDao(this, this.mDbpath);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        updateAttributeMap(this.itemList);
        GdbAttributesMap<String, Object> data = getAttributeMap();
        int iSortID = getSortID();
        switch (this.iOpType) {
            case 1:
                this.mSqlDB.add(this.tableName, data, getfieldsMap());
                break;
            case 3:
                this.mSqlDB.update(this.tableName, iSortID, data, getfieldsMap());
                break;
        }
        setResult(-1);
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void onClickSave() {
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void InitFieldsInfo(HashMap<String, AttributeDBActivity.FieldType> fields) {
        fields.put("_ID", AttributeDBActivity.FieldType.INT);
        fields.put("SAMCODE", AttributeDBActivity.FieldType.STRING);
        fields.put("WORKPLACE", AttributeDBActivity.FieldType.STRING);
        fields.put("LOCATION", AttributeDBActivity.FieldType.STRING);
        fields.put("MAINID", AttributeDBActivity.FieldType.INT);
        fields.put("DEVICETYPE", AttributeDBActivity.FieldType.STRING);
        fields.put("LOCATIONMAPID", AttributeDBActivity.FieldType.STRING);
        fields.put("PERSON", AttributeDBActivity.FieldType.STRING);
        fields.put("REMARK", AttributeDBActivity.FieldType.STRING);
    }

    private void initMainView(ViewGroup container) {
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.GEOCHEM25W_MULMEDIA__PROMPT_TITLE);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = this.mSampleCode;
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA_FLDNAME_SAMCODE, R.string.GEOCHEM25W_MULMEDIA__PROMPT_SAMCODE, EditTextEditorInfo.textEditor(), map, defData);
        item.getEditorEditText().setEnabled(false);
        this.itemList.add(item);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_WORKPLACE, R.string.GEOCHEM25W_MULMEDIA__PROMPT_WORKPLACE, EditTextEditorInfo.textEditor(), map, ""));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_LOCATION, R.string.GEOCHEM25W_MULMEDIA__PROMPT_LOCATION, EditTextEditorInfo.textEditor(), map, ""));
        String defData2 = this.mMainID;
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_MAINID, R.string.GEOCHEM25W_MULMEDIA__PROMPT_MAINID, EditTextEditorInfo.unsignedNumberEditor(), map, defData2);
        item2.getEditorEditText().setEnabled(false);
        this.itemList.add(item2);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_DEVICETYPE, R.string.GEOCHEM25W_MULMEDIA__PROMPT_DEVICETYPE, EditTextEditorInfo.textEditor(), map, ""));
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_LOCATIONMAPID, R.string.GEOCHEM25W_MULMEDIA__PROMPT_LOCATIONMAPID, EditTextEditorInfo.textEditor(), map, "");
        this.itemList.add(item3);
        this.mItem = item3;
        item3.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulMedia25wActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                Intent intent = new Intent(AttGeoChemMulMedia25wActivity.this, (Class<?>) AoDlgRGMapActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(AoDlgRGMapActivity.AODLGRGMAP_ITEM_FLORD, AttGeoChemMulMedia25wActivity.this.mPath);
                bundle.putString(AoDlgRGMapActivity.AODLGRGMAP_ITEM_SAMPLECODE, AttGeoChemMulMedia25wActivity.this.mSampleCode);
                bundle.putString(AoDlgRGMapActivity.AODLGRGMAP_ITEM_MAINID, AttGeoChemMulMedia25wActivity.this.mMainID);
                intent.putExtras(bundle);
                AttGeoChemMulMedia25wActivity.this.startActivityForResult(intent, 1);
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_crop)));
        item3.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulMedia25wActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String fileString = AttGeoChemMulMedia25wActivity.this.mSampleCode + "_" + AttGeoChemMulMedia25wActivity.this.mMainID + "_loc";
                Intent intentimg = new Intent(AttGeoChemMulMedia25wActivity.this, (Class<?>) MyImageView1Activity.class);
                Bundle bundle = new Bundle();
                bundle.putString("VIDE0PATH", fileString);
                bundle.putString("FolderATH", AttGeoChemMulMedia25wActivity.this.mPath);
                intentimg.putExtras(bundle);
                AttGeoChemMulMedia25wActivity.this.startActivity(intentimg);
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_ff)));
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_PERSON, R.string.GEOCHEM25W_MULMEDIA__PROMPT_PERSON, EditTextEditorInfo.textEditor(), map, "");
        item4.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item4);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.GEOCHEM25W_MULMEDIA__FLDNAME_REMARK, R.string.GEOCHEM25W_MULMEDIA__PROMPT_REMARK, EditTextEditorInfo.textEditor(), map, ""));
        AttributeItem.AttributeItemParams mediaDesc = new AttributeItem.AttributeItemParams();
        mediaDesc.label = getResources().getString(R.string.GEOCHEM25W_MULMEDIA__PROMPT_TOOL);
        mediaDesc.clickable = new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemMulMedia25wActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent intent6 = new Intent(AttGeoChemMulMedia25wActivity.this, (Class<?>) MulActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString(MulActivity.MEDIA_DB_PATH, AttGeoChemMulMedia25wActivity.this.mDbpath);
                bundle.putString("DbFLORD", AttGeoChemMulMedia25wActivity.this.mPath);
                bundle.putString("SampleCode", AttGeoChemMulMedia25wActivity.this.mSampleCode);
                bundle.putString("MainID", AttGeoChemMulMedia25wActivity.this.mMainID);
                intent6.putExtras(bundle);
                AttGeoChemMulMedia25wActivity.this.startActivity(intent6);
            }
        }, null);
        group.addItem(mediaDesc);
        container.addView(group.getInnerView());
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == -1) {
            switch (requestCode) {
                case 1:
                    String fileString = this.mSampleCode + "_" + this.mMainID + "_loc";
                    this.mItem.getEditorEditText().setText(fileString);
                    break;
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }
}
