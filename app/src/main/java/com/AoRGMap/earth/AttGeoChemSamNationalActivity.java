package com.AoRGMap.earth;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.prb.PointParams;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGeoChemSamNationalActivity extends AttributeActivity {
    public static final int RequestCamera = 1;
    private AttributeItem ItemLandSetting;
    private AttributeItem ItemTopography;
    private AttributeItem ItemType;
    private AttributeItem ItemWeather;
    private AttributeItem ItemZ;
    private AttributeItem dataItem;
    double m_dCenterLon;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    double lpX = 0.0d;
    double lpY = 0.0d;
    WorkAreaParams.ProjectionType m_PrjType = WorkAreaParams.ProjectionType.None;
    WorkAreaParams.EarthType m_EarthType = WorkAreaParams.EarthType.Beijing54;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttGeoChemSamNationalActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strGeoChemSamNational));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        this.m_PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        this.m_EarthType = MapParam.getEarthType();
        this.m_dCenterLon = getRadFromDeg(MapParam.lon);
        if (this.m_PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType);
            String szTemp = String.format("%.4f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            getAttributeMap().put("Longitude", String.valueOf(dTemp));
            String szTemp2 = String.format("%.4f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            getAttributeMap().put("Latitude", String.valueOf(dTemp2));
        } else if (this.m_PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType, MapParam.dx, MapParam.dy);
            String szTemp3 = String.format("%.4f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            getAttributeMap().put("Longitude", String.valueOf(dTemp3));
            String szTemp4 = String.format("%.4f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            getAttributeMap().put("Latitude", String.valueOf(dTemp4));
        } else {
            getAttributeMap().put("Longitude", String.valueOf(getX()));
            getAttributeMap().put("Latitude", String.valueOf(getY()));
        }
        getGeometryId();
        area.setNamedAttributes(getGeometryId(), data);
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_earth_5geosoil);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        AttributeItem item = AttributeUIHelper.createItem(group, "RouteCode", R.string.GEOCHEMNATIONAL_PROMPT_RouteCode, EditTextEditorInfo.textEditor(), map, defData);
        this.itemList.add(item);
        AttributeItem item2 = AttributeUIHelper.createItem(group, "CardNo", R.string.GEOCHEMNATIONAL_PROMPT_CARDNO, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item2);
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String defData2 = df.format(date);
        AttributeItem item3 = AttributeUIHelper.createItem(group, "SamDate", R.string.GEOCHEMNATIONAL_PROMPT_SamDate, EditTextEditorInfo.textEditor(), map, defData2);
        this.itemList.add(item3);
        this.dataItem = item3;
        item3.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttGeoChemSamNationalActivity.this);
                String str = AttGeoChemSamNationalActivity.this.getResources().getString(R.string.RGMAP_PROMPT_SETDATE);
                dateDialog.showListDialog(str, AttGeoChemSamNationalActivity.this.dataItem.getEditorEditText());
            }
        }, null));
        AttributeItem item4 = AttributeUIHelper.createItem(group, "Weather", R.string.GEOCHEMNATIONAL_PROMPT_Weather, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item4);
        item4.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String[] itemValues = {"Clear", " Cloudy", "Raniy"};
                AttGeoChemSamNationalActivity.this.showDic(AttGeoChemSamNationalActivity.this.ItemWeather, itemValues);
            }
        }, null));
        this.ItemWeather = item4;
        AttributeItem item5 = AttributeUIHelper.createItem(group, "SamPerson", R.string.GEOCHEMNATIONAL_PROMPT_SamPerson, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, "Registrar", R.string.GEOCHEMNATIONAL_PROMPT_Registrar, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, "Leader", R.string.GEOCHEMNATIONAL_PROMPT_Leader, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, "SamNo", R.string.GEOCHEMNATIONAL_PROMPT_SamNo, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, "MapSheet", R.string.GEOCHEMNATIONAL_PROMPT_MapSheet, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item9);
        String defData3 = String.valueOf(getX());
        AttributeItem item10 = AttributeUIHelper.createItem(group, "CoordX", R.string.GEOCHEMNATIONAL_PROMPT_CoordX, EditTextEditorInfo.textEditor(), map, defData3);
        item10.getEditorEditText().setEnabled(false);
        this.itemList.add(item10);
        String defData4 = String.valueOf(getY());
        AttributeItem item11 = AttributeUIHelper.createItem(group, "CoordY", R.string.GEOCHEMNATIONAL_PROMPT_CoordY, EditTextEditorInfo.textEditor(), map, defData4);
        item11.getEditorEditText().setEnabled(false);
        this.itemList.add(item11);
        AttributeItem item12 = AttributeUIHelper.createItem(group, "Height", R.string.GEOCHEMNATIONAL_PROMPT_Height, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item12);
        if (isNewFlag() && RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
            double z = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + gstate.getGpsRectifyZ();
            item12.setItemData(Double.toString(z));
        }
        this.itemList.add(item12);
        this.ItemZ = item12;
        item12.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
                    double z2 = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + AttGeoChemSamNationalActivity.this.mGState.getGpsRectifyZ();
                    AttGeoChemSamNationalActivity.this.ItemZ.setItemData(Double.toString(z2));
                }
            }
        }, getResources().getDrawable(android.R.drawable.btn_star)));
        AttributeItem item13 = AttributeUIHelper.createItem(group, "Village", R.string.GEOCHEMNATIONAL_PROMPT_Village, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item13);
        AttributeItem item14 = AttributeUIHelper.createItem(group, "County", R.string.GEOCHEMNATIONAL_PROMPT_County, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item14);
        AttributeItem item15 = AttributeUIHelper.createItem(group, "Province", R.string.GEOCHEMNATIONAL_PROMPT_Province, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item15);
        AttributeItem item16 = AttributeUIHelper.createItem(group, "Country", R.string.GEOCHEMNATIONAL_PROMPT_Country, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item16);
        AttributeItem item17 = AttributeUIHelper.createItem(group, "StreamName", R.string.GEOCHEMNATIONAL_PROMPT_StreamName, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item17);
        AttributeItem item18 = AttributeUIHelper.createItem(group, "StreamWidth", R.string.GEOCHEMNATIONAL_PROMPT_StreamWidth, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item18);
        AttributeItem item19 = AttributeUIHelper.createItem(group, "StreamFlow", R.string.GEOCHEMNATIONAL_PROMPT_StreamFlow, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item19);
        AttributeItem item20 = AttributeUIHelper.createItem(group, "Topography", R.string.GEOCHEMNATIONAL_PROMPT_Topography, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item20);
        item20.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String[] itemValues = {"Plain", " hills", "mountains"};
                AttGeoChemSamNationalActivity.this.showDic(AttGeoChemSamNationalActivity.this.ItemTopography, itemValues);
            }
        }, null));
        this.ItemTopography = item20;
        AttributeItem item21 = AttributeUIHelper.createItem(group, "LandSetting", R.string.GEOCHEMNATIONAL_PROMPT_LandSetting, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item21);
        item21.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String[] itemValues = {"Farmland", " grassland", "desert", "forest", "wetland", "vegetation"};
                AttGeoChemSamNationalActivity.this.showDic(AttGeoChemSamNationalActivity.this.ItemLandSetting, itemValues);
            }
        }, null));
        this.ItemLandSetting = item21;
        AttributeItem item22 = AttributeUIHelper.createItem(group, "SamType", R.string.GEOCHEMNATIONAL_PROMPT_SamType, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item22);
        item22.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String[] itemValues = {"stream sediments", " overbank sediments", "soils"};
                AttGeoChemSamNationalActivity.this.showDic(AttGeoChemSamNationalActivity.this.ItemType, itemValues);
            }
        }, null));
        this.ItemType = item22;
        AttributeItem item23 = AttributeUIHelper.createItem(group, "TopSamSize", R.string.GEOCHEMNATIONAL_PROMPT_TopSamSize, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item23);
        AttributeItem item24 = AttributeUIHelper.createItem(group, "TopSamClay", R.string.GEOCHEMNATIONAL_PROMPT_TopSamClay, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item24);
        AttributeItem item25 = AttributeUIHelper.createItem(group, "TopSamSilt", R.string.GEOCHEMNATIONAL_PROMPT_TopSamSilt, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item25);
        AttributeItem item26 = AttributeUIHelper.createItem(group, "TopSamFinesand", R.string.GEOCHEMNATIONAL_PROMPT_TopSamFinesand, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item26);
        AttributeItem item27 = AttributeUIHelper.createItem(group, "TopSamSand", R.string.GEOCHEMNATIONAL_PROMPT_TopSamSand, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item27);
        AttributeItem item28 = AttributeUIHelper.createItem(group, "TopSamColor", R.string.GEOCHEMNATIONAL_PROMPT_TopSamColor, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item28);
        AttributeItem item29 = AttributeUIHelper.createItem(group, "DeepSamSize", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamSize, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item29);
        AttributeItem item30 = AttributeUIHelper.createItem(group, "DeepSamClay", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamClay, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item30);
        AttributeItem item31 = AttributeUIHelper.createItem(group, "DeepSamSilt", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamSilt, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item31);
        AttributeItem item32 = AttributeUIHelper.createItem(group, "DeepSamFineSand", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamFinesand, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item32);
        AttributeItem item33 = AttributeUIHelper.createItem(group, "DeepSamSand", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamSand, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item33);
        AttributeItem item34 = AttributeUIHelper.createItem(group, "DeepSamColor", R.string.GEOCHEMNATIONAL_PROMPT_DeepSamColor, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item34);
        AttributeItem item35 = AttributeUIHelper.createItem(group, "PictureNo", R.string.GEOCHEMNATIONAL_PROMPT_PictureNo, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item35);
        AttributeItem item36 = AttributeUIHelper.createItem(group, "Desc", R.string.GEOCHEMNATIONAL_PROMPT_Desc, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item36);
        AttributeItem item37 = AttributeUIHelper.createItem(group, "OrgSamNo", R.string.GEOCHEMNATIONAL_PROMPT_OrgSamNo, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item37);
        container.addView(group.getInnerView());
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            switch (requestCode) {
                case 1:
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
            }
        }
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }

    public int getPhotolv(String keyword, File filepath) {
        File[] files;
        int Photolv = 1;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        Photolv = getPhotolv(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf("jpg") > -1) {
                        Photolv++;
                    }
                }
            }
        }
        return Photolv;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showDic(final AttributeItem item, final String[] itemValues) {
        new AlertDialog.Builder(this).setTitle("Dictionary").setItems(itemValues, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemSamNationalActivity.7
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                Toast.makeText(AttGeoChemSamNationalActivity.this, itemValues[which], 0).show();
                item.getEditorTextView().setText(itemValues[which]);
            }
        }).setNegativeButton("取消", (DialogInterface.OnClickListener) null).show();
    }
}
