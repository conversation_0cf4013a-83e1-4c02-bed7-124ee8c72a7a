package com.AoRGMap.earth;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.Toast;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.MyImageView1Activity;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.RGMapPreferences;
import com.AoRGMap.dao.DBOpenHelper;
import com.AoRGMap.drawbitmap.DrawBitmapActivity;
import com.AoRGMap.prb.DrawSketchActivity;
import com.AoRGMap.prb.PointParams;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGeoChemRock5wActivity extends AttributeActivity {
    public static final int RequestCamera = 1;
    public static final int RequestCamera2 = 2;
    Button btn_rocktype1;
    Button btn_rocktype2;
    Button btn_rocktype3;
    SimpleAdapter listItemAdapter;
    ListView list_rocktype;
    double m_dCenterLon;
    String imgcodeString = "";
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    double lpX = 0.0d;
    double lpY = 0.0d;
    String m_strDicDbpath = "/data/data/com.AoRGMap/AoGIS/GeoChemDict5w.db";
    private DBOpenHelper m_helper = null;
    private SQLiteDatabase m_db = null;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    int iSelFiled = 0;
    String[] fieldsStrings = null;
    AttributeItem itemRockSamType = null;
    AttributeItem ItemRockType = null;
    AttributeItem itemWeathered = null;
    AttributeItem dateitem = null;
    AttributeItem SamCodeItem = null;
    AttributeItem PhotoItem = null;
    AttributeItem ItemZ = null;
    AttributeItem LocItem = null;
    ArrayList<HashMap<String, Object>> listItem = new ArrayList<>();
    String m_strRockTypeString = null;
    String mImglocString = null;
    WorkAreaParams.ProjectionType m_PrjType = WorkAreaParams.ProjectionType.None;
    WorkAreaParams.EarthType m_EarthType = WorkAreaParams.EarthType.Beijing54;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttGeoChemRock5wActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_str5GeoRock));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.m_helper = new DBOpenHelper(getBaseContext(), this.m_strDicDbpath);
        this.m_db = this.m_helper.getReadableDatabase();
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        String itemvalue = this.itemRockSamType.getEditorEditText().getText().toString();
        String[] itemString = itemvalue.split(":");
        if (itemString.length > 1) {
            getAttributeMap().put("SamType", itemString[1]);
            getAttributeMap().put("SamType_c", itemString[0]);
        } else {
            getAttributeMap().put("SamType", "");
            getAttributeMap().put("SamType_c", "");
        }
        String itemvalue2 = this.ItemRockType.getEditorEditText().getText().toString();
        String[] itemString2 = itemvalue2.split(":");
        if (itemString2.length > 1) {
            getAttributeMap().put("RockName", itemString2[1]);
            getAttributeMap().put("RockType", itemString2[0]);
        } else {
            getAttributeMap().put("RockName", "");
            getAttributeMap().put("RockType", "");
        }
        String itemvalue3 = this.itemWeathered.getEditorEditText().getText().toString();
        String[] itemString3 = itemvalue3.split(":");
        if (itemString3.length > 1) {
            getAttributeMap().put("Weathered", itemString3[1]);
            getAttributeMap().put("Weathered_c", itemString3[0]);
        } else {
            getAttributeMap().put("Weathered", "");
            getAttributeMap().put("Weathered_c", "");
        }
        this.m_PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        this.m_EarthType = MapParam.getEarthType();
        this.m_dCenterLon = getRadFromDeg(MapParam.lon);
        if (this.m_PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType);
            String szTemp = String.format("%.4f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            getAttributeMap().put("longitude", Double.valueOf(dTemp));
            String szTemp2 = String.format("%.4f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            getAttributeMap().put("latitude", Double.valueOf(dTemp2));
        } else if (this.m_PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType, MapParam.dx, MapParam.dy);
            String szTemp3 = String.format("%.4f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            getAttributeMap().put("longitude", Double.valueOf(dTemp3));
            String szTemp4 = String.format("%.4f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            getAttributeMap().put("latitude", Double.valueOf(dTemp4));
        } else {
            getAttributeMap().put("longitude", Double.valueOf(getX()));
            getAttributeMap().put("latitude", Double.valueOf(getY()));
        }
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTECODE")) {
            String strCode = getAttributeMap().get("ROUTECODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        setResult(1, new Intent());
        finish();
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_earth_5geosoil);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        AttributeItem item = AttributeUIHelper.createItem(group, "ProjectName", R.string.EARTH_GEOCHEMSTREAM5W_PROJECTNAME, EditTextEditorInfo.textEditor(), map, (String) null);
        item.setPropDictName(R.string.dic_prjname);
        this.itemList.add(item);
        AttributeItem item2 = AttributeUIHelper.createItem(group, "MapCode", R.string.EARTH_GEOCHEMSTREAM5W_MAPCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item2.setPropDictName(R.string.dic_mapcode);
        this.itemList.add(item2);
        AttributeItem item3 = AttributeUIHelper.createItem(group, RGMapPreferences.mMapName, R.string.EARTH_GEOCHEMSTREAM5W_MAPNAME, EditTextEditorInfo.textEditor(), map, (String) null);
        item3.setPropDictName(R.string.dic_mapname);
        this.itemList.add(item3);
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, "RouteCode", R.string.EARTH_GEOCHEMSTREAM5W_ROUTECODE, EditTextEditorInfo.textEditor(), map, defData));
        AttributeItem item4 = AttributeUIHelper.createItem(group, "MainID", R.string.EARTH_GEOCHEMROCK5W_MINECODE, EditTextEditorInfo.textEditor(), map, "岩石");
        item4.getEditorEditText().setEnabled(false);
        this.itemList.add(item4);
        AttributeItem item5 = AttributeUIHelper.createItem(group, "MainID_C", R.string.EARTH_GEOCHEMROCK5W_MINECODEID, EditTextEditorInfo.textEditor(), map, "1");
        item5.getEditorEditText().setEnabled(false);
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, "SamCode", R.string.EARTH_GEOCHEMSTREAM5W_SAMCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item6);
        this.SamCodeItem = item6;
        String defData2 = String.valueOf(getX());
        AttributeItem item7 = AttributeUIHelper.createItem(group, "CoordX", R.string.EARTH_GEOCHEMSTREAM5W_COORDX, EditTextEditorInfo.textEditor(), map, defData2);
        item7.getEditorEditText().setEnabled(false);
        this.itemList.add(item7);
        String defData3 = String.valueOf(getY());
        AttributeItem item8 = AttributeUIHelper.createItem(group, "CoordY", R.string.EARTH_GEOCHEMSTREAM5W_COORDY, EditTextEditorInfo.textEditor(), map, defData3);
        item8.getEditorEditText().setEnabled(false);
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, "Altitude", R.string.EARTH_GEOCHEMSTREAM5W_ALTITUDE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item9);
        if (isNewFlag() && RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
            double z = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + gstate.getGpsRectifyZ();
            item9.setItemData(Double.toString(z));
        }
        this.itemList.add(item9);
        this.ItemZ = item9;
        item9.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
                    double z2 = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + AttGeoChemRock5wActivity.this.mGState.getGpsRectifyZ();
                    AttGeoChemRock5wActivity.this.ItemZ.setItemData(Double.toString(z2));
                }
            }
        }, getResources().getDrawable(android.R.drawable.btn_star)));
        String defData4 = null;
        if (!isNewFlag() && !map.get("SamType").toString().equals("")) {
            defData4 = map.get("SamType_c").toString() + ":" + map.get("SamType").toString();
        }
        AttributeItem item10 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEMROCK5W_SAMTYPE, EditTextEditorInfo.textEditor(), defData4, (String) null);
        item10.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Cursor SectionSursor = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from RockSamType", null);
                int isize = SectionSursor.getCount();
                AttGeoChemRock5wActivity.this.fieldsStrings = new String[isize];
                int iFiled = 0;
                while (SectionSursor.moveToNext()) {
                    int columnIndex1 = SectionSursor.getColumnIndex("Item");
                    String szFieldValue1 = SectionSursor.getString(columnIndex1);
                    int columnIndex = SectionSursor.getColumnIndex("Code");
                    String szFieldValue = SectionSursor.getString(columnIndex);
                    AttGeoChemRock5wActivity.this.fieldsStrings[iFiled] = szFieldValue + ":" + szFieldValue1;
                    iFiled++;
                }
                AlertDialog.Builder builder1 = new AlertDialog.Builder(AttGeoChemRock5wActivity.this);
                builder1.setTitle(R.string.EARTH_GEOCHEMSOIL5W_SOILGENETICTYPE);
                builder1.setSingleChoiceItems(AttGeoChemRock5wActivity.this.fieldsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.2.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemRock5wActivity.this.iSelFiled = which;
                    }
                });
                builder1.setPositiveButton(AttGeoChemRock5wActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.2.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemRock5wActivity.this.itemRockSamType.getEditorEditText().setText(AttGeoChemRock5wActivity.this.fieldsStrings[AttGeoChemRock5wActivity.this.iSelFiled]);
                    }
                });
                builder1.show();
            }
        }, getResources().getDrawable(R.drawable.search)));
        item10.getEditorEditText().setEnabled(false);
        this.itemList.add(item10);
        this.itemRockSamType = item10;
        String defData5 = null;
        if (!isNewFlag() && !map.get("RockType").toString().equals("")) {
            defData5 = map.get("RockType").toString() + ":" + map.get("RockName").toString();
        }
        AttributeItem item11 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEMROCK5W_ROCKTYPE, EditTextEditorInfo.textEditor(), map, defData5);
        item11.getEditorEditText().setEnabled(false);
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                LayoutInflater inflater = AttGeoChemRock5wActivity.this.getLayoutInflater();
                View layout = inflater.inflate(R.layout.rocktype, (ViewGroup) AttGeoChemRock5wActivity.this.findViewById(R.id.rocktypedlg));
                AttGeoChemRock5wActivity.this.btn_rocktype1 = (Button) layout.findViewById(R.id.button1);
                AttGeoChemRock5wActivity.this.btn_rocktype1.setTag(1);
                AttGeoChemRock5wActivity.this.btn_rocktype2 = (Button) layout.findViewById(R.id.button2);
                AttGeoChemRock5wActivity.this.btn_rocktype2.setTag(2);
                AttGeoChemRock5wActivity.this.btn_rocktype3 = (Button) layout.findViewById(R.id.button3);
                AttGeoChemRock5wActivity.this.btn_rocktype3.setTag(3);
                AttGeoChemRock5wActivity.this.list_rocktype = (ListView) layout.findViewById(R.id.listView1);
                Cursor SectionSursor = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='b'", null);
                while (SectionSursor.moveToNext()) {
                    int columnIndex = SectionSursor.getColumnIndex("Code");
                    String szFieldValue = SectionSursor.getString(columnIndex);
                    int columnIndex1 = SectionSursor.getColumnIndex("Item");
                    String szFieldValue1 = SectionSursor.getString(columnIndex1);
                    HashMap<String, Object> map2 = new HashMap<>();
                    map2.put("ItemTitle", szFieldValue);
                    map2.put("ItemText", szFieldValue1);
                    AttGeoChemRock5wActivity.this.listItem.add(map2);
                }
                AttGeoChemRock5wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemRock5wActivity.this, AttGeoChemRock5wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                AttGeoChemRock5wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemRock5wActivity.this.listItemAdapter);
                AttGeoChemRock5wActivity.this.list_rocktype.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3.1
                    @Override // android.widget.AdapterView.OnItemClickListener
                    public void onItemClick(AdapterView<?> arg0, View arg1, int arg2, long arg3) {
                        if (((ListView) arg0).getTag() != null) {
                            ((View) ((ListView) arg0).getTag()).setBackgroundDrawable(null);
                        }
                        ((ListView) arg0).setTag(arg1);
                        arg1.setBackgroundColor(-16776961);
                        Map<String, Object> map3 = (Map) arg0.getItemAtPosition(arg2);
                        AttGeoChemRock5wActivity.this.m_strRockTypeString = map3.get("ItemTitle").toString() + ":" + map3.get("ItemText").toString();
                    }
                });
                AttGeoChemRock5wActivity.this.btn_rocktype1.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemRock5wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='b'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemRock5wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemRock5wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemRock5wActivity.this, AttGeoChemRock5wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemRock5wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemRock5wActivity.this.listItemAdapter);
                    }
                });
                AttGeoChemRock5wActivity.this.btn_rocktype2.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemRock5wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='c'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemRock5wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemRock5wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemRock5wActivity.this, AttGeoChemRock5wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemRock5wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemRock5wActivity.this.listItemAdapter);
                    }
                });
                AttGeoChemRock5wActivity.this.btn_rocktype3.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemRock5wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='d'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemRock5wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemRock5wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemRock5wActivity.this, AttGeoChemRock5wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemRock5wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemRock5wActivity.this.listItemAdapter);
                    }
                });
                AlertDialog.Builder builder1 = new AlertDialog.Builder(AttGeoChemRock5wActivity.this);
                builder1.setTitle(R.string.EARTH_GEOCHEMSTREAM5W_ROCKTYPE);
                builder1.setView(layout);
                builder1.setPositiveButton(AttGeoChemRock5wActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.3.5
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemRock5wActivity.this.ItemRockType.getEditorEditText().setText(AttGeoChemRock5wActivity.this.m_strRockTypeString);
                    }
                });
                builder1.show();
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item11);
        this.ItemRockType = item11;
        String defData6 = null;
        if (!isNewFlag() && !map.get("Weathered").toString().equals("")) {
            defData6 = map.get("Weathered_c").toString() + ":" + map.get("Weathered").toString();
        }
        AttributeItem item12 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEMROCK5W_WEATHERED, EditTextEditorInfo.textEditor(), defData6, (String) null);
        item12.getEditorEditText().setEnabled(false);
        item12.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Cursor SectionSursor = AttGeoChemRock5wActivity.this.m_db.rawQuery("select * from Weathered", null);
                int isize = SectionSursor.getCount();
                AttGeoChemRock5wActivity.this.fieldsStrings = new String[isize];
                int iFiled = 0;
                while (SectionSursor.moveToNext()) {
                    int columnIndex1 = SectionSursor.getColumnIndex("Item");
                    String szFieldValue1 = SectionSursor.getString(columnIndex1);
                    int columnIndex = SectionSursor.getColumnIndex("Code");
                    String szFieldValue = SectionSursor.getString(columnIndex);
                    AttGeoChemRock5wActivity.this.fieldsStrings[iFiled] = szFieldValue + ":" + szFieldValue1;
                    iFiled++;
                }
                AlertDialog.Builder builder1 = new AlertDialog.Builder(AttGeoChemRock5wActivity.this);
                builder1.setTitle(R.string.EARTH_GEOCHEMSTREAM5W_ALTERATION);
                builder1.setSingleChoiceItems(AttGeoChemRock5wActivity.this.fieldsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.4.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemRock5wActivity.this.iSelFiled = which;
                    }
                });
                builder1.setPositiveButton(AttGeoChemRock5wActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.4.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemRock5wActivity.this.itemWeathered.getEditorEditText().setText(AttGeoChemRock5wActivity.this.fieldsStrings[AttGeoChemRock5wActivity.this.iSelFiled]);
                    }
                });
                builder1.show();
            }
        }, getResources().getDrawable(R.drawable.search)));
        this.itemList.add(item12);
        this.itemWeathered = item12;
        this.itemList.add(AttributeUIHelper.createItem(group, "Lithology", R.string.EARTH_GEOCHEMROCK5W_LITHOLOGY, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Structure", R.string.EARTH_GEOCHEMROCK5W_STRUCTURE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "Alteration", R.string.EARTH_GEOCHEMROCK5W_ALTERATION, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item13 = AttributeUIHelper.createItem(group, "PhotoCode", R.string.EARTH_GEOCHEMROCK5W_PHOTOCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item13);
        this.PhotoItem = item13;
        item13.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttGeoChemRock5wActivity.this.imgcodeString = AttGeoChemRock5wActivity.this.PhotoItem.getEditorEditText().getText().toString();
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                String imgFolder = rootPath + "/images/";
                File vFilepath = new File(imgFolder);
                String imgname = AttGeoChemRock5wActivity.this.SamCodeItem.getEditorEditText().getText().toString();
                int photolv = AttGeoChemRock5wActivity.this.getPhotolv(imgname, vFilepath);
                String imgname2 = imgname + "_" + String.valueOf(photolv);
                if ("".equals(AttGeoChemRock5wActivity.this.imgcodeString)) {
                    AttGeoChemRock5wActivity.this.imgcodeString = imgname2;
                } else {
                    AttGeoChemRock5wActivity.this.imgcodeString += "," + imgname2;
                }
                String imgPath = imgFolder + imgname2 + ".jpg";
                File vFile = new File(imgPath);
                if (!vFile.exists()) {
                    File vDirPath = vFile.getParentFile();
                    vDirPath.mkdirs();
                }
                Uri uri = Uri.fromFile(vFile);
                Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
                intent.putExtra("output", uri);
                intent.putExtra("isNew", true);
                try {
                    AttGeoChemRock5wActivity.this.startActivityForResult(intent, 1);
                } catch (Exception e) {
                    Toast.makeText(AttGeoChemRock5wActivity.this, R.string.RGMAP_BITMAP_NODECEVICE, 1).show();
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_camera)));
        item13.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttGeoChemRock5wActivity.this.imgcodeString = "";
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                String imgFolder = rootPath + "/images/";
                AttGeoChemRock5wActivity.this.imgcodeString = AttGeoChemRock5wActivity.this.PhotoItem.getEditorEditText().getText().toString();
                if (!"".equals(AttGeoChemRock5wActivity.this.imgcodeString)) {
                    Intent intentimg = new Intent(AttGeoChemRock5wActivity.this, (Class<?>) MyImageView1Activity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", AttGeoChemRock5wActivity.this.imgcodeString);
                    bundle.putString("FolderATH", imgFolder);
                    intentimg.putExtras(bundle);
                    AttGeoChemRock5wActivity.this.startActivity(intentimg);
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_play)));
        AttributeItem item14 = AttributeUIHelper.createItem(group, "SketchCode", R.string.EARTH_GEOCHEMROCK5W_SKETCHCODE, EditTextEditorInfo.textEditor(), map, "1");
        this.itemList.add(item14);
        item14.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.7
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String str;
                String strSketchPath = AoRGMapActivity.getCurrentMapPath();
                if (strSketchPath != null && (str = AttGeoChemRock5wActivity.this.SamCodeItem.getEditorEditText().getText().toString()) != null && str != "") {
                    AttGeoChemRock5wActivity.this.mGState.setiDrawSwkrchGpsPos(0);
                    Intent intent = new Intent(AttGeoChemRock5wActivity.this, (Class<?>) DrawSketchActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("SketchMapName", strSketchPath + "/sketch/" + (str + "1"));
                    intent.putExtras(bundle);
                    AttGeoChemRock5wActivity.this.startActivity(intent);
                }
            }
        }, getResources().getDrawable(R.drawable.sketcha)));
        item14.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.8
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String str;
                String strSketchPath = AoRGMapActivity.getCurrentMapPath();
                if (strSketchPath != null && (str = AttGeoChemRock5wActivity.this.SamCodeItem.getEditorEditText().getText().toString()) != null && str != "") {
                    Intent intent = new Intent(AttGeoChemRock5wActivity.this, (Class<?>) DrawBitmapActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString(DrawBitmapActivity.BUNDLE_SKETCHMAPNAME, strSketchPath + "/sketch/" + (str + "1"));
                    intent.putExtras(bundle);
                    AttGeoChemRock5wActivity.this.startActivity(intent);
                }
            }
        }, getResources().getDrawable(R.drawable.sketchb)));
        this.itemList.add(AttributeUIHelper.createItem(group, "GPSFileNo", R.string.EARTH_GEOCHEMSTREAM5W_GPSFILENO, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item15 = AttributeUIHelper.createItem(group, "MarkPos", R.string.EARTH_GEOCHEMSTREAM5W_MARKPOS, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item15);
        this.LocItem = item15;
        item15.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.9
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttGeoChemRock5wActivity.this.mImglocString = AttGeoChemRock5wActivity.this.LocItem.getEditorEditText().getText().toString();
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                String imgFolder = rootPath + "/images/";
                File vFilepath = new File(imgFolder);
                String imgname = AttGeoChemRock5wActivity.this.SamCodeItem.getEditorEditText().getText().toString();
                if (!imgname.equals("")) {
                    int photolv = AttGeoChemRock5wActivity.this.getPhotolv(imgname + "_loc", vFilepath);
                    String imgname2 = imgname + "_loc_" + String.valueOf(photolv);
                    if ("".equals(AttGeoChemRock5wActivity.this.mImglocString)) {
                        AttGeoChemRock5wActivity.this.mImglocString = imgname2;
                    } else {
                        AttGeoChemRock5wActivity.this.mImglocString += "," + imgname2;
                    }
                    String imgPath = imgFolder + imgname2 + ".jpg";
                    File vFile = new File(imgPath);
                    if (!vFile.exists()) {
                        File vDirPath = vFile.getParentFile();
                        if (!vDirPath.exists()) {
                            vDirPath.mkdirs();
                        }
                    }
                    Uri uri = Uri.fromFile(vFile);
                    Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
                    intent.putExtra("output", uri);
                    intent.putExtra("isNew", true);
                    try {
                        AttGeoChemRock5wActivity.this.startActivityForResult(intent, 2);
                    } catch (Exception e) {
                        Toast.makeText(AttGeoChemRock5wActivity.this, R.string.RGMAP_BITMAP_NODECEVICE, 1).show();
                    }
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_camera)));
        item15.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.10
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttGeoChemRock5wActivity.this.mImglocString = "";
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                String imgFolder = rootPath + "/images/";
                AttGeoChemRock5wActivity.this.mImglocString = AttGeoChemRock5wActivity.this.LocItem.getEditorEditText().getText().toString();
                if (!"".equals(AttGeoChemRock5wActivity.this.mImglocString)) {
                    Intent intentimg = new Intent(AttGeoChemRock5wActivity.this, (Class<?>) MyImageView1Activity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", AttGeoChemRock5wActivity.this.mImglocString);
                    bundle.putString("FolderATH", imgFolder);
                    intentimg.putExtras(bundle);
                    AttGeoChemRock5wActivity.this.startActivity(intentimg);
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_play)));
        this.itemList.add(AttributeUIHelper.createItem(group, "BagNo", R.string.EARTH_GEOCHEMSTREAM5W_BAGNO, EditTextEditorInfo.textEditor(), map, (String) null));
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String defData7 = df.format(date);
        AttributeItem item16 = AttributeUIHelper.createItem(group, "SamDate", R.string.EARTH_GEOCHEMSTREAM5W_SAMDATE, EditTextEditorInfo.textEditor(), map, defData7);
        this.itemList.add(item16);
        this.dateitem = item16;
        item16.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemRock5wActivity.11
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttGeoChemRock5wActivity.this);
                String str = AttGeoChemRock5wActivity.this.getResources().getString(R.string.RGMAP_PROMPT_SETDATE);
                dateDialog.showListDialog(str, AttGeoChemRock5wActivity.this.dateitem.getEditorEditText());
            }
        }, null));
        AttributeItem item17 = AttributeUIHelper.createItem(group, "RecordMan", R.string.EARTH_GEOCHEMSTREAM5W_RECORDMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item17.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item17);
        AttributeItem item18 = AttributeUIHelper.createItem(group, "SamMan", R.string.EARTH_GEOCHEMSTREAM5W_SAMMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item18.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item18);
        AttributeItem item19 = AttributeUIHelper.createItem(group, "CheckMan", R.string.EARTH_GEOCHEMSTREAM5W_CHECKMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item19.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item19);
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark", R.string.EARTH_GEOCHEMSTREAM5W_REMARK, EditTextEditorInfo.textEditor(), map, (String) null));
        container.addView(group.getInnerView());
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }

    public int getPhotolv(String keyword, File filepath) {
        File[] files;
        int Photolv = 1;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        Photolv = getPhotolv(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf("jpg") > -1) {
                        Photolv++;
                    }
                }
            }
        }
        return Photolv;
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            switch (requestCode) {
                case 1:
                    this.PhotoItem.getEditorEditText().setText(this.imgcodeString);
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
                case 2:
                    this.LocItem.getEditorEditText().setText(this.mImglocString);
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
            }
        }
    }

    public void moveFile(String oldPath, String newPath) {
        int bytesum = 0;
        try {
            File oldFile = new File(oldPath);
            File newFile = new File(newPath);
            if (!newFile.exists()) {
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                while (true) {
                    int byteread = inStream.read(buffer);
                    if (byteread != -1) {
                        bytesum += byteread;
                        System.out.println(bytesum);
                        fs.write(buffer, 0, byteread);
                    } else {
                        inStream.close();
                        oldFile.delete();
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
