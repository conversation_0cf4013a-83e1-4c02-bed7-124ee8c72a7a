package com.AoRGMap.earth;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.prb.PointParams;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/* loaded from: classes.dex */
public class AttMulstremActivity extends AttributeActivity {
    double lpX;
    double lpY;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttMulstremActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strMulstrem));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        if (isNewFlag()) {
            RGMapApplication.getCurrentApp().getCurrentGlobal().addStartPointNumber();
        }
        updateAttributeMap(this.itemList);
        String kX = getResources().getString(R.string.RGMAP_FLDNAME_PXX);
        String kY = getResources().getString(R.string.RGMAP_FLDNAME_PYY);
        getAttributeMap().put(kX, Double.valueOf(getX()));
        getAttributeMap().put(kY, Double.valueOf(getY()));
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTE")) {
            String strCode = getAttributeMap().get("ROUTE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_p);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_ROUTH, R.string.EARTH_MULSTREM_ROUTH, EditTextEditorInfo.textEditor(), map, defData));
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAM_TYPE, R.string.EARTH_MULSTREM_SAM_TYPE, EditTextEditorInfo.textEditor(), map, (String) null);
        item.setPropDictName(getResources().getString(R.string.dic_mulsam));
        this.itemList.add(item);
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_PROVICE_CODE, R.string.EARTH_MULSTREM_PROVICE_CODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item2.setPropDictName(getResources().getString(R.string.dic_mulsm));
        this.itemList.add(item2);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_PROVICE_NAME, R.string.EARTH_MULSTREM_PROVICE_NAME, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_MAPCODE, R.string.EARTH_MULSTREM_MAPCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item3.setPropDictName(getResources().getString(R.string.dic_multfh));
        this.itemList.add(item3);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAM_CODE, R.string.EARTH_MULSTREM_SAM_CODE, EditTextEditorInfo.textEditor(), map, (String) null));
        String strData = String.format("%f, %f", Double.valueOf(getX()), Double.valueOf(getY()));
        String defData2 = getResources().getString(R.string.nodata);
        AttributeItem item4 = AttributeUIHelper.createItem(group, (String) null, R.string.gpoint_xy, EditTextEditorInfo.textEditor(), strData, defData2);
        item4.getEditorEditText().setEnabled(false);
        this.itemList.add(item4);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_WATER_DEPTH, R.string.EARTH_MULSTREM_WATER_DEPTH, EditTextEditorInfo.decimalEditor(), map, (String) null));
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_PHYSIOGNOMY, R.string.EARTH_MULSTREM_PHYSIOGNOMY, EditTextEditorInfo.textEditor(), map, (String) null);
        item5.setPropDictName(getResources().getString(R.string.dic_muldm));
        this.itemList.add(item5);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_POSITION, R.string.EARTH_MULSTREM_POSITION, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAMED_DEPTH, R.string.EARTH_MULSTREM_SAMED_DEPTH, EditTextEditorInfo.decimalEditor(), map, (String) null));
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_COMPONENT_1, R.string.EARTH_MULSTREM_COMPONENT_1, EditTextEditorInfo.textEditor(), map, (String) null);
        item6.setPropDictName(getResources().getString(R.string.dic_mulypzf));
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_COMPONENT_2, R.string.EARTH_MULSTREM_COMPONENT_2, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.setPropDictName(getResources().getString(R.string.dic_mulypzf));
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_COMPONENT_3, R.string.EARTH_MULSTREM_COMPONENT_3, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.setPropDictName(getResources().getString(R.string.dic_mulypzf));
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_CUTICLE_COLOR1, R.string.EARTH_MULSTREM_CUTICLE_COLOR1, EditTextEditorInfo.textEditor(), map, (String) null);
        item9.setPropDictName(getResources().getString(R.string.dic_mulys));
        this.itemList.add(item9);
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAM_COLOR2, R.string.EARTH_MULSTREM_SAM_COLOR2, EditTextEditorInfo.textEditor(), map, (String) null);
        item10.setPropDictName(getResources().getString(R.string.dic_mulys));
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_DEBRIS, R.string.EARTH_MULSTREM_DEBRIS, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.setPropDictName(getResources().getString(R.string.dic_mulswch));
        this.itemList.add(item11);
        AttributeItem item12 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_POLLUTION, R.string.EARTH_MULSTREM_POLLUTION, EditTextEditorInfo.textEditor(), map, (String) null);
        item12.setPropDictName(getResources().getString(R.string.dic_mulwr));
        this.itemList.add(item12);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_ORG_SAMCODE, R.string.EARTH_MULSTREM_ORG_SAMCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAM_BAGCODE, R.string.EARTH_MULSTREM_SAM_BAGCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item13 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_TAG_POS, R.string.EARTH_MULSTREM_TAG_POS, EditTextEditorInfo.textEditor(), map, (String) null);
        item13.setPropDictName(getResources().getString(R.string.dic_mulbjwz));
        this.itemList.add(item13);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_DESCRIBE, R.string.EARTH_MULSTREM_DESCRIBE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_REMARK, R.string.EARTH_MULSTREM_REMARK, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item14 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_HYDROBIOLOGY, R.string.EARTH_MULSTREM_HYDROBIOLOGY, EditTextEditorInfo.textEditor(), map, (String) null);
        item14.setPropDictName(getResources().getString(R.string.dic_mulsswfb));
        this.itemList.add(item14);
        AttributeItem item15 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_CULTURIST, R.string.EARTH_MULSTREM_CULTURIST, EditTextEditorInfo.textEditor(), map, (String) null);
        item15.setPropDictName(getResources().getString(R.string.dic_mulyzgk));
        this.itemList.add(item15);
        AttributeItem item16 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_POLLUTION_DES, R.string.EARTH_MULSTREM_POLLUTION_DES, EditTextEditorInfo.textEditor(), map, (String) null);
        item16.setPropDictName(getResources().getString(R.string.dic_mulwr));
        this.itemList.add(item16);
        AttributeItem item17 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_RECORDER, R.string.EARTH_MULSTREM_RECORDER, EditTextEditorInfo.textEditor(), map, (String) null);
        item17.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item17);
        AttributeItem item18 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_SAMPER, R.string.EARTH_MULSTREM_SAMPER, EditTextEditorInfo.textEditor(), map, (String) null);
        item18.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item18);
        AttributeItem item19 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_CHECKER, R.string.EARTH_MULSTREM_CHECKER, EditTextEditorInfo.textEditor(), map, (String) null);
        item19.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item19);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_CARD_NO, R.string.EARTH_MULSTREM_CARD_NO, EditTextEditorInfo.textEditor(), map, (String) null));
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        df.format(date);
        final AttributeItem item20 = AttributeUIHelper.createItem(group, R.string.EARTH_FLDNAME_MULSTREM_DATE, R.string.EARTH_MULSTREM_DATE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item20);
        item20.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttMulstremActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttMulstremActivity.this);
                String str = AttMulstremActivity.this.getResources().getString(R.string.RGMAP_PROMPT_SETDATE);
                dateDialog.showListDialog(str, item20.getEditorEditText());
            }
        }, null));
        container.addView(group.getInnerView());
    }
}
