package com.AoRGMap.earth;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.Toast;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.dao.DBOpenHelper;
import com.AoRGMap.dao.GeoChemMulMedia25wDao;
import com.AoRGMap.pm.AttCommParams;
import com.AoRGMap.prb.PointParams;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGeoChemStream25wActivity extends AttributeActivity {
    Button btn_rocktype1;
    Button btn_rocktype2;
    Button btn_rocktype3;
    SimpleAdapter listItemAdapter;
    ListView list_rocktype;
    ArrayAdapter<CharSequence> mAdapter;
    double m_dCenterLon;
    Spinner spinner1;
    Spinner spinner2;
    Spinner spinner3;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    double lpX = 0.0d;
    double lpY = 0.0d;
    String m_strDicDbpath = "/data/data/com.AoRGMap/AoGIS/GeoChemDict25w.db";
    private DBOpenHelper m_helper = null;
    private SQLiteDatabase m_db = null;
    int iSelFiled = 0;
    String[] fieldsStrings = null;
    AttributeItem itemColor = null;
    AttributeItem itemStreamOrder = null;
    AttributeItem itemProvince = null;
    AttributeItem itemSamDate = null;
    AttributeItem itemComponent = null;
    AttributeItem ItemRockType = null;
    AttributeItem ItemSamPos = null;
    AttributeItem ItemSamCode = null;
    int iselpos1 = -1;
    int iselpos2 = -1;
    int iselpos3 = -1;
    String strselpos1 = null;
    String strselpos2 = null;
    String strselpos3 = null;
    ArrayList<HashMap<String, Object>> listItem = new ArrayList<>();
    String m_strRockTypeString = null;
    WorkAreaParams.ProjectionType m_PrjType = WorkAreaParams.ProjectionType.None;
    WorkAreaParams.EarthType m_EarthType = WorkAreaParams.EarthType.Beijing54;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttGeoChemStream25wActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_str25GeoWater));
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.m_helper = new DBOpenHelper(getBaseContext(), this.m_strDicDbpath);
        this.m_db = this.m_helper.getReadableDatabase();
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        String itemvalue = this.ItemSamPos.getEditorEditText().getText().toString();
        String[] itemString = itemvalue.split(":");
        if (itemString.length > 1) {
            getAttributeMap().put("SamPos", itemString[1]);
            getAttributeMap().put("SamPos_c", itemString[0]);
        } else {
            getAttributeMap().put("SamPos", "");
            getAttributeMap().put("SamPos_c", "");
        }
        String itemvalue2 = this.itemComponent.getEditorEditText().getText().toString();
        String[] itemString2 = itemvalue2.split(":");
        if (itemString2.length > 1) {
            getAttributeMap().put("Component", itemString2[1]);
            getAttributeMap().put("Component_c", itemString2[0]);
        } else {
            getAttributeMap().put("Component", "");
            getAttributeMap().put("Component_c", "");
        }
        String itemvalue3 = this.itemColor.getEditorEditText().getText().toString();
        String[] itemString3 = itemvalue3.split(":");
        if (itemString3.length > 1) {
            getAttributeMap().put("Color", itemString3[1]);
            getAttributeMap().put("Color_c", itemString3[0]);
        } else {
            getAttributeMap().put("Color", "");
            getAttributeMap().put("Color_c", "");
        }
        String itemvalue4 = this.itemStreamOrder.getEditorEditText().getText().toString();
        String[] itemString4 = itemvalue4.split(":");
        if (itemString4.length > 1) {
            getAttributeMap().put("StreamOrder", itemString4[1]);
            getAttributeMap().put("StreamOrder_c", itemString4[0]);
        } else {
            getAttributeMap().put("StreamOrder", "");
            getAttributeMap().put("StreamOrder_c", "");
        }
        String itemvalue5 = this.ItemRockType.getEditorEditText().getText().toString();
        String[] itemString5 = itemvalue5.split(":");
        if (itemString5.length > 1) {
            getAttributeMap().put("RockType", itemString5[0]);
            getAttributeMap().put("RockName", itemString5[1]);
        } else {
            getAttributeMap().put("RockType", "");
            getAttributeMap().put("RockName", "");
        }
        String itemvalue6 = this.itemProvince.getEditorEditText().getText().toString();
        String[] itemString6 = itemvalue6.split(":");
        if (itemString6.length > 1) {
            getAttributeMap().put("Province", itemString6[1]);
            getAttributeMap().put("Province_c", itemString6[0]);
        } else {
            getAttributeMap().put("Province", "");
            getAttributeMap().put("Province_c", "");
        }
        this.m_PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        this.m_EarthType = MapParam.getEarthType();
        this.m_dCenterLon = getRadFromDeg(MapParam.lon);
        if (this.m_PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType);
            String szTemp = String.format("%.4f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            getAttributeMap().put("longitude", Double.valueOf(dTemp));
            String szTemp2 = String.format("%.4f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            getAttributeMap().put("latitude", Double.valueOf(dTemp2));
        } else if (this.m_PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(this.m_dCenterLon, getX(), getY(), this.m_EarthType, MapParam.dx, MapParam.dy);
            String szTemp3 = String.format("%.4f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            getAttributeMap().put("longitude", Double.valueOf(dTemp3));
            String szTemp4 = String.format("%.4f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            getAttributeMap().put("latitude", Double.valueOf(dTemp4));
        } else {
            getAttributeMap().put("longitude", Double.valueOf(getX()));
            getAttributeMap().put("latitude", Double.valueOf(getY()));
        }
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTECODE")) {
            String strCode = getAttributeMap().get("ROUTECODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_earth_25geowater);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        AttributeItem item = AttributeUIHelper.createItem(group, "MainID", R.string.EARTH_GEOCHEM25W_MAINID, EditTextEditorInfo.textEditor(), map, "2");
        item.getEditorEditText().setEnabled(false);
        this.itemList.add(item);
        this.itemList.add(AttributeUIHelper.createItem(group, "MapCode", R.string.EARTH_GEOCHEM25W_MAPCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "SamAge", R.string.EARTH_GEOCHEM25W_SAMAGE, EditTextEditorInfo.textEditor(), map, (String) null));
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, "RouteCode", R.string.EARTH_GEOCHEM25W_ROUTECODE, EditTextEditorInfo.textEditor(), map, defData));
        AttributeItem item2 = AttributeUIHelper.createItem(group, "SamCode", R.string.EARTH_GEOCHEM25W_SAMCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item2);
        this.ItemSamCode = item2;
        this.itemList.add(AttributeUIHelper.createItem(group, "SamOrgCode", R.string.EARTH_GEOCHEM25W_SAMORGCODE, EditTextEditorInfo.textEditor(), map, (String) null));
        String defData2 = String.valueOf(getX());
        AttributeItem item3 = AttributeUIHelper.createItem(group, "CoordX", R.string.EARTH_GEOCHEM25W_COORDX, EditTextEditorInfo.textEditor(), map, defData2);
        item3.getEditorEditText().setEnabled(false);
        this.itemList.add(item3);
        String defData3 = String.valueOf(getY());
        AttributeItem item4 = AttributeUIHelper.createItem(group, "CoordY", R.string.EARTH_GEOCHEM25W_COORDY, EditTextEditorInfo.textEditor(), map, defData3);
        item4.getEditorEditText().setEnabled(false);
        this.itemList.add(item4);
        this.itemList.add(AttributeUIHelper.createItem(group, "Altitude", R.string.EARTH_GEOCHEM25W_ALTITUDE, EditTextEditorInfo.textEditor(), map, (String) null));
        String defData4 = null;
        if (!isNewFlag() && !map.get("SamPos").toString().equals("")) {
            defData4 = map.get("SamPos_C").toString() + ":" + map.get("SamPos").toString();
        }
        AttributeItem item5 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_SAMLAYER, EditTextEditorInfo.textEditor(), defData4, (String) null);
        item5.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String titleString = AttGeoChemStream25wActivity.this.getResources().getString(R.string.EARTH_GEOCHEM25W_SAMLAYER);
                AttGeoChemStream25wActivity.this.BtnClickFun(AttGeoChemStream25wActivity.this.ItemSamPos, titleString, "StreamSamPos", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        item5.getEditorEditText().setEnabled(false);
        this.itemList.add(item5);
        this.ItemSamPos = item5;
        String defData5 = null;
        if (!isNewFlag() && !map.get("Component").toString().equals("")) {
            defData5 = map.get("Component_c").toString() + ":" + map.get("Component").toString();
        }
        AttributeItem item6 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_COMPONENT, EditTextEditorInfo.textEditor(), defData5, (String) null);
        item6.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttGeoChemStream25wActivity.this.mAdapter = ArrayAdapter.createFromResource(AttGeoChemStream25wActivity.this, R.array.Component_array, android.R.layout.simple_spinner_dropdown_item);
                LayoutInflater inflater = AttGeoChemStream25wActivity.this.getLayoutInflater();
                View layout = inflater.inflate(R.layout.myearthdialog, (ViewGroup) AttGeoChemStream25wActivity.this.findViewById(R.id.mydlg));
                AttGeoChemStream25wActivity.this.spinner1 = (Spinner) layout.findViewById(R.id.mydlg_spinner1);
                AttGeoChemStream25wActivity.this.spinner2 = (Spinner) layout.findViewById(R.id.mydlg_spinner2);
                AttGeoChemStream25wActivity.this.spinner3 = (Spinner) layout.findViewById(R.id.mydlg_spinner3);
                AttGeoChemStream25wActivity.this.spinner1.setAdapter((SpinnerAdapter) AttGeoChemStream25wActivity.this.mAdapter);
                AttGeoChemStream25wActivity.this.spinner2.setAdapter((SpinnerAdapter) AttGeoChemStream25wActivity.this.mAdapter);
                AttGeoChemStream25wActivity.this.spinner3.setAdapter((SpinnerAdapter) AttGeoChemStream25wActivity.this.mAdapter);
                AdapterView.OnItemSelectedListener spinnerListener = AttGeoChemStream25wActivity.this.new myOnItemSelectedListener(AttGeoChemStream25wActivity.this, AttGeoChemStream25wActivity.this.mAdapter);
                AttGeoChemStream25wActivity.this.spinner1.setOnItemSelectedListener(spinnerListener);
                AttGeoChemStream25wActivity.this.spinner1.setTag(1);
                AttGeoChemStream25wActivity.this.spinner2.setOnItemSelectedListener(spinnerListener);
                AttGeoChemStream25wActivity.this.spinner2.setTag(2);
                AttGeoChemStream25wActivity.this.spinner3.setOnItemSelectedListener(spinnerListener);
                AttGeoChemStream25wActivity.this.spinner3.setTag(3);
                AlertDialog.Builder builder1 = new AlertDialog.Builder(AttGeoChemStream25wActivity.this);
                builder1.setTitle(R.string.EARTH_GEOCHEMSTREAM5W_COMPONENT);
                builder1.setView(layout);
                builder1.setPositiveButton(AttGeoChemStream25wActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.2.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        String strData = AttGeoChemStream25wActivity.this.getResources().getString(R.string.RGMAP_PROMPT_COMPANT1) + "-" + AttGeoChemStream25wActivity.this.strselpos1 + ";" + AttGeoChemStream25wActivity.this.getResources().getString(R.string.RGMAP_PROMPT_COMPANT2) + "-" + AttGeoChemStream25wActivity.this.strselpos2 + ";" + AttGeoChemStream25wActivity.this.getResources().getString(R.string.RGMAP_PROMPT_COMPANT3) + "-" + AttGeoChemStream25wActivity.this.strselpos3;
                        AttGeoChemStream25wActivity.this.itemComponent.getEditorEditText().setText(String.valueOf(AttGeoChemStream25wActivity.this.iselpos1) + String.valueOf(AttGeoChemStream25wActivity.this.iselpos2) + String.valueOf(AttGeoChemStream25wActivity.this.iselpos3) + ":" + strData);
                    }
                });
                builder1.show();
            }
        }, getResources().getDrawable(R.drawable.search)));
        item6.getEditorEditText().setEnabled(false);
        this.itemList.add(item6);
        this.itemComponent = item6;
        String defData6 = null;
        if (!isNewFlag() && !map.get("Color_C").toString().equals("")) {
            defData6 = map.get("Color_C").toString() + ":" + map.get("Color").toString();
        }
        AttributeItem item7 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_COLOR, EditTextEditorInfo.textEditor(), defData6, (String) null);
        item7.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String titleString = AttGeoChemStream25wActivity.this.getResources().getString(R.string.EARTH_GEOCHEM25W_COLOR);
                AttGeoChemStream25wActivity.this.BtnClickFun(AttGeoChemStream25wActivity.this.itemColor, titleString, "StreamColor", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        item7.getEditorEditText().setEnabled(false);
        this.itemList.add(item7);
        this.itemColor = item7;
        String defData7 = null;
        if (!isNewFlag() && !map.get("StreamOrder").toString().equals("")) {
            defData7 = map.get("StreamOrder_C").toString() + ":" + map.get("StreamOrder").toString();
        }
        AttributeItem item8 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_STREAMORDER, EditTextEditorInfo.textEditor(), defData7, (String) null);
        item8.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String titleString = AttGeoChemStream25wActivity.this.getResources().getString(R.string.EARTH_GEOCHEM25W_STREAMORDER);
                AttGeoChemStream25wActivity.this.BtnClickFun(AttGeoChemStream25wActivity.this.itemStreamOrder, titleString, "SteamOrder", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        item8.getEditorEditText().setEnabled(false);
        this.itemList.add(item8);
        this.itemStreamOrder = item8;
        String defData8 = null;
        if (!isNewFlag() && !map.get("RockType").toString().equals("")) {
            defData8 = map.get("RockType").toString() + ":" + map.get("RockName").toString();
        }
        AttributeItem item9 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_ROCKTYPE, EditTextEditorInfo.textEditor(), defData8, (String) null);
        item9.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                LayoutInflater inflater = AttGeoChemStream25wActivity.this.getLayoutInflater();
                View layout = inflater.inflate(R.layout.rocktype, (ViewGroup) AttGeoChemStream25wActivity.this.findViewById(R.id.rocktypedlg));
                AttGeoChemStream25wActivity.this.btn_rocktype1 = (Button) layout.findViewById(R.id.button1);
                AttGeoChemStream25wActivity.this.btn_rocktype1.setTag(1);
                AttGeoChemStream25wActivity.this.btn_rocktype2 = (Button) layout.findViewById(R.id.button2);
                AttGeoChemStream25wActivity.this.btn_rocktype2.setTag(2);
                AttGeoChemStream25wActivity.this.btn_rocktype3 = (Button) layout.findViewById(R.id.button3);
                AttGeoChemStream25wActivity.this.btn_rocktype3.setTag(3);
                AttGeoChemStream25wActivity.this.list_rocktype = (ListView) layout.findViewById(R.id.listView1);
                Cursor SectionSursor = AttGeoChemStream25wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='b'", null);
                while (SectionSursor.moveToNext()) {
                    int columnIndex = SectionSursor.getColumnIndex("Code");
                    String szFieldValue = SectionSursor.getString(columnIndex);
                    int columnIndex1 = SectionSursor.getColumnIndex("Item");
                    String szFieldValue1 = SectionSursor.getString(columnIndex1);
                    HashMap<String, Object> map2 = new HashMap<>();
                    map2.put("ItemTitle", szFieldValue);
                    map2.put("ItemText", szFieldValue1);
                    AttGeoChemStream25wActivity.this.listItem.add(map2);
                }
                AttGeoChemStream25wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemStream25wActivity.this, AttGeoChemStream25wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                AttGeoChemStream25wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemStream25wActivity.this.listItemAdapter);
                AttGeoChemStream25wActivity.this.list_rocktype.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5.1
                    @Override // android.widget.AdapterView.OnItemClickListener
                    public void onItemClick(AdapterView<?> arg0, View arg1, int arg2, long arg3) {
                        if (((ListView) arg0).getTag() != null) {
                            ((View) ((ListView) arg0).getTag()).setBackgroundDrawable(null);
                        }
                        ((ListView) arg0).setTag(arg1);
                        arg1.setBackgroundColor(-16776961);
                        Map<String, Object> map3 = (Map) arg0.getItemAtPosition(arg2);
                        AttGeoChemStream25wActivity.this.m_strRockTypeString = map3.get("ItemTitle").toString() + ":" + map3.get("ItemText").toString();
                    }
                });
                AttGeoChemStream25wActivity.this.btn_rocktype1.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5.2
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemStream25wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemStream25wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='b'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemStream25wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemStream25wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemStream25wActivity.this, AttGeoChemStream25wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemStream25wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemStream25wActivity.this.listItemAdapter);
                    }
                });
                AttGeoChemStream25wActivity.this.btn_rocktype2.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5.3
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemStream25wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemStream25wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='c'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemStream25wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemStream25wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemStream25wActivity.this, AttGeoChemStream25wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemStream25wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemStream25wActivity.this.listItemAdapter);
                    }
                });
                AttGeoChemStream25wActivity.this.btn_rocktype3.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5.4
                    @Override // android.view.View.OnClickListener
                    public void onClick(View v2) {
                        AttGeoChemStream25wActivity.this.listItem.clear();
                        Cursor SectionSursor2 = AttGeoChemStream25wActivity.this.m_db.rawQuery("select * from RockType where chasstype ='d'", null);
                        while (SectionSursor2.moveToNext()) {
                            int columnIndex2 = SectionSursor2.getColumnIndex("Code");
                            String szFieldValue2 = SectionSursor2.getString(columnIndex2);
                            int columnIndex12 = SectionSursor2.getColumnIndex("Item");
                            String szFieldValue12 = SectionSursor2.getString(columnIndex12);
                            HashMap<String, Object> map3 = new HashMap<>();
                            map3.put("ItemTitle", szFieldValue2);
                            map3.put("ItemText", szFieldValue12);
                            AttGeoChemStream25wActivity.this.listItem.add(map3);
                        }
                        AttGeoChemStream25wActivity.this.listItemAdapter = new SimpleAdapter(AttGeoChemStream25wActivity.this, AttGeoChemStream25wActivity.this.listItem, R.layout.listitem, new String[]{"ItemTitle", "ItemText"}, new int[]{R.id.textView_title, R.id.textView_text});
                        AttGeoChemStream25wActivity.this.list_rocktype.setAdapter((ListAdapter) AttGeoChemStream25wActivity.this.listItemAdapter);
                    }
                });
                AlertDialog.Builder builder1 = new AlertDialog.Builder(AttGeoChemStream25wActivity.this);
                builder1.setTitle(R.string.EARTH_GEOCHEMSTREAM5W_ROCKTYPE);
                builder1.setView(layout);
                builder1.setPositiveButton(AttGeoChemStream25wActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.5.5
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        AttGeoChemStream25wActivity.this.ItemRockType.getEditorEditText().setText(AttGeoChemStream25wActivity.this.m_strRockTypeString);
                    }
                });
                builder1.show();
            }
        }, getResources().getDrawable(R.drawable.search)));
        item9.getEditorEditText().setEnabled(false);
        this.itemList.add(item9);
        this.ItemRockType = item9;
        this.itemList.add(AttributeUIHelper.createItem(group, "PH", R.string.EARTH_GEOCHEM25W_PH, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "BagNo", R.string.EARTH_GEOCHEM25W_BAGNO, EditTextEditorInfo.textEditor(), map, (String) null));
        String defData9 = null;
        if (!isNewFlag() && !map.get("Province_C").toString().equals("")) {
            defData9 = map.get("Province_C").toString() + ":" + map.get("Province").toString();
        }
        AttributeItem item10 = AttributeUIHelper.createItem(group, (String) null, R.string.EARTH_GEOCHEM25W_PROVINCE, EditTextEditorInfo.textEditor(), defData9, (String) null);
        item10.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String titleString = AttGeoChemStream25wActivity.this.getResources().getString(R.string.EARTH_GEOCHEM25W_PROVINCE);
                AttGeoChemStream25wActivity.this.BtnClickFun(AttGeoChemStream25wActivity.this.itemProvince, titleString, "Province", "Code", "Item");
            }
        }, getResources().getDrawable(R.drawable.search)));
        item10.getEditorEditText().setEnabled(false);
        this.itemList.add(item10);
        this.itemProvince = item10;
        this.itemList.add(AttributeUIHelper.createItem(group, "WorkUnit", R.string.EARTH_GEOCHEM25W_WORKUNIT, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "WorkUnit_C", R.string.EARTH_GEOCHEM25W_WORKUNITC, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "WorkArea", R.string.EARTH_GEOCHEM25W_WORKAREA, EditTextEditorInfo.textEditor(), map, (String) null));
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        df.format(date);
        AttributeItem item11 = AttributeUIHelper.createItem(group, "SamDate", R.string.EARTH_GEOCHEM25W_SAMEDATE, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.7
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttGeoChemStream25wActivity.this);
                String str = AttGeoChemStream25wActivity.this.getResources().getString(R.string.RGMAP_PROMPT_SETDATE);
                dateDialog.showListDialog(str, AttGeoChemStream25wActivity.this.itemSamDate.getEditorEditText());
            }
        }, null));
        this.itemList.add(item11);
        this.itemSamDate = item11;
        AttributeItem item12 = AttributeUIHelper.createItem(group, "RecordMan", R.string.EARTH_GEOCHEM25W_RECORDMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item12.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item12);
        AttributeItem item13 = AttributeUIHelper.createItem(group, "SamMan", R.string.EARTH_GEOCHEM25W_SAMMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item13.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item13);
        AttributeItem item14 = AttributeUIHelper.createItem(group, "CheckMan", R.string.EARTH_GEOCHEM25W_CHECKMAN, EditTextEditorInfo.textEditor(), map, (String) null);
        item14.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item14);
        this.itemList.add(AttributeUIHelper.createItem(group, "Remark", R.string.EARTH_GEOCHEM25W_REMARK, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem.AttributeItemParams mediaDesc = new AttributeItem.AttributeItemParams();
        mediaDesc.label = getResources().getString(R.string.GEOCHEM25W_MULMEDIA__PROMPT_TOOL);
        mediaDesc.clickable = new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.8
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String samcode = AttGeoChemStream25wActivity.this.ItemSamCode.getEditorEditText().getText().toString();
                if (samcode.equals("")) {
                    String tm = AttGeoChemStream25wActivity.this.getResources().getString(R.string.GEOCHEM25W_MULMEDIA__PROMPT_NULL);
                    Toast.makeText(AttGeoChemStream25wActivity.this, tm, 0).show();
                    return;
                }
                String path = AoRGMapActivity.getCurrentMapPath() + "/Image_GeoChem/";
                File PathFile = new File(path);
                if (!PathFile.exists()) {
                    PathFile.mkdir();
                }
                String path2 = path + "GeoChemMultiMedia25w.db";
                File file = new File(path2);
                if (!file.exists()) {
                    try {
                        file.createNewFile();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                GeoChemMulMedia25wDao mTempSqlDB = new GeoChemMulMedia25wDao(AttGeoChemStream25wActivity.this, path2);
                int sortID = AttGeoChemStream25wActivity.this.GetSortID(mTempSqlDB, path2, samcode, 2);
                Intent intent = new Intent(AttGeoChemStream25wActivity.this, (Class<?>) AttGeoChemMulMedia25wActivity.class);
                Bundle bundle = new Bundle();
                int sd = RGMapApplication.getCurrentApp().addInAppTaskData(mTempSqlDB);
                bundle.putInt("SqlDB", sd);
                bundle.putString("TableName", "MediaBaseInfo");
                bundle.putString("DictPath", RGMapApplication.getCurrentApp().getCurrentGlobal().getDictPath());
                bundle.putString(AttCommParams.PARAM_STRING_DB_PATH, path2);
                bundle.putString("SampleCode", samcode);
                bundle.putString("MainID", "2");
                bundle.putString("DbFLORD", path);
                if (sortID > -1) {
                    bundle.putBoolean("isNew", false);
                    bundle.putInt(AttributeDBActivity.PARAM_INT_SORTID, sortID);
                    bundle.putInt(AttCommParams.PARAM_INT_SQLDB_OP_TYPE, 3);
                } else {
                    bundle.putBoolean("isNew", true);
                    bundle.putInt(AttCommParams.PARAM_INT_SQLDB_OP_TYPE, 1);
                }
                intent.putExtras(bundle);
                double[] pos = {AttGeoChemStream25wActivity.this.getX() / AttGeoChemStream25wActivity.this.mGState.getCoordUnitRate(), AttGeoChemStream25wActivity.this.getY() / AttGeoChemStream25wActivity.this.mGState.getCoordUnitRate()};
                AttGeoChemStream25wActivity.this.mGState.setChemSamPos(pos);
                AttGeoChemStream25wActivity.this.startActivity(intent);
            }
        }, null);
        group.addItem(mediaDesc);
        container.addView(group.getInnerView());
    }

    protected int GetSortID(GeoChemMulMedia25wDao mSqlDB, String path, String samcode, int i) {
        return mSqlDB.GetSortID(samcode, i);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void BtnClickFun(final AttributeItem v, String title, String tabname, String field1, String field2) {
        String strSQL = String.format("select * from %s", tabname);
        Cursor SectionSursor = this.m_db.rawQuery(strSQL, null);
        int isize = SectionSursor.getCount();
        this.fieldsStrings = new String[isize];
        int iFiled = 0;
        while (SectionSursor.moveToNext()) {
            int columnIndex = SectionSursor.getColumnIndex(field1);
            String szFieldValue = SectionSursor.getString(columnIndex);
            int columnIndex1 = SectionSursor.getColumnIndex(field2);
            String szFieldValue1 = SectionSursor.getString(columnIndex1);
            this.fieldsStrings[iFiled] = szFieldValue + ":" + szFieldValue1;
            iFiled++;
        }
        AlertDialog.Builder builder1 = new AlertDialog.Builder(this);
        builder1.setTitle(title);
        builder1.setSingleChoiceItems(this.fieldsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.9
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                AttGeoChemStream25wActivity.this.iSelFiled = which;
            }
        });
        builder1.setPositiveButton(getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.earth.AttGeoChemStream25wActivity.10
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                v.getEditorEditText().setText(AttGeoChemStream25wActivity.this.fieldsStrings[AttGeoChemStream25wActivity.this.iSelFiled]);
            }
        });
        builder1.show();
    }

    public class myOnItemSelectedListener implements AdapterView.OnItemSelectedListener {
        ArrayAdapter<CharSequence> mLocalAdapter;
        Activity mLocalContext;

        public myOnItemSelectedListener(Activity c, ArrayAdapter<CharSequence> ad) {
            this.mLocalContext = c;
            this.mLocalAdapter = ad;
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public void onItemSelected(AdapterView<?> arg0, View arg1, int arg2, long arg3) {
            int tag = ((Integer) arg0.getTag()).intValue();
            switch (tag) {
                case 1:
                    AttGeoChemStream25wActivity.this.iselpos1 = arg2;
                    AttGeoChemStream25wActivity.this.strselpos1 = arg0.getItemAtPosition(arg2).toString();
                    break;
                case 2:
                    AttGeoChemStream25wActivity.this.iselpos2 = arg2;
                    AttGeoChemStream25wActivity.this.strselpos2 = arg0.getItemAtPosition(arg2).toString();
                    break;
                case 3:
                    AttGeoChemStream25wActivity.this.iselpos3 = arg2;
                    AttGeoChemStream25wActivity.this.strselpos3 = arg0.getItemAtPosition(arg2).toString();
                    break;
            }
        }

        @Override // android.widget.AdapterView.OnItemSelectedListener
        public void onNothingSelected(AdapterView<?> arg0) {
        }
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }
}
