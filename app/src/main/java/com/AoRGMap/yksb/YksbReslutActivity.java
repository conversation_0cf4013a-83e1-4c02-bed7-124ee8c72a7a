package com.AoRGMap.yksb;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.Nullable;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;
import com.AoRGMap.R;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class YksbReslutActivity extends Activity {
    private static final int BDBKRESLUT = 9999;
    public static final String YKSBRESLUT = "YKSBRESULT";
    private String[] mResults;
    private final String WebUrl1 = "http://219.142.81.39:8080/BigDataService/rest/themes/stones/search/1.0?&keys=";
    private final String WebUrl2 = "http://www.gsigrid.cgs.gov.cn/newgsigrid/BigDataPortal/showWord.aspx?filePath=";
    private String mResult = "";
    private TextView mResultTv = null;
    private WebView mBdbkResult = null;
    private Handler mHandler = new Handler() { // from class: com.AoRGMap.yksb.YksbReslutActivity.1
        @Override // android.os.Handler
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case YksbReslutActivity.BDBKRESLUT /* 9999 */:
                    String url = (String) msg.obj;
                    YksbReslutActivity.this.mBdbkResult.loadDataWithBaseURL(null, url, "text/html", "UTF-8", null);
                    break;
            }
        }
    };

    /* JADX WARN: Type inference failed for: r5v22, types: [com.AoRGMap.yksb.YksbReslutActivity$3] */
    @Override // android.app.Activity
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.yksb_result_activity);
        Bundle bundle = getIntent().getExtras();
        this.mResult = bundle.getString(YKSBRESLUT);
        this.mResultTv = (TextView) findViewById(R.id.id_yksg_result);
        this.mBdbkResult = (WebView) findViewById(R.id.id_yksg_bdbk);
        WebSettings mWebSettings = this.mBdbkResult.getSettings();
        mWebSettings.setBuiltInZoomControls(true);
        mWebSettings.setSupportZoom(true);
        mWebSettings.setDefaultZoom(WebSettings.ZoomDensity.CLOSE);
        mWebSettings.setJavaScriptEnabled(true);
        mWebSettings.setDefaultTextEncodingName("UTF-8");
        mWebSettings.setJavaScriptEnabled(true);
        mWebSettings.setBlockNetworkImage(false);
        mWebSettings.setMixedContentMode(2);
        this.mBdbkResult.setWebViewClient(new WebViewClient() { // from class: com.AoRGMap.yksb.YksbReslutActivity.2
            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Uri uri = Uri.parse(url);
                Intent intent = new Intent("android.intent.action.VIEW");
                if (url.toLowerCase().endsWith(".mp4")) {
                    intent.setDataAndType(uri, "video/mp4");
                } else if (url.toLowerCase().endsWith(".mp3")) {
                    intent.setDataAndType(uri, "audio/mp3");
                } else if (url.toLowerCase().endsWith(".wav")) {
                    intent.setDataAndType(uri, "audio/wav");
                }
                view.getContext().startActivity(intent);
                return true;
            }
        });
        mWebSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);
        this.mResultTv.setText(this.mResult);
        this.mResults = this.mResult.split("-");
        String keys = this.mResults[0];
        for (int i = 1; i < this.mResults.length; i++) {
            keys = keys + "," + this.mResults[i];
        }
        final String key = keys;
        new Thread() { // from class: com.AoRGMap.yksb.YksbReslutActivity.3
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                String reslult = null;
                try {
                    reslult = YksbReslutActivity.this.onHttpRequest("http://219.142.81.39:8080/BigDataService/rest/themes/stones/search/1.0?&keys=" + URLEncoder.encode(key, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (!reslult.equals("")) {
                    String reslut1 = null;
                    try {
                        reslut1 = YksbReslutActivity.this.onHttpRequest2("http://www.gsigrid.cgs.gov.cn/newgsigrid/BigDataPortal/showWord.aspx?filePath=" + URLEncoder.encode(reslult, "utf-8"));
                    } catch (UnsupportedEncodingException e2) {
                        e2.printStackTrace();
                    }
                    Message msg = new Message();
                    msg.obj = reslut1;
                    msg.what = YksbReslutActivity.BDBKRESLUT;
                    YksbReslutActivity.this.mHandler.sendMessage(msg);
                }
            }
        }.start();
    }

    public String onHttpRequest(String strURL) {
        String resString;
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead == -1) {
                    break;
                }
                baos.write(buffer, 0, hasRead);
            }
            inSteam.close();
            String resString2 = new String(baos.toByteArray());
            try {
                JSONObject myJsonArray = new JSONObject(resString2);
                resString = resString2;
                for (int i = this.mResults.length - 1; i >= 0; i--) {
                    try {
                        String Key = this.mResults[i];
                        if (myJsonArray.has(Key)) {
                            resString = myJsonArray.getString(Key);
                            if (!resString.equals("")) {
                                return resString;
                            }
                        }
                    } catch (JSONException e) {
                        e = e;
                        e.printStackTrace();
                        return resString;
                    }
                }
            } catch (JSONException e2) {
                e = e2;
                resString = resString2;
            }
            return resString;
        } catch (Exception e3) {
            e3.printStackTrace();
            return "";
        }
    }

    public String onHttpRequest2(String strURL) {
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead != -1) {
                    baos.write(buffer, 0, hasRead);
                } else {
                    inSteam.close();
                    return new String(baos.toByteArray());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
