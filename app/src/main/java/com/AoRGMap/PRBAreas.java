package com.AoRGMap;

import com.AoGIS.base.PointInfo;
import com.AoGIS.edit.info.DefaultInfo;
import com.AoRGMap.ExtLayer.AttLargeScaleActivity;
import com.AoRGMap.earth.Att25RockActivity;
import com.AoRGMap.earth.Att25RockRActivity;
import com.AoRGMap.earth.Att25SoilActivity;
import com.AoRGMap.earth.Att25SoilRActivity;
import com.AoRGMap.earth.Att25StreamActivity;
import com.AoRGMap.earth.Att25StreamRActivity;
import com.AoRGMap.earth.Att25WaterActivity;
import com.AoRGMap.earth.Att25WaterRActivity;
import com.AoRGMap.earth.AttGeoChemMulSedimentActivity;
import com.AoRGMap.earth.AttGeoChemMulSoilActivity;
import com.AoRGMap.earth.AttGeoChemMulWaterActivity;
import com.AoRGMap.earth.AttGeoChemRock25wActivity;
import com.AoRGMap.earth.AttGeoChemRock5wActivity;
import com.AoRGMap.earth.AttGeoChemSamNationalActivity;
import com.AoRGMap.earth.AttGeoChemSoil25wActivity;
import com.AoRGMap.earth.AttGeoChemSoil5wActivity;
import com.AoRGMap.earth.AttGeoChemStream25wActivity;
import com.AoRGMap.earth.AttGeoChemStream5wActivity;
import com.AoRGMap.earth.AttGeoRockActivity;
import com.AoRGMap.earth.AttGeoRockRActivity;
import com.AoRGMap.earth.AttGeoSoilActivity;
import com.AoRGMap.earth.AttGeoSoilRActivity;
import com.AoRGMap.earth.AttGeoWaterActivity;
import com.AoRGMap.earth.AttGeoWaterRActivity;
import com.AoRGMap.earth.AttMulsoilActivity;
import com.AoRGMap.earth.AttMulsoilRActivity;
import com.AoRGMap.earth.AttMulstremActivity;
import com.AoRGMap.earth.AttMulstremRActivity;
import com.AoRGMap.earth.AttMulwaterActivity;
import com.AoRGMap.earth.AttMulwaterRActivity;
import com.AoRGMap.earth.AttSandSediActivity;
import com.AoRGMap.earth.AttSandSediRActivity;
import com.AoRGMap.earth.AttSoilSediActivity;
import com.AoRGMap.earth.AttSoilSediRActivity;
import com.AoRGMap.earth.AttStremSediActivity;
import com.AoRGMap.earth.AttStremSediRActivity;
import com.AoRGMap.pm.AttSectionActivity;
import com.AoRGMap.pm.SectionActivity;
import com.AoRGMap.prb.AttAttitudeActivity;
import com.AoRGMap.prb.AttEngPointActivity;
import com.AoRGMap.prb.AttFossilActivity;
import com.AoRGMap.prb.AttFreeLineActivity;
import com.AoRGMap.prb.AttGBoundaryActivity;
import com.AoRGMap.prb.AttGPointActivity;
import com.AoRGMap.prb.AttHydPointActivity;
import com.AoRGMap.prb.AttOrecheckActivity;
import com.AoRGMap.prb.AttPhotoActivity;
import com.AoRGMap.prb.AttRemainActivity;
import com.AoRGMap.prb.AttRemainLinActivity;
import com.AoRGMap.prb.AttRoutingActivity;
import com.AoRGMap.prb.AttSampleActivity;
import com.AoRGMap.prb.AttSketchActivity;

/* loaded from: classes.dex */
public class PRBAreas {
    public static final String m_str25GeoRock = "GeoChemRock25w.TM";
    public static final String m_str25GeoSoil = "GeoChemSoil25w.TM";
    public static final String m_str25GeoWater = "GEOCHEMSTREAM25W.TM";
    public static final String m_str25Rock = "25ROCK.TM";
    public static final String m_str25RockR = "25ROCK.LM";
    public static final String m_str25Soil = "25SOIL.TM";
    public static final String m_str25SoilR = "25SOIL.LM";
    public static final String m_str25Stream = "25STREAM.TM";
    public static final String m_str25StreamR = "25STREAM.LM";
    public static final String m_str25Water = "25WATER.TM";
    public static final String m_str25WaterR = "25WATER.LM";
    public static final String m_str5GeoRock = "GeoChemRock5w.TM";
    public static final String m_str5GeoSoil = "GeoChemSoil5w.TM";
    public static final String m_str5GeoWater = "GeoChemStream5w.TM";
    public static final String m_strAttitude = "ATTITUDE.TM";
    public static final String m_strBoundary = "BOUNDARY.LM";
    public static String m_strCurEditArea = null;
    public static final String m_strEngPoint = "ENGPOINT.TM";
    public static final String m_strFossil = "FOSSIL.TM";
    public static final String m_strFreeL = "FREE.LM";
    public static final String m_strFreeT = "FREE.TM";
    public static final String m_strGPS = "GPS.TM";
    public static final String m_strGPSL = "GPS.LM";
    public static final String m_strGPoint = "GPOINT.TM";
    public static final String m_strGRoute = "GROUTE.LM";
    public static final String m_strGeoChemMulSediment = "GEOCHEMMULSEDIMENT.TM";
    public static final String m_strGeoChemMulWater = "GEOCHEMMULWATER.TM";
    public static final String m_strGeoChemMulsoil = "GEOCHEMMULSOIL.TM";
    public static final String m_strGeoChemSamNational = "GEOCHEMSAMNATIONAL.TM";
    public static final String m_strGeoRock = "GEOROCK.TM";
    public static final String m_strGeoRockR = "GEOROCK.LM";
    public static final String m_strGeoSection = "GEOSECTION.TM";
    public static final String m_strGeoSoil = "GEOSOIL.TM";
    public static final String m_strGeoSoilR = "GEOSOIL.LM";
    public static final String m_strGeoWater = "GEOWATER.TM";
    public static final String m_strGeoWaterR = "GEOWATER.LM";
    public static final String m_strHydPoint = "HYDPOINT.TM";
    public static final String m_strLargeScale = "LARGESCALE_AREA.LM";
    public static final String m_strMulsoil = "MULSOIL.TM";
    public static final String m_strMulsoilR = "MULSOIL.LM";
    public static final String m_strMulstrem = "MULSTREM.TM";
    public static final String m_strMulstremR = "MULSTREM.LM";
    public static final String m_strMulwater = "MULWATER.TM";
    public static final String m_strMulwaterR = "MULWATER.LM";
    public static final String m_strOrecheck = "ORECHECK.TM";
    public static final String m_strPhoto = "PHOTO.TM";
    public static final String m_strRemain = "GEORELICS.TM";
    public static final String m_strRemainLin = "GEORELICS.LM";
    public static final String m_strRouting = "ROUTING.LM";
    public static final String m_strSample = "SAMPLE.TM";
    public static final String m_strSandSedi = "SAND_SEDI.TM";
    public static final String m_strSandSediR = "SAND_SEDI.LM";
    public static final String m_strSketch = "SKETCH.TM";
    public static final String m_strSoilSedi = "SOIL_SEDI.TM";
    public static final String m_strSoilSediR = "SOIL_SEDI.LM";
    public static final String m_strStremSedi = "STREM_SEDI.TM";
    public static final String m_strStremSediR = "STREM_SEDI.LM";

    public static String getAreaChineseName(String strFileName) {
        RGMapApplication app = RGMapApplication.getCurrentApp();
        if (strFileName.compareToIgnoreCase(m_strGPoint) == 0) {
            String strArea = app.getResources().getString(R.string.menu_main_prb_p);
            return strArea;
        }
        if (strFileName.compareToIgnoreCase(m_strRouting) == 0) {
            String strArea2 = app.getResources().getString(R.string.menu_main_prb_r);
            return strArea2;
        }
        if (strFileName.compareToIgnoreCase(m_strBoundary) == 0) {
            String strArea3 = app.getResources().getString(R.string.menu_main_prb_b);
            return strArea3;
        }
        if (strFileName.compareToIgnoreCase(m_strAttitude) == 0) {
            String strArea4 = app.getResources().getString(R.string.menu_main_prb_att);
            return strArea4;
        }
        if (strFileName.compareToIgnoreCase(m_strSketch) == 0) {
            String strArea5 = app.getResources().getString(R.string.menu_main_prb_sketch);
            return strArea5;
        }
        if (strFileName.compareToIgnoreCase(m_strPhoto) == 0) {
            String strArea6 = app.getResources().getString(R.string.menu_main_prb_photo);
            return strArea6;
        }
        if (strFileName.compareToIgnoreCase(m_strFossil) == 0) {
            String strArea7 = app.getResources().getString(R.string.menu_main_prb_fossil);
            return strArea7;
        }
        if (strFileName.compareToIgnoreCase(m_strGRoute) == 0) {
            String strArea8 = app.getResources().getString(R.string.menu_main_prb_route);
            return strArea8;
        }
        if (strFileName.compareToIgnoreCase(m_strSample) == 0) {
            String strArea9 = app.getResources().getString(R.string.menu_main_prb_sample);
            return strArea9;
        }
        if (strFileName.compareToIgnoreCase(m_strOrecheck) == 0) {
            String strArea10 = app.getResources().getString(R.string.menu_main_prb_orecheck);
            return strArea10;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeT) == 0) {
            String strArea11 = app.getResources().getString(R.string.menu_main_prb_pfree);
            return strArea11;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeL) == 0) {
            String strArea12 = app.getResources().getString(R.string.menu_main_prb_lfree);
            return strArea12;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSection) == 0) {
            String strArea13 = app.getResources().getString(R.string.menu_main_section);
            return strArea13;
        }
        if (strFileName.compareToIgnoreCase(m_strRemain) == 0) {
            String strArea14 = app.getResources().getString(R.string.menu_main_prb_remain);
            return strArea14;
        }
        if (strFileName.compareToIgnoreCase(m_strRemainLin) == 0) {
            String strArea15 = app.getResources().getString(R.string.menu_main_prb_remainLin);
            return strArea15;
        }
        if (strFileName.compareToIgnoreCase(m_strEngPoint) == 0) {
            String strArea16 = app.getResources().getString(R.string.menu_main_prb_engpoint);
            return strArea16;
        }
        if (strFileName.compareToIgnoreCase(m_strHydPoint) == 0) {
            String strArea17 = app.getResources().getString(R.string.menu_main_prb_hydpoint);
            return strArea17;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSedi) == 0) {
            String strArea18 = app.getResources().getString(R.string.menu_main_earth_sand_sedi);
            return strArea18;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRock) == 0) {
            String strArea19 = app.getResources().getString(R.string.menu_main_earth_georock);
            return strArea19;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoil) == 0) {
            String strArea20 = app.getResources().getString(R.string.menu_main_earth_geosoil);
            return strArea20;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWater) == 0) {
            String strArea21 = app.getResources().getString(R.string.menu_main_earth_geowater);
            return strArea21;
        }
        if (strFileName.compareToIgnoreCase(m_str25Rock) == 0) {
            String strArea22 = app.getResources().getString(R.string.menu_main_earth_25rock);
            return strArea22;
        }
        if (strFileName.compareToIgnoreCase(m_str25Soil) == 0) {
            String strArea23 = app.getResources().getString(R.string.menu_main_earth_25soil);
            return strArea23;
        }
        if (strFileName.compareToIgnoreCase(m_str25Water) == 0) {
            String strArea24 = app.getResources().getString(R.string.menu_main_earth_25water);
            return strArea24;
        }
        if (strFileName.compareToIgnoreCase(m_str25Stream) == 0) {
            String strArea25 = app.getResources().getString(R.string.menu_main_earth_25stream);
            return strArea25;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSediR) == 0) {
            String strArea26 = app.getResources().getString(R.string.menu_main_earth_sand_sedi_r);
            return strArea26;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRockR) == 0) {
            String strArea27 = app.getResources().getString(R.string.menu_main_earth_georock_r);
            return strArea27;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoilR) == 0) {
            String strArea28 = app.getResources().getString(R.string.menu_main_earth_geosoil_r);
            return strArea28;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWaterR) == 0) {
            String strArea29 = app.getResources().getString(R.string.menu_main_earth_geowater_r);
            return strArea29;
        }
        if (strFileName.compareToIgnoreCase(m_str25RockR) == 0) {
            String strArea30 = app.getResources().getString(R.string.menu_main_earth_25rock_r);
            return strArea30;
        }
        if (strFileName.compareToIgnoreCase(m_str25SoilR) == 0) {
            String strArea31 = app.getResources().getString(R.string.menu_main_earth_25soil_r);
            return strArea31;
        }
        if (strFileName.compareToIgnoreCase(m_str25WaterR) == 0) {
            String strArea32 = app.getResources().getString(R.string.menu_main_earth_25water_r);
            return strArea32;
        }
        if (strFileName.compareToIgnoreCase(m_str25StreamR) == 0) {
            String strArea33 = app.getResources().getString(R.string.menu_main_earth_25stream_r);
            return strArea33;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSedi) == 0) {
            String strArea34 = app.getResources().getString(R.string.menu_main_earth_strem_sedi);
            return strArea34;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSedi) == 0) {
            String strArea35 = app.getResources().getString(R.string.menu_main_earth_soil_sedi);
            return strArea35;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoil) == 0) {
            String strArea36 = app.getResources().getString(R.string.menu_main_earth_mulsoil);
            return strArea36;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwater) == 0) {
            String strArea37 = app.getResources().getString(R.string.menu_main_earth_mulwater);
            return strArea37;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstrem) == 0) {
            String strArea38 = app.getResources().getString(R.string.menu_main_earth_mulstrem);
            return strArea38;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSediR) == 0) {
            String strArea39 = app.getResources().getString(R.string.menu_main_earth_strem_sedi_r);
            return strArea39;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSediR) == 0) {
            String strArea40 = app.getResources().getString(R.string.menu_main_earth_soil_sedi_r);
            return strArea40;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoilR) == 0) {
            String strArea41 = app.getResources().getString(R.string.menu_main_earth_mulsoil_r);
            return strArea41;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstremR) == 0) {
            String strArea42 = app.getResources().getString(R.string.menu_main_earth_mulstrem_r);
            return strArea42;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwaterR) == 0) {
            String strArea43 = app.getResources().getString(R.string.menu_main_earth_mulwater_r);
            return strArea43;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoSoil) == 0) {
            String strArea44 = app.getResources().getString(R.string.menu_main_earth_5geosoil);
            return strArea44;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoWater) == 0) {
            String strArea45 = app.getResources().getString(R.string.menu_main_earth_5geowater);
            return strArea45;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoRock) == 0) {
            String strArea46 = app.getResources().getString(R.string.menu_main_earth_5georock);
            return strArea46;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoSoil) == 0) {
            String strArea47 = app.getResources().getString(R.string.menu_main_earth_25geosoil);
            return strArea47;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoWater) == 0) {
            String strArea48 = app.getResources().getString(R.string.menu_main_earth_25geowater);
            return strArea48;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoRock) == 0) {
            String strArea49 = app.getResources().getString(R.string.menu_main_earth_25georock);
            return strArea49;
        }
        if (strFileName.compareToIgnoreCase(m_strLargeScale) == 0) {
            return "大比例尺综合地质图";
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemSamNational) == 0) {
            String strArea50 = app.getResources().getString(R.string.RGMAP_PROMPT_MENUGEOCHEM7);
            return strArea50;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulsoil) == 0) {
            return "土壤地球化学采样";
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulSediment) == 0) {
            return "近岸海域沉积物地球化学采样";
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulWater) == 0) {
            return "水地球化学采样";
        }
        return null;
    }

    public static Class<?> getAttActivityClass(String strFileName) {
        if (strFileName.compareToIgnoreCase(m_strGPoint) == 0) {
            return AttGPointActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strRouting) == 0) {
            return AttRoutingActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strBoundary) == 0) {
            return AttGBoundaryActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strAttitude) == 0) {
            return AttAttitudeActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strSketch) == 0) {
            return AttSketchActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strPhoto) == 0) {
            return AttPhotoActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strFossil) == 0) {
            return AttFossilActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGRoute) == 0) {
            return null;
        }
        if (strFileName.compareToIgnoreCase(m_strSample) == 0) {
            return AttSampleActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strOrecheck) == 0) {
            return AttOrecheckActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeT) == 0) {
            return null;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeL) == 0) {
            return AttFreeLineActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSection) == 0) {
            return AttSectionActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strRemain) == 0) {
            return AttRemainActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strRemainLin) == 0) {
            return AttRemainLinActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strEngPoint) == 0) {
            return AttEngPointActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strHydPoint) == 0) {
            return AttHydPointActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSedi) == 0) {
            return AttSandSediActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRock) == 0) {
            return AttGeoRockActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoil) == 0) {
            return AttGeoSoilActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWater) == 0) {
            return AttGeoWaterActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25Rock) == 0) {
            return Att25RockActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25Soil) == 0) {
            return Att25SoilActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25Water) == 0) {
            return Att25WaterActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25Stream) == 0) {
            return Att25StreamActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSediR) == 0) {
            return AttSandSediRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRockR) == 0) {
            return AttGeoRockRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoilR) == 0) {
            return AttGeoSoilRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWaterR) == 0) {
            return AttGeoWaterRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25RockR) == 0) {
            return Att25RockRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25SoilR) == 0) {
            return Att25SoilRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25WaterR) == 0) {
            return Att25WaterRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25StreamR) == 0) {
            return Att25StreamRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSedi) == 0) {
            return AttStremSediActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSedi) == 0) {
            return AttSoilSediActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoil) == 0) {
            return AttMulsoilActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwater) == 0) {
            return AttMulwaterActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstrem) == 0) {
            return AttMulstremActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSediR) == 0) {
            return AttStremSediRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSediR) == 0) {
            return AttSoilSediRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoilR) == 0) {
            return AttMulsoilRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstremR) == 0) {
            return AttMulstremRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwaterR) == 0) {
            return AttMulwaterRActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoSoil) == 0) {
            return AttGeoChemSoil5wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoWater) == 0) {
            return AttGeoChemStream5wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoRock) == 0) {
            return AttGeoChemRock5wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoSoil) == 0) {
            return AttGeoChemSoil25wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoWater) == 0) {
            return AttGeoChemStream25wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoRock) == 0) {
            return AttGeoChemRock25wActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strLargeScale) == 0) {
            return AttLargeScaleActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemSamNational) == 0) {
            return AttGeoChemSamNationalActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulsoil) == 0) {
            return AttGeoChemMulSoilActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulSediment) == 0) {
            return AttGeoChemMulSedimentActivity.class;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulWater) == 0) {
            return AttGeoChemMulWaterActivity.class;
        }
        return null;
    }

    public static Class<?> getSecActivityClass(String strFileName) {
        if (strFileName.compareToIgnoreCase(m_strGeoSection) == 0) {
            return SectionActivity.class;
        }
        return null;
    }

    public static boolean setAreaDefaultInfo(String strFileName) {
        GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
        if (strFileName.compareToIgnoreCase(m_strGPoint) == 0) {
            PointInfo info = DefaultInfo.getDefSubInfo();
            info.subInfo.setSubno(1147);
            info.subInfo.setHeight(mGState.getDefPRBSubSize());
            info.subInfo.setWidth(mGState.getDefPRBSubSize());
            info.setIclr(1);
            m_strCurEditArea = m_strGPoint;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strRouting) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(1);
            m_strCurEditArea = m_strRouting;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strBoundary) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(23);
            m_strCurEditArea = m_strBoundary;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strAttitude) == 0) {
            PointInfo info2 = DefaultInfo.getDefSubInfo();
            info2.subInfo.setSubno(1001);
            info2.setIclr(1);
            info2.subInfo.setHeight(mGState.getDefAttitudeSubSize());
            info2.subInfo.setWidth(mGState.getDefAttitudeSubSize());
            m_strCurEditArea = m_strAttitude;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSketch) == 0) {
            PointInfo info3 = DefaultInfo.getDefSubInfo();
            info3.subInfo.setSubno(797);
            info3.setIclr(97);
            info3.subInfo.setHeight(mGState.getDefPRBSubSize());
            info3.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strSketch;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strPhoto) == 0) {
            PointInfo info4 = DefaultInfo.getDefSubInfo();
            info4.subInfo.setSubno(1160);
            info4.setIclr(97);
            info4.subInfo.setHeight(mGState.getDefPRBSubSize());
            info4.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strPhoto;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strFossil) == 0) {
            PointInfo info5 = DefaultInfo.getDefSubInfo();
            info5.subInfo.setSubno(1201);
            info5.setIclr(148);
            info5.subInfo.setHeight(mGState.getDefPRBSubSize());
            info5.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strFossil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGRoute) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(6);
            m_strCurEditArea = m_strGRoute;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSample) == 0) {
            PointInfo info6 = DefaultInfo.getDefSubInfo();
            info6.subInfo.setSubno(1153);
            info6.setIclr(1);
            info6.subInfo.setHeight(mGState.getDefPRBSubSize());
            info6.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strSample;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strOrecheck) == 0) {
            PointInfo info7 = DefaultInfo.getDefSubInfo();
            info7.subInfo.setSubno(1155);
            info7.setIclr(1);
            info7.subInfo.setHeight(mGState.getDefPRBSubSize());
            info7.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strOrecheck;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeT) == 0) {
            DefaultInfo.getDefSubInfo().setBytes(RGMapApplication.getCurrentApp().getCurrentGlobal().getDefSubInfo().getBytes());
            DefaultInfo.getDefNoteInfo().setBytes(RGMapApplication.getCurrentApp().getCurrentGlobal().getDefNoteInfo().getBytes());
            m_strCurEditArea = m_strFreeT;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeL) == 0) {
            DefaultInfo.getDefLineInfo().setBytes(RGMapApplication.getCurrentApp().getCurrentGlobal().getDefLineInfo().getBytes());
            m_strCurEditArea = m_strFreeL;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSection) == 0) {
            PointInfo info8 = DefaultInfo.getDefSubInfo();
            info8.subInfo.setSubno(31);
            info8.setIclr(1);
            info8.subInfo.setHeight(mGState.getDefPRBSubSize());
            info8.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoSection;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strRemain) == 0) {
            PointInfo info9 = DefaultInfo.getDefSubInfo();
            info9.subInfo.setSubno(330);
            info9.setIclr(1);
            info9.subInfo.setHeight(mGState.getDefPRBSubSize());
            info9.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strRemain;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strRemainLin) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(5);
            m_strCurEditArea = m_strRemainLin;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strEngPoint) == 0) {
            PointInfo info10 = DefaultInfo.getDefSubInfo();
            info10.subInfo.setSubno(36);
            info10.setIclr(1);
            info10.subInfo.setHeight(mGState.getDefPRBSubSize());
            info10.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strEngPoint;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strHydPoint) == 0) {
            PointInfo info11 = DefaultInfo.getDefSubInfo();
            info11.subInfo.setSubno(37);
            info11.setIclr(1);
            info11.subInfo.setHeight(mGState.getDefPRBSubSize());
            info11.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strHydPoint;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGPSL) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(5);
            m_strCurEditArea = m_strGPSL;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSedi) == 0) {
            PointInfo info12 = DefaultInfo.getDefSubInfo();
            info12.subInfo.setSubno(31);
            info12.setIclr(1);
            info12.subInfo.setHeight(mGState.getDefPRBSubSize());
            info12.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strSandSedi;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRock) == 0) {
            PointInfo info13 = DefaultInfo.getDefSubInfo();
            info13.subInfo.setSubno(32);
            info13.setIclr(1);
            info13.subInfo.setHeight(mGState.getDefPRBSubSize());
            info13.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoRock;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoil) == 0) {
            PointInfo info14 = DefaultInfo.getDefSubInfo();
            info14.subInfo.setSubno(1492);
            info14.setIclr(1);
            info14.subInfo.setHeight(mGState.getDefPRBSubSize());
            info14.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoSoil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWater) == 0) {
            PointInfo info15 = DefaultInfo.getDefSubInfo();
            info15.subInfo.setSubno(1407);
            info15.setIclr(1);
            info15.subInfo.setHeight(mGState.getDefPRBSubSize());
            info15.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoWater;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25Rock) == 0) {
            PointInfo info16 = DefaultInfo.getDefSubInfo();
            info16.subInfo.setSubno(19);
            info16.setIclr(1);
            info16.subInfo.setHeight(mGState.getDefPRBSubSize());
            info16.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25Rock;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25Soil) == 0) {
            PointInfo info17 = DefaultInfo.getDefSubInfo();
            info17.subInfo.setSubno(20);
            info17.setIclr(1);
            info17.subInfo.setHeight(mGState.getDefPRBSubSize());
            info17.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25Soil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25Water) == 0) {
            PointInfo info18 = DefaultInfo.getDefSubInfo();
            info18.subInfo.setSubno(21);
            info18.setIclr(1);
            info18.subInfo.setHeight(mGState.getDefPRBSubSize());
            info18.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25Water;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25Stream) == 0) {
            PointInfo info19 = DefaultInfo.getDefSubInfo();
            info19.subInfo.setSubno(22);
            info19.setIclr(1);
            info19.subInfo.setHeight(mGState.getDefPRBSubSize());
            info19.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25Stream;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSediR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(7);
            m_strCurEditArea = m_strSandSediR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRockR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(8);
            m_strCurEditArea = m_strGeoRockR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoilR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(9);
            m_strCurEditArea = m_strGeoSoilR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWaterR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(10);
            m_strCurEditArea = m_strGeoWaterR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25RockR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(11);
            m_strCurEditArea = m_str25RockR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25SoilR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(12);
            m_strCurEditArea = m_str25SoilR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25WaterR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(13);
            m_strCurEditArea = m_str25WaterR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25StreamR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(14);
            m_strCurEditArea = m_str25StreamR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSedi) == 0) {
            PointInfo info20 = DefaultInfo.getDefSubInfo();
            info20.subInfo.setSubno(1407);
            info20.setIclr(1);
            info20.subInfo.setHeight(mGState.getDefPRBSubSize());
            info20.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strStremSedi;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSedi) == 0) {
            PointInfo info21 = DefaultInfo.getDefSubInfo();
            info21.subInfo.setSubno(1492);
            info21.setIclr(1);
            info21.subInfo.setHeight(mGState.getDefPRBSubSize());
            info21.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strSoilSedi;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoil) == 0) {
            PointInfo info22 = DefaultInfo.getDefSubInfo();
            info22.subInfo.setSubno(1492);
            info22.setIclr(1);
            info22.subInfo.setHeight(mGState.getDefPRBSubSize());
            info22.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strMulsoil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwater) == 0) {
            PointInfo info23 = DefaultInfo.getDefSubInfo();
            info23.subInfo.setSubno(1163);
            info23.setIclr(1);
            info23.subInfo.setHeight(mGState.getDefPRBSubSize());
            info23.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strMulwater;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstrem) == 0) {
            PointInfo info24 = DefaultInfo.getDefSubInfo();
            info24.subInfo.setSubno(1407);
            info24.setIclr(1);
            info24.subInfo.setHeight(mGState.getDefPRBSubSize());
            info24.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strMulstrem;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSediR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(15);
            m_strCurEditArea = m_strStremSediR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSediR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(16);
            m_strCurEditArea = m_strSoilSediR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoilR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(17);
            m_strCurEditArea = m_strMulsoilR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstremR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(18);
            m_strCurEditArea = m_strMulstremR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwaterR) == 0) {
            DefaultInfo.getDefLineInfo().setLclr(19);
            m_strCurEditArea = m_strMulwaterR;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoSoil) == 0) {
            PointInfo info25 = DefaultInfo.getDefSubInfo();
            info25.subInfo.setSubno(1407);
            info25.setIclr(1);
            info25.subInfo.setHeight(mGState.getDefPRBSubSize());
            info25.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str5GeoSoil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoWater) == 0) {
            PointInfo info26 = DefaultInfo.getDefSubInfo();
            info26.subInfo.setSubno(1407);
            info26.setIclr(1);
            info26.subInfo.setHeight(mGState.getDefPRBSubSize());
            info26.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str5GeoWater;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoRock) == 0) {
            PointInfo info27 = DefaultInfo.getDefSubInfo();
            info27.subInfo.setSubno(1407);
            info27.setIclr(1);
            info27.subInfo.setHeight(mGState.getDefPRBSubSize());
            info27.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str5GeoRock;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoSoil) == 0) {
            PointInfo info28 = DefaultInfo.getDefSubInfo();
            info28.subInfo.setSubno(1407);
            info28.setIclr(1);
            info28.subInfo.setHeight(mGState.getDefPRBSubSize());
            info28.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25GeoSoil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoWater) == 0) {
            PointInfo info29 = DefaultInfo.getDefSubInfo();
            info29.subInfo.setSubno(1407);
            info29.setIclr(1);
            info29.subInfo.setHeight(mGState.getDefPRBSubSize());
            info29.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25GeoWater;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoRock) == 0) {
            PointInfo info30 = DefaultInfo.getDefSubInfo();
            info30.subInfo.setSubno(1407);
            info30.setIclr(1);
            info30.subInfo.setHeight(mGState.getDefPRBSubSize());
            info30.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_str25GeoRock;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strLargeScale) == 0) {
            m_strCurEditArea = m_strLargeScale;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemSamNational) == 0) {
            PointInfo info31 = DefaultInfo.getDefSubInfo();
            info31.subInfo.setSubno(1407);
            info31.setIclr(1);
            info31.subInfo.setHeight(mGState.getDefPRBSubSize());
            info31.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoChemSamNational;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulsoil) == 0) {
            PointInfo info32 = DefaultInfo.getDefSubInfo();
            info32.subInfo.setSubno(1492);
            info32.setIclr(1);
            info32.subInfo.setHeight(mGState.getDefPRBSubSize());
            info32.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoChemMulsoil;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulSediment) == 0) {
            PointInfo info33 = DefaultInfo.getDefSubInfo();
            info33.subInfo.setSubno(1407);
            info33.setIclr(1);
            info33.subInfo.setHeight(mGState.getDefPRBSubSize());
            info33.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoChemMulSediment;
            return true;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulWater) == 0) {
            PointInfo info34 = DefaultInfo.getDefSubInfo();
            info34.subInfo.setSubno(32);
            info34.setIclr(1);
            info34.subInfo.setHeight(mGState.getDefPRBSubSize());
            info34.subInfo.setWidth(mGState.getDefPRBSubSize());
            m_strCurEditArea = m_strGeoChemMulWater;
            return true;
        }
        return false;
    }

    public static PointInfo GetAreaDefaultInfo(String strFileName) {
        GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
        PointInfo info = new PointInfo();
        if (strFileName.compareToIgnoreCase(m_strGPoint) == 0) {
            PointInfo info2 = DefaultInfo.getDefSubInfo();
            info2.subInfo.setSubno(1147);
            info2.subInfo.setHeight(mGState.getDefPRBSubSize());
            info2.subInfo.setWidth(mGState.getDefPRBSubSize());
            info2.setIclr(1);
            return info2;
        }
        if (strFileName.compareToIgnoreCase(m_strAttitude) == 0) {
            PointInfo info3 = DefaultInfo.getDefSubInfo();
            info3.subInfo.setSubno(1001);
            info3.setIclr(1);
            info3.subInfo.setHeight(mGState.getDefAttitudeSubSize());
            info3.subInfo.setWidth(mGState.getDefAttitudeSubSize());
            return info3;
        }
        if (strFileName.compareToIgnoreCase(m_strSketch) == 0) {
            PointInfo info4 = DefaultInfo.getDefSubInfo();
            info4.subInfo.setSubno(797);
            info4.setIclr(97);
            info4.subInfo.setHeight(mGState.getDefPRBSubSize());
            info4.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info4;
        }
        if (strFileName.compareToIgnoreCase(m_strPhoto) == 0) {
            PointInfo info5 = DefaultInfo.getDefSubInfo();
            info5.subInfo.setSubno(1160);
            info5.setIclr(97);
            info5.subInfo.setHeight(mGState.getDefPRBSubSize());
            info5.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info5;
        }
        if (strFileName.compareToIgnoreCase(m_strFossil) == 0) {
            PointInfo info6 = DefaultInfo.getDefSubInfo();
            info6.subInfo.setSubno(1201);
            info6.setIclr(148);
            info6.subInfo.setHeight(mGState.getDefPRBSubSize());
            info6.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info6;
        }
        if (strFileName.compareToIgnoreCase(m_strSample) == 0) {
            PointInfo info7 = DefaultInfo.getDefSubInfo();
            info7.subInfo.setSubno(1153);
            info7.setIclr(1);
            info7.subInfo.setHeight(mGState.getDefPRBSubSize());
            info7.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info7;
        }
        if (strFileName.compareToIgnoreCase(m_strOrecheck) == 0) {
            PointInfo info8 = DefaultInfo.getDefSubInfo();
            info8.subInfo.setSubno(1155);
            info8.setIclr(1);
            info8.subInfo.setHeight(mGState.getDefPRBSubSize());
            info8.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info8;
        }
        if (strFileName.compareToIgnoreCase(m_strFreeT) == 0) {
            PointInfo info9 = DefaultInfo.getDefSubInfo();
            info9.setBytes(RGMapApplication.getCurrentApp().getCurrentGlobal().getDefSubInfo().getBytes());
            PointInfo info10 = DefaultInfo.getDefNoteInfo();
            info10.setBytes(RGMapApplication.getCurrentApp().getCurrentGlobal().getDefNoteInfo().getBytes());
            return info10;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSection) == 0) {
            PointInfo info11 = DefaultInfo.getDefSubInfo();
            info11.subInfo.setSubno(31);
            info11.setIclr(1);
            info11.subInfo.setHeight(mGState.getDefPRBSubSize());
            info11.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info11;
        }
        if (strFileName.compareToIgnoreCase(m_strRemain) == 0) {
            PointInfo info12 = DefaultInfo.getDefSubInfo();
            info12.subInfo.setSubno(330);
            info12.setIclr(1);
            info12.subInfo.setHeight(mGState.getDefPRBSubSize());
            info12.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info12;
        }
        if (strFileName.compareToIgnoreCase(m_strEngPoint) == 0) {
            PointInfo info13 = DefaultInfo.getDefSubInfo();
            info13.subInfo.setSubno(36);
            info13.setIclr(1);
            info13.subInfo.setHeight(mGState.getDefPRBSubSize());
            info13.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info13;
        }
        if (strFileName.compareToIgnoreCase(m_strHydPoint) == 0) {
            PointInfo info14 = DefaultInfo.getDefSubInfo();
            info14.subInfo.setSubno(37);
            info14.setIclr(1);
            info14.subInfo.setHeight(mGState.getDefPRBSubSize());
            info14.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info14;
        }
        if (strFileName.compareToIgnoreCase(m_strSandSedi) == 0) {
            PointInfo info15 = DefaultInfo.getDefSubInfo();
            info15.subInfo.setSubno(31);
            info15.setIclr(1);
            info15.subInfo.setHeight(mGState.getDefPRBSubSize());
            info15.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info15;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoRock) == 0) {
            PointInfo info16 = DefaultInfo.getDefSubInfo();
            info16.subInfo.setSubno(32);
            info16.setIclr(1);
            info16.subInfo.setHeight(mGState.getDefPRBSubSize());
            info16.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info16;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoSoil) == 0) {
            PointInfo info17 = DefaultInfo.getDefSubInfo();
            info17.subInfo.setSubno(1492);
            info17.setIclr(1);
            info17.subInfo.setHeight(mGState.getDefPRBSubSize());
            info17.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info17;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoWater) == 0) {
            PointInfo info18 = DefaultInfo.getDefSubInfo();
            info18.subInfo.setSubno(1407);
            info18.setIclr(1);
            info18.subInfo.setHeight(mGState.getDefPRBSubSize());
            info18.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info18;
        }
        if (strFileName.compareToIgnoreCase(m_str25Rock) == 0) {
            PointInfo info19 = DefaultInfo.getDefSubInfo();
            info19.subInfo.setSubno(19);
            info19.setIclr(1);
            info19.subInfo.setHeight(mGState.getDefPRBSubSize());
            info19.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info19;
        }
        if (strFileName.compareToIgnoreCase(m_str25Soil) == 0) {
            PointInfo info20 = DefaultInfo.getDefSubInfo();
            info20.subInfo.setSubno(20);
            info20.setIclr(1);
            info20.subInfo.setHeight(mGState.getDefPRBSubSize());
            info20.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info20;
        }
        if (strFileName.compareToIgnoreCase(m_str25Water) == 0) {
            PointInfo info21 = DefaultInfo.getDefSubInfo();
            info21.subInfo.setSubno(21);
            info21.setIclr(1);
            info21.subInfo.setHeight(mGState.getDefPRBSubSize());
            info21.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info21;
        }
        if (strFileName.compareToIgnoreCase(m_str25Stream) == 0) {
            PointInfo info22 = DefaultInfo.getDefSubInfo();
            info22.subInfo.setSubno(22);
            info22.setIclr(1);
            info22.subInfo.setHeight(mGState.getDefPRBSubSize());
            info22.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info22;
        }
        if (strFileName.compareToIgnoreCase(m_strStremSedi) == 0) {
            PointInfo info23 = DefaultInfo.getDefSubInfo();
            info23.subInfo.setSubno(1407);
            info23.setIclr(1);
            info23.subInfo.setHeight(mGState.getDefPRBSubSize());
            info23.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info23;
        }
        if (strFileName.compareToIgnoreCase(m_strSoilSedi) == 0) {
            PointInfo info24 = DefaultInfo.getDefSubInfo();
            info24.subInfo.setSubno(1492);
            info24.setIclr(1);
            info24.subInfo.setHeight(mGState.getDefPRBSubSize());
            info24.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info24;
        }
        if (strFileName.compareToIgnoreCase(m_strMulsoil) == 0) {
            PointInfo info25 = DefaultInfo.getDefSubInfo();
            info25.subInfo.setSubno(1492);
            info25.setIclr(1);
            info25.subInfo.setHeight(mGState.getDefPRBSubSize());
            info25.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info25;
        }
        if (strFileName.compareToIgnoreCase(m_strMulwater) == 0) {
            PointInfo info26 = DefaultInfo.getDefSubInfo();
            info26.subInfo.setSubno(1163);
            info26.setIclr(1);
            info26.subInfo.setHeight(mGState.getDefPRBSubSize());
            info26.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info26;
        }
        if (strFileName.compareToIgnoreCase(m_strMulstrem) == 0) {
            PointInfo info27 = DefaultInfo.getDefSubInfo();
            info27.subInfo.setSubno(1407);
            info27.setIclr(1);
            info27.subInfo.setHeight(mGState.getDefPRBSubSize());
            info27.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info27;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoSoil) == 0) {
            PointInfo info28 = DefaultInfo.getDefSubInfo();
            info28.subInfo.setSubno(1407);
            info28.setIclr(1);
            info28.subInfo.setHeight(mGState.getDefPRBSubSize());
            info28.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info28;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoWater) == 0) {
            PointInfo info29 = DefaultInfo.getDefSubInfo();
            info29.subInfo.setSubno(1407);
            info29.setIclr(1);
            info29.subInfo.setHeight(mGState.getDefPRBSubSize());
            info29.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info29;
        }
        if (strFileName.compareToIgnoreCase(m_str5GeoRock) == 0) {
            PointInfo info30 = DefaultInfo.getDefSubInfo();
            info30.subInfo.setSubno(1407);
            info30.setIclr(1);
            info30.subInfo.setHeight(mGState.getDefPRBSubSize());
            info30.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info30;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoSoil) == 0) {
            PointInfo info31 = DefaultInfo.getDefSubInfo();
            info31.subInfo.setSubno(1492);
            info31.setIclr(1);
            info31.subInfo.setHeight(mGState.getDefPRBSubSize());
            info31.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info31;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoWater) == 0) {
            PointInfo info32 = DefaultInfo.getDefSubInfo();
            info32.subInfo.setSubno(1407);
            info32.setIclr(1);
            info32.subInfo.setHeight(mGState.getDefPRBSubSize());
            info32.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info32;
        }
        if (strFileName.compareToIgnoreCase(m_str25GeoRock) == 0) {
            PointInfo info33 = DefaultInfo.getDefSubInfo();
            info33.subInfo.setSubno(32);
            info33.setIclr(1);
            info33.subInfo.setHeight(mGState.getDefPRBSubSize());
            info33.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info33;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemSamNational) == 0) {
            PointInfo info34 = DefaultInfo.getDefSubInfo();
            info34.subInfo.setSubno(1407);
            info34.setIclr(1);
            info34.subInfo.setHeight(mGState.getDefPRBSubSize());
            info34.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info34;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulsoil) == 0) {
            PointInfo info35 = DefaultInfo.getDefSubInfo();
            info35.subInfo.setSubno(1407);
            info35.setIclr(1);
            info35.subInfo.setHeight(mGState.getDefPRBSubSize());
            info35.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info35;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulSediment) == 0) {
            PointInfo info36 = DefaultInfo.getDefSubInfo();
            info36.subInfo.setSubno(1407);
            info36.setIclr(1);
            info36.subInfo.setHeight(mGState.getDefPRBSubSize());
            info36.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info36;
        }
        if (strFileName.compareToIgnoreCase(m_strGeoChemMulWater) == 0) {
            PointInfo info37 = DefaultInfo.getDefSubInfo();
            info37.subInfo.setSubno(1407);
            info37.setIclr(1);
            info37.subInfo.setHeight(mGState.getDefPRBSubSize());
            info37.subInfo.setWidth(mGState.getDefPRBSubSize());
            return info37;
        }
        return info;
    }
}
