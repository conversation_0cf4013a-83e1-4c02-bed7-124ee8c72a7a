package com.AoRGMap;

import android.app.Activity;
import android.os.Bundle;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ViewFlipper;

/* loaded from: classes.dex */
public class MenuDemo extends Activity implements GestureDetector.OnGestureListener, View.OnTouchListener {
    private static final int FLING_MIN_DISTANCE = 100;
    private static final int FLING_MIN_VELOCITY = 200;
    private int mCurrentLayoutState;
    private ViewFlipper mFlipper;
    GestureDetector mGestureDetector;

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.flipper);
        this.mFlipper = (ViewFlipper) findViewById(R.id.flipper);
        this.mGestureDetector = new GestureDetector(this);
        this.mFlipper.setOnTouchListener(this);
        this.mCurrentLayoutState = 0;
        this.mFlipper.setLongClickable(true);
    }

    public void switchLayoutStateTo(int switchTo) {
        while (this.mCurrentLayoutState != switchTo) {
            if (this.mCurrentLayoutState > switchTo) {
                this.mCurrentLayoutState--;
                this.mFlipper.setInAnimation(inFromLeftAnimation());
                this.mFlipper.setOutAnimation(outToRightAnimation());
                this.mFlipper.showPrevious();
            } else {
                this.mCurrentLayoutState++;
                this.mFlipper.setInAnimation(inFromRightAnimation());
                this.mFlipper.setOutAnimation(outToLeftAnimation());
                this.mFlipper.showNext();
            }
        }
    }

    protected Animation inFromRightAnimation() {
        Animation inFromRight = new TranslateAnimation(2, 1.0f, 2, 0.0f, 2, 0.0f, 2, 0.0f);
        inFromRight.setDuration(500L);
        inFromRight.setInterpolator(new AccelerateInterpolator());
        return inFromRight;
    }

    protected Animation outToLeftAnimation() {
        Animation outtoLeft = new TranslateAnimation(2, 0.0f, 2, -1.0f, 2, 0.0f, 2, 0.0f);
        outtoLeft.setDuration(500L);
        outtoLeft.setInterpolator(new AccelerateInterpolator());
        return outtoLeft;
    }

    protected Animation inFromLeftAnimation() {
        Animation inFromLeft = new TranslateAnimation(2, -1.0f, 2, 0.0f, 2, 0.0f, 2, 0.0f);
        inFromLeft.setDuration(500L);
        inFromLeft.setInterpolator(new AccelerateInterpolator());
        return inFromLeft;
    }

    protected Animation outToRightAnimation() {
        Animation outtoRight = new TranslateAnimation(2, 0.0f, 2, 1.0f, 2, 0.0f, 2, 0.0f);
        outtoRight.setDuration(500L);
        outtoRight.setInterpolator(new AccelerateInterpolator());
        return outtoRight;
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public boolean onDown(MotionEvent e) {
        return false;
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        if (e1.getX() - e2.getX() > 100.0f && Math.abs(velocityX) > 200.0f) {
            this.mFlipper.setInAnimation(inFromRightAnimation());
            this.mFlipper.setOutAnimation(outToLeftAnimation());
            this.mFlipper.showNext();
            return false;
        }
        if (e2.getX() - e1.getX() > 100.0f && Math.abs(velocityX) > 200.0f) {
            this.mFlipper.setInAnimation(inFromLeftAnimation());
            this.mFlipper.setOutAnimation(outToRightAnimation());
            this.mFlipper.showPrevious();
            return false;
        }
        return false;
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public void onLongPress(MotionEvent e) {
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        return false;
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public void onShowPress(MotionEvent e) {
    }

    @Override // android.view.GestureDetector.OnGestureListener
    public boolean onSingleTapUp(MotionEvent e) {
        return false;
    }

    @Override // android.view.View.OnTouchListener
    public boolean onTouch(View v, MotionEvent event) {
        return this.mGestureDetector.onTouchEvent(event);
    }
}
