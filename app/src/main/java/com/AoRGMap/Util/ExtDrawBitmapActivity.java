package com.AoRGMap.Util;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.support.v4.internal.view.SupportMenu;
import android.support.v4.view.InputDeviceCompat;
import android.support.v4.view.ViewCompat;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.Util.DrawView;
import java.io.File;
import java.io.FileNotFoundException;

/* loaded from: classes.dex */
public class ExtDrawBitmapActivity extends Activity {
    public static final String SKETCH_BITMAP_PATH = "BITMAP_PATH";
    DrawView mDrawView = null;
    private RadioGroup mMenuGroup = null;
    private String mBitmapPath = null;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private Button mSetPaint = null;
    private Button mSetEraser = null;
    private Button mSetPaintWidth = null;
    private Button mSetPaintColor = null;
    private Button mSetbackground = null;
    private Button mSaveBitmap = null;
    private final int REQUEST_BITMAP = 1;
    private final int REQUEST_PBITMAP = 2;
    int mSel = 3;
    private int mGetBitMapType = 0;
    private Bitmap mBitmapbk = null;

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.drawbitmap);
        Bundle bundle = getIntent().getExtras();
        this.mBitmapPath = bundle.getString(SKETCH_BITMAP_PATH);
        InitView();
        InitLisener();
    }

    private void InitLisener() {
        this.mSetPaint.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                DrawView.OPERATIONTYPE msel = ExtDrawBitmapActivity.this.mDrawView.getOperationType();
                if (msel == DrawView.OPERATIONTYPE.ZOOM) {
                    ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ADD);
                    ExtDrawBitmapActivity.this.mMenuGroup.check(R.id.menu_sketch_setpen);
                } else {
                    ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ZOOM);
                    ExtDrawBitmapActivity.this.mMenuGroup.clearCheck();
                }
            }
        });
        this.mSetEraser.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ADD);
                ExtDrawBitmapActivity.this.mDrawView.setEraser(0);
            }
        });
        this.mSetPaintWidth.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ZOOM);
                ExtDrawBitmapActivity.this.mMenuGroup.clearCheck();
                String[] itemsStrings = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
                AlertDialog.Builder builder = new AlertDialog.Builder(ExtDrawBitmapActivity.this);
                builder.setTitle("");
                builder.setSingleChoiceItems(itemsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.3.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        ExtDrawBitmapActivity.this.mSel = which;
                    }
                });
                builder.setPositiveButton(ExtDrawBitmapActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.3.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        ExtDrawBitmapActivity.this.mDrawView.setStrokeWidth(ExtDrawBitmapActivity.this.mSel);
                    }
                });
                builder.setNegativeButton(ExtDrawBitmapActivity.this.getString(R.string.btnCancel), (DialogInterface.OnClickListener) null);
                builder.show();
            }
        });
        this.mSetPaintColor.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ZOOM);
                ExtDrawBitmapActivity.this.mMenuGroup.clearCheck();
                int Lang = ExtDrawBitmapActivity.this.mGState.getAoGISLanguage();
                String[] itemsStrings = new String[8];
                if (Lang == 0) {
                    itemsStrings[0] = "黑色";
                    itemsStrings[1] = "灰色";
                    itemsStrings[2] = "红色";
                    itemsStrings[3] = "蓝色";
                    itemsStrings[4] = "绿色";
                    itemsStrings[5] = "白色";
                    itemsStrings[6] = "黄色";
                    itemsStrings[7] = "青色";
                } else {
                    itemsStrings[0] = "Black";
                    itemsStrings[1] = "Grey";
                    itemsStrings[2] = "Red";
                    itemsStrings[3] = "Blue";
                    itemsStrings[4] = "Green";
                    itemsStrings[5] = "White";
                    itemsStrings[6] = "Yellow";
                    itemsStrings[7] = "Magenta";
                }
                AlertDialog.Builder builder = new AlertDialog.Builder(ExtDrawBitmapActivity.this);
                builder.setTitle("");
                builder.setSingleChoiceItems(itemsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.4.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        ExtDrawBitmapActivity.this.mSel = which;
                    }
                });
                builder.setPositiveButton(ExtDrawBitmapActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.4.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        int color = ViewCompat.MEASURED_STATE_MASK;
                        switch (ExtDrawBitmapActivity.this.mSel) {
                            case 0:
                                color = ViewCompat.MEASURED_STATE_MASK;
                                break;
                            case 1:
                                color = -7829368;
                                break;
                            case 2:
                                color = SupportMenu.CATEGORY_MASK;
                                break;
                            case 3:
                                color = -16776961;
                                break;
                            case 4:
                                color = -16711936;
                                break;
                            case 5:
                                color = -1;
                                break;
                            case 6:
                                color = InputDeviceCompat.SOURCE_ANY;
                                break;
                            case 7:
                                color = -65281;
                                break;
                        }
                        ExtDrawBitmapActivity.this.mDrawView.setColor(color);
                    }
                });
                builder.setNegativeButton(ExtDrawBitmapActivity.this.getString(R.string.btnCancel), (DialogInterface.OnClickListener) null);
                builder.show();
            }
        });
        this.mSetbackground.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                ExtDrawBitmapActivity.this.mMenuGroup.clearCheck();
                ExtDrawBitmapActivity.this.mDrawView.setOperationType(DrawView.OPERATIONTYPE.ZOOM);
                AlertDialog.Builder builder = new AlertDialog.Builder(ExtDrawBitmapActivity.this);
                builder.setIcon(R.drawable.info1);
                builder.setTitle("选择类型");
                builder.setSingleChoiceItems(new String[]{"拍摄照片", "文件选择"}, ExtDrawBitmapActivity.this.mGetBitMapType, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.5.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i) {
                        ExtDrawBitmapActivity.this.mGetBitMapType = i;
                    }
                });
                builder.setPositiveButton("确定", new DialogInterface.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.5.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialogInterface, int i) {
                        if (ExtDrawBitmapActivity.this.mGetBitMapType == 0) {
                            File vFile = new File(ExtDrawBitmapActivity.this.mBitmapPath);
                            Uri uri = Uri.fromFile(vFile);
                            Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
                            intent.putExtra("output", uri);
                            intent.putExtra("isNew", true);
                            ExtDrawBitmapActivity.this.startActivityForResult(intent, 2);
                            return;
                        }
                        ExtDrawBitmapActivity.this.startActivityForResult(new Intent("android.intent.action.PICK", MediaStore.Images.Media.EXTERNAL_CONTENT_URI), 1);
                    }
                });
                builder.setNegativeButton("取消", (DialogInterface.OnClickListener) null);
                builder.show();
            }
        });
        this.mSaveBitmap.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.Util.ExtDrawBitmapActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                ExtDrawBitmapActivity.this.mDrawView.saveImage(ExtDrawBitmapActivity.this.mBitmapPath);
                ExtDrawBitmapActivity.this.finish();
            }
        });
    }

    private void InitView() {
        this.mDrawView = (DrawView) findViewById(R.id.main_view);
        this.mMenuGroup = (RadioGroup) findViewById(R.id.main_sketch_menu);
        this.mSetbackground = (Button) findViewById(R.id.menu_sketch_setbk);
        this.mSetPaintColor = (Button) findViewById(R.id.menu_sketch_setcolor);
        this.mSetPaintWidth = (Button) findViewById(R.id.menu_sketch_setwidth);
        this.mSetPaint = (Button) findViewById(R.id.menu_sketch_setpen);
        this.mSetEraser = (Button) findViewById(R.id.menu_sketch_Eraser);
        this.mSaveBitmap = (Button) findViewById(R.id.menu_sketch_save);
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            Uri imageFileUri = null;
            switch (requestCode) {
                case 1:
                    imageFileUri = data.getData();
                    break;
                case 2:
                    imageFileUri = Uri.fromFile(new File(this.mBitmapPath));
                    break;
            }
            if (imageFileUri != null) {
                float dw = this.mDrawView.getWidth();
                float dh = this.mDrawView.getHeight();
                try {
                    BitmapFactory.Options options = new BitmapFactory.Options();
                    options.inJustDecodeBounds = true;
                    BitmapFactory.decodeStream(getContentResolver().openInputStream(imageFileUri), null, options);
                    float f = options.outWidth;
                    float f2 = options.outHeight;
                    int heightRatio = (int) Math.ceil(options.outHeight / dh);
                    int widthRatio = (int) Math.ceil(options.outWidth / dw);
                    if (heightRatio > 1 && widthRatio > 1) {
                        if (heightRatio > widthRatio) {
                            options.inSampleSize = heightRatio;
                        } else {
                            options.inSampleSize = widthRatio;
                        }
                    }
                    options.inJustDecodeBounds = false;
                    this.mBitmapbk = BitmapFactory.decodeStream(getContentResolver().openInputStream(imageFileUri), null, options);
                    this.mBitmapbk.getByteCount();
                    this.mDrawView.setBitmap(this.mBitmapbk);
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
