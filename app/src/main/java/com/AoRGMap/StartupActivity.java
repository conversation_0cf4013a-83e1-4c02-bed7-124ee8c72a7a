package com.AoRGMap;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.ContextThemeWrapper;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.util.DisplayHelper;
import java.util.Date;

/* loaded from: classes.dex */
public class StartupActivity extends Activity {
    protected View m_btnGPSRectify = null;
    protected TextView m_textCurTime = null;
    protected Button m_btnOK = null;
    protected Button m_btnCancel = null;
    protected TextView m_labelDx = null;
    protected TextView m_labelDy = null;
    protected EditText m_editLine = null;
    protected EditText m_editPoint = null;
    protected boolean m_bResult = false;
    GlobalState m_state = null;

    @Override // android.app.Activity
    protected Dialog onCreateDialog(int id, Bundle args) {
        return super.onCreateDialog(id, args);
    }

    @Override // android.app.Activity
    protected Dialog onCreateDialog(int id) {
        return super.onCreateDialog(id);
    }

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.startup);
        this.m_labelDx = (TextView) findViewById(R.id.labelDx);
        this.m_labelDy = (TextView) findViewById(R.id.labelDy);
        this.m_btnGPSRectify = findViewById(R.id.btnGPSRectify_TableRow);
        this.m_btnGPSRectify.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.StartupActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                StartupActivity.this.showGPSRectifyDialog(v.getContext());
            }
        });
        this.m_textCurTime = (TextView) findViewById(R.id.curTime);
        this.m_textCurTime.post(new Runnable() { // from class: com.AoRGMap.StartupActivity.2
            @Override // java.lang.Runnable
            public void run() {
                StartupActivity.this.m_textCurTime.setText(new Date().toLocaleString());
                StartupActivity.this.m_textCurTime.postDelayed(this, 1000L);
            }
        });
        this.m_editLine = (EditText) findViewById(R.id.editTextLine);
        this.m_editPoint = (EditText) findViewById(R.id.editTextDot);
        this.m_btnOK = (Button) findViewById(R.id.startup_btnOK);
        this.m_btnOK.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.StartupActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String routeCode = StartupActivity.this.m_editLine.getText().toString();
                String pointCode = StartupActivity.this.m_editPoint.getText().toString();
                if (pointCode.length() < 1 || !Character.isLetter(pointCode.charAt(0)) || !Character.isDigit(pointCode.charAt(pointCode.length() - 1))) {
                    Toast toast = DisplayHelper.getCommonToast();
                    toast.setText(R.string.RGMAP_PROMPT_GPOINTSTYLE);
                    toast.show();
                    return;
                }
                boolean fail = false;
                int sp2 = 0;
                int i = 0;
                int digital = 0;
                while (true) {
                    if (i >= pointCode.length()) {
                        break;
                    }
                    char x = pointCode.charAt(i);
                    if (digital == 0) {
                        if (!Character.isLetter(x) && !Character.isDigit(x)) {
                            fail = true;
                            break;
                        }
                        if (Character.isDigit(x)) {
                            digital = 1;
                            sp2 = i;
                        }
                        i++;
                    } else if (Character.isDigit(x)) {
                        i++;
                    } else {
                        fail = true;
                        break;
                    }
                }
                if (fail) {
                    Toast toast2 = DisplayHelper.getCommonToast();
                    toast2.setText(R.string.RGMAP_PROMPT_GPOINTSTYLE);
                    toast2.show();
                    return;
                }
                StartupActivity.this.m_bResult = true;
                String strPointPrefix = pointCode.substring(0, sp2);
                int strPointNumber = Integer.parseInt(pointCode.substring(sp2));
                int isize = pointCode.length() - sp2;
                StartupActivity.this.m_state.setStartPointNumber(strPointNumber - 1);
                StartupActivity.this.m_state.setRoutePrefix(routeCode);
                StartupActivity.this.m_state.setPointPrefix(strPointPrefix);
                GlobalState globalState = StartupActivity.this.m_state;
                GlobalState.setDigitSize(isize);
                ((RGMapApplication) StartupActivity.this.getApplication()).setCurrentData(StartupActivity.this.m_state);
                StartupActivity.this.finish();
            }
        });
        this.m_btnCancel = (Button) findViewById(R.id.startup_btnCancel);
        this.m_btnCancel.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.StartupActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                StartupActivity.this.m_state.TryUpdateRouteCode("");
                StartupActivity.this.m_state.setStartPointNumber(1);
                GlobalState globalState = StartupActivity.this.m_state;
                GlobalState.setDigitSize(4);
                RGMapPreferences profile = new RGMapPreferences(StartupActivity.this);
                if (profile.openForRead()) {
                    String strMapName = profile.getString(RGMapPreferences.mMapName, "");
                    if (strMapName.length() > 0 && strMapName.toLowerCase().endsWith(".gpj")) {
                        String routeCode = strMapName;
                        int sindex = routeCode.lastIndexOf(47);
                        if (sindex >= 0) {
                            routeCode = routeCode.substring(sindex + 1);
                        }
                        if (routeCode.toLowerCase().endsWith(".gpj")) {
                            routeCode = routeCode.substring(0, routeCode.length() - 4);
                        }
                        StartupActivity.this.m_state.TryUpdateRouteCode(routeCode);
                    }
                }
                StartupActivity.this.m_state.UpdatePointCodeFromMap();
                ((RGMapApplication) StartupActivity.this.getApplication()).setCurrentData(StartupActivity.this.m_state);
                StartupActivity.this.m_bResult = false;
                StartupActivity.this.finish();
            }
        });
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState state = app.getCurrentGlobal();
        if (state != null) {
            this.m_state = state.m10clone();
        } else {
            this.m_state = new GlobalState();
        }
        this.m_state.readInitialValue();
        refreshGPSRectify();
        refreshEditors();
        int iLang = this.m_state.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void refreshGPSRectify() {
        this.m_labelDx.setText(Float.toString(this.m_state.getGpsRectifyX()));
        this.m_labelDy.setText(Float.toString(this.m_state.getGpsRectifyY()));
    }

    private void refreshEditors() {
        this.m_state.UpdatePointCodeFromMap();
        this.m_editLine.setText(this.m_state.GetCurrentRouteCode());
        StringBuilder append = new StringBuilder().append("%s%0");
        GlobalState globalState = this.m_state;
        String szFormat = append.append(GlobalState.getDigitSize()).append("d").toString();
        this.m_editPoint.setText(String.format(szFormat, this.m_state.getPointPrefix(), Integer.valueOf(this.m_state.getStartPointNumber())));
        if (this.m_state.GetCurrentPointCode().length() <= 0) {
            RGMapPreferences profile = new RGMapPreferences(this);
            if (profile.openForRead()) {
                String strMapName = profile.getString(RGMapPreferences.mMapName, "");
                if (strMapName.length() > 0 && strMapName.toLowerCase().endsWith(".gpj")) {
                    String routeCode = strMapName;
                    int sindex = routeCode.lastIndexOf(47);
                    if (sindex >= 0) {
                        routeCode = routeCode.substring(sindex + 1);
                    }
                    if (routeCode.toLowerCase().endsWith(".gpj")) {
                        routeCode = routeCode.substring(0, routeCode.length() - 4);
                    }
                    this.m_editLine.setText(routeCode);
                }
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void showGPSRectifyDialog(Context context) {
        int iLang = this.m_state.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        View v = View.inflate(new ContextThemeWrapper(context, R.style.popup_theme), R.layout.dlg_gps_rectify, null);
        Dialog dlg = new AlertDialog.Builder(new ContextThemeWrapper(context, R.style.popup_theme)).setTitle(R.string.startup_gps_button).setView(v).setPositiveButton(R.string.btnOK, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.StartupActivity.6
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int whichButton) {
                Dialog dlg2 = (Dialog) dialog;
                try {
                    EditText editor = (EditText) dlg2.findViewById(R.id.dlg_gps_rectify_dx);
                    StartupActivity.this.m_state.setGpsRectifyX(Float.parseFloat(editor.getText().toString()));
                    EditText editor2 = (EditText) dlg2.findViewById(R.id.dlg_gps_rectify_dy);
                    StartupActivity.this.m_state.setGpsRectifyY(Float.parseFloat(editor2.getText().toString()));
                    EditText editor3 = (EditText) dlg2.findViewById(R.id.dlg_gps_rectify_dz);
                    StartupActivity.this.m_state.setGpsRectifyZ(Float.parseFloat(editor3.getText().toString()));
                } catch (Throwable th) {
                }
                StartupActivity.this.refreshGPSRectify();
            }
        }).setNegativeButton(R.string.btnCancel, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.StartupActivity.5
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int whichButton) {
            }
        }).create();
        EditText editor = (EditText) v.findViewById(R.id.dlg_gps_rectify_dx);
        editor.setText(Float.toString(this.m_state.getGpsRectifyX()));
        EditText editor2 = (EditText) v.findViewById(R.id.dlg_gps_rectify_dy);
        editor2.setText(Float.toString(this.m_state.getGpsRectifyY()));
        EditText editor3 = (EditText) v.findViewById(R.id.dlg_gps_rectify_dz);
        editor3.setText(Float.toString(this.m_state.getGpsRectifyZ()));
        dlg.show();
    }
}
