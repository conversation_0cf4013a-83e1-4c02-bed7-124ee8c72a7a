package com.AoRGMap.dataquery;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListView;
import com.AoRGMap.R;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* loaded from: classes.dex */
public abstract class TreeListViewAdapter extends BaseAdapter {
    protected List<TreeNode> mAllNodes;
    protected Context mContext;
    protected LayoutInflater mInflater;
    protected List<TreeNode> mNodes;
    private int mSelPos = -1;
    private OnTreeNodeClickListener onTreeNodeClickListener;

    public interface OnTreeNodeClickListener {
        void onClick(TreeNode treeNode, int i);
    }

    public abstract View getConvertView(TreeNode treeNode, int i, View view, ViewGroup viewGroup);

    public void setSelPos(int position) {
        this.mSelPos = position;
    }

    public void setOnTreeNodeClickListener(OnTreeNodeClickListener onTreeNodeClickListener) {
        this.onTreeNodeClickListener = onTreeNodeClickListener;
    }

    public TreeListViewAdapter(ListView mTree, Context context, List<TreeNode> datas, int defaultExpandLevel) throws IllegalArgumentException, IllegalAccessException {
        this.mContext = context;
        this.mAllNodes = getSortedNodes(datas, defaultExpandLevel);
        this.mNodes = filterVisibleNode(this.mAllNodes);
        this.mInflater = LayoutInflater.from(context);
        mTree.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.dataquery.TreeListViewAdapter.1
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                TreeListViewAdapter.this.expandOrCollapse(position);
                if (TreeListViewAdapter.this.onTreeNodeClickListener != null) {
                    TreeListViewAdapter.this.onTreeNodeClickListener.onClick(TreeListViewAdapter.this.mNodes.get(position), position);
                }
            }
        });
    }

    public void expandOrCollapse(int position) {
        TreeNode n = this.mNodes.get(position);
        if (n != null && !n.isLeaf()) {
            n.setExpand(!n.isExpand());
            this.mNodes = filterVisibleNode(this.mAllNodes);
            notifyDataSetChanged();
        }
    }

    @Override // android.widget.Adapter
    public int getCount() {
        return this.mNodes.size();
    }

    @Override // android.widget.Adapter
    public Object getItem(int position) {
        return this.mNodes.get(position);
    }

    @Override // android.widget.Adapter
    public long getItemId(int position) {
        return position;
    }

    @Override // android.widget.Adapter
    public View getView(int position, View convertView, ViewGroup parent) {
        TreeNode node = this.mNodes.get(position);
        View convertView2 = getConvertView(node, position, convertView, parent);
        convertView2.setPadding(node.getLevel() * 30, 3, 3, 3);
        return convertView2;
    }

    public static List<TreeNode> getSortedNodes(List<TreeNode> datas, int defaultExpandLevel) throws IllegalArgumentException, IllegalAccessException {
        List<TreeNode> result = new ArrayList<>();
        List<TreeNode> nodes = convetData2Node(datas);
        List<TreeNode> rootNodes = getRootNodes(nodes);
        for (TreeNode node : rootNodes) {
            addNode(result, node, defaultExpandLevel, 1);
        }
        return result;
    }

    public static List<TreeNode> filterVisibleNode(List<TreeNode> nodes) {
        List<TreeNode> result = new ArrayList<>();
        for (TreeNode node : nodes) {
            if (node.isRoot() || node.isParentExpand()) {
                setNodeIcon(node);
                result.add(node);
            }
        }
        return result;
    }

    private static List<TreeNode> convetData2Node(List<TreeNode> datas) throws IllegalArgumentException, IllegalAccessException {
        for (int i = 0; i < datas.size(); i++) {
            TreeNode n = datas.get(i);
            for (int j = i + 1; j < datas.size(); j++) {
                TreeNode m = datas.get(j);
                if (m.getpId() == n.getId()) {
                    n.getChildren().add(m);
                    m.setParent(n);
                } else if (m.getId() == n.getpId()) {
                    m.getChildren().add(n);
                    n.setParent(m);
                }
            }
        }
        Iterator<TreeNode> it = datas.iterator();
        while (it.hasNext()) {
            setNodeIcon(it.next());
        }
        return datas;
    }

    private static List<TreeNode> getRootNodes(List<TreeNode> nodes) {
        List<TreeNode> root = new ArrayList<>();
        for (TreeNode node : nodes) {
            if (node.isRoot()) {
                root.add(node);
            }
        }
        return root;
    }

    private static void addNode(List<TreeNode> nodes, TreeNode node, int defaultExpandLeval, int currentLevel) {
        nodes.add(node);
        if (defaultExpandLeval >= currentLevel) {
            node.setExpand(true);
        }
        if (!node.isLeaf()) {
            for (int i = 0; i < node.getChildren().size(); i++) {
                addNode(nodes, node.getChildren().get(i), defaultExpandLeval, currentLevel + 1);
            }
        }
    }

    private static void setNodeIcon(TreeNode node) {
        if (node.getChildren().size() > 0 && node.isExpand()) {
            node.setIcon(R.drawable.tree_ex);
        } else if (node.getChildren().size() > 0 && !node.isExpand()) {
            node.setIcon(R.drawable.tree_ec);
        } else {
            node.setIcon(-1);
        }
    }
}
