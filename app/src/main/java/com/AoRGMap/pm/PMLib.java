package com.AoRGMap.pm;

import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;

/* loaded from: classes.dex */
public class PMLib {
    public static final int FOSSIL = 6;
    public static final int GPOINT = 7;
    public static final int MORE1 = 9;
    public static final int MORE2 = 10;
    public static final int PHOTO = 2;
    public static final int SAMPLE = 5;
    public static final int SECATT = 3;
    public static final int SECTION = 8;
    public static final int SKETCH = 4;
    public static final int SLAYER = 1;
    public static final int SURVEY = 0;
    public static int m_iCurTableID = -1;
    public static final String m_strGpoint = "Gpoint";
    public static final String m_strSample = "Sample";
    public static final String m_strSecatt = "Secatt";
    public static final String m_strSection = "Section";
    public static final String m_strSfossil = "Sfossil";
    public static final String m_strSketch = "Sketch";
    public static final String m_strSlayer = "SLayer";
    public static final String m_strSlayerNote = "SLayerNote";
    public static final String m_strSphoto = "Sphoto";
    public static final String m_strSurvey = "Survey";

    public static void resetCurrentTableID() {
        m_iCurTableID = -1;
    }

    public static void setCurrentTableID(int iIdx) {
        m_iCurTableID = iIdx;
    }

    public static int getCurrentTableID() {
        return m_iCurTableID;
    }

    public static boolean isCurrentTable(int iIdx) {
        return iIdx == m_iCurTableID;
    }

    public static String getCurrentTableName() {
        return getTableName(m_iCurTableID);
    }

    public static String getTableName(int iIdx) {
        switch (iIdx) {
            case 0:
                return m_strSurvey;
            case 1:
                return m_strSlayer;
            case 2:
                return m_strSphoto;
            case 3:
                return m_strSecatt;
            case 4:
                return m_strSketch;
            case 5:
                return m_strSample;
            case 6:
                return m_strSfossil;
            case 7:
                return m_strGpoint;
            default:
                return null;
        }
    }

    public static String getCurTableChineseName() {
        RGMapApplication app = RGMapApplication.getCurrentApp();
        switch (m_iCurTableID) {
            case 0:
                String strName = app.getResources().getString(R.string.RGMAP_PROMPT_LINE);
                return strName;
            case 1:
                String strName2 = app.getResources().getString(R.string.RGMAP_PROMPT_LAYER);
                return strName2;
            case 2:
                String strName3 = app.getResources().getString(R.string.menu_main_prb_photo);
                return strName3;
            case 3:
                String strName4 = app.getResources().getString(R.string.menu_main_prb_att);
                return strName4;
            case 4:
                String strName5 = app.getResources().getString(R.string.menu_main_prb_sketch);
                return strName5;
            case 5:
                String strName6 = app.getResources().getString(R.string.menu_main_prb_sample);
                return strName6;
            case 6:
                String strName7 = app.getResources().getString(R.string.menu_main_prb_fossil);
                return strName7;
            case 7:
                String strName8 = app.getResources().getString(R.string.menu_main_prb_p);
                return strName8;
            default:
                return null;
        }
    }

    public static Class<?> getCurAttActivityClass() {
        switch (m_iCurTableID) {
            case 0:
                return AttSurveyActivity.class;
            case 1:
                return AttSlayerActivity.class;
            case 2:
                return AttSphotoActivity.class;
            case 3:
                return AttSecattActivity.class;
            case 4:
                return AttSsketchActivity.class;
            case 5:
                return AttSsampleActivity.class;
            case 6:
                return AttSfossilActivity.class;
            case 7:
                return AttSgpointActivity.class;
            default:
                return null;
        }
    }
}
