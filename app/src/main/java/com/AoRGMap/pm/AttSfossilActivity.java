package com.AoRGMap.pm;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.dao.CommonDAO;
import com.AoRGMap.dialog.GPSDBDialog;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes.dex */
public class AttSfossilActivity extends AttributeDBActivity {
    private AttributeItem item_Dis;
    private AttributeItem item_LineCode;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private CommonDAO mSqlDB = null;

    public AttSfossilActivity() {
        setTitle(PMLib.getCurTableChineseName());
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void onInitializeViews(AttributeDBActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.mSqlDB = (CommonDAO) getSqlDB();
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        updateAttributeMap(this.itemList);
        GdbAttributesMap<String, Object> data = getAttributeMap();
        int iSortID = getSortID();
        Bundle bundle = getIntent().getExtras();
        int iOpType = bundle.getInt(AttCommParams.PARAM_INT_SQLDB_OP_TYPE);
        switch (iOpType) {
            case 1:
                this.mSqlDB.add(PMLib.m_strSfossil, data, getfieldsMap());
                break;
            case 2:
                this.mSqlDB.delete(PMLib.m_strSfossil, iSortID);
                break;
            case 3:
                this.mSqlDB.update(PMLib.m_strSfossil, iSortID, data, getfieldsMap());
                break;
            case 4:
                this.mSqlDB.resort(PMLib.m_strSfossil, iSortID);
                this.mSqlDB.insert(PMLib.m_strSfossil, iSortID, data, getfieldsMap());
                break;
        }
        setResult(-1);
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.fos_fossil);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.getCurSecCode();
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_SECCODE, R.string.fos_seccode, EditTextEditorInfo.textEditor(), map, defData);
        item.getEditorEditText().setEnabled(false);
        this.itemList.add(item);
        String defData2 = gstate.getCurLineCode();
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_SECPOINT, R.string.fos_secpoint, EditTextEditorInfo.textEditor(), map, defData2);
        item2.getEditorEditText().setEnabled(false);
        this.itemList.add(item2);
        this.item_LineCode = item2;
        String defData3 = gstate.getCurLayerCode();
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_LAYCODE, R.string.fos_laycode, EditTextEditorInfo.unsignedNumberEditor(), map, defData3);
        item3.getEditorEditText().setEnabled(false);
        this.itemList.add(item3);
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_SLOPE_L, R.string.fos_slope_l, EditTextEditorInfo.decimalEditor(), map, (String) null);
        this.itemList.add(item4);
        this.item_Dis = item4;
        item4.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.pm.AttSfossilActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                GlobalState GState = RGMapApplication.getCurrentApp().getCurrentGlobal();
                GPSDBDialog gpsDialog = new GPSDBDialog(AttSfossilActivity.this, GState.getWorkAreaParams(), GState.getGpsRectifyX(), GState.getGpsRectifyY());
                String str = AttSfossilActivity.this.getResources().getString(R.string.RGMAP_PROMPT_GPSINFO);
                gpsDialog.showListDialog(str, AttSfossilActivity.this.mSqlDB, AttSfossilActivity.this.item_LineCode.getEditorEditText().getText().toString(), AttSfossilActivity.this.item_Dis.getEditorEditText());
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_mylocation)));
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_CODE, R.string.fos_code, EditTextEditorInfo.textEditor(), map, (String) null);
        if (isNewFlag()) {
            String strCode = this.mSqlDB.getNextCode(PMLib.m_strSfossil, "CODE", "LAYCODE", gstate.getCurLayerCode());
            item5.setItemData(strCode);
        }
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_TYPE, R.string.fos_type, EditTextEditorInfo.textEditor(), map, (String) null);
        item6.setPropDictName(getResources().getString(R.string.dic_FossilType));
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_AGE, R.string.fos_age, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.setPropDictName(getResources().getString(R.string.dic_tiantu_unit));
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_GEOUNIT, R.string.fos_geounit, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.setPropDictName(getResources().getString(R.string.dic_stone_name));
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_NAME, R.string.fos_name, EditTextEditorInfo.textEditor(), map, (String) null);
        item9.setPropDictName(getResources().getString(R.string.dic_gushengwu));
        this.itemList.add(item9);
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_ANALYSE, R.string.fos_analyse, EditTextEditorInfo.textEditor(), map, (String) null);
        item10.setPropDictName(getResources().getString(R.string.dic_gushengwu));
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_SAMPLING, R.string.fos_sampling, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item11);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.FOS_FLDNAME_REMARK, R.string.fos_remark, EditTextEditorInfo.textEditor(), map, (String) null));
        container.addView(group.getInnerView());
    }

    @Override // com.AoDevBase.ui.AttributeDBActivity
    public void InitFieldsInfo(HashMap<String, AttributeDBActivity.FieldType> fields) {
        fields.put("_id", AttributeDBActivity.FieldType.INT);
        fields.put("SECCODE", AttributeDBActivity.FieldType.STRING);
        fields.put("SECPOINT", AttributeDBActivity.FieldType.STRING);
        fields.put("LAYCODE", AttributeDBActivity.FieldType.STRING);
        fields.put("SLOPE_L", AttributeDBActivity.FieldType.DOUBLE);
        fields.put("CODE", AttributeDBActivity.FieldType.STRING);
        fields.put("TYPE", AttributeDBActivity.FieldType.STRING);
        fields.put("AGE", AttributeDBActivity.FieldType.STRING);
        fields.put("SAMPLING", AttributeDBActivity.FieldType.STRING);
        fields.put("SAMDATE", AttributeDBActivity.FieldType.STRING);
        fields.put("GEOUNIT", AttributeDBActivity.FieldType.STRING);
        fields.put("NAME", AttributeDBActivity.FieldType.STRING);
        fields.put("ANALYSE", AttributeDBActivity.FieldType.STRING);
        fields.put("REMARK", AttributeDBActivity.FieldType.STRING);
        fields.put("GBCODE", AttributeDBActivity.FieldType.STRING);
    }
}
