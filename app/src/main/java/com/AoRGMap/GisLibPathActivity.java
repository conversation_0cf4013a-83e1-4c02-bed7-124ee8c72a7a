package com.AoRGMap;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import com.AoDevBase.ui.filebrowser.FileListActivity;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.render.AoSysLib;

/* loaded from: classes.dex */
public class GisLibPathActivity extends Activity {
    private static int REQUSET_CODE_SELPATH = 1;
    EditText editText = null;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private View.OnClickListener listener = new View.OnClickListener() { // from class: com.AoRGMap.GisLibPathActivity.1
        @Override // android.view.View.OnClickListener
        public void onClick(View v) {
            Button btn = (But<PERSON>) v;
            switch (btn.getId()) {
                case R.id.buttonCancel /* 2131230845 */:
                    GisLibPathActivity.this.finish();
                    break;
                case R.id.buttonOK /* 2131230846 */:
                    if (AoSysLib.checkSysLib(GisLibPathActivity.this.editText.getText().toString())) {
                        GisLibPathActivity.this.writeGisLibPath();
                        GisLibPathActivity.this.startActivity(new Intent(GisLibPathActivity.this, (Class<?>) AoRGMapActivity.class));
                        GisLibPathActivity.this.finish();
                        break;
                    } else {
                        Toast.makeText(GisLibPathActivity.this, R.string.RGMAP_PROMPT_FINDLIB, 1).show();
                        break;
                    }
                case R.id.buttonSelect /* 2131230848 */:
                    Intent intent = new Intent(GisLibPathActivity.this, (Class<?>) FileListActivity.class);
                    String[] ends = {".@@@"};
                    intent.putExtra("file_ends", ends);
                    String sdcardPath = Environment.getExternalStorageDirectory().toString() + "/";
                    intent.putExtra("initial_directory", sdcardPath);
                    intent.putExtra("isDirectory", true);
                    GisLibPathActivity.this.startActivityForResult(intent, GisLibPathActivity.REQUSET_CODE_SELPATH);
                    break;
            }
        }
    };

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.gislibpath);
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.editText = (EditText) findViewById(R.id.editText);
        Button buttonSelect = (Button) findViewById(R.id.buttonSelect);
        buttonSelect.setOnClickListener(this.listener);
        Button buttonOK = (Button) findViewById(R.id.buttonOK);
        buttonOK.setOnClickListener(this.listener);
        Button buttonCancel = (Button) findViewById(R.id.buttonCancel);
        buttonCancel.setOnClickListener(this.listener);
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUSET_CODE_SELPATH && resultCode == 1) {
            Bundle bundle = data.getExtras();
            String GisLibPath = bundle.getString("back");
            if (GisLibPath != null) {
                this.editText.setText(GisLibPath);
            }
        }
    }

    public void writeGisLibPath() {
        RGMapPreferences profile = new RGMapPreferences(this);
        if (profile.openForWrite()) {
            profile.putString(RGMapPreferences.mAoGISLibPath, this.editText.getText().toString());
            profile.closeForWrite();
        }
    }
}
