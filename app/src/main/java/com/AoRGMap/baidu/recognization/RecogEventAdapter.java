package com.AoRGMap.baidu.recognization;

import android.util.Log;
import com.AoRGMap.baidu.control.ErrorTranslation;
import com.AoRGMap.baidu.util.Logger;
import com.baidu.speech.EventListener;
import com.baidu.speech.asr.SpeechConstant;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class RecogEventAdapter implements EventListener {
    private static final String TAG = "RecogEventAdapter";
    protected String currentJson;
    private IRecogListener listener;

    public RecogEventAdapter(IRecogListener listener) {
        this.listener = listener;
    }

    @Override // com.baidu.speech.EventListener
    public void onEvent(String name, String params, byte[] data, int offset, int length) {
        this.currentJson = params;
        String logMessage = "name:" + name + "; params:" + params;
        Log.i(TAG, logMessage);
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_LOADED)) {
            this.listener.onOfflineLoaded();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_UNLOADED)) {
            this.listener.onOfflineUnLoaded();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_READY)) {
            this.listener.onAsrReady();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_BEGIN)) {
            this.listener.onAsrBegin();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_END)) {
            this.listener.onAsrEnd();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_PARTIAL)) {
            RecogResult recogResult = RecogResult.parseJson(params);
            String[] results = recogResult.getResultsRecognition();
            if (recogResult.isFinalResult()) {
                this.listener.onAsrFinalResult(results, recogResult);
                return;
            } else if (recogResult.isPartialResult()) {
                this.listener.onAsrPartialResult(results, recogResult);
                return;
            } else {
                if (recogResult.isNluResult()) {
                    this.listener.onAsrOnlineNluResult(new String(data, offset, length));
                    return;
                }
                return;
            }
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_FINISH)) {
            RecogResult recogResult2 = RecogResult.parseJson(params);
            if (recogResult2.hasError()) {
                int errorCode = recogResult2.getError();
                Logger.error(TAG, "asr error:" + params);
                this.listener.onAsrFinishError(errorCode, ErrorTranslation.recogError(errorCode), recogResult2.getDesc());
                return;
            }
            this.listener.onAsrFinish(recogResult2);
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_LONG_SPEECH)) {
            this.listener.onAsrLongFinish();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_EXIT)) {
            this.listener.onAsrExit();
            return;
        }
        if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_VOLUME)) {
            Volume vol = parseVolumeJson(params);
            this.listener.onAsrVolume(vol.volumePercent, vol.volume);
        } else if (name.equals(SpeechConstant.CALLBACK_EVENT_ASR_AUDIO)) {
            if (data.length != length) {
                Logger.error(TAG, "internal error: asr.audio callback data length is not equal to length param");
            }
            this.listener.onAsrAudio(data, offset, length);
        }
    }

    private Volume parseVolumeJson(String jsonStr) {
        Volume vol = new Volume();
        vol.origalJson = jsonStr;
        try {
            JSONObject json = new JSONObject(jsonStr);
            vol.volumePercent = json.getInt("volume-percent");
            vol.volume = json.getInt("volume");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return vol;
    }

    private class Volume {
        private String origalJson;
        private int volume;
        private int volumePercent;

        private Volume() {
            this.volumePercent = -1;
            this.volume = -1;
        }
    }
}
