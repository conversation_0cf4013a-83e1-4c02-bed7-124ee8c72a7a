package com.AoRGMap.baidu.util;

import android.content.res.AssetManager;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: classes.dex */
public class FileUtil {
    public static boolean makeDir(String dirPath) {
        File file = new File(dirPath);
        if (file.exists()) {
            return true;
        }
        return file.mkdirs();
    }

    public static String getContentFromAssetsFile(AssetManager assets, String source) {
        try {
            InputStream is = assets.open(source);
            int lenght = is.available();
            byte[] buffer = new byte[lenght];
            is.read(buffer);
            String result = new String(buffer, "utf8");
            return result;
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static boolean copyFromAssets(AssetManager assets, String source, String dest, boolean isCover) throws IOException {
        File file = new File(dest);
        boolean isCopyed = false;
        if (isCover || (!isCover && !file.exists())) {
            InputStream is = null;
            FileOutputStream fos = null;
            try {
                is = assets.open(source);
                FileOutputStream fos2 = new FileOutputStream(dest);
                try {
                    byte[] buffer = new byte[1024];
                    while (true) {
                        int size = is.read(buffer, 0, 1024);
                        if (size < 0) {
                            break;
                        }
                        fos2.write(buffer, 0, size);
                    }
                    isCopyed = true;
                    if (fos2 != null) {
                        try {
                            fos2.close();
                            if (is != null) {
                                is.close();
                            }
                        } finally {
                            if (is != null) {
                                is.close();
                            }
                        }
                    }
                } catch (Throwable th) {
                    th = th;
                    fos = fos2;
                    if (fos != null) {
                        try {
                            fos.close();
                        } finally {
                            if (is != null) {
                                is.close();
                            }
                        }
                    }
                    throw th;
                }
            } catch (Throwable th2) {
                th = th2;
            }
        }
        return isCopyed;
    }
}
