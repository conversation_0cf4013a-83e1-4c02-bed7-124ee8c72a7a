package com.AoRGMap.common;

/* loaded from: classes.dex */
public class GPS3DDOT {
    public double dx;
    public double dy;
    public double dz;

    public GPS3DDOT() {
        this.dx = 0.0d;
        this.dy = 0.0d;
        this.dz = 0.0d;
    }

    public GPS3DDOT(double dx, double dy, double dz) {
        this.dx = dx;
        this.dy = dy;
        this.dz = dz;
    }

    public int hashCode() {
        long temp = Double.doubleToLongBits(this.dx);
        int result = ((int) ((temp >>> 32) ^ temp)) + 31;
        long temp2 = Double.doubleToLongBits(this.dy);
        int result2 = (result * 31) + ((int) ((temp2 >>> 32) ^ temp2));
        long temp3 = Double.doubleToLongBits(this.dz);
        return (result2 * 31) + ((int) ((temp3 >>> 32) ^ temp3));
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null && getClass() == obj.getClass()) {
            GPS3DDOT other = (GPS3DDOT) obj;
            return Double.doubleToLongBits(this.dx) == Double.doubleToLongBits(other.dx) && Double.doubleToLongBits(this.dy) == Double.doubleToLongBits(other.dy) && Double.doubleToLongBits(this.dz) == Double.doubleToLongBits(other.dz);
        }
        return false;
    }

    public double getDx() {
        return this.dx;
    }

    public void setDx(double dx) {
        this.dx = dx;
    }

    public double getDy() {
        return this.dy;
    }

    public void setDy(double dy) {
        this.dy = dy;
    }

    public double getDz() {
        return this.dz;
    }

    public void setDz(double dz) {
        this.dz = dz;
    }
}
