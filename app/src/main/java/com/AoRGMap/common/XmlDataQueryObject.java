package com.AoRGMap.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class XmlDataQueryObject implements Serializable {
    private String mSzName;
    private int mbButton;
    private int miType;
    private List<String> mlist;

    public int getMiType() {
        return this.miType;
    }

    public void setMiType(int miType) {
        this.miType = miType;
    }

    public String getmSzName() {
        return this.mSzName;
    }

    public void setmSzName(String mSzName) {
        this.mSzName = mSzName;
    }

    public List<String> getMlist() {
        return this.mlist;
    }

    public void setMlist(List<String> mlist) {
        this.mlist = mlist;
    }

    public int getMbButton() {
        return this.mbButton;
    }

    public void setMbButton(int mbButton) {
        this.mbButton = mbButton;
    }

    public XmlDataQueryObject(int miType, String mSzName, List<String> mlist) {
        this.miType = 0;
        this.mSzName = "";
        this.mlist = null;
        this.mbButton = 0;
        this.miType = miType;
        this.mSzName = mSzName;
        this.mlist = mlist;
        this.mbButton = 0;
    }

    public XmlDataQueryObject(int miType, String mSzName) {
        this.miType = 0;
        this.mSzName = "";
        this.mlist = null;
        this.mbButton = 0;
        this.miType = miType;
        this.mSzName = mSzName;
        this.mlist = new ArrayList();
        this.mbButton = 0;
    }

    public XmlDataQueryObject() {
        this.miType = 0;
        this.mSzName = "";
        this.mlist = null;
        this.mbButton = 0;
        this.miType = -1;
        this.mSzName = "";
        this.mlist = new ArrayList();
        this.mbButton = 0;
    }
}
