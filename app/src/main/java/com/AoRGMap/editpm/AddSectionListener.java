package com.AoRGMap.editpm;

import android.content.Intent;
import android.os.Bundle;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoGIS.base.GeoPoint;
import com.AoGIS.base.IAoView;
import com.AoGIS.base.PointType;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.edit.PointEditListener;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.pm.AttSectionActivity;
import com.AoRGMap.prb.PointParams;

/* loaded from: classes.dex */
public class AddSectionListener extends PointEditListener {
    String mStrAreaName;

    public AddSectionListener(IAoView v, String strAreaName, PointType pntType) {
        super(v, MODE_NEW, pntType);
        this.mStrAreaName = null;
        this.mStrAreaName = strAreaName;
    }

    @Override // com.AoGIS.edit.PointEditListener, com.AoGIS.render.BaseTouchListener
    public boolean onEditOk() {
        boolean bAppend = super.onEditOk();
        if (bAppend) {
            int iNo = getLastGeometryId();
            Class<?> cls = PRBAreas.getAttActivityClass(this.mStrAreaName);
            if (cls != null) {
                Intent intent = new Intent(getView().getViewContext(), cls);
                Bundle bundle = new Bundle();
                int index = getView().getMap().getItemIndex(this.mStrAreaName);
                WorkArea area = getView().getMap().getItemWorkArea((short) index);
                if (area != null) {
                    GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
                    bundle.putInt("SqlDB", 0);
                    bundle.putInt(AttributeDBActivity.PARAM_INT_SORTID, 0);
                    bundle.putBoolean("isNew", true);
                    bundle.putString("DictPath", mGState.getDictPath());
                    int wa = RGMapApplication.getCurrentApp().addInAppTaskData(area);
                    bundle.putBoolean(AttSectionActivity.PARAM_BOOLEAN_WITH_MAP, true);
                    bundle.putInt("WorkArea", wa);
                    bundle.putInt("gid", iNo);
                    GeoPoint point = (GeoPoint) area.getGeometry(iNo);
                    WorkAreaParams.ProjectionType type = mGState.getProjectionType();
                    double dRate = mGState.getCoordUnitRate();
                    if (type == WorkAreaParams.ProjectionType.None) {
                        bundle.putDouble(PointParams.PARAM_DOUBLE_X, point.getX());
                        bundle.putDouble(PointParams.PARAM_DOUBLE_Y, point.getY());
                    } else {
                        bundle.putDouble(PointParams.PARAM_DOUBLE_X, point.getX() * dRate);
                        bundle.putDouble(PointParams.PARAM_DOUBLE_Y, point.getY() * dRate);
                    }
                    intent.putExtras(bundle);
                    getView().getViewContext().startActivity(intent);
                }
            }
        }
        return bAppend;
    }
}
