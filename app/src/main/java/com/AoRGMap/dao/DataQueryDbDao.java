package com.AoRGMap.dao;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.AoDevBase.db.IAttributeDB;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoGIS.util.GdbAttributesMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* loaded from: classes.dex */
public class DataQueryDbDao implements IAttributeDB {
    private SQLiteDatabase mDb;
    private DataQueryDbOpenHelper mHelper;

    public DataQueryDbDao(Context context, String name) {
        this.mHelper = new DataQueryDbOpenHelper(context, name);
    }

    public SQLiteDatabase getSQLDBForRead() {
        this.mDb = this.mHelper.getReadableDatabase();
        return this.mDb;
    }

    public SQLiteDatabase getSQLDBForWrite() {
        this.mDb = this.mHelper.getWritableDatabase();
        return this.mDb;
    }

    public void closeSQLDB() {
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public GdbAttributesMap<String, Object> select(String table, int id, HashMap<String, AttributeDBActivity.FieldType> fields) {
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where _ID=" + id;
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        GdbAttributesMap<String, Object> map = new GdbAttributesMap<>();
        if (cursor.moveToNext()) {
            for (Map.Entry<String, ?> entry : fields.entrySet()) {
                String key = entry.getKey();
                AttributeDBActivity.FieldType type = entry.getValue();
                if (type == AttributeDBActivity.FieldType.STRING) {
                    map.put(key, cursor.getString(cursor.getColumnIndex(key)));
                } else if (type == AttributeDBActivity.FieldType.LONG) {
                    long l = cursor.getLong(cursor.getColumnIndex(key));
                    map.put(key, Long.valueOf(l));
                } else if (type == AttributeDBActivity.FieldType.INT) {
                    int i = cursor.getInt(cursor.getColumnIndex(key));
                    map.put(key, Integer.valueOf(i));
                } else if (type == AttributeDBActivity.FieldType.DOUBLE) {
                    double d = cursor.getDouble(cursor.getColumnIndex(key));
                    map.put(key, Double.valueOf(d));
                }
            }
        }
        this.mDb.close();
        return map;
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public GdbAttributesMap<String, Object> select(String strSQL, HashMap<String, AttributeDBActivity.FieldType> fields) {
        GdbAttributesMap<String, Object> map = new GdbAttributesMap<>();
        this.mDb = this.mHelper.getReadableDatabase();
        Cursor cursor = this.mDb.rawQuery(strSQL, null);
        if (cursor.moveToNext()) {
            for (Map.Entry<String, ?> entry : fields.entrySet()) {
                String key = entry.getKey();
                AttributeDBActivity.FieldType type = entry.getValue();
                if (type == AttributeDBActivity.FieldType.STRING) {
                    map.put(key, cursor.getString(cursor.getColumnIndex(key)));
                } else if (type == AttributeDBActivity.FieldType.LONG) {
                    long l = cursor.getLong(cursor.getColumnIndex(key));
                    map.put(key, Long.valueOf(l));
                } else if (type == AttributeDBActivity.FieldType.INT) {
                    int i = cursor.getInt(cursor.getColumnIndex(key));
                    map.put(key, Integer.valueOf(i));
                } else if (type == AttributeDBActivity.FieldType.DOUBLE) {
                    double d = cursor.getDouble(cursor.getColumnIndex(key));
                    map.put(key, Double.valueOf(d));
                }
            }
        }
        this.mDb.close();
        return map;
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void add(String table, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fields) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlField = null;
        String sqlValues = null;
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            sqlField = sqlField + ',' + key;
            if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlValues = sqlValues + ",'" + value + "'";
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (((String) value).equals("")) {
                    value = Double.valueOf(0.0d);
                }
                sqlValues = sqlValues + "," + value;
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.INT) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            }
        }
        String subField = sqlField.substring(5);
        String subValues = sqlValues.substring(5);
        String sqlInsert = "insert into " + table + " (" + subField + ") Values (" + subValues + ")";
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void update(String table, int id, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fieldType) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlAssign = null;
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlAssign = sqlAssign + "," + key + "='" + value + "'";
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (value == null) {
                    value = 0;
                }
                sqlAssign = sqlAssign + "," + key + "=" + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (value == null) {
                    value = Double.valueOf(0.0d);
                }
                sqlAssign = sqlAssign + "," + key + "=" + value;
            }
        }
        String subAssign = sqlAssign.substring(5);
        String sqlInsert = "update " + table + " set " + subAssign + " where _id=" + id;
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void delete(String table, int id) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlDelete = "delete from " + table + " where _id=" + id;
        this.mDb.execSQL(sqlDelete);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void insert(String table, int sortId, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fieldType) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlField = "_id";
        String sqlValues = "\"" + sortId + "\"";
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            sqlField = sqlField + ',' + key;
            if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlValues = sqlValues + ",'" + value + "'";
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (((String) value).equals("")) {
                    value = Double.valueOf(0.0d);
                }
                sqlValues = sqlValues + "," + value;
            }
        }
        String sqlInsert = "insert into " + table + " (" + sqlField + ") Values (" + sqlValues + ")";
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    public List<Map<String, Object>> select(String table, String key) {
        List<Map<String, Object>> mArrayList = new ArrayList<>();
        List<String> fieldStrings = new ArrayList<>();
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "PRAGMA table_info(" + table + ") ";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        while (cursor.moveToNext()) {
            fieldStrings.add(cursor.getString(cursor.getColumnIndex("name")));
        }
        if (fieldStrings.size() <= 0) {
            return null;
        }
        String sqlSelect2 = "select * from " + table + "  where " + fieldStrings.get(0) + "=\"" + key + "\"";
        Cursor cursor2 = this.mDb.rawQuery(sqlSelect2, null);
        while (cursor2.moveToNext()) {
            for (int i = 0; i < fieldStrings.size(); i++) {
                Map<String, Object> map = new HashMap<>();
                String fieldString = fieldStrings.get(i);
                map.put("FIELD", fieldString);
                map.put("VALUE", cursor2.getString(cursor2.getColumnIndex(fieldString)));
                mArrayList.add(map);
            }
        }
        this.mDb.close();
        return mArrayList;
    }

    public String selectlry(String table, String key) {
        String resultString = "";
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + "  where LAYCODE=\"" + key + "\"";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        while (cursor.moveToNext()) {
            resultString = cursor.getString(cursor.getColumnIndex("FIELDDESC"));
        }
        this.mDb.close();
        return resultString;
    }

    public String selectlryPz(String table, String key) {
        String resultString = "";
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + "  where LAYCODE=\"" + key + "\"";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        while (cursor.moveToNext()) {
            resultString = cursor.getString(cursor.getColumnIndex("FieldDescPz"));
        }
        this.mDb.close();
        return resultString;
    }

    public List<String> getTabSet() {
        List<String> rsult = new ArrayList<>();
        this.mDb = this.mHelper.getReadableDatabase();
        Cursor cursor = this.mDb.rawQuery("SELECT * FROM sqlite_master where name!='sqlite_sequence' and name!='android_metadata'", null);
        while (cursor.moveToNext()) {
            String resultString = cursor.getString(cursor.getColumnIndex("name"));
            rsult.add(resultString);
        }
        this.mDb.close();
        return rsult;
    }

    public String select(String table, HashMap<String, String> fields, String Filed) {
        String resultString = "";
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table;
        String termString = "";
        boolean bAdd = false;
        for (Map.Entry<String, String> entry : fields.entrySet()) {
            String key = entry.getKey();
            String Value = entry.getValue();
            if (bAdd) {
                termString = termString + " and ";
            }
            termString = termString + key + "='" + Value + "'";
            bAdd = true;
        }
        if (bAdd) {
            sqlSelect = sqlSelect + " where " + termString;
        }
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        while (cursor.moveToNext()) {
            resultString = cursor.getString(cursor.getColumnIndex(Filed));
        }
        this.mDb.close();
        return resultString;
    }
}
