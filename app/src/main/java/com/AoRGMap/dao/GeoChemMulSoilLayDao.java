package com.AoRGMap.dao;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.AoDevBase.db.IAttributeDB;
import com.AoDevBase.ui.AttributeDBActivity;
import com.AoGIS.util.GdbAttributesMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* loaded from: classes.dex */
public class GeoChemMulSoilLayDao implements IAttributeDB {
    private SQLiteDatabase mDb;
    private GeoChemMulSoilLayHelper mHelper;

    public GeoChemMulSoilLayDao(Context context, String name) {
        this.mHelper = new GeoChemMulSoilLayHelper(context, name);
    }

    public SQLiteDatabase getSQLDBForRead() {
        this.mDb = this.mHelper.getReadableDatabase();
        return this.mDb;
    }

    public SQLiteDatabase getSQLDBForWrite() {
        this.mDb = this.mHelper.getWritableDatabase();
        return this.mDb;
    }

    public void closeSQLDB() {
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public GdbAttributesMap<String, Object> select(String table, int id, HashMap<String, AttributeDBActivity.FieldType> fields) {
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where _ID=" + id;
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        GdbAttributesMap<String, Object> map = new GdbAttributesMap<>();
        if (cursor.moveToNext()) {
            for (Map.Entry<String, ?> entry : fields.entrySet()) {
                String key = entry.getKey();
                AttributeDBActivity.FieldType type = entry.getValue();
                if (type == AttributeDBActivity.FieldType.STRING) {
                    map.put(key, cursor.getString(cursor.getColumnIndex(key)));
                } else if (type == AttributeDBActivity.FieldType.LONG) {
                    long l = cursor.getLong(cursor.getColumnIndex(key));
                    map.put(key, Long.valueOf(l));
                } else if (type == AttributeDBActivity.FieldType.INT) {
                    int i = cursor.getInt(cursor.getColumnIndex(key));
                    map.put(key, Integer.valueOf(i));
                } else if (type == AttributeDBActivity.FieldType.DOUBLE) {
                    double d = cursor.getDouble(cursor.getColumnIndex(key));
                    map.put(key, Double.valueOf(d));
                }
            }
        }
        this.mDb.close();
        return map;
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public GdbAttributesMap<String, Object> select(String strSQL, HashMap<String, AttributeDBActivity.FieldType> fields) {
        GdbAttributesMap<String, Object> map = new GdbAttributesMap<>();
        this.mDb = this.mHelper.getReadableDatabase();
        Cursor cursor = this.mDb.rawQuery(strSQL, null);
        if (cursor.moveToNext()) {
            for (Map.Entry<String, ?> entry : fields.entrySet()) {
                String key = entry.getKey();
                AttributeDBActivity.FieldType type = entry.getValue();
                if (type == AttributeDBActivity.FieldType.STRING) {
                    map.put(key, cursor.getString(cursor.getColumnIndex(key)));
                } else if (type == AttributeDBActivity.FieldType.LONG) {
                    long l = cursor.getLong(cursor.getColumnIndex(key));
                    map.put(key, Long.valueOf(l));
                } else if (type == AttributeDBActivity.FieldType.INT) {
                    int i = cursor.getInt(cursor.getColumnIndex(key));
                    map.put(key, Integer.valueOf(i));
                } else if (type == AttributeDBActivity.FieldType.DOUBLE) {
                    double d = cursor.getDouble(cursor.getColumnIndex(key));
                    map.put(key, Double.valueOf(d));
                }
            }
        }
        this.mDb.close();
        return map;
    }

    public void resort(String table, int sort_id) {
        this.mDb = this.mHelper.getWritableDatabase();
        Cursor cursor = this.mDb.query(table, new String[]{"_ID"}, "_ID>=?", new String[]{String.valueOf(sort_id)}, null, null, "_id DESC");
        while (cursor.moveToNext()) {
            int i = cursor.getInt(cursor.getColumnIndex("_ID"));
            int j = i + 1;
            String sqlResort = "update " + table + " set _ID = " + j + " where _ID=" + i;
            this.mDb.execSQL(sqlResort);
        }
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void add(String table, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fields) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlField = null;
        String sqlValues = null;
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            sqlField = sqlField + ',' + key;
            if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlValues = sqlValues + ",'" + value + "'";
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (((String) value).equals("")) {
                    value = Double.valueOf(0.0d);
                }
                sqlValues = sqlValues + "," + value;
            } else if (fields.get(key.toUpperCase()) == AttributeDBActivity.FieldType.INT) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            }
        }
        String subField = sqlField.substring(5);
        String subValues = sqlValues.substring(5);
        String sqlInsert = "insert into " + table + " (" + subField + ") Values (" + subValues + ")";
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void update(String table, int id, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fieldType) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlAssign = null;
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlAssign = sqlAssign + "," + key + "='" + value + "'";
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (value == null) {
                    value = 0;
                }
                sqlAssign = sqlAssign + "," + key + "=" + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.INT) {
                if (value == null) {
                    value = 0;
                }
                sqlAssign = sqlAssign + "," + key + "=" + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (value == null) {
                    value = Double.valueOf(0.0d);
                }
                sqlAssign = sqlAssign + "," + key + "=" + value;
            }
        }
        String subAssign = sqlAssign.substring(5);
        String sqlInsert = "update " + table + " set " + subAssign + " where _id=" + id;
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void delete(String table, int id) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlDelete = "delete from " + table + " where _id=" + id;
        this.mDb.execSQL(sqlDelete);
        this.mDb.close();
    }

    @Override // com.AoDevBase.db.IAttributeDB
    public void insert(String table, int sortId, GdbAttributesMap<String, Object> data, HashMap<String, AttributeDBActivity.FieldType> fieldType) {
        this.mDb = this.mHelper.getWritableDatabase();
        String sqlField = "_id";
        String sqlValues = "\"" + sortId + "\"";
        for (Map.Entry<String, ?> entry : data.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            sqlField = sqlField + ',' + key;
            if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.STRING) {
                sqlValues = sqlValues + ",'" + value + "'";
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.LONG) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.INT) {
                if (((String) value).equals("")) {
                    value = 0;
                }
                sqlValues = sqlValues + "," + value;
            } else if (fieldType.get(key.toUpperCase()) == AttributeDBActivity.FieldType.DOUBLE) {
                if (((String) value).equals("")) {
                    value = Double.valueOf(0.0d);
                }
                sqlValues = sqlValues + "," + value;
            }
        }
        String sqlInsert = "insert into " + table + " (" + sqlField + ") Values (" + sqlValues + ")";
        this.mDb.execSQL(sqlInsert);
        this.mDb.close();
    }

    public List<Map<String, Object>> select(String table, String key1) {
        List<Map<String, Object>> mArrayList = new ArrayList<>();
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where SAMCODE='" + key1 + "' order by ID";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        while (cursor.moveToNext()) {
            Map<String, Object> map = new HashMap<>();
            map.put("ID", Integer.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
            map.put("STARTDEPTH", cursor.getString(cursor.getColumnIndex("STARTDEPTH")));
            map.put("ENDDEPTH", cursor.getString(cursor.getColumnIndex("ENDDEPTH")));
            map.put("SOILTYPE", cursor.getString(cursor.getColumnIndex("SOILTYPE")));
            map.put("DESC", cursor.getString(cursor.getColumnIndex("DESC")));
            mArrayList.add(map);
        }
        this.mDb.close();
        return mArrayList;
    }

    public int getSize(String table, String key1) {
        int size = 0;
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select  count(*) as sum from " + table + " where SAMCODE = '" + key1 + "'";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        if (cursor.moveToNext()) {
            size = cursor.getInt(0);
        }
        this.mDb.close();
        return size;
    }

    public int getStartDepth(String table, String key1) {
        int end = 0;
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where SAMCODE='" + key1 + "' order by ID desc";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        if (cursor.moveToFirst()) {
            end = cursor.getInt(cursor.getColumnIndex("ENDDEPTH"));
        }
        this.mDb.close();
        return end;
    }

    public int getSortID(String table, String mSampleID, String iD) {
        int SORTID = 0;
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where SAMCODE='" + mSampleID + "' and ID = " + iD;
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        if (cursor.moveToFirst()) {
            SORTID = cursor.getInt(cursor.getColumnIndex("_ID"));
        }
        this.mDb.close();
        return SORTID;
    }

    public int getMaxLayer(String table, String key1) {
        int layer = 0;
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where SAMCODE='" + key1 + "' order by ID desc";
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        if (cursor.moveToFirst()) {
            layer = cursor.getInt(cursor.getColumnIndex("ID"));
        }
        this.mDb.close();
        return layer;
    }

    public boolean bLayerExist(String table, String key1, String key2) {
        boolean b = false;
        this.mDb = this.mHelper.getReadableDatabase();
        String sqlSelect = "select * from " + table + " where SAMCODE='" + key1 + "' and ID= " + key2;
        Cursor cursor = this.mDb.rawQuery(sqlSelect, null);
        if (cursor.moveToNext()) {
            b = true;
        }
        this.mDb.close();
        return b;
    }
}
