package com.AoRGMap.SmartService;

/* loaded from: classes.dex */
public class SmartSpaceServiceMapItem {
    String Url;
    boolean bsel;
    String group;
    String name;
    String tile;

    public String getGroup() {
        return this.group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public SmartSpaceServiceMapItem(boolean bsel, String group, String name, String url, String tile) {
        this.bsel = bsel;
        this.tile = tile;
        this.group = group;
        this.name = name;
        this.Url = url;
    }

    public boolean isBsel() {
        return this.bsel;
    }

    public String getTile() {
        return this.tile;
    }

    public String getUrl() {
        return this.Url;
    }

    public void setBsel(boolean bsel) {
        this.bsel = bsel;
    }

    public void setTile(String tile) {
        this.tile = tile;
    }

    public void setUrl(String url) {
        this.Url = url;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SmartSpaceServiceMapItem)) {
            return false;
        }
        SmartSpaceServiceMapItem that = (SmartSpaceServiceMapItem) o;
        if (this.bsel == that.bsel && this.tile.equals(that.tile) && this.Url.equals(that.Url) && this.group.equals(that.group)) {
            return this.name.equals(that.name);
        }
        return false;
    }

    public int hashCode() {
        int result = this.bsel ? 1 : 0;
        return (((((((result * 31) + this.tile.hashCode()) * 31) + this.Url.hashCode()) * 31) + this.group.hashCode()) * 31) + this.name.hashCode();
    }
}
