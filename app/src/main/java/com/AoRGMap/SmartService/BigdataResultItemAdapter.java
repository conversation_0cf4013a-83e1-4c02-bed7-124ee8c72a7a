package com.AoRGMap.SmartService;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.TextView;
import com.AoRGMap.R;
import java.util.ArrayList;
import java.util.HashMap;

/* loaded from: classes.dex */
public class BigdataResultItemAdapter extends BaseAdapter {
    private Context mContext;
    private LayoutInflater mInflater;
    private ArrayList<HashMap<String, Object>> mList;

    public BigdataResultItemAdapter(Context context, ArrayList<HashMap<String, Object>> list) {
        this.mContext = context;
        this.mList = list;
        this.mInflater = LayoutInflater.from(context);
    }

    @Override // android.widget.Adapter
    public int getCount() {
        return this.mList.size();
    }

    @Override // android.widget.Adapter
    public Object getItem(int arg0) {
        return this.mList.get(arg0);
    }

    @Override // android.widget.Adapter
    public long getItemId(int arg0) {
        return arg0;
    }

    @Override // android.widget.Adapter
    public View getView(int pos, View convertView, ViewGroup arg2) {
        childViewHolder viewHolder;
        if (convertView == null) {
            convertView = this.mInflater.inflate(R.layout.simple_list_item_for_bigdataquery, (ViewGroup) null);
            viewHolder = new childViewHolder();
            viewHolder.titleView = (TextView) convertView.findViewById(R.id.textView1);
            viewHolder.checkBox = (CheckBox) convertView.findViewById(R.id.checkBox1);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (childViewHolder) convertView.getTag();
        }
        viewHolder.titleView.setText(this.mList.get(pos).get("ItemName").toString());
        viewHolder.checkBox.setChecked(((Boolean) this.mList.get(pos).get("ItemCheck")).booleanValue());
        return convertView;
    }

    class childViewHolder {
        public CheckBox checkBox;
        public TextView titleView;

        childViewHolder() {
        }
    }
}
