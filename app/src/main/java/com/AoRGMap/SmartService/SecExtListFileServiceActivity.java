package com.AoRGMap.SmartService;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Gallery;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.SpinnerAdapter;
import android.widget.TextView;
import android.widget.Toast;
import com.AoRGMap.R;
import com.AoRGMap.SmartService.LIstServiceContentItemAdapter;
import com.baidu.speech.asr.SpeechConstant;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import org.apache.http.cookie.ClientCookie;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class SecExtListFileServiceActivity extends Activity implements LIstServiceContentItemAdapter.Callback {
    private Context mContext;
    private ListView m_RootListView;
    private TextView m_TitleTextView;
    private ListView m_contentListView;
    private ImageView m_menuButton;
    private Gallery m_pathGallery;
    private ImageView m_returnButton;
    private SlidingLayout slidingLayout;
    private int UPDATA_VIEW = 9999;
    private int UPDATE_CONTENTVIEW = 8888;
    private int UPDATE_DETIALVIEW = 7777;
    private ArrayList<HashMap<String, Object>> mArrayRootList = null;
    private ArrayList<HashMap<String, Object>> mArrayContentList = null;
    private ArrayList<HashMap<String, Object>> mArrayPathList = null;
    HashMap<String, Object> mCurRootHashMap = new HashMap<>();
    private LIstServiceRootItemAdapter mIstServiceRootItemAdapter = null;
    private LIstServiceContentItemAdapter mIstServiceContentItemAdapter = null;
    private TextGalleryAdapter mTextGalleryAdapter = null;
    private ArrayList<String> mArraypathStrList = new ArrayList<>();
    private boolean m_bfinsh = true;
    private String[] mdeltailStrings = new String[22];
    private Handler mHandler = new Handler() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.1
        @Override // android.os.Handler
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 7777:
                    SecExtListFileServiceActivity.this.UPdateDetialView();
                    SecExtListFileServiceActivity.this.m_bfinsh = true;
                    break;
                case 8888:
                    SecExtListFileServiceActivity.this.UPdateContentView();
                    SecExtListFileServiceActivity.this.m_bfinsh = true;
                    break;
                case 9999:
                    SecExtListFileServiceActivity.this.UPdateView();
                    SecExtListFileServiceActivity.this.m_bfinsh = true;
                    break;
            }
        }
    };

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.ext_dlg_listservice);
        this.mContext = this;
        InitView();
        Initdata();
        InitListener();
    }

    private void InitListener() {
        this.m_menuButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                if (SecExtListFileServiceActivity.this.slidingLayout.isLeftLayoutVisible()) {
                    SecExtListFileServiceActivity.this.slidingLayout.scrollToRightLayout();
                    SecExtListFileServiceActivity.this.m_menuButton.setImageResource(android.R.drawable.ic_menu_sort_by_size);
                } else {
                    SecExtListFileServiceActivity.this.slidingLayout.scrollToLeftLayout();
                    SecExtListFileServiceActivity.this.m_menuButton.setImageResource(android.R.drawable.ic_menu_revert);
                }
            }
        });
        this.m_returnButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.3
            /* JADX WARN: Type inference failed for: r1v9, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$3$1] */
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                final int isize = SecExtListFileServiceActivity.this.mArrayPathList.size();
                if (isize > 1 && SecExtListFileServiceActivity.this.IsNetworkConnected(SecExtListFileServiceActivity.this.mContext).booleanValue() && SecExtListFileServiceActivity.this.m_bfinsh) {
                    SecExtListFileServiceActivity.this.m_bfinsh = false;
                    new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.3.1
                        @Override // java.lang.Thread, java.lang.Runnable
                        public void run() {
                            HashMap<String, Object> ContentMap = (HashMap) SecExtListFileServiceActivity.this.mArrayPathList.get(SecExtListFileServiceActivity.this.mArrayPathList.size() - 2);
                            String nodeId = ContentMap.get("nodeId").toString();
                            String nodetype = ContentMap.get("type").toString();
                            String ContentWebUrl = "";
                            if (nodetype.equals("2")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=" + nodeId;
                            } else if (nodetype.equals("1")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/show.do?id=" + nodeId;
                            }
                            SecExtListFileServiceActivity.this.onHttpRequestSecond(ContentWebUrl);
                            SecExtListFileServiceActivity.this.mArrayPathList.remove(isize - 1);
                            SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATE_CONTENTVIEW);
                        }
                    }.start();
                }
            }
        });
        this.m_contentListView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.4
            /* JADX WARN: Type inference failed for: r1v6, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$4$1] */
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> arg0, View arg1, final int arg2, long arg3) {
                if (SecExtListFileServiceActivity.this.IsNetworkConnected(SecExtListFileServiceActivity.this.mContext).booleanValue() && SecExtListFileServiceActivity.this.m_bfinsh) {
                    SecExtListFileServiceActivity.this.m_bfinsh = false;
                    new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.4.1
                        @Override // java.lang.Thread, java.lang.Runnable
                        public void run() {
                            HashMap<String, Object> ContentMap = (HashMap) SecExtListFileServiceActivity.this.mArrayContentList.get(arg2);
                            String nodeId = ContentMap.get("nodeId").toString();
                            String nodetype = ContentMap.get("type").toString();
                            String ContentWebUrl = "";
                            if (nodetype.equals("2")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=" + nodeId;
                            } else if (nodetype.equals("1")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/show.do?id=" + nodeId;
                            }
                            SecExtListFileServiceActivity.this.onHttpRequestSecond(ContentWebUrl);
                            SecExtListFileServiceActivity.this.mArrayPathList.add(ContentMap);
                            SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATE_CONTENTVIEW);
                        }
                    }.start();
                }
            }
        });
        this.m_RootListView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.5
            /* JADX WARN: Type inference failed for: r1v13, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$5$1] */
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> arg0, View arg1, final int arg2, long arg3) {
                if (SecExtListFileServiceActivity.this.slidingLayout.isLeftLayoutVisible()) {
                    SecExtListFileServiceActivity.this.slidingLayout.scrollToRightLayout();
                    SecExtListFileServiceActivity.this.m_menuButton.setImageResource(android.R.drawable.ic_menu_sort_by_size);
                } else {
                    SecExtListFileServiceActivity.this.slidingLayout.scrollToLeftLayout();
                    SecExtListFileServiceActivity.this.m_menuButton.setImageResource(android.R.drawable.ic_menu_revert);
                }
                if (SecExtListFileServiceActivity.this.IsNetworkConnected(SecExtListFileServiceActivity.this.mContext).booleanValue() && SecExtListFileServiceActivity.this.m_bfinsh) {
                    SecExtListFileServiceActivity.this.m_bfinsh = false;
                    new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.5.1
                        @Override // java.lang.Thread, java.lang.Runnable
                        public void run() {
                            if (SecExtListFileServiceActivity.this.mArrayRootList.size() > 0) {
                                HashMap<String, Object> rootMap = (HashMap) SecExtListFileServiceActivity.this.mArrayRootList.get(arg2);
                                String nodeId = rootMap.get("nodeId").toString();
                                String nodetype = rootMap.get("type").toString();
                                String ContentWebUrl = "";
                                if (nodetype.equals("2")) {
                                    ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=" + nodeId;
                                } else if (nodetype.equals("1")) {
                                    ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/show.do?id=" + nodeId;
                                }
                                SecExtListFileServiceActivity.this.onHttpRequestSecond(ContentWebUrl);
                                SecExtListFileServiceActivity.this.mArrayPathList.clear();
                                SecExtListFileServiceActivity.this.mArrayPathList.add(rootMap);
                                SecExtListFileServiceActivity.this.mCurRootHashMap = rootMap;
                                SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATA_VIEW);
                            }
                        }
                    }.start();
                }
            }
        });
        this.m_pathGallery.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.6
            /* JADX WARN: Type inference failed for: r1v6, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$6$1] */
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> arg0, View arg1, final int arg2, long arg3) {
                if (SecExtListFileServiceActivity.this.IsNetworkConnected(SecExtListFileServiceActivity.this.mContext).booleanValue() && SecExtListFileServiceActivity.this.m_bfinsh) {
                    SecExtListFileServiceActivity.this.m_bfinsh = false;
                    new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.6.1
                        @Override // java.lang.Thread, java.lang.Runnable
                        public void run() {
                            HashMap<String, Object> ContentMap = (HashMap) SecExtListFileServiceActivity.this.mArrayPathList.get(arg2);
                            String nodeId = ContentMap.get("nodeId").toString();
                            String nodetype = ContentMap.get("type").toString();
                            String ContentWebUrl = "";
                            if (nodetype.equals("2")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=" + nodeId;
                            } else if (nodetype.equals("1")) {
                                ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/show.do?id=" + nodeId;
                            }
                            SecExtListFileServiceActivity.this.onHttpRequestSecond(ContentWebUrl);
                            SecExtListFileServiceActivity.this.deletePathItem(arg2);
                            SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATE_CONTENTVIEW);
                        }
                    }.start();
                }
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void UPdateView() {
        this.mIstServiceRootItemAdapter = new LIstServiceRootItemAdapter(this.mContext, this.mArrayRootList);
        this.m_RootListView.setAdapter((ListAdapter) this.mIstServiceRootItemAdapter);
        this.mIstServiceContentItemAdapter = new LIstServiceContentItemAdapter(this.mContext, this.mArrayContentList, this);
        this.m_contentListView.setAdapter((ListAdapter) this.mIstServiceContentItemAdapter);
        this.m_TitleTextView.setText(this.mCurRootHashMap.get("name").toString());
        this.mTextGalleryAdapter = new TextGalleryAdapter(this.mArrayPathList, this.mContext);
        this.m_pathGallery.setAdapter((SpinnerAdapter) this.mTextGalleryAdapter);
        this.m_pathGallery.setSelection(this.mArrayPathList.size() - 1);
        this.mTextGalleryAdapter.SetSelectPos(this.mArrayPathList.size() - 1);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void UPdateContentView() {
        this.mIstServiceContentItemAdapter = new LIstServiceContentItemAdapter(this.mContext, this.mArrayContentList, this);
        this.m_contentListView.setAdapter((ListAdapter) this.mIstServiceContentItemAdapter);
        this.mTextGalleryAdapter = new TextGalleryAdapter(this.mArrayPathList, this.mContext);
        this.m_pathGallery.setAdapter((SpinnerAdapter) this.mTextGalleryAdapter);
        this.m_pathGallery.setSelection(this.mArrayPathList.size() - 1);
        this.mTextGalleryAdapter.SetSelectPos(this.mArrayPathList.size() - 1);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void UPdateDetialView() {
        new AlertDialog.Builder(this.mContext).setTitle("详细信息").setItems(this.mdeltailStrings, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.7
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                Toast.makeText(SecExtListFileServiceActivity.this.mContext, SecExtListFileServiceActivity.this.mdeltailStrings[which], 0).show();
            }
        }).setNegativeButton("取消", (DialogInterface.OnClickListener) null).show();
    }

    /* JADX WARN: Type inference failed for: r0v8, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$8] */
    private void Initdata() {
        this.mArrayRootList = new ArrayList<>();
        this.mArrayContentList = new ArrayList<>();
        this.mArrayPathList = new ArrayList<>();
        if (IsNetworkConnected(this.mContext).booleanValue() && this.m_bfinsh) {
            this.m_bfinsh = false;
            new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.8
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() {
                    if (SecExtListFileServiceActivity.this.onHttpRequestRoot("http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=-1").booleanValue() && SecExtListFileServiceActivity.this.mArrayRootList.size() > 0) {
                        new HashMap();
                        HashMap<String, Object> rootMap = (HashMap) SecExtListFileServiceActivity.this.mArrayRootList.get(0);
                        String nodeId = rootMap.get("nodeId").toString();
                        String nodetype = rootMap.get("type").toString();
                        String ContentWebUrl = "";
                        if (nodetype.equals("2")) {
                            ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/projectTree/show.do?id=" + nodeId;
                        } else if (nodetype.equals("1")) {
                            ContentWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/show.do?id=" + nodeId;
                        }
                        SecExtListFileServiceActivity.this.onHttpRequestSecond(ContentWebUrl);
                        SecExtListFileServiceActivity.this.mArrayPathList.add(rootMap);
                        SecExtListFileServiceActivity.this.mCurRootHashMap = rootMap;
                        SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATA_VIEW);
                    }
                }
            }.start();
        }
    }

    private void InitView() {
        this.slidingLayout = (SlidingLayout) findViewById(R.id.slidingLayout);
        this.m_menuButton = (ImageView) findViewById(R.id.imageView_menu);
        this.m_returnButton = (ImageView) findViewById(R.id.imageView_return);
        this.m_TitleTextView = (TextView) findViewById(R.id.textView_title);
        this.m_contentListView = (ListView) findViewById(R.id.listView2);
        this.m_RootListView = (ListView) findViewById(R.id.listView1);
        this.m_pathGallery = (Gallery) findViewById(R.id.gallery1);
    }

    public Boolean IsNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return Boolean.valueOf(mNetworkInfo.isAvailable());
            }
        }
        return false;
    }

    public Boolean onHttpRequestRoot(String strURL) {
        this.mArrayRootList.clear();
        boolean resString = false;
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead == -1) {
                    break;
                }
                baos.write(buffer, 0, hasRead);
            }
            inSteam.close();
            String string = new String(baos.toByteArray());
            string.length();
            if (string.equals("")) {
                return resString;
            }
            try {
                if (!string.equals("")) {
                    JSONArray reslutjArray = new JSONArray(string);
                    for (int i = 0; i < reslutjArray.length(); i++) {
                        JSONObject reObject = (JSONObject) reslutjArray.get(i);
                        if (reObject != null) {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("nodeId", reObject.getString("nodeId"));
                            map.put(SpeechConstant.PID, reObject.getString(SpeechConstant.PID));
                            map.put("type", reObject.getString("type"));
                            map.put("name", reObject.getString("name"));
                            map.put("groupId", reObject.getString("groupId"));
                            map.put("isParent", reObject.getString("isParent"));
                            map.put("createTime", reObject.getString("createTime"));
                            map.put("isPreview", reObject.getString("isPreview"));
                            map.put("size", reObject.getString("size"));
                            map.put("fileInfo", reObject.getString("fileInfo"));
                            this.mArrayRootList.add(map);
                        }
                    }
                }
                return true;
            } catch (JSONException e) {
                return resString;
            }
        } catch (Exception e2) {
            e2.printStackTrace();
            return resString;
        }
    }

    public Boolean onHttpRequestSecond(String strURL) {
        this.mArrayContentList.clear();
        boolean resString = false;
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead == -1) {
                    break;
                }
                baos.write(buffer, 0, hasRead);
            }
            inSteam.close();
            String string = new String(baos.toByteArray());
            string.length();
            if (string.equals("")) {
                return resString;
            }
            try {
                if (!string.equals("")) {
                    JSONArray reslutjArray = new JSONArray(string);
                    for (int i = 0; i < reslutjArray.length(); i++) {
                        JSONObject reObject = (JSONObject) reslutjArray.get(i);
                        if (reObject != null) {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("nodeId", reObject.getString("nodeId"));
                            map.put(SpeechConstant.PID, reObject.getString(SpeechConstant.PID));
                            map.put("type", reObject.getString("type"));
                            map.put("name", reObject.getString("name"));
                            map.put("groupId", reObject.getString("groupId"));
                            map.put("isParent", reObject.getString("isParent"));
                            map.put("createTime", reObject.getString("createTime"));
                            map.put("isPreview", reObject.getString("isPreview"));
                            map.put("size", reObject.getString("size"));
                            this.mArrayContentList.add(map);
                        }
                    }
                }
                return true;
            } catch (JSONException e) {
                return resString;
            }
        } catch (Exception e2) {
            e2.printStackTrace();
            return resString;
        }
    }

    private String getPath() {
        String pathString = "";
        for (int i = 0; i < this.mArrayPathList.size(); i++) {
            HashMap<String, Object> map = this.mArrayPathList.get(i);
            if (pathString.equals("")) {
                pathString = map.get("name").toString();
            } else {
                pathString = pathString + ">" + map.get("name").toString();
            }
        }
        return pathString;
    }

    private void getPathString() {
        this.mArraypathStrList.clear();
        for (int i = 0; i < this.mArrayPathList.size(); i++) {
            HashMap<String, Object> map = this.mArrayPathList.get(i);
            this.mArraypathStrList.add(map.get("name").toString());
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void deletePathItem(int pos) {
        for (int i = this.mArrayPathList.size() - 1; i > pos; i--) {
            this.mArrayPathList.remove(i);
        }
    }

    /* JADX WARN: Type inference failed for: r3v10, types: [com.AoRGMap.SmartService.SecExtListFileServiceActivity$9] */
    @Override // com.AoRGMap.SmartService.LIstServiceContentItemAdapter.Callback
    public void click(View v) {
        int pos = ((Integer) v.getTag()).intValue();
        new HashMap();
        HashMap<String, Object> Map = this.mArrayContentList.get(pos);
        final String nodeId = Map.get("nodeId").toString();
        if (IsNetworkConnected(this.mContext).booleanValue() && this.m_bfinsh) {
            this.m_bfinsh = false;
            new Thread() { // from class: com.AoRGMap.SmartService.SecExtListFileServiceActivity.9
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() {
                    String DetailsWebUrl = "http://219.142.81.184/ResourceDirectoryService/fileTree/showFileInfo.do?id=" + nodeId;
                    if (SecExtListFileServiceActivity.this.onHttpRequestDetails(DetailsWebUrl).booleanValue()) {
                        SecExtListFileServiceActivity.this.mHandler.sendEmptyMessage(SecExtListFileServiceActivity.this.UPDATE_DETIALVIEW);
                    }
                }
            }.start();
        }
    }

    public Boolean onHttpRequestDetails(String strURL) {
        boolean resString = false;
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead == -1) {
                    break;
                }
                baos.write(buffer, 0, hasRead);
            }
            inSteam.close();
            String string = new String(baos.toByteArray());
            string.length();
            if (string.equals("")) {
                return resString;
            }
            try {
                JSONObject mJsonObject = new JSONObject(string);
                if (mJsonObject != null) {
                    String resultString = mJsonObject.getString("result");
                    if (!resultString.equals("")) {
                        JSONArray reslutjArray = new JSONArray(resultString);
                        for (int i = 0; i < reslutjArray.length(); i++) {
                            JSONObject reObject = (JSONObject) reslutjArray.get(i);
                            if (reObject != null) {
                                this.mdeltailStrings[0] = "文件序号:" + reObject.getString("ID");
                                this.mdeltailStrings[1] = "文件类型:" + reObject.getString("dataType");
                                this.mdeltailStrings[2] = "文件ID:" + reObject.getString("nodeId");
                                this.mdeltailStrings[3] = "文件名称:" + reObject.getString("name");
                                this.mdeltailStrings[4] = "存放路径:" + reObject.getString(ClientCookie.PATH_ATTR);
                                this.mdeltailStrings[5] = "文件大小:" + reObject.getString("fileSize");
                                this.mdeltailStrings[6] = "专业类别:" + reObject.getString("Category");
                                this.mdeltailStrings[7] = "比例尺:" + reObject.getString("Scale");
                                this.mdeltailStrings[8] = "完成单位:" + reObject.getString("FinishCmy");
                                this.mdeltailStrings[9] = "完成时间:" + reObject.getString("FinishedDate");
                                this.mdeltailStrings[10] = "完成人:" + reObject.getString("FinishedMan");
                                this.mdeltailStrings[11] = "经度起:" + reObject.getString("LonBgn");
                                this.mdeltailStrings[12] = "经度止:" + reObject.getString("LonEnd");
                                this.mdeltailStrings[13] = "纬度起:" + reObject.getString("LatBgn");
                                this.mdeltailStrings[14] = "纬度止:" + reObject.getString("LatEnd");
                                this.mdeltailStrings[15] = "资金来源:" + reObject.getString("MoneyFrom");
                                this.mdeltailStrings[16] = "是否共享:" + reObject.getString("IsShared");
                                this.mdeltailStrings[17] = "当前级别:" + reObject.getString("CurrentLvl");
                                this.mdeltailStrings[18] = "级别名称:" + reObject.getString("CurrentLvlName");
                                this.mdeltailStrings[19] = "存放时间:" + reObject.getString("SaveDate");
                                this.mdeltailStrings[20] = "存放人:" + reObject.getString("SaveMan");
                                this.mdeltailStrings[21] = "共享级别:" + reObject.getString("SharedLvl");
                            }
                        }
                    }
                }
                return true;
            } catch (JSONException e) {
                return resString;
            }
        } catch (Exception e2) {
            e2.printStackTrace();
            return resString;
        }
    }
}
