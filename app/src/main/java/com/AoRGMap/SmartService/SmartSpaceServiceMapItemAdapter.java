package com.AoRGMap.SmartService;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.CheckBox;
import android.widget.TextView;
import com.AoRGMap.R;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class SmartSpaceServiceMapItemAdapter extends BaseAdapter {
    private Context mContext;
    private ArrayList<SmartSpaceServiceMapItem> mMapItemList;
    private LayoutInflater mlayoutInflater;

    public SmartSpaceServiceMapItemAdapter(ArrayList<SmartSpaceServiceMapItem> mMapItemList, Context mContext) {
        this.mMapItemList = null;
        this.mlayoutInflater = null;
        this.mMapItemList = mMapItemList;
        this.mContext = mContext;
        this.mlayoutInflater = LayoutInflater.from(mContext);
    }

    @Override // android.widget.Adapter
    public int getCount() {
        if (this.mMapItemList != null) {
            return this.mMapItemList.size();
        }
        return 0;
    }

    @Override // android.widget.Adapter
    public Object getItem(int position) {
        if (this.mMapItemList != null) {
            return this.mMapItemList.get(position);
        }
        return null;
    }

    @Override // android.widget.Adapter
    public long getItemId(int position) {
        return position;
    }

    @Override // android.widget.Adapter
    public View getView(int position, View convertView, ViewGroup parent) {
        MapItemHoder mapItemHoder;
        if (convertView == null) {
            convertView = this.mlayoutInflater.inflate(R.layout.simple_list_item_for_mapitem, (ViewGroup) null);
            mapItemHoder = new MapItemHoder();
            mapItemHoder.itemtile = (TextView) convertView.findViewById(R.id.map_item_title);
            mapItemHoder.itemcheck = (CheckBox) convertView.findViewById(R.id.map_item_check);
            convertView.setTag(mapItemHoder);
        } else {
            mapItemHoder = (MapItemHoder) convertView.getTag();
        }
        mapItemHoder.itemtile.setText(this.mMapItemList.get(position).getTile().toString());
        mapItemHoder.itemcheck.setChecked(this.mMapItemList.get(position).isBsel());
        return convertView;
    }

    class MapItemHoder {
        public CheckBox itemcheck;
        public TextView itemtile;

        MapItemHoder() {
        }
    }
}
