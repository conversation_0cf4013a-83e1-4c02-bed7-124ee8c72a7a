package com.AoRGMap;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.ui.filebrowser.FileListActivity;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.AoMap;
import com.AoGIS.geometry.GeoClassType;
import com.AoGIS.ui.AoGISUIActivity;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
public class DataQueryConfigActivity extends AoGISUIActivity {
    private static int REQUSET_DATA_PATH = 1;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private int mSelIndex = 0;
    private AttributeItem mDataPathItem = null;
    private String mDataPath = null;
    private AttributeItem mSampleLayerItem = null;
    private String mSampleLayer = null;
    List<String> mPathList = new ArrayList();
    private int m_iselIndex = 0;
    private String[] mArrayPath = null;
    private ProgressDialog pDialog = null;

    @Override // com.AoGIS.ui.AoGISUIActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        View v = (ViewGroup) LayoutInflater.from(this).inflate(R.layout.sys_config, (ViewGroup) null, false);
        ViewGroup container = (ViewGroup) v.findViewById(R.id.config_container);
        this.mBottomLayout = (ViewGroup) v;
        initItems(container);
        super.onCreate(savedInstanceState);
        this.m_btnCancel.setVisibility(4);
        container.setFocusable(true);
        container.setFocusableInTouchMode(true);
        container.requestFocus();
    }

    private void initItems(ViewGroup container) {
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.RGMAP_HISRESOURCE_PROMPT1);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        AttributeItem.AttributeItemParams params = new AttributeItem.AttributeItemParams();
        params.label = getResources().getString(R.string.RGMAP_HISRESOURCE_PROMPT2);
        params.editorInfos = EditTextEditorInfo.textEditor();
        AttributeItem item = group.addItem(params);
        item.setItemData(this.mGState.getOnlineDataPack());
        item.getEditorEditText().setEnabled(false);
        item.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent intent = new Intent(DataQueryConfigActivity.this, (Class<?>) FileListActivity.class);
                String[] ends = {".@@@"};
                intent.putExtra("file_ends", ends);
                String sdcardPath = Environment.getExternalStorageDirectory().toString() + "/";
                intent.putExtra("initial_directory", sdcardPath);
                intent.putExtra("isDirectory", true);
                DataQueryConfigActivity.this.startActivityForResult(intent, DataQueryConfigActivity.REQUSET_DATA_PATH);
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_search)));
        item.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String strSD = Environment.getExternalStorageDirectory().toString();
                File[] files = new File(strSD).listFiles();
                DataQueryConfigActivity.this.mPathList.clear();
                DataQueryConfigActivity.this.SearchFile(files);
                if (DataQueryConfigActivity.this.mPathList.size() == 0) {
                    Toast.makeText(DataQueryConfigActivity.this, "No DataPack", 0).show();
                    return;
                }
                DataQueryConfigActivity.this.mArrayPath = (String[]) DataQueryConfigActivity.this.mPathList.toArray(new String[DataQueryConfigActivity.this.mPathList.size()]);
                AlertDialog.Builder builder = new AlertDialog.Builder(DataQueryConfigActivity.this);
                builder.setTitle("");
                builder.setSingleChoiceItems(DataQueryConfigActivity.this.mArrayPath, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.2.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        DataQueryConfigActivity.this.m_iselIndex = which;
                    }
                });
                builder.setPositiveButton(DataQueryConfigActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.2.2
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        DataQueryConfigActivity.this.mDataPath = DataQueryConfigActivity.this.mArrayPath[DataQueryConfigActivity.this.m_iselIndex];
                        DataQueryConfigActivity.this.mDataPathItem.getEditorEditText().setText(DataQueryConfigActivity.this.mDataPath);
                    }
                });
                builder.setNegativeButton(DataQueryConfigActivity.this.getString(R.string.btnCancel), (DialogInterface.OnClickListener) null);
                builder.show();
            }
        }, null));
        this.mDataPathItem = item;
        AttributeItem.AttributeItemParams params2 = new AttributeItem.AttributeItemParams();
        params2.label = getResources().getString(R.string.RGMAP_HISRESOURCE_PROMPT3);
        params2.editorInfos = EditTextEditorInfo.textEditor();
        AttributeItem item2 = group.addItem(params2);
        item2.setItemData(this.mGState.getDataQuerySamLayer());
        item2.getEditorEditText().setEnabled(false);
        item2.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                short WorkAreaNum;
                AoMap mMap = AoRGMapActivity.getCurrentMap();
                if (mMap != null && (WorkAreaNum = mMap.getWorkAreaNum()) > 0) {
                    String StrTemp = "";
                    for (short i = 0; i < WorkAreaNum; i = (short) (i + 1)) {
                        GeoClassType Areatype = mMap.getItemType(i);
                        if (Areatype == GeoClassType.POINT) {
                            StrTemp = StrTemp + mMap.getItemName(i) + ",";
                        }
                    }
                    final String[] WorkAreaName = StrTemp.split(",");
                    if (WorkAreaName.length > 0) {
                        AlertDialog.Builder builder = new AlertDialog.Builder(DataQueryConfigActivity.this);
                        builder.setTitle(DataQueryConfigActivity.this.getString(R.string.RGMAP_PROMPT_SELLAYER));
                        builder.setSingleChoiceItems(WorkAreaName, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.3.1
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialog, int which) {
                                DataQueryConfigActivity.this.mSelIndex = which;
                            }
                        });
                        builder.setPositiveButton(DataQueryConfigActivity.this.getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.DataQueryConfigActivity.3.2
                            @Override // android.content.DialogInterface.OnClickListener
                            public void onClick(DialogInterface dialog, int which) {
                                DataQueryConfigActivity.this.mSampleLayer = WorkAreaName[DataQueryConfigActivity.this.mSelIndex];
                                DataQueryConfigActivity.this.mSampleLayerItem.setItemData(DataQueryConfigActivity.this.mSampleLayer);
                            }
                        });
                        builder.setNegativeButton(DataQueryConfigActivity.this.getString(R.string.btnCancel), (DialogInterface.OnClickListener) null);
                        builder.show();
                    }
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_search)));
        this.mSampleLayerItem = item2;
        container.addView(group.getInnerView());
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUSET_DATA_PATH && resultCode == 1) {
            Bundle bundle = data.getExtras();
            this.mDataPath = bundle.getString("back");
            if (this.mDataPath != null) {
                this.mDataPathItem.getEditorEditText().setText(this.mDataPath);
            }
        }
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    protected void onClickCancel() {
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    protected void onClickOK() {
        if (this.mDataPath != null) {
            this.mGState.setOnlineDataPack(this.mDataPath);
        }
        if (this.mSampleLayer != null) {
            this.mGState.setDataQuerySamLayer(this.mSampleLayer);
        }
        finish();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void SearchFile(File[] files) {
        for (File file : files) {
            if (file.isDirectory()) {
                String Name = file.getName();
                file.getPath();
                if (Name.contains("DataPack")) {
                    this.mPathList.add(file.getPath());
                } else {
                    SearchFile(file.listFiles());
                }
            }
        }
    }
}
