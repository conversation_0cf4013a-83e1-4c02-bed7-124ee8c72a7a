package com.AoRGMap.edit;

import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.os.Environment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.Button;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.SimpleAdapter;
import com.AoGIS.base.IAoView;
import com.AoGIS.database.WorkArea;
import com.AoGIS.edit.ModifyEditListener;
import com.AoGIS.edit.TouchSelectDirListener;
import com.AoGIS.render.AoSelectPool;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.MyImageView1Activity;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.dao.DataQueryDbDao;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/* loaded from: classes.dex */
public class TouchCommHisAttEditListener extends TouchSelectDirListener {
    private final String FIELDMAP;
    private final String FIELDNOTE;
    String[] felds;
    private ListView listView;
    private SimpleAdapter mAdapter;
    private List<Map<String, Object>> mArrayList;
    private GdbAttributesMap<String, Object> mAttrMap;
    private DataQueryDbDao mDao;
    GlobalState mGState;
    private String mMapPath;
    private AoSelectPool mPool;
    private ViewGroup mSamResultType;
    private int mSelPos;
    private PopupWindow popupWindow;
    private View popupWindowView;
    int[] vaules;

    public TouchCommHisAttEditListener(IAoView v, WorkArea wa, ModifyEditListener listener, String mapPath) {
        super(v, wa, listener);
        this.mPool = null;
        this.mAttrMap = null;
        this.mArrayList = null;
        this.listView = null;
        this.mAdapter = null;
        this.popupWindow = null;
        this.popupWindowView = null;
        this.mSelPos = 0;
        this.felds = new String[]{"FIELD", "VALUE"};
        this.vaules = new int[]{R.id.id_dataqueryresult_field, R.id.id_dataqueryresult_value};
        this.mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
        this.FIELDMAP = "野外手图";
        this.FIELDNOTE = "note";
        this.mSamResultType = null;
        this.mMapPath = "";
        this.mMapPath = mapPath;
        this.popupWindowView = LayoutInflater.from(getView().getViewContext()).inflate(R.layout.data_queryresult_popwindow, (ViewGroup) null);
        this.popupWindow = new PopupWindow(this.popupWindowView, -1, getView().getViewHeight() / 3, true);
        this.popupWindow.setBackgroundDrawable(new BitmapDrawable());
        this.popupWindow.setInputMethodMode(1);
        this.popupWindow.setSoftInputMode(16);
        this.popupWindow.setFocusable(false);
        this.listView = (ListView) this.popupWindowView.findViewById(R.id.id_dataquery_poplist);
        View headerView = LayoutInflater.from(getView().getViewContext()).inflate(R.layout.dataqueryresult_headerview, (ViewGroup) null);
        this.listView.addHeaderView(headerView);
        this.listView.setOnScrollListener(new AbsListView.OnScrollListener() { // from class: com.AoRGMap.edit.TouchCommHisAttEditListener.1
            @Override // android.widget.AbsListView.OnScrollListener
            public void onScrollStateChanged(AbsListView arg0, int arg1) {
            }

            @Override // android.widget.AbsListView.OnScrollListener
            public void onScroll(AbsListView arg0, int arg1, int arg2, int arg3) {
                TouchCommHisAttEditListener.this.mSelPos = arg1;
            }
        });
        this.mSamResultType = (ViewGroup) this.popupWindowView.findViewById(R.id.id_dataquery_Type);
        this.mSamResultType.setVisibility(8);
    }

    @Override // com.AoGIS.edit.TouchSelectDirListener, com.AoGIS.edit.ModifyEditListener
    public boolean onSelectOK() {
        super.onSelectOK();
        this.mSamResultType.setVisibility(8);
        this.mPool = getView().getSelectPool();
        if (this.mPool != null && this.mPool.getPoolEntityCount() >= 1) {
            WorkArea wa = this.mPool.getEntityWorkArea(0);
            if (wa == null) {
                return false;
            }
            String waName = wa.getFileName();
            String szPath = getView().getMap().getItemPathName(getView().getMap().getItemIndex(waName));
            int lastIndex = szPath.lastIndexOf(47);
            if (lastIndex <= 0) {
                lastIndex = szPath.lastIndexOf(92);
            }
            if (lastIndex > 0) {
                this.mMapPath = szPath.substring(0, lastIndex);
            }
            this.mAttrMap = wa.getNamedAttributeStrings(this.mPool.getEntityID(0));
            if (waName.toUpperCase().equals(PRBAreas.m_strPhoto) && this.mAttrMap.containsKey("ROUTECODE") && this.mAttrMap.containsKey("GEOPOINT") && this.mAttrMap.containsKey("NUMBER")) {
                this.mGState.getOnlineDataPack();
                this.mAttrMap.get("ROUTECODE").toString();
                String pntCode = this.mAttrMap.get("GEOPOINT").toString();
                String photoNumber = this.mAttrMap.get("NUMBER").toString();
                String photocode = this.mAttrMap.get("CODE").toString();
                boolean bShowPhoto = true;
                String imagenumber = "";
                if (photoNumber == null || photoNumber.equals("")) {
                    bShowPhoto = false;
                }
                String imgPath = this.mMapPath + File.separator + "images/";
                File imgFile = new File(imgPath);
                if (!imgFile.exists()) {
                    bShowPhoto = false;
                } else {
                    imagenumber = getPhotoCodes(pntCode + "_" + photocode, imgFile);
                }
                if (bShowPhoto && !imagenumber.equals("")) {
                    Intent intentimg = new Intent(getView().getViewContext(), (Class<?>) MyImageView1Activity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", imagenumber);
                    bundle.putString("FolderATH", imgPath);
                    intentimg.putExtras(bundle);
                    getView().getViewContext().startActivity(intentimg);
                } else {
                    this.mArrayList = Convert(this.mAttrMap);
                    this.mAdapter = new SimpleAdapter(getView().getViewContext(), this.mArrayList, R.layout.data_query_result, this.felds, this.vaules);
                    this.listView.setAdapter((ListAdapter) this.mAdapter);
                    if (this.mArrayList.size() >= this.mSelPos) {
                        this.listView.setSelection(this.mSelPos);
                    } else {
                        this.listView.setSelection(0);
                    }
                    this.mAdapter.notifyDataSetChanged();
                    this.popupWindow.showAtLocation((View) getView(), 48, 0, 0);
                }
            } else if ((waName.toUpperCase().equals(PRBAreas.m_strSample) || waName.equals(this.mGState.getDataQuerySamLayer())) && this.mAttrMap.containsKey("CODE")) {
                String dataPath = this.mGState.getOnlineDataPack();
                String samplePath = dataPath + File.separator + "样品分析结果" + File.separator + "RgSampleResult.db";
                final String SamCode = (String) this.mAttrMap.get("CODE");
                File file = new File(samplePath);
                if (file.exists()) {
                    this.mDao = new DataQueryDbDao(getView().getViewContext(), samplePath);
                    List<String> mTypes = this.mDao.getTabSet();
                    if (mTypes.size() > 1) {
                        this.mSamResultType.setVisibility(0);
                        for (int i = 0; i < mTypes.size(); i++) {
                            String Type = mTypes.get(i);
                            Button btn = new Button(getView().getViewContext());
                            btn.setTag(Type);
                            btn.setText(Type);
                            btn.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.edit.TouchCommHisAttEditListener.2
                                @Override // android.view.View.OnClickListener
                                public void onClick(View v) {
                                    String Type2 = v.getTag().toString();
                                    TouchCommHisAttEditListener.this.mArrayList = TouchCommHisAttEditListener.this.mDao.select(Type2, SamCode);
                                    TouchCommHisAttEditListener.this.mAdapter = new SimpleAdapter(TouchCommHisAttEditListener.this.getView().getViewContext(), TouchCommHisAttEditListener.this.mArrayList, R.layout.data_query_result, TouchCommHisAttEditListener.this.felds, TouchCommHisAttEditListener.this.vaules);
                                    TouchCommHisAttEditListener.this.listView.setAdapter((ListAdapter) TouchCommHisAttEditListener.this.mAdapter);
                                    TouchCommHisAttEditListener.this.mAdapter.notifyDataSetChanged();
                                }
                            });
                            this.mSamResultType.addView(btn);
                        }
                        this.mArrayList = this.mDao.select(mTypes.get(0), SamCode);
                    } else if (mTypes.size() > 0) {
                        this.mArrayList = this.mDao.select(mTypes.get(0), SamCode);
                    }
                } else {
                    this.mArrayList = Convert(this.mAttrMap);
                }
                if (this.mArrayList != null) {
                    this.mAdapter = new SimpleAdapter(getView().getViewContext(), this.mArrayList, R.layout.data_query_result, this.felds, this.vaules);
                    this.listView.setAdapter((ListAdapter) this.mAdapter);
                    if (this.mArrayList.size() >= this.mSelPos) {
                        this.listView.setSelection(this.mSelPos);
                    } else {
                        this.listView.setSelection(0);
                    }
                    this.mAdapter.notifyDataSetChanged();
                    this.popupWindow.showAtLocation((View) getView(), 48, 0, 0);
                }
            } else {
                this.mArrayList = Convert(this.mAttrMap);
                if (waName.toUpperCase().equals(PRBAreas.m_strGPoint) && this.mAttrMap.containsKey("ROUTECODE") && this.mAttrMap.containsKey("GEOPOINT")) {
                    this.mGState.getOnlineDataPack();
                    String routeCode = this.mAttrMap.get("ROUTECODE").toString();
                    String pntCode2 = this.mAttrMap.get("GEOPOINT").toString();
                    HashMap<String, String> map = new HashMap<>();
                    map.put("GEOPOINT", pntCode2);
                    String desc = getDescFormDb("GPOINT", this.mMapPath, routeCode, map, "DESC");
                    if (!desc.equals("")) {
                        HashMap<String, Object> itemMap = new HashMap<>();
                        itemMap.put("FIELD", "Desc");
                        itemMap.put("VALUE", desc);
                        this.mArrayList.add(itemMap);
                    }
                } else if (waName.toUpperCase().equals(PRBAreas.m_strBoundary) && this.mAttrMap.containsKey("ROUTECODE") && this.mAttrMap.containsKey("GEOPOINT") && this.mAttrMap.containsKey("SUBPOINT")) {
                    this.mGState.getOnlineDataPack();
                    this.mAttrMap = wa.getNamedAttributeStrings(this.mPool.getEntityID(0));
                    String routeCode2 = this.mAttrMap.get("ROUTECODE").toString();
                    String pntCode3 = this.mAttrMap.get("GEOPOINT").toString();
                    String boudaryCode = this.mAttrMap.get("SUBPOINT").toString();
                    HashMap<String, String> map2 = new HashMap<>();
                    map2.put("GEOPOINT", pntCode3);
                    map2.put("B_CODE", boudaryCode);
                    String desc2 = getDescFormDb("BOUNDARY", this.mMapPath, routeCode2, map2, "DESC");
                    if (!desc2.equals("")) {
                        HashMap<String, Object> itemMap2 = new HashMap<>();
                        itemMap2.put("FIELD", "Desc");
                        itemMap2.put("VALUE", desc2);
                        this.mArrayList.add(itemMap2);
                    }
                } else if (waName.equals(PRBAreas.m_strRouting) && this.mAttrMap.containsKey("ROUTECODE") && this.mAttrMap.containsKey("GEOPOINT") && this.mAttrMap.containsKey("R_CODE")) {
                    this.mGState.getOnlineDataPack();
                    this.mAttrMap = wa.getNamedAttributeStrings(this.mPool.getEntityID(0));
                    String routeCode3 = this.mAttrMap.get("ROUTECODE").toString();
                    String pntCode4 = this.mAttrMap.get("GEOPOINT").toString();
                    String rCode = this.mAttrMap.get("R_CODE").toString();
                    HashMap<String, String> map3 = new HashMap<>();
                    map3.put("GEOPOINT", pntCode4);
                    map3.put("R_CODE", rCode);
                    String desc3 = getDescFormDb("ROUTING", this.mMapPath, routeCode3, map3, "DESC");
                    if (!desc3.equals("")) {
                        HashMap<String, Object> itemMap3 = new HashMap<>();
                        itemMap3.put("FIELD", "Desc");
                        itemMap3.put("VALUE", desc3);
                        this.mArrayList.add(itemMap3);
                    }
                }
                this.mAdapter = new SimpleAdapter(getView().getViewContext(), this.mArrayList, R.layout.data_query_result, this.felds, this.vaules);
                this.listView.setAdapter((ListAdapter) this.mAdapter);
                if (this.mArrayList.size() >= this.mSelPos) {
                    this.listView.setSelection(this.mSelPos);
                } else {
                    this.listView.setSelection(0);
                }
                this.mAdapter.notifyDataSetChanged();
                this.popupWindow.showAtLocation((View) getView(), 48, 0, 0);
            }
        } else {
            dimissPopWindow();
        }
        return true;
    }

    private String getDescFormDb(String table, String dataPath, String routeCode, HashMap<String, String> map, String field) {
        String result = "";
        if (dataPath == null || dataPath.equals("")) {
            return "";
        }
        String dbPath = dataPath + "note" + File.separator + routeCode + ".db";
        File dbFile = new File(dbPath);
        if (dbFile.exists()) {
            this.mDao = new DataQueryDbDao(getView().getViewContext(), dbPath);
            result = this.mDao.select(table, map, field);
        }
        return result;
    }

    private List<Map<String, Object>> Convert(GdbAttributesMap<String, Object> datamap) {
        List<Map<String, Object>> mArrayList = new ArrayList<>();
        Set<String> setKey = datamap.keySet();
        Iterator it = setKey.iterator();
        while (it.hasNext()) {
            Map<String, Object> map = new HashMap<>();
            String fieldString = it.next().toString();
            String fieldVaule = datamap.get(fieldString).toString();
            if (fieldVaule != null && !fieldVaule.equals("")) {
                map.put("FIELD", fieldString);
                map.put("VALUE", datamap.get(fieldString).toString());
                mArrayList.add(map);
            }
        }
        return mArrayList;
    }

    public void dimissPopWindow() {
        if (this.popupWindow != null && this.popupWindow.isShowing()) {
            this.popupWindow.dismiss();
        }
    }

    public String getPhotoCodes(String keyword, File filepath) {
        File[] files;
        String PhotoCodes = "";
        boolean bAdd = false;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        PhotoCodes = getPhotoCodes(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.toLowerCase().indexOf("jpg") > -1) {
                        String Temp = strname.substring(0, strname.lastIndexOf("."));
                        if (bAdd) {
                            PhotoCodes = PhotoCodes + "," + Temp;
                        } else {
                            PhotoCodes = PhotoCodes + Temp;
                        }
                        bAdd = true;
                    }
                }
            }
        }
        return PhotoCodes;
    }
}
