package com.AoRGMap.ExtLayer;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.prb.DrawSketchActivity;
import com.AoRGMap.prb.PrbAttributeActivity;
import java.io.File;
import java.util.ArrayList;
import java.util.Map;

/* loaded from: classes.dex */
public class AttLargeScaleActivity extends PrbAttributeActivity {
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private String mGlobalDataPath = AoRGMapActivity.getCurrentMapPath();
    AttributeItem mItemName = null;

    public AttLargeScaleActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strLargeScale));
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        super.onClickCancel();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
        super.onClickSave();
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        finish();
        super.onClickOK();
    }

    private void initMainView(ViewGroup container) {
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_MAPCODE, R.string.RGMAP_PROMPT_LARGESCALE_MAPCODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item.getEditorEditText().setEnabled(false);
        this.itemList.add(item);
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_AREANAME, R.string.RGMAP_PROMPT_LARGESCALE_AREANAME, EditTextEditorInfo.textEditor(), map, (String) null);
        item2.getEditorEditText().setEnabled(false);
        this.itemList.add(item2);
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_AREACODE, R.string.RGMAP_PROMPT_LARGESCALE_AREACODE, EditTextEditorInfo.textEditor(), map, (String) null);
        item3.getEditorEditText().setEnabled(false);
        this.itemList.add(item3);
        this.mItemName = item3;
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_AREASCALE, R.string.RGMAP_PROMPT_LARGESCALE_AREASCALE, EditTextEditorInfo.textEditor(), map, (String) null);
        item4.setPropDictName(getResources().getString(R.string.dic_boundary_type));
        item4.getEditorEditText().setEnabled(false);
        this.itemList.add(item4);
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_X1, R.string.RGMAP_PROMPT_LARGESCALE_X1, EditTextEditorInfo.textEditor(), map, (String) null);
        item5.getEditorEditText().setEnabled(false);
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_Y1, R.string.RGMAP_PROMPT_LARGESCALE_Y1, EditTextEditorInfo.textEditor(), map, (String) null);
        item6.getEditorEditText().setEnabled(false);
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_X2, R.string.RGMAP_PROMPT_LARGESCALE_X2, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.getEditorEditText().setEnabled(false);
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_Y2, R.string.RGMAP_PROMPT_LARGESCALE_Y2, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.getEditorEditText().setEnabled(false);
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_GEOLOGICALNOTE, R.string.RGMAP_PROMPT_LARGESCALE_GEOLOGICALNOTE, EditTextEditorInfo.textEditor(), map, (String) null);
        item9.getEditorEditText().setEnabled(false);
        this.itemList.add(item9);
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_CHEMICALNOTE, R.string.RGMAP_PROMPT_LARGESCALE_CHEMICALNOTE, EditTextEditorInfo.textEditor(), map, (String) null);
        item10.getEditorEditText().setEnabled(false);
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LARGESCALE_GEOPHYSICALNOTE, R.string.RGMAP_PROMPT_LARGESCALE_GEOPHYSICALNOTE, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.getEditorEditText().setEnabled(false);
        this.itemList.add(item11);
        AttributeItem.AttributeItemParams picDesc = new AttributeItem.AttributeItemParams();
        picDesc.label = "单击浏览大比例综合地质图";
        picDesc.clickable = new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.ExtLayer.AttLargeScaleActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String tmpString = AttLargeScaleActivity.this.mItemName.getEditorEditText().getText().toString();
                AttLargeScaleActivity.this.mGlobalDataPath = AttLargeScaleActivity.this.mGState.getOnlineDataPack();
                String mGpjPath = AttLargeScaleActivity.this.mGlobalDataPath + File.separator + "大比例尺综合图" + File.separator + tmpString;
                String strTemp = mGpjPath + File.separator + tmpString + ".GPJ";
                File file = new File(strTemp);
                if (file.exists()) {
                    String mGpjPath2 = mGpjPath + File.separator + tmpString;
                    AttLargeScaleActivity.this.mGState.setiDrawSwkrchGpsPos(0);
                    Intent intent = new Intent(AttLargeScaleActivity.this, (Class<?>) DrawSketchActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("SketchMapName", mGpjPath2);
                    intent.putExtras(bundle);
                    AttLargeScaleActivity.this.startActivity(intent);
                }
            }
        }, null);
        group.addItem(picDesc);
        container.addView(group.getInnerView());
    }
}
