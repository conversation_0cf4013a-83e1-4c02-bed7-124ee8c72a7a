package com.AoRGMap.threeDmodel;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.render.AoSurfaceView;
import com.AoGIS.tms.tileprovider.tilesource.WebSourceTileLayer;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.SmartService.WebServiceUrl;
import com.AoRGMap.edit.SelectMarkerListener;
import com.AoRGMap.net.Marker;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class Model3DFrameView02 extends Fragment {
    double m_dCenterLon;
    WorkAreaParams.CoordSysType m_CoordSysType = WorkAreaParams.CoordSysType.Projection;
    WorkAreaParams.EarthType m_EarthType = WorkAreaParams.EarthType.WGS84;
    WorkAreaParams.ProjectionType m_PrjType = WorkAreaParams.ProjectionType.WebMercator;
    double m_dRate = 1.0d;
    int m_iCoordUnitRate = 1;
    double m_mapdy = 0.0d;
    double m_mapdx = 0.0d;
    private View mViewContent02 = null;
    private Model3DMapView m3DMapView = null;
    private String mCenterLon = "";
    private String mCenterLat = "";
    private final int GET_SUCCEED = 9999;
    private final int GET_ERROR = 9998;
    private Handler mHandler = new Handler() { // from class: com.AoRGMap.threeDmodel.Model3DFrameView02.1
        @Override // android.os.Handler
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 9998:
                    Toast.makeText(Model3DFrameView02.this.getActivity(), "暂时未实现！", 0).show();
                    break;
                case 9999:
                    SelectMarkerListener listener = new SelectMarkerListener(Model3DFrameView02.this.m3DMapView);
                    Model3DFrameView02.this.m3DMapView.setTouchListener(listener);
                    break;
            }
        }
    };

    @Override // android.support.v4.app.Fragment
    @Nullable
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        this.mViewContent02 = inflater.inflate(R.layout.model3d_layout_view02, container, false);
        InitView();
        InitData();
        return this.mViewContent02;
    }

    private void InitView() {
        this.m3DMapView = (Model3DMapView) this.mViewContent02.findViewById(R.id.id_model3d_mapview);
        WebSourceTileLayer ws = new WebSourceTileLayer(WebServiceUrl.GOOGLE_VECTOR_NAME, WebServiceUrl.GOOGLE_VECTOR_URL);
        ws.setName(WebServiceUrl.GOOGLE_VECTOR_NAME).setAttribution("© Google Contributors").setMinimumZoomLevel(1.0f).setMaximumZoomLevel(18.0f);
        this.m3DMapView.setTileSource(ws);
        this.m3DMapView.setTileProjection(AoSurfaceView.TileProjection.TileMercator);
        SelectMarkerListener selectMarkerListener = new SelectMarkerListener(this.m3DMapView);
        this.m3DMapView.setTouchListener(selectMarkerListener);
    }

    /* JADX WARN: Type inference failed for: r0v5, types: [com.AoRGMap.threeDmodel.Model3DFrameView02$2] */
    private void InitData() {
        if (IsNetworkConnected(getActivity()).booleanValue()) {
            new Thread() { // from class: com.AoRGMap.threeDmodel.Model3DFrameView02.2
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() {
                    if (Model3DFrameView02.this.getDatas()) {
                        Model3DFrameView02.this.mHandler.sendEmptyMessage(9999);
                    } else {
                        Toast.makeText(Model3DFrameView02.this.getActivity(), R.string.RGMAP_PROMPT_OPENNETWORK, 0).show();
                    }
                }
            }.start();
        } else {
            Toast.makeText(getActivity(), R.string.RGMAP_PROMPT_OPENNETWORK, 0).show();
        }
    }

    public Boolean IsNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return Boolean.valueOf(mNetworkInfo.isAvailable());
            }
        }
        return false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean getDatas() {
        JSONArray mdlDatas;
        GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
        mGState.claerMarkers();
        try {
            URL url = new URL("http://219.142.81.184/publishservice/rest/model3d/get/all");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead == -1) {
                    break;
                }
                baos.write(buffer, 0, hasRead);
            }
            inSteam.close();
            String string = new String(baos.toByteArray());
            string.length();
            if (string.equals("")) {
                return false;
            }
            try {
                JSONObject mainObj = new JSONObject(string);
                if (mainObj != null) {
                    int state = mainObj.getInt("state");
                    if (state == 1) {
                        String ObjData = mainObj.getString("t");
                        if (!ObjData.equals("") && (mdlDatas = new JSONArray(ObjData)) != null) {
                            for (int i = 0; i < mdlDatas.length(); i++) {
                                JSONObject mdlData = mdlDatas.getJSONObject(i);
                                if (mdlData != null) {
                                    String strUrl = "http://219.142.81.39:8080/max3d/App/index.html?alias=" + mdlData.getString("alias") + "&path=" + mdlData.getString("scene");
                                    String szdLat = mdlData.getString("lat");
                                    String szdLon = mdlData.getString("lon");
                                    if (szdLat != null && !szdLat.equals("") && szdLon != null && !szdLon.equals("")) {
                                        double[] dPos = dealWithPosition(Double.valueOf(szdLon).doubleValue(), Double.valueOf(szdLat).doubleValue(), true, true);
                                        Bitmap bitmap2 = BitmapFactory.decodeResource(getResources(), R.drawable.loc);
                                        Marker marker = new Marker(getActivity(), dPos[0], dPos[1], bitmap2, strUrl);
                                        mGState.addMarker(marker);
                                    }
                                }
                            }
                        }
                    }
                }
                return true;
            } catch (JSONException e) {
                e.printStackTrace();
                return false;
            }
        } catch (Exception e2) {
            e2.printStackTrace();
            return false;
        }
    }

    public double[] dealWithPosition(double x, double y, boolean isProjection, boolean doRate) {
        double[] dPos = new double[2];
        if (!isProjection || this.m_PrjType == WorkAreaParams.ProjectionType.None) {
            dPos[0] = x;
            dPos[1] = y;
        } else {
            double x2 = (x / 180.0d) * 3.141592653589793d;
            double y2 = (y / 180.0d) * 3.141592653589793d;
            if (this.m_PrjType == WorkAreaParams.ProjectionType.Gauss) {
                dPos = ProjectionHelper.Gauss(this.m_dCenterLon, x2, y2, this.m_EarthType);
            } else if (this.m_PrjType == WorkAreaParams.ProjectionType.UTM) {
                dPos = ProjectionHelper.UTM(this.m_dCenterLon, x2, y2, this.m_EarthType, this.m_mapdx, this.m_mapdy);
            } else if (this.m_PrjType == WorkAreaParams.ProjectionType.WebMercator) {
                dPos = ProjectionHelper.WebMercator(x2, y2, (byte) 1);
            }
            if (dPos != null && doRate) {
                dPos[0] = (dPos[0] / this.m_dRate) * this.m_iCoordUnitRate;
                dPos[1] = (dPos[1] / this.m_dRate) * this.m_iCoordUnitRate;
            }
        }
        return dPos;
    }

    public void goToSelectPos() {
        this.mCenterLon = ((Model3DMainActivity) getActivity()).getmCenterLon();
        this.mCenterLat = ((Model3DMainActivity) getActivity()).getmCenterLat();
        if (this.mCenterLon != null && !this.mCenterLon.equals("") && this.mCenterLat != null && !this.mCenterLat.equals("")) {
            double[] dPos = dealWithPosition(Double.valueOf(this.mCenterLon).doubleValue(), Double.valueOf(this.mCenterLat).doubleValue(), true, true);
            this.m3DMapView.gotoView(dPos[0], dPos[1]);
        }
    }
}
