package com.AoRGMap.net;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.text.Html;
import java.io.IOException;
import org.ksoap2.serialization.SoapObject;
import org.ksoap2.serialization.SoapSerializationEnvelope;
import org.ksoap2.transport.HttpResponseException;
import org.ksoap2.transport.HttpTransportSE;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class WebServiceThread extends Thread {
    private static final String METHOD_GETHTML = "GPSPosition";
    private static final String NAMESPACE = "http://www.gsigrid.cgs.gov.cn/";
    private static final String SERVICEURL = "http://www.gsigrid.cgs.gov.cn/HelloUser/Service.asmx";
    private static String mSoapAction = "";
    private static SoapObject mSoapObject = null;
    private static HttpTransportSE mHttpTransportSE = null;
    private static SoapSerializationEnvelope mSoapEnvelope = null;

    public WebServiceThread(String szVerificationcode, int dataType, double longitude, double latitude, double altitude) {
        mHttpTransportSE = new HttpTransportSE(SERVICEURL);
        mSoapEnvelope = new SoapSerializationEnvelope(110);
    }

    public Boolean isNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return Boolean.valueOf(mNetworkInfo.isAvailable());
            }
        }
        return false;
    }

    private Html doGetHtml(String code, int type, double Longitude, double Latitude, double Altitude) {
        mSoapObject = new SoapObject(NAMESPACE, METHOD_GETHTML);
        mSoapObject.addProperty("code", code);
        mSoapObject.addProperty("type", Integer.valueOf(type));
        mSoapObject.addProperty("x", Double.valueOf(Longitude));
        mSoapObject.addProperty("y", Double.valueOf(Latitude));
        mSoapObject.addProperty("z", Double.valueOf(Altitude));
        mSoapEnvelope.bodyOut = mSoapObject;
        mSoapEnvelope.dotNet = true;
        mSoapEnvelope.setOutputSoapObject(mSoapObject);
        mSoapAction = "http://www.gsigrid.cgs.gov.cn/GPSPosition";
        try {
            mHttpTransportSE.call(mSoapAction, mSoapEnvelope);
            SoapObject soapObject = (SoapObject) mSoapEnvelope.bodyIn;
            if (soapObject != null) {
                soapObject.getProperty("GetPosPictureResult").toString();
            }
        } catch (HttpResponseException e) {
            e.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        } catch (XmlPullParserException e3) {
            e3.printStackTrace();
        }
        return null;
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public void run() {
    }
}
