package com.AoRGMap.net;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Base64;
import android.util.Log;
import com.baidu.speech.utils.AsrError;
import com.baidu.tts.loopj.RequestParams;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.ksoap2.SoapFault;
import org.ksoap2.serialization.SoapObject;
import org.ksoap2.serialization.SoapSerializationEnvelope;
import org.ksoap2.transport.HttpResponseException;
import org.ksoap2.transport.HttpTransportSE;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class WebServiceUtils {
    private static final String METHOD_POSTPOSIAMGE = "GetPosPicture";
    private static final String METHOD_POSTUSERPOS2 = "GPSPosition";
    private static final String NAMESPACE = "http://www.gsigrid.cgs.gov.cn/";
    private static final String SERVICEURL = "http://www.gsigrid.cgs.gov.cn/HelloUser/Service.asmx";
    private static String mSoapAction = "";
    private static SoapObject mSoapObject = null;
    private static HttpTransportSE mHttpTransportSE = null;
    private static SoapSerializationEnvelope mSoapEnvelope = null;

    public static String doPost(List<NameValuePair> params, String url) throws Exception {
        HttpClient httpClient = new DefaultHttpClient();
        HttpPost httpPost = new HttpPost(url);
        if (params != null) {
            HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
            httpPost.setEntity(entity);
        }
        HttpResponse httpResp = httpClient.execute(httpPost);
        httpResp.getStatusLine().getStatusCode();
        if (httpResp.getStatusLine().getStatusCode() == 200) {
            String result = EntityUtils.toString(httpResp.getEntity(), "UTF-8");
            return result;
        }
        Log.i("HttpPost", "HttpPost方式请求失败");
        return null;
    }

    public void doPost3() throws UnsupportedEncodingException {
        try {
            URL httUrl = new URL("http://10.90.3.181:8080/BigDataService/rest/folder/archive/search?searchType=content&filter=89.25227,29.11059");
            HttpURLConnection connection = (HttpURLConnection) httUrl.openConnection();
            connection.setRequestMethod(HttpPost.METHOD_NAME);
            connection.setReadTimeout(5000);
            connection.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuffer sbBuffer = null;
            while (true) {
                String str = reader.readLine();
                if (str != null) {
                    sbBuffer.append(str);
                } else {
                    System.out.print(sbBuffer.toString());
                    return;
                }
            }
        } catch (Exception e) {
            e.getMessage();
            e.getMessage();
        }
    }

    public static String readParse(String urlPath) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        URL url = new URL(urlPath);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        InputStream inStream = conn.getInputStream();
        while (true) {
            int len = inStream.read(data);
            if (len != -1) {
                outStream.write(data, 0, len);
            } else {
                inStream.close();
                return new String(outStream.toByteArray());
            }
        }
    }

    public static String doPost1(String url, List<NameValuePair> nameValuePairs) throws ClientProtocolException, IOException {
        DefaultHttpClient defaultHttpClient = new DefaultHttpClient();
        HttpPost httppost = new HttpPost(url);
        httppost.setHeader("content-type", RequestParams.APPLICATION_JSON);
        if (nameValuePairs != null) {
            StringEntity entity = new StringEntity("这里是JSON数据}", "utf-8");
            entity.setContentType(RequestParams.APPLICATION_JSON);
            entity.setContentEncoding("utf-8");
            httppost.setEntity(entity);
        }
        HttpResponse response = defaultHttpClient.execute(httppost);
        if (response.getStatusLine().getStatusCode() == 404) {
            return null;
        }
        String result = EntityUtils.toString(response.getEntity());
        return result;
    }

    public byte[] onHttpRequest(String strURL) {
        try {
            URL url = new URL(strURL);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(AsrError.ERROR_AUDIO_INCORRECT);
            conn.setRequestMethod("GET");
            conn.setUseCaches(true);
            conn.connect();
            InputStream inSteam = conn.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while (true) {
                int hasRead = inSteam.read(buffer);
                if (hasRead != -1) {
                    baos.write(buffer, 0, hasRead);
                } else {
                    inSteam.close();
                    return baos.toByteArray();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public WebServiceUtils() {
        mHttpTransportSE = new HttpTransportSE(SERVICEURL);
        mSoapEnvelope = new SoapSerializationEnvelope(110);
    }

    public Boolean isNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return Boolean.valueOf(mNetworkInfo.isAvailable());
            }
        }
        return false;
    }

    public static String upLoadUserPos(String longitude, String latitude) throws SoapFault, UnsupportedEncodingException {
        mSoapObject = new SoapObject(NAMESPACE, METHOD_POSTUSERPOS2);
        mSoapObject.addProperty("x", longitude);
        mSoapObject.addProperty("y", latitude);
        mSoapEnvelope.bodyOut = mSoapObject;
        mSoapEnvelope.dotNet = true;
        mSoapEnvelope.setOutputSoapObject(mSoapObject);
        mSoapAction = "http://www.gsigrid.cgs.gov.cn/GPSPosition";
        try {
            mHttpTransportSE.call(mSoapAction, mSoapEnvelope);
        } catch (HttpResponseException e) {
            e.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        } catch (XmlPullParserException e3) {
            e3.printStackTrace();
        }
        SoapObject resultObject = (SoapObject) mSoapEnvelope.bodyIn;
        if (resultObject == null) {
            return "";
        }
        String resultString = resultObject.getProperty("GPSPositionResult").toString();
        return resultString;
    }

    public static Bitmap downLoadPosWebImage(String longitude, String latitude, int width, int height) {
        mSoapObject = new SoapObject(NAMESPACE, METHOD_POSTPOSIAMGE);
        mSoapObject.addProperty("x", longitude);
        mSoapObject.addProperty("y", latitude);
        mSoapObject.addProperty("w", Integer.valueOf(width));
        mSoapObject.addProperty("h", Integer.valueOf(height));
        mSoapEnvelope.bodyOut = mSoapObject;
        mSoapEnvelope.dotNet = true;
        mSoapEnvelope.setOutputSoapObject(mSoapObject);
        mSoapAction = "http://www.gsigrid.cgs.gov.cn/GetPosPicture";
        try {
            mHttpTransportSE.call(mSoapAction, mSoapEnvelope);
        } catch (HttpResponseException e) {
            e.printStackTrace();
        } catch (IOException e2) {
            e2.printStackTrace();
        } catch (XmlPullParserException e3) {
            e3.printStackTrace();
        }
        SoapObject resultObject = (SoapObject) mSoapEnvelope.bodyIn;
        if (resultObject == null) {
            return null;
        }
        String info = resultObject.getProperty("GetPosPictureResult").toString();
        byte[] bitmapArray = Base64.decode(info, 0);
        Bitmap bitmap = BitmapFactory.decodeByteArray(bitmapArray, 0, bitmapArray.length);
        return bitmap;
    }
}
