package com.AoRGMap.camera;

import android.app.Activity;
import android.graphics.Bitmap;
import android.hardware.Camera;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import com.AoRGMap.R;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/* loaded from: classes.dex */
public class CameraTakerActivity extends Activity implements View.OnClickListener {
    public static final String JPGPATH = "JPGPANTH";
    private ImageView mImageView;
    private SurfaceHolder mSurfaceHolder;
    private SurfaceView mSurfaceView;
    private ImageView mtticImageView;
    private Camera mCamera = null;
    private boolean mPreviewRunning = false;
    int mitype = 0;
    byte[] mImagedata = null;
    private Button mSaveButton = null;
    private Button mCacelButton = null;
    private Bitmap mbitmap = null;
    private boolean mbDelFinish = true;
    int miZoom = 0;
    private String mJpgPathString = "";
    Camera.AutoFocusCallback mAutoFocusCallback = new Camera.AutoFocusCallback() { // from class: com.AoRGMap.camera.CameraTakerActivity.4
        @Override // android.hardware.Camera.AutoFocusCallback
        public void onAutoFocus(boolean arg0, Camera arg1) {
        }
    };
    SurfaceHolder.Callback surfaceCallback = new SurfaceHolder.Callback() { // from class: com.AoRGMap.camera.CameraTakerActivity.5
        @Override // android.view.SurfaceHolder.Callback
        public void surfaceDestroyed(SurfaceHolder arg0) {
            if (CameraTakerActivity.this.mCamera != null) {
                CameraTakerActivity.this.mCamera.release();
                CameraTakerActivity.this.mCamera = null;
            }
        }

        @Override // android.view.SurfaceHolder.Callback
        public void surfaceCreated(SurfaceHolder arg0) {
            if (CameraTakerActivity.this.mCamera != null) {
                CameraTakerActivity.this.mCamera.release();
                CameraTakerActivity.this.mCamera = null;
            }
            CameraTakerActivity.this.mCamera = Camera.open();
            try {
                CameraTakerActivity.this.mCamera.setPreviewDisplay(CameraTakerActivity.this.mSurfaceHolder);
            } catch (IOException e) {
                CameraTakerActivity.this.mCamera.release();
                CameraTakerActivity.this.mCamera = null;
            }
        }

        @Override // android.view.SurfaceHolder.Callback
        public void surfaceChanged(SurfaceHolder arg0, int arg1, int arg2, int arg3) {
            Camera.Parameters parameters = CameraTakerActivity.this.mCamera.getParameters();
            parameters.setPictureFormat(256);
            parameters.setRotation(90);
            CameraTakerActivity.this.mCamera.setDisplayOrientation(0);
            CameraTakerActivity.this.mCamera.setParameters(parameters);
            CameraTakerActivity.this.mCamera.startPreview();
            CameraTakerActivity.this.miZoom = parameters.getMaxZoom();
            CameraTakerActivity.this.mPreviewRunning = true;
        }
    };
    Camera.PictureCallback mPictureCallback = new Camera.PictureCallback() { // from class: com.AoRGMap.camera.CameraTakerActivity.6
        @Override // android.hardware.Camera.PictureCallback
        public void onPictureTaken(byte[] data, Camera camera) {
            if (data != null) {
                CameraTakerActivity.this.mCacelButton.setVisibility(0);
                CameraTakerActivity.this.mSaveButton.setVisibility(0);
                CameraTakerActivity.this.mtticImageView.setVisibility(8);
                CameraTakerActivity.this.mImagedata = data;
                CameraTakerActivity.this.mPreviewRunning = false;
            }
        }
    };
    Camera.ShutterCallback mShutterCallback = new Camera.ShutterCallback() { // from class: com.AoRGMap.camera.CameraTakerActivity.7
        @Override // android.hardware.Camera.ShutterCallback
        public void onShutter() {
            Log.v("ShutterCallback", "…onShutter…");
        }
    };

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setRequestedOrientation(0);
        setContentView(R.layout.photocamera);
        Bundle bundle = getIntent().getExtras();
        this.mJpgPathString = bundle.getString("JPGPANTH");
        this.mSurfaceView = (SurfaceView) findViewById(R.id.id_photo_surfaceView);
        this.mSurfaceView.setOnClickListener(this);
        this.mSurfaceHolder = this.mSurfaceView.getHolder();
        this.mSurfaceHolder.setType(3);
        this.mSurfaceHolder.addCallback(this.surfaceCallback);
        this.mSaveButton = (Button) findViewById(R.id.id_photo_save);
        this.mSaveButton.setVisibility(8);
        this.mSaveButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTakerActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTakerActivity.this.new SavePictureTask().execute(CameraTakerActivity.this.mImagedata);
                CameraTakerActivity.this.mbDelFinish = true;
                CameraTakerActivity.this.setResult(-1);
                CameraTakerActivity.this.finish();
            }
        });
        this.mCacelButton = (Button) findViewById(R.id.id_photo_cancel);
        this.mCacelButton.setVisibility(8);
        this.mCacelButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTakerActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTakerActivity.this.mCacelButton.setVisibility(8);
                CameraTakerActivity.this.mSaveButton.setVisibility(8);
                CameraTakerActivity.this.mtticImageView.setVisibility(0);
                CameraTakerActivity.this.mCamera.startPreview();
                CameraTakerActivity.this.mbDelFinish = true;
                CameraTakerActivity.this.mPreviewRunning = true;
            }
        });
        this.mtticImageView = (ImageView) findViewById(R.id.id_imageView_takepic);
        this.mtticImageView.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTakerActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTakerActivity.this.takePic();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void takePic() {
        if (this.mCamera != null && this.mPreviewRunning && this.mbDelFinish) {
            this.mbDelFinish = false;
            this.mCamera.autoFocus(this.mAutoFocusCallback);
            this.mCamera.takePicture(this.mShutterCallback, null, this.mPictureCallback);
        }
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View arg0) {
        if (this.mCamera != null && this.mPreviewRunning) {
            this.mCamera.autoFocus(this.mAutoFocusCallback);
        }
    }

    class SavePictureTask extends AsyncTask<byte[], String, String> {
        SavePictureTask() {
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // android.os.AsyncTask
        public String doInBackground(byte[]... arg0) {
            File picture = new File(CameraTakerActivity.this.mJpgPathString);
            try {
                FileOutputStream fos = new FileOutputStream(picture.getPath());
                fos.write(arg0[0]);
                fos.close();
                return null;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }
}
