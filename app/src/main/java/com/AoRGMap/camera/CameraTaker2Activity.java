package com.AoRGMap.camera;

import android.app.Activity;
import android.graphics.Bitmap;
import android.hardware.Camera;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import com.AoRGMap.R;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/* loaded from: classes.dex */
public class CameraTaker2Activity extends Activity implements View.OnClickListener {
    public static final String JPGPATH = "JPGPANTH";
    private boolean flag;
    private ImageView mImageView;
    private SurfaceHolder mSurfaceHolder;
    private SurfaceView mSurfaceView;
    private Camera mCamera = null;
    private boolean mPreviewRunning = false;
    int mitype = 0;
    byte[] mImagedata = null;
    private Button mSaveButton = null;
    private Button mCacelButton = null;
    private Button mtticImageView = null;
    private Bitmap mbitmap = null;
    private boolean mbDelFinish = true;
    int miZoom = 0;
    private String mJpgPathString = "";
    Camera.AutoFocusCallback mAutoFocusCallback = new Camera.AutoFocusCallback() { // from class: com.AoRGMap.camera.CameraTaker2Activity.4
        @Override // android.hardware.Camera.AutoFocusCallback
        public void onAutoFocus(boolean arg0, Camera arg1) {
        }
    };
    SurfaceHolder.Callback surfaceCallback = new SurfaceHolder.Callback() { // from class: com.AoRGMap.camera.CameraTaker2Activity.5
        @Override // android.view.SurfaceHolder.Callback
        public void surfaceDestroyed(SurfaceHolder arg0) {
            if (CameraTaker2Activity.this.mCamera != null) {
                CameraTaker2Activity.this.mCamera.release();
                CameraTaker2Activity.this.mCamera = null;
            }
        }

        @Override // android.view.SurfaceHolder.Callback
        public void surfaceCreated(SurfaceHolder arg0) {
            if (CameraTaker2Activity.this.mCamera != null) {
                CameraTaker2Activity.this.mCamera.release();
                CameraTaker2Activity.this.mCamera = null;
            }
            CameraTaker2Activity.this.mCamera = Camera.open();
            try {
                CameraTaker2Activity.this.mCamera.setPreviewDisplay(CameraTaker2Activity.this.mSurfaceHolder);
            } catch (IOException e) {
                CameraTaker2Activity.this.mCamera.release();
                CameraTaker2Activity.this.mCamera = null;
            }
        }

        @Override // android.view.SurfaceHolder.Callback
        public void surfaceChanged(SurfaceHolder arg0, int arg1, int arg2, int arg3) {
            int PreviewWidth = 0;
            int PreviewHeight = 0;
            Camera.Parameters parameters = CameraTaker2Activity.this.mCamera.getParameters();
            List<Camera.Size> sizeList = parameters.getSupportedPreviewSizes();
            if (sizeList.size() > 1) {
                for (Camera.Size cur : sizeList) {
                    if (cur.width >= PreviewWidth && cur.height >= PreviewHeight) {
                        PreviewWidth = cur.width;
                        PreviewHeight = cur.height;
                    }
                }
            }
            parameters.setPreviewSize(PreviewWidth, PreviewHeight);
            parameters.setPictureFormat(256);
            parameters.set("jpeg-quality", 100);
            parameters.setPictureSize(PreviewWidth, PreviewHeight);
            if (CameraTaker2Activity.this.getResources().getConfiguration().orientation == 1) {
                CameraTaker2Activity.this.mCamera.setDisplayOrientation(90);
                CameraTaker2Activity.this.flag = true;
                Log.i("por", "1");
            }
            if (CameraTaker2Activity.this.getResources().getConfiguration().orientation == 2) {
                parameters.set("orientation", "landscape");
                parameters.set("rotation", 90);
                CameraTaker2Activity.this.mCamera.setDisplayOrientation(0);
                CameraTaker2Activity.this.flag = false;
                Log.i("orientation", "1");
            }
            CameraTaker2Activity.this.mCamera.setParameters(parameters);
            CameraTaker2Activity.this.mCamera.startPreview();
            CameraTaker2Activity.this.mPreviewRunning = true;
        }
    };
    Camera.PictureCallback mPictureCallback = new Camera.PictureCallback() { // from class: com.AoRGMap.camera.CameraTaker2Activity.6
        @Override // android.hardware.Camera.PictureCallback
        public void onPictureTaken(byte[] data, Camera camera) {
            if (data != null) {
                CameraTaker2Activity.this.mCacelButton.setVisibility(0);
                CameraTaker2Activity.this.mSaveButton.setVisibility(0);
                CameraTaker2Activity.this.mtticImageView.setVisibility(8);
                CameraTaker2Activity.this.mImagedata = data;
                CameraTaker2Activity.this.mPreviewRunning = false;
            }
        }
    };
    Camera.ShutterCallback mShutterCallback = new Camera.ShutterCallback() { // from class: com.AoRGMap.camera.CameraTaker2Activity.7
        @Override // android.hardware.Camera.ShutterCallback
        public void onShutter() {
            Log.v("ShutterCallback", "…onShutter…");
        }
    };

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.extphotocamera);
        Bundle bundle = getIntent().getExtras();
        this.mJpgPathString = bundle.getString("JPGPANTH");
        this.mSurfaceView = (SurfaceView) findViewById(R.id.id_photo_surfaceView);
        this.mSaveButton = (Button) findViewById(R.id.id_photo_save);
        this.mSaveButton.setVisibility(8);
        this.mCacelButton = (Button) findViewById(R.id.id_photo_cancel);
        this.mCacelButton.setVisibility(8);
        this.mtticImageView = (Button) findViewById(R.id.id_imageView_takepic);
        this.mSurfaceView.setOnClickListener(this);
        this.mSurfaceHolder = this.mSurfaceView.getHolder();
        this.mSurfaceHolder.setType(3);
        this.mSurfaceHolder.addCallback(this.surfaceCallback);
        this.mSaveButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTaker2Activity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTaker2Activity.this.new SavePictureTask().execute(CameraTaker2Activity.this.mImagedata);
                CameraTaker2Activity.this.mbDelFinish = true;
                CameraTaker2Activity.this.setResult(-1);
                CameraTaker2Activity.this.finish();
            }
        });
        this.mCacelButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTaker2Activity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTaker2Activity.this.mCacelButton.setVisibility(8);
                CameraTaker2Activity.this.mSaveButton.setVisibility(8);
                CameraTaker2Activity.this.mtticImageView.setVisibility(0);
                CameraTaker2Activity.this.mCamera.startPreview();
                CameraTaker2Activity.this.mbDelFinish = true;
                CameraTaker2Activity.this.mPreviewRunning = true;
            }
        });
        this.mtticImageView.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.camera.CameraTaker2Activity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CameraTaker2Activity.this.takePic();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void takePic() {
        if (this.mCamera != null && this.mPreviewRunning && this.mbDelFinish) {
            this.mbDelFinish = false;
            this.mCamera.autoFocus(this.mAutoFocusCallback);
            this.mCamera.takePicture(this.mShutterCallback, null, this.mPictureCallback);
        }
    }

    @Override // android.view.View.OnClickListener
    public void onClick(View arg0) {
        if (this.mCamera != null && this.mPreviewRunning) {
            this.mCamera.autoFocus(this.mAutoFocusCallback);
        }
    }

    class SavePictureTask extends AsyncTask<byte[], String, String> {
        SavePictureTask() {
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // android.os.AsyncTask
        public String doInBackground(byte[]... arg0) {
            File picture = new File(CameraTaker2Activity.this.mJpgPathString);
            try {
                FileOutputStream fos = new FileOutputStream(picture.getPath());
                fos.write(arg0[0]);
                fos.flush();
                fos.close();
                return null;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }
}
