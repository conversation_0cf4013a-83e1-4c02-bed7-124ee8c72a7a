package com.AoRGMap.prb;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SimpleAdapter;
import android.widget.Toast;
import com.AoDevBase.util.UILanguageUtil;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/* loaded from: classes.dex */
public class AttExtFileDataActivity extends Activity {
    public static final String PARAM_BOOL_IS_NEW_ATTRIBUTE = "isNew";
    public static final String PARAM_STRING_FILE_PATH = "FilePath";
    public static final String PARAM_STRING_FLDNAME = "FldName";
    public static final String PARAM_STRING_KEYWORD = "KeyWord";
    public static final String PARAM_STRING_TABLE_NAME = "TableName";
    private SimpleAdapter mAdapter;
    private String mFldName;
    private String mKeyString;
    private String mTblName;
    private String mFilePath = AoRGMapActivity.getCurrentMapPath() + "/images/";
    private int mMaxNum = 0;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private ArrayList<Map<String, Object>> mArrayList = null;
    private String FILEPATH = "Filefath";
    int mSelPos = -1;
    private ListView mListView = null;
    private RadioButton mRdAddButton = null;
    private RadioButton mRdDelButton = null;
    private RadioGroup mRgMenu = null;

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.att_geopoint_file_desc);
        this.mArrayList = new ArrayList<>();
        InitView();
        InitListener();
        InitData();
    }

    private void InitView() {
        this.mRgMenu = (RadioGroup) findViewById(R.id.id_radioGroup_file_menu);
        this.mListView = (ListView) findViewById(R.id.id_listview_geopoint_file_desc);
        this.mRdAddButton = (RadioButton) findViewById(R.id.id_menu_add_file);
        this.mRdDelButton = (RadioButton) findViewById(R.id.id_menu_delete_file);
    }

    private void InitListener() {
        this.mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.prb.AttExtFileDataActivity.1
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> arg0, View arg1, int arg2, long arg3) {
                if (((ListView) arg0).getTag() != null) {
                    ((View) ((ListView) arg0).getTag()).setBackgroundDrawable(null);
                }
                ((ListView) arg0).setTag(arg1);
                arg1.setBackgroundColor(-3355444);
                AttExtFileDataActivity.this.mSelPos = arg2;
            }
        });
        this.mRdAddButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttExtFileDataActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                AttExtFileDataActivity.this.mRgMenu.clearCheck();
                Intent intent = new Intent("android.intent.action.GET_CONTENT");
                intent.setType("*/*");
                intent.addCategory("android.intent.category.OPENABLE");
                AttExtFileDataActivity.this.startActivityForResult(Intent.createChooser(intent, "Select a File to Upload"), 1);
            }
        });
        this.mRdDelButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttExtFileDataActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                AttExtFileDataActivity.this.mRgMenu.clearCheck();
                if (AttExtFileDataActivity.this.mSelPos == -1) {
                    Toast.makeText(AttExtFileDataActivity.this, R.string.RGMAP_PROMPT_DELETEPOS, 0).show();
                } else {
                    new AlertDialog.Builder(AttExtFileDataActivity.this).setTitle("RGMap").setMessage(R.string.RGMAP_PROMPT_DELPROMPT).setNegativeButton(R.string.RGMAP_PROMPT_BUTTONCANCEL, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttExtFileDataActivity.3.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                        }
                    }).setPositiveButton(R.string.RGMAP_PROMPT_BUTTONOK, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttExtFileDataActivity.3.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                            Map<String, Object> map = (Map) AttExtFileDataActivity.this.mArrayList.get(AttExtFileDataActivity.this.mSelPos);
                            Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
                            Map.Entry<String, Object> entry = it.next();
                            String itemString = entry.getValue().toString();
                            String itempathString = AttExtFileDataActivity.this.mFilePath + "/" + itemString;
                            File file = new File(itempathString);
                            AttExtFileDataActivity.this.deleteFile(file);
                            AttExtFileDataActivity.this.mSelPos = -1;
                            AttExtFileDataActivity.this.getData();
                            AttExtFileDataActivity.this.mAdapter.notifyDataSetChanged();
                            if (AttExtFileDataActivity.this.mListView.getTag() != null) {
                                ((View) AttExtFileDataActivity.this.mListView.getTag()).setBackgroundDrawable(null);
                            }
                            AttExtFileDataActivity.this.mSelPos = -1;
                        }
                    }).show();
                }
            }
        });
    }

    private void InitData() {
        Bundle bundle = getIntent().getExtras();
        this.mKeyString = bundle.getString("KeyWord");
        this.mFldName = bundle.getString("FldName");
        this.mTblName = bundle.getString("TableName");
        String tmpStr = bundle.getString("FilePath");
        if (tmpStr != null && !tmpStr.equals("")) {
            this.mFilePath = tmpStr;
        }
        InitFileData();
    }

    private void InitFileData() {
        getData();
        this.mAdapter = new SimpleAdapter(this, this.mArrayList, R.layout.att_geopoint_desclist, new String[]{this.FILEPATH}, new int[]{R.id.id_item_descpath});
        this.mListView.setAdapter((ListAdapter) this.mAdapter);
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        String fileType;
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            Uri uri = data.getData();
            FileUtils fu = new FileUtils();
            String recordPath = fu.getPath(this, uri);
            if (recordPath != null) {
                int dotIndex = recordPath.lastIndexOf(".");
                if (-1 == dotIndex) {
                    fileType = ".file";
                } else {
                    fileType = recordPath.substring(dotIndex, recordPath.length());
                }
                StringBuilder append = new StringBuilder().append(this.mFilePath).append(this.mKeyString).append("_").append(this.mTblName).append("_").append(this.mFldName).append("_");
                int i = this.mMaxNum + 1;
                this.mMaxNum = i;
                String filePath = append.append(String.valueOf(i)).append(fileType).toString();
                moveFile(recordPath, filePath);
                getData();
                this.mAdapter.notifyDataSetChanged();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void getData() {
        File[] files;
        this.mArrayList.clear();
        this.mMaxNum = 0;
        File vFilepath = new File(this.mFilePath);
        String keywords = this.mKeyString + "_" + this.mTblName + "_" + this.mFldName;
        String keywords2 = keywords.toLowerCase();
        if (Environment.getExternalStorageState().equals("mounted") && (files = vFilepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isFile()) {
                    String strname = file.getName().toLowerCase();
                    if (strname.contains(keywords2)) {
                        Map<String, Object> map = new HashMap<>();
                        map.put(this.FILEPATH, strname);
                        this.mArrayList.add(map);
                        String indexString = strname.substring(strname.lastIndexOf("_") + 1, strname.lastIndexOf("."));
                        int idex = Integer.parseInt(indexString);
                        if (this.mMaxNum < idex) {
                            this.mMaxNum = idex;
                        }
                    }
                }
            }
        }
    }

    public void deleteFile(File file) {
        if (file.exists()) {
            if (file.isFile()) {
                file.delete();
            } else if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File file2 : files) {
                    deleteFile(file2);
                }
            }
            file.delete();
        }
    }

    public void moveFile(String oldPath, String newPath) {
        int bytesum = 0;
        try {
            File oldFile = new File(oldPath);
            File newFile = new File(newPath);
            if (!newFile.exists()) {
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                while (true) {
                    int byteread = inStream.read(buffer);
                    if (byteread != -1) {
                        bytesum += byteread;
                        System.out.println(bytesum);
                        fs.write(buffer, 0, byteread);
                    } else {
                        inStream.close();
                        oldFile.delete();
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public class FileUtils {
        public FileUtils() {
        }

        public String getPath(Context context, Uri uri) {
            if ("content".equalsIgnoreCase(uri.getScheme())) {
                String[] projection = {"_data"};
                try {
                    Cursor cursor = context.getContentResolver().query(uri, projection, null, null, null);
                    int column_index = cursor.getColumnIndexOrThrow("_data");
                    if (cursor.moveToFirst()) {
                        return cursor.getString(column_index);
                    }
                } catch (Exception e) {
                }
            } else if ("file".equalsIgnoreCase(uri.getScheme())) {
                return uri.getPath();
            }
            return null;
        }
    }
}
