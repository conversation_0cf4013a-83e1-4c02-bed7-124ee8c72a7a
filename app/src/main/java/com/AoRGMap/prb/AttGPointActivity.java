package com.AoRGMap.prb;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeExtActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.GdbDataType;
import com.AoGIS.database.GdbFieldInfo;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.baidu.activity.ActivityAllRecog;
import com.AoRGMap.baidu.activity.ActivityRecog;
import com.AoRGMap.commtool.CommTool;
import com.AoRGMap.dao.DBOpenHelper;
import com.AoRGMap.extdb.ExtStorageAttDB;
import com.AoRGMap.extdb.MapCodeXmlService;
import com.AoRGMap.extdb.TableSetModel;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGPointActivity extends PrbAttributeActivity {
    private final String CHINESEJEDTYPE = "界线点";
    private final String ENGLISHJEDTYPE = "Control point of boundary between different mapping units";
    String m_strTszfDbpath = "/data/data/com.AoRGMap/AoGIS/geotszf.db";
    private DBOpenHelper m_helper = null;
    private SQLiteDatabase m_db = null;
    String[] fieldsStrings = null;
    int iSelFiled = 0;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private AttributeItem item_miaoshu = null;
    private AttributeItem itemGeoPoint = null;
    private double[] mdJwd = new double[2];
    double lpX = 0.0d;
    double lpY = 0.0d;
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private final int RECOGNIZER_REQUEST_CODE = 9999;
    private final int BAIDUVOICE_REQUEST_CODE = 9998;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttGPointActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strGPoint));
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        this.m_helper = new DBOpenHelper(getBaseContext(), this.m_strTszfDbpath);
        this.m_db = this.m_helper.getReadableDatabase();
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        boolean updated = false;
        GdbFieldInfo[] flds = area.getAttributeSchemaManager().getFieldInfos();
        for (GdbFieldInfo gdbFieldInfo : flds) {
            if (gdbFieldInfo.getFieldname().compareToIgnoreCase("geo_desc") == 0) {
                updated = true;
            }
        }
        if (!updated) {
            Log.d("Str", "AttModi");
            GdbFieldInfo info = new GdbFieldInfo();
            info.setFieldname("geo_desc");
            info.setDisplayLength((short) 10);
            info.setDecimalScale((byte) 0);
            info.setFieldtype(GdbDataType.STRING);
            info.setPrintFlag((byte) 0);
            info.setMskLeng((short) -1);
            info.setIsEditable((short) 1);
            area.getAttributeSchemaManager().appendField(info);
            Toast toast = Toast.makeText(this, R.string.RGMAP_PROMPT_ATTSTRUADD, 0);
            toast.show();
        }
        updateAttributeMap(this.itemList);
        String strGeoPointCode = getAttributeMap().get("GEOPOINT").toString();
        int iGeoPointCode = getStartGroPointID(strGeoPointCode);
        if (iGeoPointCode != -1) {
            if (isNewFlag()) {
                RGMapApplication.getCurrentApp().getCurrentGlobal().addStartPointNumber();
            } else {
                this.mGState.setStartPointNumber(iGeoPointCode);
            }
        }
        String kX = getResources().getString(R.string.RGMAP_FLDNAME_PXX);
        String kY = getResources().getString(R.string.RGMAP_FLDNAME_PYY);
        getAttributeMap().put(kX, Double.valueOf(getX()));
        getAttributeMap().put(kY, Double.valueOf(getY()));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LONGITUDE, Double.valueOf(this.mdJwd[0]));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LATITUDE, Double.valueOf(this.mdJwd[1]));
        if (isNewFlag()) {
            Date date = new Date();
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddhhmmss");
            String defData = df.format(date);
            getAttributeMap().put(AttributeExtActivity.PARAM_STRING_DATE, defData);
        }
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTECODE")) {
            String strCode = getAttributeMap().get("ROUTECODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        setResult(1, new Intent());
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        final GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_p);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_ROUTECODE, R.string.rg_route_no, EditTextEditorInfo.textEditor(), map, defData));
        String defData2 = gstate.GetNextPointCode();
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_GEOPOINT, R.string.rg_gpoint_no, EditTextEditorInfo.textEditor(), map, defData2);
        this.itemList.add(item);
        this.itemGeoPoint = item;
        String strData = String.format("%f, %f", Double.valueOf(getX()), Double.valueOf(getY()));
        String defData3 = getResources().getString(R.string.nodata);
        AttributeUIHelper.createItem(group, (String) null, R.string.gpoint_xy, EditTextEditorInfo.textEditor(), strData, defData3).getEditorEditText().setEnabled(false);
        this.mdJwd = ConverXyToJwd(getX(), getY());
        String strData2 = String.format("%.2f, %.2f", Double.valueOf(this.mdJwd[0]), Double.valueOf(this.mdJwd[1]));
        String defData4 = getResources().getString(R.string.nodata);
        AttributeUIHelper.createItem(group, (String) null, R.string.RGMAP_PROMPT_JWCOORD, EditTextEditorInfo.textEditor(), strData2, defData4).getEditorEditText().setEnabled(false);
        final AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_ALTITUDE, R.string.gpoint_z, EditTextEditorInfo.decimalEditor(), map, (String) null);
        if (isNewFlag() && RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
            double z = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + gstate.getGpsRectifyZ();
            item2.setItemData(Double.toString(z));
        }
        this.itemList.add(item2);
        item2.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (RGMapApplication.getCurrentApp().mGPSLastStatus && RGMapApplication.getCurrentApp().mLastLocation != null) {
                    double z2 = RGMapApplication.getCurrentApp().mLastLocation.getAltitude() + gstate.getGpsRectifyZ();
                    item2.setItemData(Double.toString(z2));
                }
            }
        }, getResources().getDrawable(android.R.drawable.btn_star)));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LOCATION, R.string.gpoint_location, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_GEOMORPH, R.string.gpoint_weidimao, EditTextEditorInfo.textEditor(), map, (String) null);
        item3.setPropDictName(getResources().getString(R.string.dic_tiantu_weidimao));
        this.itemList.add(item3);
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_TYPE, R.string.gpoint_dianxing, EditTextEditorInfo.textEditor(), map, (String) null);
        item4.setPropDictName(getResources().getString(R.string.dic_tiantu_pclass));
        this.itemList.add(item4);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_OUTCROP, R.string.gpoint_lutou, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_WEATHING, R.string.gpoint_fenghua, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRAPHA, R.string.gpoint_tiantuA, EditTextEditorInfo.textEditor(), map, (String) null);
        item5.setPropDictName(getResources().getString(R.string.dic_tiantu_unit));
        this.itemList.add(item5);
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LITHO_A, R.string.gpoint_yanshiA, EditTextEditorInfo.textEditor(), map, (String) null);
        item6.setPropDictName(getResources().getString(R.string.dic_stone_name));
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRAPHB, R.string.gpoint_tiantuB, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.setPropDictName(getResources().getString(R.string.dic_tiantu_unit));
        this.itemList.add(item7);
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LITHO_B, R.string.gpoint_yanshiB, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.setPropDictName(getResources().getString(R.string.dic_stone_name));
        this.itemList.add(item8);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRAPHC, R.string.gpoint_tiantuC, EditTextEditorInfo.textEditor(), map, (String) null);
        item9.setPropDictName(getResources().getString(R.string.dic_tiantu_unit));
        this.itemList.add(item9);
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_LITHO_C, R.string.gpoint_yanshiC, EditTextEditorInfo.textEditor(), map, (String) null);
        item10.setPropDictName(getResources().getString(R.string.dic_stone_name));
        this.itemList.add(item10);
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRARAB, R.string.gpoint_rAB, EditTextEditorInfo.textEditor(), map, (String) null);
        item11.setPropDictName(getResources().getString(R.string.dic_contact_relationship));
        this.itemList.add(item11);
        AttributeItem item12 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRARBC, R.string.gpoint_rBC, EditTextEditorInfo.textEditor(), map, (String) null);
        item12.setPropDictName(getResources().getString(R.string.dic_contact_relationship));
        this.itemList.add(item12);
        AttributeItem item13 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_STRARAC, R.string.gpoint_rAC, EditTextEditorInfo.textEditor(), map, (String) null);
        item13.setPropDictName(getResources().getString(R.string.dic_contact_relationship));
        this.itemList.add(item13);
        container.addView(group.getInnerView());
        AttributeGroup.AttributeGroupParams groupParam2 = new AttributeGroup.AttributeGroupParams();
        groupParam2.title = getResources().getString(R.string.rg_dizhi);
        AttributeGroup group2 = new AttributeGroup(this, groupParam2, container);
        String str = getResources().getString(R.string.RGMAP_PROMPT_DICNAME);
        this.item_miaoshu = AttributeUIHelper.createItem(group2, R.string.rg_geo_desc, R.string.rg_dizhi, EditTextEditorInfo.textEditor(), map, (String) null);
        this.item_miaoshu.setPropDict(str, true, true, null, 0);
        this.itemList.add(this.item_miaoshu);
        this.item_miaoshu.removeLabel();
        this.item_miaoshu.getEditorEditText().setHint(R.string.RGMAP_PROMPT_DECINFO);
        this.item_miaoshu.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                CommTool tool = new CommTool();
                if (tool.IsNetworkConnected(AttGPointActivity.this).booleanValue()) {
                    Intent intent = new Intent(AttGPointActivity.this, (Class<?>) ActivityAllRecog.class);
                    AttGPointActivity.this.startActivityForResult(intent, 9998);
                } else {
                    Toast.makeText(AttGPointActivity.this, "请检查网络连接", 0).show();
                    AoRGMapActivity.getCurrentActivity().BdTTSSpeak("请检查网络连接");
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_btn_speak_now)));
        this.item_miaoshu.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                String szGp = AttGPointActivity.this.itemGeoPoint.getItemDataString();
                if (szGp.equals("")) {
                    Toast.makeText(AttGPointActivity.this, R.string.RGMAP_PROMPT_GPEMPTY, 0).show();
                    return;
                }
                Intent intent = new Intent(AttGPointActivity.this, (Class<?>) AttGPointDescActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString("KeyWord", szGp + "_P");
                intent.putExtras(bundle);
                AttGPointActivity.this.startActivity(intent);
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_camera)));
        container.addView(group2.getInnerView());
        if (0 != 0) {
            gaowenlingTest(gstate, groupParam2, group2, container, item13, map, item2);
            return;
        }
        final String dbPath = getMapNameNoExt() + "prb";
        if (new File(dbPath).exists()) {
            final String dictPath = gstate.getDictPath();
            final String ImageExt = getMapForld() + "ImageExt/";
            File file = new File(ImageExt);
            if (!file.exists()) {
                file.mkdir();
            }
            final String FileExt = getMapForld() + "FileExt/";
            File file2 = new File(FileExt);
            if (!file2.exists()) {
                file2.mkdir();
            }
            final String SketchExt = getMapForld() + "SketchExt/";
            File file3 = new File(SketchExt);
            if (!file3.exists()) {
                file3.mkdir();
            }
            ExtStorageAttDB.open(this, dbPath);
            ArrayList<String> tblTypes = ExtStorageAttDB.getAllTblType(PRBAreas.m_strGPoint);
            if (tblTypes != null) {
                for (int j = 0; j < tblTypes.size(); j++) {
                    AttributeGroup.AttributeGroupParams groupParam3 = new AttributeGroup.AttributeGroupParams();
                    groupParam3.title = tblTypes.get(j);
                    AttributeGroup group3 = new AttributeGroup(this, groupParam3, container);
                    ArrayList<TableSetModel> tbls = ExtStorageAttDB.getAllExtTbl(PRBAreas.m_strGPoint, groupParam3.title);
                    if (tbls != null) {
                        Iterator<TableSetModel> it = tbls.iterator();
                        while (it.hasNext()) {
                            final TableSetModel tbl = it.next();
                            AttributeItem item14 = AttributeUIHelper.createItem(group3, tbl.getTblName(), tbl.getTblCnName(), EditTextEditorInfo.textEditor(), map, (String) null);
                            item14.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.4
                                @Override // android.view.View.OnClickListener
                                public void onClick(View arg0) {
                                    if (AttGPointActivity.this.bHasKey()) {
                                        Intent extIntent = new Intent();
                                        extIntent.setClass(AttGPointActivity.this, ExtStorageActivity.class);
                                        extIntent.putExtra("DictPath", dictPath);
                                        extIntent.putExtra("SqlDB", dbPath);
                                        extIntent.putExtra("TableName", tbl.getTblName());
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_TABLE_CNAME, tbl.getTblCnName());
                                        extIntent.putExtra("MediaPath", ImageExt);
                                        extIntent.putExtra("FilePath", FileExt);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_SKETCH_PATH, SketchExt);
                                        extIntent.putExtra("Key", gstate.GetCurrentRouteCode() + "_" + AttGPointActivity.this.itemGeoPoint.getEditorEditText().getText().toString());
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_GEOPOINTCODE, AttGPointActivity.this.itemGeoPoint.getEditorEditText().getText().toString());
                                        String strTemp = String.format("%f", Double.valueOf(AttGPointActivity.this.getX()));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_XX, strTemp);
                                        String strTemp2 = String.format("%f", Double.valueOf(AttGPointActivity.this.getY()));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_YY, strTemp2);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_MAPCODE, AttGPointActivity.this.getMapCode());
                                        String strTemp3 = String.format("%.2f", Double.valueOf(AttGPointActivity.this.mdJwd[1]));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LATITUDE, strTemp3);
                                        String strTemp4 = String.format("%.2f", Double.valueOf(AttGPointActivity.this.mdJwd[0]));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LONGITUDE, strTemp4);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_ALTITUDE, item2.getEditorEditText().getText().toString());
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_DATE, "");
                                        AttGPointActivity.this.startActivity(extIntent);
                                    }
                                }
                            }, getResources().getDrawable(android.R.drawable.ic_input_add)));
                            this.itemList.add(item14);
                        }
                    }
                    container.addView(group3.getInnerView());
                }
            }
        }
    }

    private boolean getIsJxd(String type) {
        return type.equals("界线点") || type.equals("Control point of boundary between different mapping units");
    }

    private int getStartGroPointID(String pointCode) {
        if (pointCode.length() < 1 || !Character.isLetter(pointCode.charAt(0)) || !Character.isDigit(pointCode.charAt(pointCode.length() - 1))) {
            return -1;
        }
        boolean fail = false;
        int sp2 = 0;
        int i = 0;
        int digital = 0;
        while (true) {
            if (i >= pointCode.length()) {
                break;
            }
            char x = pointCode.charAt(i);
            if (digital == 0) {
                if (!Character.isLetter(x) && !Character.isDigit(x)) {
                    fail = true;
                    break;
                }
                if (Character.isDigit(x)) {
                    digital = 1;
                    sp2 = i;
                }
                i++;
            } else if (Character.isDigit(x)) {
                i++;
            } else {
                fail = true;
                break;
            }
        }
        if (fail) {
            return -1;
        }
        pointCode.substring(0, sp2);
        return Integer.parseInt(pointCode.substring(sp2));
    }

    private void BtnClickFun(final AttributeItem v, String title, String tabname, String field1) {
        String strSQL = String.format("select * from %s", tabname);
        Cursor SectionSursor = this.m_db.rawQuery(strSQL, null);
        int isize = SectionSursor.getCount();
        this.fieldsStrings = new String[isize];
        int iFiled = 0;
        while (SectionSursor.moveToNext()) {
            int columnIndex = SectionSursor.getColumnIndex(field1);
            String szFieldValue = SectionSursor.getString(columnIndex);
            this.fieldsStrings[iFiled] = szFieldValue;
            iFiled++;
        }
        AlertDialog.Builder builder1 = new AlertDialog.Builder(this);
        builder1.setTitle(title);
        builder1.setSingleChoiceItems(this.fieldsStrings, 0, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.5
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                AttGPointActivity.this.iSelFiled = which;
            }
        });
        builder1.setPositiveButton(getString(R.string.btnOK), new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.6
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                String strOldValueString = v.getItemDataString();
                v.getEditorEditText().setText(strOldValueString + AttGPointActivity.this.fieldsStrings[AttGPointActivity.this.iSelFiled]);
                AttGPointActivity.this.iSelFiled = 0;
            }
        });
        builder1.show();
    }

    private double[] ConverXyToJwd(double dx, double dy) {
        WorkAreaParams.ProjectionType PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        WorkAreaParams.EarthType EarthType = MapParam.getEarthType();
        double dCenterLon = getRadFromDeg(MapParam.lon);
        double[] dJwd = new double[2];
        if (PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(dCenterLon, dx, dy, EarthType);
            if (dPos == null) {
                dPos = new double[]{0.0d, 0.0d};
            }
            String szTemp = String.format("%.8f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            dJwd[0] = dTemp;
            String szTemp2 = String.format("%.8f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            dJwd[1] = dTemp2;
        } else if (PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(dCenterLon, dx, dy, EarthType, MapParam.dx, MapParam.dy);
            if (dPos2 == null) {
                dPos2 = new double[]{0.0d, 0.0d};
            }
            String szTemp3 = String.format("%.8f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            dJwd[0] = dTemp3;
            String szTemp4 = String.format("%.8f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            dJwd[1] = dTemp4;
        } else if (PrjType == WorkAreaParams.ProjectionType.Lambert) {
            double[] dPos3 = ProjectionHelper.LambertRev(getRadFromDeg(MapParam.lat1), getRadFromDeg(MapParam.lat2), dCenterLon, getRadFromDeg(MapParam.lat), dx, dy, EarthType);
            if (dPos3 == null) {
                dPos3 = new double[]{0.0d, 0.0d};
            }
            String szTemp5 = String.format("%.8f", Double.valueOf((dPos3[0] * 180.0d) / 3.1415926d));
            double dTemp5 = Double.valueOf(szTemp5).doubleValue();
            dJwd[0] = dTemp5;
            String szTemp6 = String.format("%.8f", Double.valueOf((dPos3[1] * 180.0d) / 3.1415926d));
            double dTemp6 = Double.valueOf(szTemp6).doubleValue();
            dJwd[1] = dTemp6;
        } else if (PrjType == WorkAreaParams.ProjectionType.WebMercator) {
            double[] dPos4 = ProjectionHelper.WebMercatorRev(dx, dy, this.mGState.getbInChina());
            if (dPos4 == null) {
                dPos4 = new double[]{0.0d, 0.0d};
            }
            String szTemp7 = String.format("%.8f", Double.valueOf((dPos4[0] * 180.0d) / 3.1415926d));
            double dTemp7 = Double.valueOf(szTemp7).doubleValue();
            dJwd[0] = dTemp7;
            String szTemp8 = String.format("%.8f", Double.valueOf((dPos4[1] * 180.0d) / 3.1415926d));
            double dTemp8 = Double.valueOf(szTemp8).doubleValue();
            dJwd[1] = dTemp8;
        } else {
            dJwd[0] = 0.0d;
            dJwd[1] = 0.0d;
        }
        dJwd[0] = ConverDtoDFM(dJwd[0]);
        dJwd[1] = ConverDtoDFM(dJwd[1]);
        return dJwd;
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }

    private double ConverDtoDFM(double dd) {
        int d = (int) dd;
        int f = (int) ((dd - d) * 60.0d);
        double m = (((dd - d) * 60.0d) - f) * 60.0d;
        double ddfm = (d * 100 * 100) + (f * 100) + m;
        return ddfm;
    }

    @Override // com.AoDevBase.ui.AttributeActivity, android.app.Activity
    public void onDestroy() {
        super.onDestroy();
        ExtStorageAttDB.close();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public String getMapCode() {
        String XmlPath = getMapNameNoExt() + "xml";
        MapCodeXmlService service = new MapCodeXmlService();
        try {
            String MapCode = service.parseXml(XmlPath);
            return MapCode;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    private String getMapForld() {
        if (AoRGMapActivity.getCurrentMap() == null) {
            return "";
        }
        String dbPath = AoRGMapActivity.getCurrentMap().getMapName();
        int lastIndex = dbPath.lastIndexOf(47);
        if (lastIndex <= 0) {
            lastIndex = dbPath.lastIndexOf(92);
        }
        if (lastIndex <= 0) {
            Toast toast = DisplayHelper.getCommonToast();
            toast.setText("路径不正确:" + dbPath);
            toast.show();
            finish();
            return "";
        }
        String Path = dbPath.substring(0, lastIndex + 1);
        return Path;
    }

    private String getMapNameNoExt() {
        if (AoRGMapActivity.getCurrentMap() == null) {
            return "";
        }
        String dbPath = AoRGMapActivity.getCurrentMap().getMapName();
        int lastpos = dbPath.lastIndexOf(46);
        String Path = dbPath.substring(0, lastpos + 1);
        return Path;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean bHasKey() {
        String Temp = this.itemGeoPoint.getEditorEditText().getText().toString();
        return !Temp.equals("");
    }

    private void gaowenlingTest(GlobalState gstate, AttributeGroup.AttributeGroupParams groupParam, AttributeGroup group, ViewGroup container, AttributeItem item, Map<String, ?> map, final AttributeItem itemZ) {
        final String dictPath = Environment.getExternalStorageDirectory() + "";
        final String mdbPath = Environment.getExternalStorageDirectory() + "/Example.db";
        final String ImageExt = Environment.getExternalStorageDirectory() + "/ImageExt/";
        File file = new File(ImageExt);
        if (!file.exists()) {
            file.mkdir();
        }
        final String FileExt = getMapForld() + "FileExt/";
        File file2 = new File(FileExt);
        if (!file2.exists()) {
            file2.mkdir();
        }
        ExtStorageAttDB.open(this, mdbPath);
        ArrayList<String> tblTypes = ExtStorageAttDB.getAllTblType(PRBAreas.m_strGPoint);
        if (tblTypes != null) {
            for (int j = 0; j < tblTypes.size(); j++) {
                AttributeGroup.AttributeGroupParams groupParam2 = new AttributeGroup.AttributeGroupParams();
                groupParam2.title = tblTypes.get(j);
                AttributeGroup group2 = new AttributeGroup(this, groupParam2, container);
                ArrayList<TableSetModel> tbls = ExtStorageAttDB.getAllExtTbl(PRBAreas.m_strGPoint, groupParam2.title);
                if (tbls != null) {
                    Iterator<TableSetModel> it = tbls.iterator();
                    while (it.hasNext()) {
                        final TableSetModel tbl = it.next();
                        AttributeItem item2 = AttributeUIHelper.createItem(group2, tbl.getTblName(), tbl.getTblCnName(), EditTextEditorInfo.textEditor(), map, (String) null);
                        item2.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointActivity.7
                            @Override // android.view.View.OnClickListener
                            public void onClick(View arg0) {
                                if (AttGPointActivity.this.bHasKey()) {
                                    Intent extIntent = new Intent();
                                    extIntent.setClass(AttGPointActivity.this, ExtStorageActivity.class);
                                    extIntent.putExtra("DictPath", dictPath);
                                    extIntent.putExtra("SqlDB", mdbPath);
                                    extIntent.putExtra("TableName", tbl.getTblName());
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_TABLE_CNAME, tbl.getTblCnName());
                                    extIntent.putExtra("MediaPath", ImageExt);
                                    extIntent.putExtra("FilePath", FileExt);
                                    extIntent.putExtra("Key", "D0001");
                                    String strTemp = String.format("%f", Double.valueOf(AttGPointActivity.this.getX()));
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_XX, strTemp);
                                    String strTemp2 = String.format("%f", Double.valueOf(AttGPointActivity.this.getY()));
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_YY, strTemp2);
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_MAPCODE, AttGPointActivity.this.getMapCode());
                                    String strTemp3 = String.format("%.2f", Double.valueOf(AttGPointActivity.this.mdJwd[0]));
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LATITUDE, strTemp3);
                                    String strTemp4 = String.format("%.2f", Double.valueOf(AttGPointActivity.this.mdJwd[1]));
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LONGITUDE, strTemp4);
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_ALTITUDE, itemZ.getEditorEditText().getText().toString());
                                    extIntent.putExtra(AttributeExtActivity.PARAM_STRING_DATE, "");
                                    AttGPointActivity.this.startActivity(extIntent);
                                }
                            }
                        }, getResources().getDrawable(android.R.drawable.ic_input_add)));
                        this.itemList.add(item2);
                    }
                }
                container.addView(group2.getInnerView());
            }
        }
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == 9999 && resultCode == -1) {
            ArrayList<String> matchResults = data.getStringArrayListExtra("android.speech.extra.RESULTS");
            if (matchResults.size() > 0) {
                String voice_str = this.item_miaoshu.getItemDataString();
                this.item_miaoshu.setItemData(voice_str + matchResults.get(0).toString());
            }
        }
        if (requestCode == 1 && resultCode == -1) {
            Bundle results = data.getExtras();
            ArrayList<String> results_recognition = results.getStringArrayList("results_recognition");
            results_recognition.size();
        }
        if (requestCode == 9998 && resultCode == -1) {
            String Temp = data.getExtras().getString(ActivityRecog.RESULTTXT);
            String voice_str2 = this.item_miaoshu.getItemDataString();
            this.item_miaoshu.setItemData(voice_str2 + Temp);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }
}
