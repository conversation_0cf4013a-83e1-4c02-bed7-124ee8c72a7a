package com.AoRGMap.prb;

import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;

/* loaded from: classes.dex */
public class HydPointLib {
    public static final int iHydPoint = 0;
    public static int m_iCurTableID = -1;
    public static final String m_strhydpoint = "HYDPOINT";

    public static void resetCurrentTableID() {
        m_iCurTableID = -1;
    }

    public static void setCurrentTableID(int iIdx) {
        m_iCurTableID = iIdx;
    }

    public static int getCurrentTableID() {
        return m_iCurTableID;
    }

    public static boolean isCurrentTable(int iIdx) {
        return iIdx == m_iCurTableID;
    }

    public static String getCurrentTableName() {
        return getTableName(m_iCurTableID);
    }

    public static String getTableName(int iIdx) {
        switch (iIdx) {
            case 0:
                return m_strhydpoint;
            default:
                return null;
        }
    }

    public static String getCurTableChineseName() {
        RGMapApplication app = RGMapApplication.getCurrentApp();
        switch (m_iCurTableID) {
            case 0:
                String strName = app.getResources().getString(R.string.RGMAP_PROMPT_HYDPOINT);
                return strName;
            default:
                return null;
        }
    }

    public static Class<?> getCurAttActivityClass() {
        switch (m_iCurTableID) {
            case 0:
                return AttLibHydPointActivity.class;
            default:
                return null;
        }
    }
}
