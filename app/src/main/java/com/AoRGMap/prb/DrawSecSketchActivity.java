package com.AoRGMap.prb;

import android.os.Bundle;
import com.AoDevBase.util.DrawBaseActivity;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.AoMap;
import com.AoRGMap.GlobalState;
import com.AoRGMap.RGMapApplication;

/* loaded from: classes.dex */
public class DrawSecSketchActivity extends DrawBaseActivity {
    public static final String BUNDLE_SKETCHMAPNAME = "SketchMapName";
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();

    @Override // com.AoDevBase.util.DrawBaseActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        String strMapName = bundle.getString("SketchMapName");
        this.m_Map = null;
        this.m_Map = AoMap.openMap(strMapName);
        super.onCreate(savedInstanceState);
    }

    @Override // com.AoDevBase.util.DrawBaseActivity, android.app.Activity
    protected void onDestroy() {
        this.m_Map.closeMap();
        super.onDestroy();
    }
}
