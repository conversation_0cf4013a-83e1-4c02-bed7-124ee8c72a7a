package com.AoRGMap.prb;

import android.app.Activity;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import com.AoDevBase.util.UILanguageUtil;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import java.io.IOException;

/* loaded from: classes.dex */
public class SoundViewActivity extends Activity {
    public static final String FOLDER_PATH = "FolderATH";
    public static final String VIDEOFILE_PATH = "VIDE0PATH";
    private TextView Viewpath;
    String[] soundpathArr;
    private MediaPlayer mediaPlayer = new MediaPlayer();
    String soundpath = null;
    String szFolder = null;
    int idex = 0;
    int icount = 0;
    Uri uri = null;
    private Button button_next = null;
    private Button button_pre = null;
    private Button button_play = null;
    private Button button_pasue = null;
    private Button button_stop = null;

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
        int iLang = mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.sdview);
        this.button_pre = (Button) findViewById(R.id.btsoundpre);
        this.button_pre.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.SoundViewActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (SoundViewActivity.this.mediaPlayer.isPlaying()) {
                    SoundViewActivity.this.mediaPlayer.stop();
                    SoundViewActivity.this.mediaPlayer.reset();
                }
                SoundViewActivity.this.idex--;
                if (SoundViewActivity.this.idex >= 0) {
                    SoundViewActivity.this.soundpath = SoundViewActivity.this.szFolder + SoundViewActivity.this.soundpathArr[SoundViewActivity.this.idex] + ".wav";
                    try {
                        SoundViewActivity.this.Viewpath.setText(SoundViewActivity.this.soundpath);
                        SoundViewActivity.this.mediaPlayer.setDataSource(SoundViewActivity.this.soundpath);
                        SoundViewActivity.this.mediaPlayer.prepare();
                        return;
                    } catch (IOException e) {
                        e.printStackTrace();
                        return;
                    } catch (IllegalStateException e2) {
                        e2.printStackTrace();
                        return;
                    }
                }
                SoundViewActivity.this.idex++;
            }
        });
        this.button_next = (Button) findViewById(R.id.btsoundnext);
        this.button_next.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.SoundViewActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (SoundViewActivity.this.mediaPlayer.isPlaying()) {
                    SoundViewActivity.this.mediaPlayer.stop();
                    SoundViewActivity.this.mediaPlayer.reset();
                }
                SoundViewActivity.this.idex++;
                if (SoundViewActivity.this.idex < SoundViewActivity.this.icount) {
                    SoundViewActivity.this.soundpath = SoundViewActivity.this.szFolder + SoundViewActivity.this.soundpathArr[SoundViewActivity.this.idex] + ".wav";
                    try {
                        SoundViewActivity.this.Viewpath.setText(SoundViewActivity.this.soundpath);
                        SoundViewActivity.this.mediaPlayer.setDataSource(SoundViewActivity.this.soundpath);
                        SoundViewActivity.this.mediaPlayer.prepare();
                        return;
                    } catch (IOException e) {
                        e.printStackTrace();
                        return;
                    } catch (IllegalArgumentException e2) {
                        e2.printStackTrace();
                        return;
                    } catch (IllegalStateException e3) {
                        e3.printStackTrace();
                        return;
                    } catch (SecurityException e4) {
                        e4.printStackTrace();
                        return;
                    }
                }
                SoundViewActivity.this.idex--;
            }
        });
        this.button_stop = (Button) findViewById(R.id.btsoundstop);
        this.button_stop.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.SoundViewActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (SoundViewActivity.this.mediaPlayer.isPlaying()) {
                    SoundViewActivity.this.mediaPlayer.stop();
                }
            }
        });
        this.button_play = (Button) findViewById(R.id.btsoundplay);
        this.button_play.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.SoundViewActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (!SoundViewActivity.this.mediaPlayer.isPlaying()) {
                    SoundViewActivity.this.mediaPlayer.start();
                }
            }
        });
        this.button_pasue = (Button) findViewById(R.id.btsoundpause);
        this.button_pasue.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.SoundViewActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (SoundViewActivity.this.mediaPlayer.isPlaying()) {
                    SoundViewActivity.this.mediaPlayer.pause();
                }
            }
        });
        Bundle bundle = getIntent().getExtras();
        this.szFolder = bundle.getString("FolderATH");
        String szpath = bundle.getString("VIDE0PATH");
        this.soundpathArr = szpath.split(",");
        this.icount = this.soundpathArr.length;
        this.soundpath = this.szFolder + this.soundpathArr[this.idex] + ".wav";
        this.Viewpath = (TextView) findViewById(R.id.soundViewpath);
        try {
            this.Viewpath.setText(this.soundpath);
            this.mediaPlayer.setDataSource(this.soundpath);
            this.mediaPlayer.prepare();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e2) {
            e2.printStackTrace();
        } catch (IllegalStateException e3) {
            e3.printStackTrace();
        } catch (SecurityException e4) {
            e4.printStackTrace();
        }
    }
}
