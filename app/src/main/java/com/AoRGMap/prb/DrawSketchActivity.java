package com.AoRGMap.prb;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.AoGISApplication;
import com.AoGIS.base.GeoRect;
import com.AoGIS.base.PointType;
import com.AoGIS.database.AoMap;
import com.AoGIS.database.WorkArea;
import com.AoGIS.edit.LineEditListener;
import com.AoGIS.edit.PointEditListener;
import com.AoGIS.edit.TouchDelEditListener;
import com.AoGIS.edit.TouchInfoEditListener;
import com.AoGIS.edit.TouchLineEditListener;
import com.AoGIS.edit.TouchPointEditListener;
import com.AoGIS.geometry.GeoClassType;
import com.AoGIS.render.BaseTouchListener;
import com.AoGIS.ui.LayerManagerActivity;
import com.AoGIS.util.DisplayHelper;
import com.AoRGMap.GlobalState;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.edit.TouchCommAttEditListener;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;

/* loaded from: classes.dex */
public class DrawSketchActivity extends Activity {
    public static final String BUNDLE_SKETCHMAPNAME = "SketchMapName";
    DisplayMetrics dm;
    private Button m_BtCancel;
    private Button m_BtOK;
    private Button m_BtQuery;
    private Button m_Btfile;
    private Button m_Btline;
    private Button m_Btpoint;
    private TextView m_Tiletext;
    private RadioGroup mainRadioGroup;
    private static int widthH = 0;
    private static int heightH = 0;
    private static int widthP = 0;
    private static int heightP = 0;
    private AoMap m_Map = null;
    private DrawSketchView m_MapView = null;
    TouchCommAttEditListener touchCommAttEditListener = null;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private RelativeLayout m_mainRelativeLayout = null;

    private int CopySdcardFile(String fromFile, String toFile) {
        try {
            InputStream fosfrom = new FileInputStream(fromFile);
            OutputStream fosto = new FileOutputStream(toFile);
            byte[] bt = new byte[1024];
            while (true) {
                int c = fosfrom.read(bt);
                if (c > 0) {
                    fosto.write(bt, 0, c);
                } else {
                    fosfrom.close();
                    return 0;
                }
            }
        } catch (Exception e) {
            Log.e("ERROR", "CopySdCard");
            return -1;
        }
    }

    private int CopyAssetFile(String fromFile, String toFile) {
        InputStream fosfrom = getClass().getClassLoader().getResourceAsStream("assets/" + fromFile);
        try {
            OutputStream fosto = new FileOutputStream(toFile);
            byte[] bt = new byte[1024];
            while (true) {
                try {
                    int c = fosfrom.read(bt);
                    if (c > 0) {
                        fosto.write(bt, 0, c);
                    } else {
                        fosto.flush();
                        fosto.close();
                        fosfrom.close();
                        return 0;
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    return -1;
                }
            }
        } catch (FileNotFoundException e2) {
            e2.printStackTrace();
            return -1;
        }
    }

    @Override // android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.drawvectorsketch);
        this.m_MapView = (DrawSketchView) findViewById(R.id.main_view);
        this.m_Tiletext = (TextView) findViewById(R.id.textView14);
        this.m_mainRelativeLayout = (RelativeLayout) findViewById(R.id.mainRelativeLayout);
        this.m_BtCancel = (Button) findViewById(R.id.skecth_button_cancel);
        this.m_BtOK = (Button) findViewById(R.id.sketch_button_ok);
        this.mainRadioGroup = (RadioGroup) findViewById(R.id.main_sketch_menu);
        this.m_Btfile = (Button) findViewById(R.id.menu_sketch_file);
        this.m_Btpoint = (Button) findViewById(R.id.menu_sketch_point);
        this.m_Btline = (Button) findViewById(R.id.menu_sketch_line);
        this.m_BtQuery = (Button) findViewById(R.id.menu_sketch_search);
        InitListener();
        Bundle bundle = getIntent().getExtras();
        String strMapName = bundle.getString("SketchMapName");
        int lastIndex = strMapName.lastIndexOf(47);
        if (lastIndex <= 0) {
            lastIndex = strMapName.lastIndexOf(92);
        }
        if (lastIndex > 0) {
            String strPath = strMapName.substring(0, lastIndex + 1);
            String strName = strMapName.substring(lastIndex + 1);
            File f = new File(strPath);
            if (!f.exists() && !f.mkdirs()) {
                Toast toast = DisplayHelper.getCommonToast();
                String str = getResources().getString(R.string.RGMAP_PROMPT_CREATEFLODER);
                toast.setText(str + strPath);
                toast.show();
                finish();
                return;
            }
            this.m_Map = null;
            File mapFile = new File(strMapName + ".GPJ");
            if (!mapFile.exists()) {
                int result = CopyAssetFile("Sketch.GPJ", strMapName + ".GPJ") | CopyAssetFile("Sketch.lm", strPath + strName + ".lm") | CopyAssetFile("Sketch.la", strPath + strName + ".la") | CopyAssetFile("Sketch.tm", strPath + strName + ".tm") | CopyAssetFile("Sketch.ta", strPath + strName + ".ta");
                File gridFile = new File(strPath + "Grid100.lm");
                if (!gridFile.exists()) {
                    result = result | CopyAssetFile("Grid100.lm", strPath + "Grid100.lm") | CopyAssetFile("Grid100.la", strPath + "Grid100.la");
                }
                if (result != 0) {
                    Toast toast2 = DisplayHelper.getCommonToast();
                    toast2.setText(R.string.RGMAP_PROMPT_COPYORGFILE);
                    toast2.show();
                    finish();
                    return;
                }
                this.m_Map = AoMap.openMap(strMapName + ".GPJ");
                this.m_Map.appendItem(strPath + strName + ".lm");
                this.m_Map.appendItem(strPath + strName + ".tm");
                this.m_Map.setItemState((short) 1, AoMap.GeoItemState.CUREDIT);
                this.m_Map.setItemState((short) 2, AoMap.GeoItemState.CUREDIT);
            } else {
                this.m_Map = AoMap.openMap(strMapName + ".GPJ");
                int inum = this.m_Map.getWorkAreaNum();
                for (int i = 0; i < inum; i++) {
                    AoMap.GeoItemState state = this.m_Map.getItemState((short) i);
                    if (state != AoMap.GeoItemState.EDIT && state != AoMap.GeoItemState.CUREDIT) {
                        this.m_Map.setItemState((short) i, AoMap.GeoItemState.EDIT);
                    }
                }
                this.m_Map.saveMapAndAreas();
            }
            this.m_MapView.setMap(this.m_Map);
            this.m_Tiletext.setText(strName + ".GPJ");
            this.dm = new DisplayMetrics();
            getWindowManager().getDefaultDisplay().getMetrics(this.dm);
            widthP = this.dm.widthPixels;
            heightP = this.dm.heightPixels;
            this.m_MapView.resetView();
            this.m_MapView.updateView();
            return;
        }
        Toast toast3 = DisplayHelper.getCommonToast();
        toast3.setText(R.string.RGMAP_PROMPT_CREATESKETCH);
        toast3.show();
        finish();
    }

    private void InitListener() {
        BaseTouchListener listener = this.m_MapView.getTouchListener();
        String[] strs = listener.getExtendFunctions();
        for (int i = 0; i < strs.length; i++) {
            Button btn = new Button(this);
            btn.setText(strs[i]);
            btn.setTag(Integer.valueOf(i));
            btn.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.1
                @Override // android.view.View.OnClickListener
                public void onClick(View v) {
                    DrawSketchActivity.this.m_MapView.getTouchListener().onEditOk();
                }
            });
        }
        this.m_BtCancel.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawSketchActivity.this.finish();
            }
        });
        this.m_BtOK.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawSketchActivity.this.m_Map.saveMapAndAreas();
                DrawSketchActivity.this.finish();
            }
        });
        this.m_Btfile.setOnClickListener(new AnonymousClass4());
        this.m_Btpoint.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int iLang = DrawSketchActivity.this.mGState.getAoGISLanguage();
                UILanguageUtil.setAoLanguage(DrawSketchActivity.this, iLang);
                new AlertDialog.Builder(DrawSketchActivity.this).setTitle(R.string.menu_sketch_point).setItems(new String[]{DrawSketchActivity.this.getString(R.string.menu_sketch_point_newsym), DrawSketchActivity.this.getString(R.string.menu_sketch_point_newnote), DrawSketchActivity.this.getString(R.string.menu_sketch_point_del), DrawSketchActivity.this.getString(R.string.menu_sketch_point_move), DrawSketchActivity.this.getString(R.string.menu_sketch_point_copy), DrawSketchActivity.this.getString(R.string.menu_sketch_point_info)}, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.5.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which) {
                            case 0:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new PointEditListener(DrawSketchActivity.this.m_MapView, PointEditListener.MODE_NEW, PointType.SubImage));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                            case 1:
                                PointEditListener listener2 = new PointEditListener(DrawSketchActivity.this.m_MapView, PointEditListener.MODE_NEW, PointType.Note);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener2);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                            case 2:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchDelEditListener(DrawSketchActivity.this.m_MapView, GeoClassType.POINT, null));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                            case 3:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchPointEditListener(DrawSketchActivity.this.m_MapView, TouchPointEditListener.MODE_MODIFY));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                            case 4:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchPointEditListener(DrawSketchActivity.this.m_MapView, TouchPointEditListener.MODE_COPY));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                            case 5:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchInfoEditListener(DrawSketchActivity.this.m_MapView, GeoClassType.POINT, null));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.POINT);
                                break;
                        }
                    }
                }).show();
                DrawSketchActivity.this.mainRadioGroup.clearCheck();
            }
        });
        this.m_Btline.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                int iLang = DrawSketchActivity.this.mGState.getAoGISLanguage();
                UILanguageUtil.setAoLanguage(DrawSketchActivity.this, iLang);
                new AlertDialog.Builder(DrawSketchActivity.this).setTitle(R.string.menu_sketch_line).setItems(new String[]{DrawSketchActivity.this.getString(R.string.menu_sketch_line_new1), DrawSketchActivity.this.getString(R.string.menu_sketch_line_new2), DrawSketchActivity.this.getString(R.string.menu_sketch_line_new3), DrawSketchActivity.this.getString(R.string.menu_sketch_line_del), DrawSketchActivity.this.getString(R.string.menu_sketch_line_node), DrawSketchActivity.this.getString(R.string.menu_sketch_line_move), DrawSketchActivity.this.getString(R.string.menu_sketch_line_copy), DrawSketchActivity.this.getString(R.string.menu_sketch_line_info)}, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.6.1
                    @Override // android.content.DialogInterface.OnClickListener
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which) {
                            case 0:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 0));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 1:
                                LineEditListener listener2 = new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 0);
                                listener2.setCurve(true);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener2);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 2:
                                LineEditListener listener3 = new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 0);
                                listener3.setCurveInDB(true);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener3);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 3:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchDelEditListener(DrawSketchActivity.this.m_MapView, GeoClassType.LINE, null));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 4:
                                LineEditListener listener4 = new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 1);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener4);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 5:
                                LineEditListener listener5 = new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 2);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener5);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 6:
                                LineEditListener listener6 = new TouchLineEditListener(DrawSketchActivity.this.m_MapView, 3);
                                DrawSketchActivity.this.m_MapView.setTouchListener(listener6);
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                            case 7:
                                DrawSketchActivity.this.m_MapView.setTouchListener(new TouchInfoEditListener(DrawSketchActivity.this.m_MapView, GeoClassType.LINE, null));
                                DrawSketchActivity.this.m_mainRelativeLayout.removeView(DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm));
                                DrawSketchActivity.this.prepareEdit(GeoClassType.LINE);
                                break;
                        }
                    }
                }).show();
                DrawSketchActivity.this.mainRadioGroup.clearCheck();
            }
        });
        this.m_BtQuery.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.7
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                if (DrawSketchActivity.this.touchCommAttEditListener == null) {
                    DrawSketchActivity.this.touchCommAttEditListener = new TouchCommAttEditListener(DrawSketchActivity.this.m_MapView, null, null);
                    DrawSketchActivity.this.m_MapView.setTouchListener(DrawSketchActivity.this.touchCommAttEditListener);
                } else {
                    DrawSketchActivity.this.touchCommAttEditListener.dimissPopWindow();
                    DrawSketchActivity.this.touchCommAttEditListener = null;
                    DrawSketchActivity.this.m_MapView.setZoomTouchListener();
                    DrawSketchActivity.this.mainRadioGroup.clearCheck();
                }
            }
        });
    }

    /* renamed from: com.AoRGMap.prb.DrawSketchActivity$4, reason: invalid class name */
    class AnonymousClass4 implements View.OnClickListener {
        AnonymousClass4() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View v) {
            int iLang = DrawSketchActivity.this.mGState.getAoGISLanguage();
            UILanguageUtil.setAoLanguage(DrawSketchActivity.this, iLang);
            new AlertDialog.Builder(DrawSketchActivity.this).setTitle(R.string.menu_main_file).setItems(new String[]{DrawSketchActivity.this.getString(R.string.menu_main_file_save), DrawSketchActivity.this.getString(R.string.menu_main_file_lyrmgr), DrawSketchActivity.this.getString(R.string.menu_main_file_resetview), DrawSketchActivity.this.getString(R.string.menu_main_file_layerrestore), DrawSketchActivity.this.getString(R.string.menu_sketch_exit)}, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.4.1
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialog, int which) {
                    switch (which) {
                        case 0:
                            DrawSketchActivity.this.m_Map.saveMapAndAreas();
                            break;
                        case 1:
                            Bundle bundle = new Bundle();
                            bundle.putInt(LayerManagerActivity.BUNDLE_MAP, AoGISApplication.getCurrentApp().addInAppTaskData(DrawSketchActivity.this.m_Map));
                            Intent intent = new Intent(DrawSketchActivity.this, (Class<?>) LayerManagerActivity.class);
                            intent.putExtras(bundle);
                            DrawSketchActivity.this.startActivityForResult(intent, 1006);
                            break;
                        case 2:
                            DrawSketchActivity.this.m_MapView.resetView();
                            break;
                        case 3:
                            ArrayList<String> list = new ArrayList<>(20);
                            short sNum = DrawSketchActivity.this.m_Map.getWorkAreaNum();
                            for (short i = 0; i < sNum; i = (short) (i + 1)) {
                                list.add(DrawSketchActivity.this.m_Map.getItemName(i));
                            }
                            String[] waNames = (String[]) list.toArray(new String[list.size()]);
                            AlertDialog.Builder builder = new AlertDialog.Builder(DrawSketchActivity.this).setTitle(R.string.RGMAP_PROMPT_RESETLAYER);
                            builder.setItems(waNames, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.4.1.1
                                @Override // android.content.DialogInterface.OnClickListener
                                public void onClick(DialogInterface dialog2, int which2) {
                                    GeoRect rect;
                                    WorkArea wa = DrawSketchActivity.this.m_Map.getItemWorkAreaMustEdit((short) which2);
                                    if (wa != null && (rect = wa.getRect()) != null) {
                                        DrawSketchActivity.this.m_MapView.resetView(rect);
                                    }
                                }
                            });
                            builder.create().show();
                            break;
                        case 4:
                            DrawSketchActivity.this.finish();
                            break;
                    }
                }
            }).show();
            DrawSketchActivity.this.mainRadioGroup.clearCheck();
        }
    }

    @Override // android.app.Activity, android.content.ComponentCallbacks
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        switch (newConfig.orientation) {
            case 1:
                double[] d = this.m_MapView.getDrawParams();
                this.dm = new DisplayMetrics();
                getWindowManager().getDefaultDisplay().getMetrics(this.dm);
                widthP = this.dm.widthPixels;
                heightP = this.dm.heightPixels;
                double lx2 = widthH / d[2];
                double ly2 = heightH / d[2];
                double x2 = d[0] + (lx2 / 2.0d);
                double y2 = d[1] + (ly2 / 2.0d);
                double px = x2 - ((widthP / 2) / d[2]);
                double py = y2 - ((heightP / 2) / d[2]);
                this.m_MapView.setDrawParams(px, py, d[2]);
                break;
            case 2:
                double[] d2 = this.m_MapView.getDrawParams();
                this.dm = new DisplayMetrics();
                getWindowManager().getDefaultDisplay().getMetrics(this.dm);
                widthH = this.dm.widthPixels;
                heightH = this.dm.heightPixels;
                double lx1 = widthP / d2[2];
                double ly1 = heightP / d2[2];
                double x1 = d2[0] + (lx1 / 2.0d);
                double y1 = d2[1] + (ly1 / 2.0d);
                double hx = x1 - ((widthH / 2) / d2[2]);
                double hy = y1 - ((heightH / 2) / d2[2]);
                this.m_MapView.setDrawParams(hx, hy, d2[2]);
                break;
        }
    }

    @Override // android.app.Activity
    protected void onDestroy() {
        this.m_Map.closeMap();
        super.onDestroy();
    }

    private boolean setEditLayout() {
        LayoutInflater inflater = getLayoutInflater();
        ViewGroup toolbar = (ViewGroup) inflater.inflate(R.layout.toolbar_edit_comm1, (ViewGroup) this.m_mainRelativeLayout, false);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(-2, -2);
        params.topMargin = (int) DisplayHelper.dip2px(this, 10.0f);
        params.leftMargin = (int) DisplayHelper.dip2px(this, 10.0f);
        params.addRule(9, -1);
        toolbar.setLayoutParams(params);
        this.m_mainRelativeLayout.addView(toolbar);
        Button btn = new Button(this);
        btn.setText(R.string.btnOK);
        btn.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.8
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawSketchActivity.this.m_MapView.getTouchListener().onEditOk();
            }
        });
        toolbar.addView(btn, new ViewGroup.LayoutParams(-2, -2));
        Button btn2 = new Button(this);
        btn2.setText(R.string.btnCancel);
        btn2.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.9
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DrawSketchActivity.this.m_MapView.getTouchListener().onEditCancel();
            }
        });
        toolbar.addView(btn2, new ViewGroup.LayoutParams(-2, -2));
        Button btn3 = new Button(this);
        btn3.setText(R.string.btnReturn);
        btn3.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.DrawSketchActivity.10
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                View toolbar2 = DrawSketchActivity.this.findViewById(R.id.toolbar_edit_comm);
                DrawSketchActivity.this.m_mainRelativeLayout.removeView(toolbar2);
                DrawSketchActivity.this.m_MapView.setZoomTouchListener();
            }
        });
        toolbar.addView(btn3, new ViewGroup.LayoutParams(-2, -2));
        return true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean prepareEdit(GeoClassType type) {
        return setEditLayout();
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case 4:
                String text = getResources().getString(R.string.attribute_back_toast);
                Toast.makeText(this, text, 0).show();
                return true;
            default:
                return super.onKeyDown(keyCode, event);
        }
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1006 && resultCode == 1) {
            this.m_MapView.updateView();
        }
        if (this.m_MapView.getTouchListener() != null) {
            this.m_MapView.getTouchListener().onActivityResult(requestCode, resultCode, data);
        }
    }
}
