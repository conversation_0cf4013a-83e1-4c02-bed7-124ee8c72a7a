package com.AoRGMap.prb;

import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.Toast;
import com.AoDevBase.dialog.DictionaryDialog;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.ui.ItemDictInfo;
import com.AoDevBase.ui.ScrollScreen;
import com.AoGIS.ui.AoGISUIActivity;
import com.AoGIS.util.GdbAttributesMap;
import com.AoGIS.util.StringUtil;
import com.AoGIS.util.dict.DictionaryManager;
import com.AoGIS.util.dict.FileDictionaryProvider;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.R;
import com.AoRGMap.extdb.AttItemModel;
import com.AoRGMap.extdb.ExtStorageAttDB;
import com.AoRGMap.extdb.OnExtButtonClickListener;
import java.util.ArrayList;
import java.util.HashMap;

/* loaded from: classes.dex */
public class InnerTableAttributeActivity extends AoGISUIActivity {
    static final int DECIMAL = 3;
    static final int EXT_FILE = 32;
    static final int EXT_MEDIA = 8;
    static final int MULTI_DICT = 2;
    public static final String PARAM_STRING_DICT_PATH = "DictPath";
    public static final String PARAM_STRING_FILE_PATH = "FilePath";
    public static final String PARAM_STRING_KEY = "Key";
    public static final String PARAM_STRING_MEDIA_PATH = "MediaPath";
    public static final String PARAM_STRING_TABLE_NAME = "TableName";
    public static final String PARAM_STRING_VALUE = "Value";
    static final int SIGN_NUMBER = 2;
    static final int SINGLE_DICT = 1;
    static final int TEXT = 0;
    static final int UNSIGN_NUMBER = 1;
    private GdbAttributesMap<String, Object> data;
    private HashMap<String, FieldType> fields;
    private FrameLayout mExtendView;
    protected String mKey;
    String[] mValue;
    protected String mValueTblName;
    private ScrollScreen screen;
    private String mMediaPath = AoRGMapActivity.getCurrentMapPath() + "/images/";
    private String mFilePath = AoRGMapActivity.getCurrentMapPath() + "/images/";
    private String mDictPath = Environment.getExternalStorageDirectory().toString();
    private ArrayList<AttributeItem> itemList = new ArrayList<>();
    private ScreenListener mInnerListener = new ScreenListener();
    private ArrayList<View> mContextViews = new ArrayList<>();
    Toast toast = null;

    public enum FieldType {
        LONG,
        STRING,
        INT,
        DOUBLE
    }

    private class ScreenListener implements ScrollScreen.ScreenContentFactory, ScrollScreen.OnScreenChangeListener {
        private ScreenListener() {
        }

        @Override // com.AoDevBase.ui.ScrollScreen.ScreenContentFactory
        public View createScreenContent(int index) {
            if (index == 0) {
                return (View) InnerTableAttributeActivity.this.mContextViews.get(0);
            }
            if (index == 1) {
                return InnerTableAttributeActivity.this.mExtendView;
            }
            throw new RuntimeException();
        }

        @Override // com.AoDevBase.ui.ScrollScreen.OnScreenChangeListener
        public void onScreenChanged(int index) {
            if (index != 0) {
                InnerTableAttributeActivity.this.m_btnCancel.setVisibility(4);
                InnerTableAttributeActivity.this.m_btnOK.setText(R.string.ao_btnReturn);
            } else {
                InnerTableAttributeActivity.this.m_btnCancel.setVisibility(0);
                InnerTableAttributeActivity.this.m_btnOK.setText(R.string.ao_btnOK);
            }
        }
    }

    private void ensureScrollScreen() {
        if (this.screen == null) {
            setContentView(R.layout.ao_screen_content);
        }
    }

    public ScrollScreen getScrollScreen() {
        ensureScrollScreen();
        return this.screen;
    }

    @Override // com.AoGIS.ui.AoGISUIActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        initData();
        setTitle(this.mKey);
        this.screen = new ScrollScreen(this);
        this.mBottomLayout = this.screen;
        super.onCreate(savedInstanceState);
        if (this.screen == null) {
            throw new RuntimeException("Your content must have a ScrollScreen whose id com.AoRGMap.ui.attribute is '.R.id.scroll_screen'");
        }
        this.mExtendView = new FrameLayout(this);
        this.m_btnOK.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.InnerTableAttributeActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (InnerTableAttributeActivity.this.getScrollScreen().getCurrentScreen() != 0) {
                    InnerTableAttributeActivity.this.getScrollScreen().snapToScreen(0);
                } else {
                    InnerTableAttributeActivity.this.onClickOK();
                }
            }
        });
        this.m_btnCancel.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.InnerTableAttributeActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                if (InnerTableAttributeActivity.this.getScrollScreen().getCurrentScreen() != 0) {
                    InnerTableAttributeActivity.this.getScrollScreen().snapToScreen(0);
                } else {
                    InnerTableAttributeActivity.this.onClickCancel();
                }
            }
        });
        onInitializeViews(new ContextViewManager());
        ScrollScreen scrollScreen = getScrollScreen();
        scrollScreen.addScreen(2, this.mInnerListener);
        scrollScreen.setOnScreenChangedListener(this.mInnerListener);
        scrollScreen.setAllowDrag(false);
        this.screen.setFocusable(true);
        this.screen.setFocusableInTouchMode(true);
        this.screen.requestFocus();
    }

    public class ContextViewManager {
        private ContextViewManager() {
        }

        public ViewGroup addStandardAttributeView() {
            ViewGroup viewGroup = (ViewGroup) LayoutInflater.from(InnerTableAttributeActivity.this).inflate(R.layout.ao_attributes_container, (ViewGroup) InnerTableAttributeActivity.this.getScrollScreen(), false);
            InnerTableAttributeActivity.this.mContextViews.add(viewGroup);
            ViewGroup container = (ViewGroup) viewGroup.findViewById(R.id.ao_attributes_container_view);
            return container;
        }

        public void addView(View v) {
            InnerTableAttributeActivity.this.mContextViews.add(v);
        }
    }

    public boolean updateAttributeMap(Iterable<AttributeItem> list) {
        String key;
        for (AttributeItem item : list) {
            Object name = item.getAttributeName();
            if (name != null) {
                if (name instanceof Number) {
                    key = getResources().getString(((Number) name).intValue());
                } else {
                    key = name.toString();
                }
                String value = item.getItemData().toString();
                this.data.put(key, value);
            }
        }
        return true;
    }

    public void onInitializeViews(ContextViewManager mgr) {
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        saveAttToDB();
        setResult(-1);
        finish();
    }

    @Override // com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        setResult(0);
        finish();
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == 4 && event.getRepeatCount() == 0 && getScrollScreen().getCurrentScreen() != 0) {
            getScrollScreen().snapToScreen(0);
            return true;
        }
        if (keyCode == 4) {
            if (this.toast == null) {
                this.toast = Toast.makeText(this, R.string.ao_attribute_back_toast, 0);
            } else {
                this.toast.cancel();
            }
            this.toast.setText(R.string.ao_attribute_back_toast);
            this.toast.show();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public void InitFieldsInfo(HashMap<String, FieldType> fields) {
    }

    public GdbAttributesMap<String, Object> getAttributeMap() {
        return this.data;
    }

    public HashMap<String, FieldType> getfieldsMap() {
        return this.fields;
    }

    private void initData() {
        Bundle bundle = getIntent().getExtras();
        this.mValueTblName = bundle.getString("TableName");
        this.mKey = bundle.getString("Key");
        this.mValue = bundle.getStringArray(PARAM_STRING_VALUE);
        this.mDictPath = bundle.getString("DictPath");
        this.mFilePath = bundle.getString("FilePath");
        this.mMediaPath = bundle.getString("MediaPath");
        this.data = new GdbAttributesMap<>();
    }

    public String getData(String name) {
        Object obj = this.data.get(name);
        if (obj != null) {
            return obj.toString();
        }
        return null;
    }

    public String getData(int string_res_id) {
        String str = getResources().getString(string_res_id);
        return getData(str);
    }

    public String getData(AttributeItem item) {
        Object obj = item.getAttributeName();
        if (obj != null) {
            if (obj instanceof Number) {
                return getData(((Number) obj).intValue());
            }
            return getData(obj.toString());
        }
        return null;
    }

    void initMainView(ViewGroup container) {
        String value;
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = this.mKey;
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        EditTextEditorInfo info = null;
        String uiTblName = this.mValueTblName + "UI";
        ArrayList<AttItemModel> itemModels = ExtStorageAttDB.getAllUIItems(uiTblName);
        if (itemModels == null) {
            container.addView(group.getInnerView());
            return;
        }
        for (int i = 0; i < itemModels.size(); i++) {
            AttItemModel atm = itemModels.get(i);
            String fldName = atm.getFldName();
            switch (atm.getValueType()) {
                case 0:
                    info = EditTextEditorInfo.textEditor();
                    break;
                case 1:
                    info = EditTextEditorInfo.unsignedNumberEditor();
                    break;
                case 2:
                    info = EditTextEditorInfo.signedNumberEditor();
                    break;
                case 3:
                    info = EditTextEditorInfo.decimalEditor();
                    break;
            }
            if (this.mValue == null) {
                value = null;
            } else {
                value = this.mValue[i + 2];
            }
            String fldCnName = atm.getFldCnName();
            if (value == null) {
                value = "";
            }
            AttributeItem item = AttributeUIHelper.createItem(group, fldName, fldCnName, info, value);
            int btnType = atm.getBtnType();
            if ((btnType & 1) == 1) {
                item.setPropDictName(atm.getBtnValue(), 1);
            } else if ((btnType & 2) == 2) {
                item.setPropDictName(atm.getBtnValue(), 2);
            }
            if ((btnType & 8) == 8) {
                item.addButton(new AttributeButton(new OnExtButtonClickListener(new String[]{fldName}) { // from class: com.AoRGMap.prb.InnerTableAttributeActivity.3
                    @Override // com.AoRGMap.extdb.OnExtButtonClickListener, android.view.View.OnClickListener
                    public void onClick(View v) {
                        String innnerFldName = getExtInfo()[0];
                        Intent intent = new Intent(InnerTableAttributeActivity.this, (Class<?>) AttExtMediaDataActivity.class);
                        Bundle bundle = new Bundle();
                        bundle.putString("KeyWord", InnerTableAttributeActivity.this.mKey);
                        bundle.putString("TableName", InnerTableAttributeActivity.this.mValueTblName);
                        bundle.putString("FldName", innnerFldName);
                        bundle.putString("MediaPath", InnerTableAttributeActivity.this.mMediaPath);
                        intent.putExtras(bundle);
                        InnerTableAttributeActivity.this.startActivityForResult(intent, 0);
                    }
                }, getResources().getDrawable(android.R.drawable.ic_input_add)));
            }
            if ((btnType & 32) == 32) {
                item.addButton(new AttributeButton(new OnExtButtonClickListener(new String[]{fldName}) { // from class: com.AoRGMap.prb.InnerTableAttributeActivity.4
                    @Override // com.AoRGMap.extdb.OnExtButtonClickListener, android.view.View.OnClickListener
                    public void onClick(View v) {
                        String innnerFldName = getExtInfo()[0];
                        Intent intent = new Intent(InnerTableAttributeActivity.this, (Class<?>) AttExtFileDataActivity.class);
                        Bundle bundle = new Bundle();
                        bundle.putString("KeyWord", InnerTableAttributeActivity.this.mKey);
                        bundle.putString("TableName", InnerTableAttributeActivity.this.mValueTblName);
                        bundle.putString("FldName", innnerFldName);
                        bundle.putString("FilePath", InnerTableAttributeActivity.this.mFilePath);
                        intent.putExtras(bundle);
                        InnerTableAttributeActivity.this.startActivityForResult(intent, 0);
                    }
                }, getResources().getDrawable(android.R.drawable.ic_dialog_email)));
            }
            this.itemList.add(item);
        }
        container.addView(group.getInnerView());
    }

    private void saveAttToDB() {
        updateAttributeMap(this.itemList);
        if (this.mValue == null) {
            ExtStorageAttDB.insertValueTable(this.mKey, this.itemList, this.mValueTblName);
        } else {
            ExtStorageAttDB.updateValueTable(Integer.parseInt(this.mValue[0]), this.itemList, this.mValueTblName);
        }
    }

    protected void MakeDictButtton(Iterable<AttributeItem> items) {
        View.OnClickListener listener = new View.OnClickListener() { // from class: com.AoRGMap.prb.InnerTableAttributeActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttributeItem item = AttributeItem.fromButtons(v);
                InnerTableAttributeActivity.this.showDictionaryDialog(item.getPropDictName(), item);
            }
        };
        for (AttributeItem item : items) {
            String prop_dict = StringUtil.Trim(item.getLabelText(), ": ");
            if (DictionaryManager.isAvailable(this.mDictPath, prop_dict) && item.getPropDictName() == null) {
                item.setPropDictName(prop_dict);
            }
            String obj = item.getPropDictName();
            if (obj != null) {
                item.addButton(new AttributeButton(listener, null));
            }
        }
    }

    public void showDictionaryDialog(String dictName, String title, EditText editor, boolean bInsert, boolean bReplace, String sperator, int levelLimit, String defaultString) {
        String[] dicts = dictName.split("[,/.|]");
        DictionaryDialog dlg = new DictionaryDialog(this, new FileDictionaryProvider(this.mDictPath, dicts));
        dlg.showListDialog(title, editor, bInsert, bReplace, sperator, levelLimit, defaultString);
    }

    public void showDictionaryDialog(String dictName, String title, EditText editor, boolean bInsert, boolean bReplace, String sperator, int levelLimit, String defaultString, int dictType) {
        String[] dicts = dictName.split("[,/.|]");
        DictionaryDialog dlg = new DictionaryDialog(this, new FileDictionaryProvider(this.mDictPath, dicts), dictType);
        dlg.showListDialog(title, editor, bInsert, bReplace, sperator, levelLimit, defaultString, dictType);
    }

    public void showDictionaryDialog(String dictName, AttributeItem item) {
        EditText editor = item.getEditorEditText();
        String title = item.getLabelText();
        ItemDictInfo info = item.getPropDict();
        if (info != null && editor != null) {
            if (info.dictType == 1) {
                showDictionaryDialog(dictName, title, editor, info.bInsert, info.bReplace, info.seperator, info.levelLimit, info.defaultString);
            } else {
                showDictionaryDialog(dictName, title, editor, info.bInsert, info.bReplace, info.seperator, info.levelLimit, info.defaultString, info.dictType);
            }
        }
    }
}
