package com.AoRGMap.prb;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SimpleAdapter;
import android.widget.Toast;
import com.AoDevBase.util.UILanguageUtil;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.MyImageView1Activity;
import com.AoRGMap.MySoundViewActivity;
import com.AoRGMap.MyVideoViewActivity;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.Util.ExtAudioRecorderActivity;
import com.AoRGMap.camera.CameraTaker2Activity;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/* loaded from: classes.dex */
public class AttGPointDescActivity extends Activity {
    public static final String KEYWORD = "KeyWord";
    public static final String PARAM_BOOL_IS_NEW_ATTRIBUTE = "isNew";
    public static final int RequestDesAudio = 1;
    public static final int RequestDesPhoto = 3;
    public static final int RequestDesVedio = 2;
    private SimpleAdapter mAdapter;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private String mPathString = AoRGMapActivity.getCurrentMapPath() + "/images/";
    private String mKeyString = "";
    private ArrayList<Map<String, Object>> mArrayList = null;
    private String FILEPATH = "Filefath";
    private int miSize = 0;
    private SELECTOR mSelector = SELECTOR.AUDIO_SEL;
    int mSelPos = -1;
    private ListView mListView = null;
    private RadioGroup mRgSelector = null;
    private RadioGroup mRgMenu = null;
    private RadioButton mRbPhoto = null;
    private RadioButton mRbAudio = null;
    private RadioButton mRbVideo = null;
    private RadioButton mRdAddButton = null;
    private RadioButton mRdPlayButton = null;
    private RadioButton mRdDelButton = null;

    enum SELECTOR {
        PHOTO_SEL,
        AUDIO_SEL,
        VEDIO_SEL
    }

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        super.onCreate(savedInstanceState);
        requestWindowFeature(1);
        setContentView(R.layout.att_geopoint_desc);
        InitView();
        InitListener();
        InitData();
    }

    private void InitView() {
        this.mListView = (ListView) findViewById(R.id.id_listview_geopointdesc);
        this.mRgSelector = (RadioGroup) findViewById(R.id.id_radioGroup_selector);
        this.mRgMenu = (RadioGroup) findViewById(R.id.id_radioGroup_menu);
        this.mRbPhoto = (RadioButton) findViewById(R.id.id_selector_photo);
        this.mRbAudio = (RadioButton) findViewById(R.id.id_selector_audio);
        this.mRbVideo = (RadioButton) findViewById(R.id.id_selector_vedio);
        this.mRdAddButton = (RadioButton) findViewById(R.id.id_menu_add);
        this.mRdPlayButton = (RadioButton) findViewById(R.id.id_menu_play);
        this.mRdDelButton = (RadioButton) findViewById(R.id.id_menu_delete);
        this.mRgSelector.check(this.mRbAudio.getId());
    }

    private void InitListener() {
        this.mListView.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.1
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> arg0, View arg1, int arg2, long arg3) {
                if (((ListView) arg0).getTag() != null) {
                    ((View) ((ListView) arg0).getTag()).setBackgroundDrawable(null);
                }
                ((ListView) arg0).setTag(arg1);
                arg1.setBackgroundColor(-3355444);
                AttGPointDescActivity.this.mSelPos = arg2;
            }
        });
        this.mRbPhoto.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                AttGPointDescActivity.this.InitPhotoData();
                AttGPointDescActivity.this.mRgSelector.check(AttGPointDescActivity.this.mRbPhoto.getId());
                AttGPointDescActivity.this.mSelector = SELECTOR.PHOTO_SEL;
                AttGPointDescActivity.this.mRgMenu.clearCheck();
            }
        });
        this.mRbAudio.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                AttGPointDescActivity.this.InitAudioData();
                AttGPointDescActivity.this.mRgSelector.check(AttGPointDescActivity.this.mRbAudio.getId());
                AttGPointDescActivity.this.mSelector = SELECTOR.AUDIO_SEL;
                AttGPointDescActivity.this.mRgMenu.clearCheck();
            }
        });
        this.mRbVideo.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                AttGPointDescActivity.this.InitVedioData();
                AttGPointDescActivity.this.mRgSelector.check(AttGPointDescActivity.this.mRbVideo.getId());
                AttGPointDescActivity.this.mSelector = SELECTOR.VEDIO_SEL;
                AttGPointDescActivity.this.mRgMenu.clearCheck();
            }
        });
        this.mRdAddButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                if (AttGPointDescActivity.this.mSelector == SELECTOR.PHOTO_SEL) {
                    if (AttGPointDescActivity.this.mGState.getCamaraType() == 0) {
                        String fileName = AttGPointDescActivity.this.mPathString + AttGPointDescActivity.this.mKeyString + "_" + String.valueOf(AttGPointDescActivity.this.miSize) + ".jpg";
                        File vFile = new File(fileName);
                        if (!vFile.exists()) {
                            File vDirPath = vFile.getParentFile();
                            vDirPath.mkdirs();
                            try {
                                vFile.createNewFile();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                        Uri uri = Uri.fromFile(vFile);
                        Intent intent = new Intent("android.media.action.IMAGE_CAPTURE");
                        intent.putExtra("output", uri);
                        intent.putExtra("isNew", true);
                        AttGPointDescActivity.this.startActivityForResult(intent, 3);
                    } else {
                        String fileName2 = AttGPointDescActivity.this.mPathString + AttGPointDescActivity.this.mKeyString + "_" + String.valueOf(AttGPointDescActivity.this.miSize) + ".jpg";
                        Intent intent2 = new Intent(AttGPointDescActivity.this, (Class<?>) CameraTaker2Activity.class);
                        Bundle bundle = new Bundle();
                        bundle.putString("JPGPANTH", fileName2);
                        intent2.putExtras(bundle);
                        AttGPointDescActivity.this.startActivityForResult(intent2, 3);
                    }
                } else if (AttGPointDescActivity.this.mSelector == SELECTOR.AUDIO_SEL) {
                    if (AttGPointDescActivity.this.mGState.getAudioType() == 0) {
                        Intent intent3 = new Intent("android.provider.MediaStore.RECORD_SOUND");
                        intent3.putExtra("isNew", true);
                        AttGPointDescActivity.this.startActivityForResult(intent3, 1);
                    } else {
                        Intent intent4 = new Intent(AttGPointDescActivity.this, (Class<?>) ExtAudioRecorderActivity.class);
                        intent4.putExtra(ExtAudioRecorderActivity.AUDIOSAVEPATH, AttGPointDescActivity.this.mPathString);
                        String fileName3 = AttGPointDescActivity.this.mKeyString + "_" + String.valueOf(AttGPointDescActivity.this.miSize) + ".wav";
                        intent4.putExtra(ExtAudioRecorderActivity.AUDIOFILENAME, fileName3);
                        AttGPointDescActivity.this.startActivityForResult(intent4, 1);
                    }
                } else if (AttGPointDescActivity.this.mSelector == SELECTOR.VEDIO_SEL) {
                    Intent intent5 = new Intent("android.media.action.VIDEO_CAPTURE");
                    intent5.putExtra("android.intent.extra.videoQuality", 1);
                    intent5.putExtra("isNew", true);
                    AttGPointDescActivity.this.startActivityForResult(intent5, 2);
                }
                AttGPointDescActivity.this.mRgMenu.clearCheck();
            }
        });
        this.mRdPlayButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.6
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                if (AttGPointDescActivity.this.mSelPos != -1) {
                    AttGPointDescActivity.this.mRgMenu.clearCheck();
                    Map<String, Object> map = (Map) AttGPointDescActivity.this.mArrayList.get(AttGPointDescActivity.this.mSelPos);
                    Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
                    Map.Entry<String, Object> entry = it.next();
                    String itemString = entry.getValue().toString();
                    String itemString2 = itemString.substring(0, itemString.indexOf("."));
                    if (AttGPointDescActivity.this.mListView.getTag() != null) {
                        ((View) AttGPointDescActivity.this.mListView.getTag()).setBackgroundDrawable(null);
                    }
                    AttGPointDescActivity.this.mSelPos = -1;
                    if (AttGPointDescActivity.this.mSelector != SELECTOR.AUDIO_SEL) {
                        if (AttGPointDescActivity.this.mSelector != SELECTOR.VEDIO_SEL) {
                            if (AttGPointDescActivity.this.mSelector == SELECTOR.PHOTO_SEL) {
                                Intent intentimg = new Intent(AttGPointDescActivity.this, (Class<?>) MyImageView1Activity.class);
                                Bundle bundle = new Bundle();
                                bundle.putString("VIDE0PATH", itemString2);
                                bundle.putString("FolderATH", AttGPointDescActivity.this.mPathString);
                                intentimg.putExtras(bundle);
                                AttGPointDescActivity.this.startActivity(intentimg);
                                return;
                            }
                            return;
                        }
                        Intent intentvideo = new Intent(AttGPointDescActivity.this, (Class<?>) MyVideoViewActivity.class);
                        Bundle bundle2 = new Bundle();
                        bundle2.putString("VIDE0PATH", itemString2);
                        bundle2.putString("FolderATH", AttGPointDescActivity.this.mPathString);
                        intentvideo.putExtras(bundle2);
                        AttGPointDescActivity.this.startActivity(intentvideo);
                        return;
                    }
                    Intent intentsound = new Intent(AttGPointDescActivity.this, (Class<?>) MySoundViewActivity.class);
                    Bundle bundle3 = new Bundle();
                    bundle3.putString("VIDE0PATH", itemString2);
                    bundle3.putString("FolderATH", AttGPointDescActivity.this.mPathString);
                    intentsound.putExtras(bundle3);
                    AttGPointDescActivity.this.startActivity(intentsound);
                    return;
                }
                Toast.makeText(AttGPointDescActivity.this, "No Selected", 0).show();
            }
        });
        this.mRdDelButton.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.7
            @Override // android.view.View.OnClickListener
            public void onClick(View arg0) {
                if (AttGPointDescActivity.this.mSelPos != -1) {
                    AttGPointDescActivity.this.mRgMenu.clearCheck();
                    new AlertDialog.Builder(AttGPointDescActivity.this).setTitle("RGMap").setMessage(R.string.RGMAP_PROMPT_DELPROMPT).setNegativeButton(R.string.RGMAP_PROMPT_BUTTONCANCEL, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.7.2
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                        }
                    }).setPositiveButton(R.string.RGMAP_PROMPT_BUTTONOK, new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttGPointDescActivity.7.1
                        @Override // android.content.DialogInterface.OnClickListener
                        public void onClick(DialogInterface dialog, int which) {
                            Map<String, Object> map = (Map) AttGPointDescActivity.this.mArrayList.get(AttGPointDescActivity.this.mSelPos);
                            Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
                            Map.Entry<String, Object> entry = it.next();
                            String itemString = entry.getValue().toString();
                            String itempathString = AttGPointDescActivity.this.mPathString + itemString;
                            File file = new File(itempathString);
                            AttGPointDescActivity.this.deleteFile(file);
                            AttGPointDescActivity.this.mSelPos = -1;
                            File vFilepath = new File(AttGPointDescActivity.this.mPathString);
                            if (AttGPointDescActivity.this.mSelector != SELECTOR.AUDIO_SEL) {
                                if (AttGPointDescActivity.this.mSelector != SELECTOR.VEDIO_SEL) {
                                    if (AttGPointDescActivity.this.mSelector == SELECTOR.PHOTO_SEL) {
                                        AttGPointDescActivity.this.miSize = AttGPointDescActivity.this.getData(AttGPointDescActivity.this.mKeyString, vFilepath, "jpg");
                                    }
                                } else {
                                    AttGPointDescActivity.this.miSize = AttGPointDescActivity.this.getData(AttGPointDescActivity.this.mKeyString, vFilepath, "mp4");
                                }
                            } else {
                                AttGPointDescActivity.this.miSize = AttGPointDescActivity.this.getData(AttGPointDescActivity.this.mKeyString, vFilepath, "wav");
                            }
                            AttGPointDescActivity.this.mAdapter.notifyDataSetChanged();
                            if (AttGPointDescActivity.this.mListView.getTag() != null) {
                                ((View) AttGPointDescActivity.this.mListView.getTag()).setBackgroundDrawable(null);
                            }
                            AttGPointDescActivity.this.mSelPos = -1;
                        }
                    }).show();
                } else {
                    Toast.makeText(AttGPointDescActivity.this, R.string.RGMAP_PROMPT_DELETEPOS, 0).show();
                }
            }
        });
    }

    private void InitData() {
        Bundle bundle = getIntent().getExtras();
        this.mKeyString = bundle.getString("KeyWord");
        InitAudioData();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void InitAudioData() {
        this.mArrayList = new ArrayList<>();
        File vFilepath = new File(this.mPathString);
        this.miSize = getData(this.mKeyString, vFilepath, "wav");
        this.mAdapter = new SimpleAdapter(this, this.mArrayList, R.layout.att_geopoint_desclist, new String[]{this.FILEPATH}, new int[]{R.id.id_item_descpath});
        this.mListView.setAdapter((ListAdapter) this.mAdapter);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void InitVedioData() {
        this.mArrayList = new ArrayList<>();
        File vFilepath = new File(this.mPathString);
        this.miSize = getData(this.mKeyString, vFilepath, "mp4");
        this.mAdapter = new SimpleAdapter(this, this.mArrayList, R.layout.att_geopoint_desclist, new String[]{this.FILEPATH}, new int[]{R.id.id_item_descpath});
        this.mListView.setAdapter((ListAdapter) this.mAdapter);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void InitPhotoData() {
        this.mArrayList = new ArrayList<>();
        File vFilepath = new File(this.mPathString);
        this.miSize = getData(this.mKeyString, vFilepath, "jpg");
        this.mAdapter = new SimpleAdapter(this, this.mArrayList, R.layout.att_geopoint_desclist, new String[]{this.FILEPATH}, new int[]{R.id.id_item_descpath});
        this.mListView.setAdapter((ListAdapter) this.mAdapter);
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            switch (requestCode) {
                case 1:
                    if (this.mGState.getAudioType() == 0) {
                        Uri uriRecord = data.getData();
                        String[] proj = {"_data"};
                        Cursor actualRecordCursor = managedQuery(uriRecord, proj, null, null, null);
                        if (actualRecordCursor == null) {
                            Toast.makeText(this, "请在【系统设置】中设置录音方式为用户录音机", 0).show();
                            break;
                        } else {
                            int actual_record_column_index = actualRecordCursor.getColumnIndexOrThrow("_data");
                            actualRecordCursor.moveToFirst();
                            String recordPath = actualRecordCursor.getString(actual_record_column_index);
                            String audioPath = this.mPathString + this.mKeyString + "_" + String.valueOf(this.miSize) + ".wav";
                            moveFile(recordPath, audioPath);
                            File vFilepath = new File(this.mPathString);
                            this.miSize = getData(this.mKeyString, vFilepath, "wav");
                            this.mAdapter.notifyDataSetChanged();
                            break;
                        }
                    } else {
                        File vFilepath2 = new File(this.mPathString);
                        this.miSize = getData(this.mKeyString, vFilepath2, "wav");
                        this.mAdapter.notifyDataSetChanged();
                        break;
                    }
                case 2:
                    Uri uriRecord2 = data.getData();
                    String[] proj2 = {"_data"};
                    Cursor actualRecordCursor2 = managedQuery(uriRecord2, proj2, null, null, null);
                    int actual_record_column_index2 = actualRecordCursor2.getColumnIndexOrThrow("_data");
                    actualRecordCursor2.moveToFirst();
                    String recordPath2 = actualRecordCursor2.getString(actual_record_column_index2);
                    String vedioPath = this.mPathString + this.mKeyString + "_" + String.valueOf(this.miSize) + ".mp4";
                    moveFile(recordPath2, vedioPath);
                    File vFilepath3 = new File(this.mPathString);
                    this.miSize = getData(this.mKeyString, vFilepath3, "mp4");
                    this.mAdapter.notifyDataSetChanged();
                    break;
                case 3:
                    File vFilepath4 = new File(this.mPathString);
                    this.miSize = getData(this.mKeyString, vFilepath4, "jpg");
                    this.mAdapter.notifyDataSetChanged();
                    break;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public int getData(String keyword, File filepath, String keyword1) {
        File[] files;
        this.mArrayList.clear();
        int Photolv = 0;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isFile()) {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf(keyword1) > -1) {
                        Map<String, Object> map = new HashMap<>();
                        map.put(this.FILEPATH, strname);
                        this.mArrayList.add(map);
                        String indexString = strname.substring(strname.lastIndexOf("_") + 1, strname.lastIndexOf("."));
                        int idex = Integer.parseInt(indexString);
                        if (Photolv < idex) {
                            Photolv = idex;
                        }
                    }
                }
            }
        }
        return Photolv + 1;
    }

    public void moveFile(String oldPath, String newPath) {
        int bytesum = 0;
        try {
            File oldFile = new File(oldPath);
            File newFile = new File(newPath);
            if (!newFile.exists()) {
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                while (true) {
                    int byteread = inStream.read(buffer);
                    if (byteread != -1) {
                        bytesum += byteread;
                        System.out.println(bytesum);
                        fs.write(buffer, 0, byteread);
                    } else {
                        inStream.close();
                        oldFile.delete();
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteFile(File file) {
        if (file.exists()) {
            if (file.isFile()) {
                file.delete();
            } else if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File file2 : files) {
                    deleteFile(file2);
                }
            }
            file.delete();
        }
    }
}
