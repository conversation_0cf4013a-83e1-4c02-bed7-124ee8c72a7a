package com.AoRGMap.prb;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import com.AoDevBase.dialog.DateDialog;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeExtActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoDevBase.util.UILanguageUtil;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.extdb.ExtStorageAttDB;
import com.AoRGMap.extdb.MapCodeXmlService;
import com.AoRGMap.extdb.TableSetModel;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;

/* loaded from: classes.dex */
public class AttSampleActivity extends PrbAttributeActivity {
    double lpX;
    double lpY;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private double[] mdJwd = new double[2];
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private AttributeItem itemGeoPoint = null;
    private AttributeItem itemSamCode = null;
    private AttributeItem itemDate = null;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttSampleActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strSample));
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        int iLang = this.mGState.getAoGISLanguage();
        UILanguageUtil.setAoLanguage(this, iLang);
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        String kX = getResources().getString(R.string.RGMAP_FLDNAME_PXX);
        String kY = getResources().getString(R.string.RGMAP_FLDNAME_PYY);
        getAttributeMap().put(kX, Double.valueOf(getX()));
        getAttributeMap().put(kY, Double.valueOf(getY()));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LONGITUDE, Double.valueOf(this.mdJwd[0]));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LATITUDE, Double.valueOf(this.mdJwd[1]));
        area.setNamedAttributes(getGeometryId(), data);
        if (getAttributeMap().containsKey("ROUTECODE")) {
            String strCode = getAttributeMap().get("ROUTECODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        final GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_sample);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_ROUTECODE, R.string.rg_route_no, EditTextEditorInfo.textEditor(), map, defData));
        String defData2 = gstate.GetCurrentPointCode();
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_GEOPOINT, R.string.rg_gpoint_no, EditTextEditorInfo.textEditor(), map, defData2);
        this.itemList.add(item);
        this.itemGeoPoint = item;
        String strData = String.format("%.2f, %.2f", Double.valueOf(getX()), Double.valueOf(getY()));
        String defData3 = getResources().getString(R.string.nodata);
        AttributeUIHelper.createItem(group, (String) null, R.string.rg_xy, EditTextEditorInfo.textEditor(), strData, defData3).getEditorEditText().setEnabled(false);
        this.mdJwd = ConverXyToJwd(getX(), getY());
        String strData2 = String.format("%f, %f", Double.valueOf(this.mdJwd[0]), Double.valueOf(this.mdJwd[1]));
        String defData4 = getResources().getString(R.string.nodata);
        AttributeUIHelper.createItem(group, (String) null, R.string.RGMAP_PROMPT_JWCOORD, EditTextEditorInfo.textEditor(), strData2, defData4).getEditorEditText().setEnabled(false);
        String defData5 = Integer.toString(gstate.GetCurrentRouteingCode() - 1);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_R_CODE, R.string.photo_rcode, EditTextEditorInfo.textEditor(), map, defData5));
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_SAMPLE_CODE, R.string.sample_no, EditTextEditorInfo.unsignedNumberEditor(), map, (String) null);
        if (isNewFlag()) {
            int[] ids = getWorkArea().getGeometryIdListByAtt(String.format(" ROUTECODE = '%s' AND GEOPOINT = '%s' ", gstate.GetCurrentRouteCode(), gstate.GetCurrentPointCode()));
            int maxid = -1;
            for (int i = 0; ids != null && i < ids.length; i++) {
                if (maxid < ids[i]) {
                    maxid = ids[i];
                }
            }
            if (maxid > 0) {
                try {
                    if (getWorkArea().getNamedAttributes(maxid).get("CODE").toString().trim().length() > 0) {
                        int newid = Integer.parseInt(getWorkArea().getNamedAttributes(maxid).get("CODE").toString());
                        item2.setItemData(Integer.toString(newid + 1));
                    }
                } catch (RuntimeException e) {
                }
            }
            item2.setItemData(Integer.toString(1));
        }
        this.itemList.add(item2);
        this.itemSamCode = item2;
        AttributeItem item3 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_TYPE, R.string.sample_class, EditTextEditorInfo.textEditor(), map, (String) null);
        item3.setPropDict(getResources().getString(R.string.dic_SampleType), true, true, ",", 1);
        this.itemList.add(item3);
        AttributeItem item4 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_FNAME, R.string.sample_yanxing, EditTextEditorInfo.textEditor(), map, (String) null);
        item4.setPropDictName(getResources().getString(R.string.dic_stone_name));
        this.itemList.add(item4);
        this.itemList.add(AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_SWEIGHT, R.string.sample_weight, EditTextEditorInfo.textEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "BLOCKS", R.string.sample_num, EditTextEditorInfo.unsignedNumberEditor(), map, (String) null));
        this.itemList.add(AttributeUIHelper.createItem(group, "DEPTH", R.string.sample_deepth, EditTextEditorInfo.textEditor(), map, (String) null));
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String defData6 = df.format(date);
        AttributeItem item5 = AttributeUIHelper.createItem(group, AttributeExtActivity.PARAM_STRING_DATE, R.string.sample_date, EditTextEditorInfo.textEditor(), map, defData6);
        this.itemList.add(item5);
        this.itemDate = item5;
        item5.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttSampleActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                DateDialog dateDialog = new DateDialog(AttSampleActivity.this);
                String str = AttSampleActivity.this.getResources().getString(R.string.RGMAP_PROMPT_SETDATE);
                dateDialog.showListDialog(str, AttSampleActivity.this.itemDate.getEditorEditText());
            }
        }, null));
        this.itemList.add(AttributeUIHelper.createItem(group, "LOCATION", R.string.sample_place, EditTextEditorInfo.textEditor(), map, (String) null));
        AttributeItem item6 = AttributeUIHelper.createItem(group, "GEOUNIT", R.string.sample_layer, EditTextEditorInfo.textEditor(), map, (String) null);
        item6.setPropDictName(getResources().getString(R.string.dic_tiantu_unit));
        this.itemList.add(item6);
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_SPNAME, R.string.sample_person, EditTextEditorInfo.textEditor(), map, (String) null);
        item7.setPropDictName(getResources().getString(R.string.dic_name));
        this.itemList.add(item7);
        container.addView(group.getInnerView());
        final String dbPath = getMapNameNoExt() + "prb";
        if (new File(dbPath).exists()) {
            final String dictPath = gstate.getDictPath();
            final String ImageExt = getMapForld() + "ImageExt/";
            File file = new File(ImageExt);
            if (!file.exists()) {
                file.mkdir();
            }
            final String FileExt = getMapForld() + "FileExt/";
            File file2 = new File(FileExt);
            if (!file2.exists()) {
                file2.mkdir();
            }
            final String SketchExt = getMapForld() + "SketchExt/";
            File file3 = new File(SketchExt);
            if (!file3.exists()) {
                file3.mkdir();
            }
            ExtStorageAttDB.open(this, dbPath);
            ArrayList<String> tblTypes = ExtStorageAttDB.getAllTblType(PRBAreas.m_strSample);
            if (tblTypes != null) {
                for (int j = 0; j < tblTypes.size(); j++) {
                    AttributeGroup.AttributeGroupParams groupParam2 = new AttributeGroup.AttributeGroupParams();
                    groupParam2.title = tblTypes.get(j);
                    AttributeGroup group2 = new AttributeGroup(this, groupParam2, container);
                    ArrayList<TableSetModel> tbls = ExtStorageAttDB.getAllExtTbl(PRBAreas.m_strSample, groupParam2.title);
                    if (tbls != null) {
                        Iterator<TableSetModel> it = tbls.iterator();
                        while (it.hasNext()) {
                            final TableSetModel tbl = it.next();
                            AttributeItem item8 = AttributeUIHelper.createItem(group2, tbl.getTblName(), tbl.getTblCnName(), EditTextEditorInfo.textEditor(), map, (String) null);
                            item8.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttSampleActivity.2
                                @Override // android.view.View.OnClickListener
                                public void onClick(View arg0) {
                                    if (AttSampleActivity.this.bHasKey()) {
                                        Intent extIntent = new Intent();
                                        extIntent.setClass(AttSampleActivity.this, ExtStorageActivity.class);
                                        extIntent.putExtra("DictPath", dictPath);
                                        extIntent.putExtra("SqlDB", dbPath);
                                        extIntent.putExtra("TableName", tbl.getTblName());
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_TABLE_CNAME, tbl.getTblCnName());
                                        extIntent.putExtra("MediaPath", ImageExt);
                                        extIntent.putExtra("FilePath", FileExt);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_SKETCH_PATH, SketchExt);
                                        extIntent.putExtra("Key", gstate.GetCurrentRouteCode() + "_" + AttSampleActivity.this.itemGeoPoint.getEditorEditText().getText().toString() + "_" + AttSampleActivity.this.itemSamCode.getEditorEditText().getText().toString());
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_GEOPOINTCODE, AttSampleActivity.this.itemGeoPoint.getEditorEditText().getText().toString());
                                        String strTemp = String.format("%f", Double.valueOf(AttSampleActivity.this.getX()));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_XX, strTemp);
                                        String strTemp2 = String.format("%f", Double.valueOf(AttSampleActivity.this.getY()));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_YY, strTemp2);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_MAPCODE, AttSampleActivity.this.getMapCode());
                                        String strTemp3 = String.format("%.2f", Double.valueOf(AttSampleActivity.this.mdJwd[1]));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LATITUDE, strTemp3);
                                        String strTemp4 = String.format("%.2f", Double.valueOf(AttSampleActivity.this.mdJwd[0]));
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_LONGITUDE, strTemp4);
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_ALTITUDE, "0");
                                        extIntent.putExtra(AttributeExtActivity.PARAM_STRING_DATE, "");
                                        AttSampleActivity.this.startActivity(extIntent);
                                    }
                                }
                            }, getResources().getDrawable(android.R.drawable.ic_input_add)));
                            this.itemList.add(item8);
                        }
                    }
                    container.addView(group2.getInnerView());
                }
            }
        }
    }

    private double[] ConverXyToJwd(double dx, double dy) {
        WorkAreaParams.ProjectionType PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        WorkAreaParams.EarthType EarthType = MapParam.getEarthType();
        double dCenterLon = getRadFromDeg(MapParam.lon);
        double[] dJwd = new double[2];
        if (PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(dCenterLon, dx, dy, EarthType);
            if (dPos == null) {
                dPos = new double[]{0.0d, 0.0d};
            }
            String szTemp = String.format("%.8f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            dJwd[0] = dTemp;
            String szTemp2 = String.format("%.8f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            dJwd[1] = dTemp2;
        } else if (PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(dCenterLon, dx, dy, EarthType, MapParam.dx, MapParam.dy);
            if (dPos2 == null) {
                dPos2 = new double[]{0.0d, 0.0d};
            }
            String szTemp3 = String.format("%.8f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            dJwd[0] = dTemp3;
            String szTemp4 = String.format("%.8f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            dJwd[1] = dTemp4;
        } else if (PrjType == WorkAreaParams.ProjectionType.Lambert) {
            double[] dPos3 = ProjectionHelper.LambertRev(getRadFromDeg(MapParam.lat1), getRadFromDeg(MapParam.lat2), dCenterLon, getRadFromDeg(MapParam.lat), dx, dy, EarthType, MapParam.dx, MapParam.dy);
            if (dPos3 == null) {
                dPos3 = new double[]{0.0d, 0.0d};
            }
            String szTemp5 = String.format("%.8f", Double.valueOf((dPos3[0] * 180.0d) / 3.1415926d));
            double dTemp5 = Double.valueOf(szTemp5).doubleValue();
            dJwd[0] = dTemp5;
            String szTemp6 = String.format("%.8f", Double.valueOf((dPos3[1] * 180.0d) / 3.1415926d));
            double dTemp6 = Double.valueOf(szTemp6).doubleValue();
            dJwd[1] = dTemp6;
        } else if (PrjType == WorkAreaParams.ProjectionType.WebMercator) {
            double[] dPos4 = ProjectionHelper.WebMercatorRev(dx, dy, this.mGState.getbInChina());
            if (dPos4 == null) {
                dPos4 = new double[]{0.0d, 0.0d};
            }
            String szTemp7 = String.format("%.8f", Double.valueOf((dPos4[0] * 180.0d) / 3.1415926d));
            double dTemp7 = Double.valueOf(szTemp7).doubleValue();
            dJwd[0] = dTemp7;
            String szTemp8 = String.format("%.8f", Double.valueOf((dPos4[1] * 180.0d) / 3.1415926d));
            double dTemp8 = Double.valueOf(szTemp8).doubleValue();
            dJwd[1] = dTemp8;
        } else {
            dJwd[0] = 0.0d;
            dJwd[1] = 0.0d;
        }
        dJwd[0] = ConverDtoDFM(dJwd[0]);
        dJwd[1] = ConverDtoDFM(dJwd[1]);
        return dJwd;
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }

    private double ConverDtoDFM(double dd) {
        int d = (int) dd;
        int f = (int) ((dd - d) * 60.0d);
        double m = (((dd - d) * 60.0d) - f) * 60.0d;
        double ddfm = (d * 100 * 100) + (f * 100) + m;
        return ddfm;
    }

    private String getMapNameNoExt() {
        if (AoRGMapActivity.getCurrentMap() == null) {
            return "";
        }
        String dbPath = AoRGMapActivity.getCurrentMap().getMapName();
        int lastpos = dbPath.lastIndexOf(46);
        String Path = dbPath.substring(0, lastpos + 1);
        return Path;
    }

    private String getMapForld() {
        if (AoRGMapActivity.getCurrentMap() == null) {
            return "";
        }
        String dbPath = AoRGMapActivity.getCurrentMap().getMapName();
        int lastIndex = dbPath.lastIndexOf(47);
        if (lastIndex <= 0) {
            lastIndex = dbPath.lastIndexOf(92);
        }
        if (lastIndex <= 0) {
            Toast toast = DisplayHelper.getCommonToast();
            toast.setText("路径不正确:" + dbPath);
            toast.show();
            finish();
            return "";
        }
        String Path = dbPath.substring(0, lastIndex + 1);
        return Path;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean bHasKey() {
        String Temp = this.itemGeoPoint.getEditorEditText().getText().toString();
        return !Temp.equals("");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public String getMapCode() {
        String XmlPath = getMapNameNoExt() + "xml";
        MapCodeXmlService service = new MapCodeXmlService();
        try {
            String MapCode = service.parseXml(XmlPath);
            return MapCode;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
