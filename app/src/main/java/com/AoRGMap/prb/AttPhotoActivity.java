package com.AoRGMap.prb;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import com.AoDevBase.ui.AoCompassActivity;
import com.AoDevBase.ui.AttributeActivity;
import com.AoDevBase.ui.AttributeButton;
import com.AoDevBase.ui.AttributeExtActivity;
import com.AoDevBase.ui.AttributeGroup;
import com.AoDevBase.ui.AttributeItem;
import com.AoDevBase.ui.AttributeUIHelper;
import com.AoDevBase.ui.EditTextEditorInfo;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.location.ProjectionHelper;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.GdbAttributesMap;
import com.AoRGMap.AoRGMapActivity;
import com.AoRGMap.ExtOrientationActivity;
import com.AoRGMap.GlobalState;
import com.AoRGMap.MyImageView1Activity;
import com.AoRGMap.MySoundViewActivity;
import com.AoRGMap.MyVideoViewActivity;
import com.AoRGMap.PRBAreas;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;
import com.AoRGMap.SmartService.HttpUploadUtil;
import com.AoRGMap.Util.ExtAudioRecorderActivity;
import com.AoRGMap.Util.ZipUtils;
import com.AoRGMap.camera.CameraTaker2Activity;
import com.AoRGMap.commtool.CommTool;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.apache.http.cookie.ClientCookie;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

/* loaded from: classes.dex */
public class AttPhotoActivity extends PrbAttributeActivity {
    public static final int Camera_Request_content = 4;
    public static final int RequestCamera = 1;
    public static final int RequestRecord = 3;
    public static final int RequestTrend = 6;
    public static final int RequestVideo = 5;
    AttributeItem itemCode;
    AttributeItem itemDesc;
    AttributeItem itemJW;
    AttributeItem itemPhotoAngle;
    AttributeItem itemPhotoNum;
    AttributeItem itemRCode;
    AttributeItem itemRecord;
    AttributeItem itemRouteCode;
    AttributeItem itemXY;
    AttributeItem itemphoto;
    AttributeItem itemsound;
    double lpX;
    double lpY;
    static String imgFolder = null;
    static String imgPath = null;
    static String videoPath = null;
    static Uri uri = null;
    private GlobalState mGState = RGMapApplication.getCurrentApp().getCurrentGlobal();
    private String imgcodeString = null;
    int iMsgId = 100;
    int iPhotoNum = 0;
    private final int NO_NET = 9999;
    private final int UPLOAD_SUCCEND = 8888;
    private final int UPLOAD_ERROR = 8887;
    private final int LOGIN_NO_USER = 8886;
    private final int LOGIN_ERROR_PASSWORD = 8885;
    private final int LOGIN_ERROR_TRY = 8884;
    private Handler mHandler = new Handler() { // from class: com.AoRGMap.prb.AttPhotoActivity.1
        @Override // android.os.Handler
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 8884:
                    Toast.makeText(AttPhotoActivity.this, "验证失败", 0).show();
                    break;
                case 8885:
                    Toast.makeText(AttPhotoActivity.this, "密码错误", 0).show();
                    break;
                case 8886:
                    Toast.makeText(AttPhotoActivity.this, "用户名不存在请注册", 0).show();
                    break;
                case 8887:
                    Toast.makeText(AttPhotoActivity.this, "上传失败", 0).show();
                    break;
                case 8888:
                    CommTool commTool = new CommTool();
                    commTool.CommNotificationBuider(AttPhotoActivity.this, null, "典型现象点上传成功", "典型现象点上传", AttPhotoActivity.this.iMsgId);
                    Toast.makeText(AttPhotoActivity.this, "上传成功", 0).show();
                    AttPhotoActivity.this.iMsgId++;
                    break;
                case 9999:
                    Toast.makeText(AttPhotoActivity.this, "请检查网络连接", 0).show();
                    break;
            }
        }
    };
    protected ArrayList<AttributeItem> itemList = new ArrayList<>();
    private ListView m_listview = null;
    private Button m_btnInsert = null;
    private Button m_btnModify = null;
    private Button m_btnDelete = null;
    private EditText m_editorDesc = null;
    private TextView m_txtRemark = null;
    private PhotoDescAdapter adapter = null;
    private Vector<String> mPhotoArray = new Vector<>();
    private double[] mdJwd = new double[2];
    private EditText m_PhotoNo = null;
    private EditText m_itemGPointNo = null;

    public double getX() {
        return this.lpX;
    }

    public double getY() {
        return this.lpY;
    }

    public AttPhotoActivity() {
        setTitle(PRBAreas.getAreaChineseName(PRBAreas.m_strPhoto));
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onInitializeViews(AttributeActivity.ContextViewManager mgr) {
        Bundle bundle = getIntent().getExtras();
        this.lpX = bundle.getDouble(PointParams.PARAM_DOUBLE_X);
        this.lpY = bundle.getDouble(PointParams.PARAM_DOUBLE_Y);
        ViewGroup container = mgr.addStandardAttributeView();
        initMainView(container);
        View v = View.inflate(this, R.layout.photo_desc, null);
        mgr.addView(v);
        this.m_listview = (ListView) v.findViewById(R.id.photo_list);
        this.m_btnInsert = (Button) v.findViewById(R.id.photo_btnInsert);
        this.m_btnModify = (Button) v.findViewById(R.id.photo_btnModify);
        this.m_btnDelete = (Button) v.findViewById(R.id.photo_btnDelete);
        this.m_editorDesc = (EditText) v.findViewById(R.id.photo_editor_desc);
        this.m_txtRemark = (TextView) v.findViewById(R.id.photo_text_remark);
        this.m_editorDesc.setOnFocusChangeListener(new View.OnFocusChangeListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.2
            @Override // android.view.View.OnFocusChangeListener
            public void onFocusChange(View v2, boolean hasFocus) {
                if (hasFocus) {
                    ((InputMethodManager) AttPhotoActivity.this.getSystemService("input_method")).showSoftInput(AttPhotoActivity.this.m_editorDesc, 2);
                } else {
                    ((InputMethodManager) AttPhotoActivity.this.getSystemService("input_method")).hideSoftInputFromWindow(AttPhotoActivity.this.m_editorDesc.getWindowToken(), 0);
                }
            }
        });
        this.m_btnInsert.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.3
            @Override // android.view.View.OnClickListener
            public void onClick(View v2) {
                int pos = AttPhotoActivity.this.adapter.getSelectedItem();
                if (pos >= AttPhotoActivity.this.adapter.getCount()) {
                    pos = -1;
                }
                String str = AttPhotoActivity.this.m_editorDesc.getText().toString();
                if (pos >= 0) {
                    AttPhotoActivity.this.mPhotoArray.insertElementAt(str, pos + 1);
                } else {
                    AttPhotoActivity.this.mPhotoArray.add(str);
                }
                AttPhotoActivity.this.adapter.notifyDataSetChanged();
                AttPhotoActivity.this.m_editorDesc.setText("");
            }
        });
        this.m_btnModify.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.4
            @Override // android.view.View.OnClickListener
            public void onClick(View v2) {
                int pos = AttPhotoActivity.this.adapter.getSelectedItem();
                if (pos >= AttPhotoActivity.this.adapter.getCount()) {
                    pos = -1;
                }
                String str = AttPhotoActivity.this.m_editorDesc.getText().toString();
                if (pos >= 0) {
                    AttPhotoActivity.this.mPhotoArray.set(pos, str);
                    AttPhotoActivity.this.adapter.notifyDataSetChanged();
                    AttPhotoActivity.this.m_editorDesc.setText("");
                }
            }
        });
        this.m_btnDelete.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.5
            @Override // android.view.View.OnClickListener
            public void onClick(View v2) {
                int pos = AttPhotoActivity.this.adapter.getSelectedItem();
                if (pos >= AttPhotoActivity.this.adapter.getCount()) {
                    pos = -1;
                }
                if (pos >= 0) {
                    AttPhotoActivity.this.mPhotoArray.remove(pos);
                    AttPhotoActivity.this.adapter.notifyDataSetChanged();
                }
            }
        });
        this.adapter = new PhotoDescAdapter(this, this.mPhotoArray);
        this.m_listview.setAdapter((ListAdapter) this.adapter);
        this.m_listview.setSelector(new Drawable() { // from class: com.AoRGMap.prb.AttPhotoActivity.6
            @Override // android.graphics.drawable.Drawable
            public void setColorFilter(ColorFilter cf) {
            }

            @Override // android.graphics.drawable.Drawable
            public void setAlpha(int alpha) {
            }

            @Override // android.graphics.drawable.Drawable
            public int getOpacity() {
                return 0;
            }

            @Override // android.graphics.drawable.Drawable
            public void draw(Canvas canvas) {
            }
        });
        this.m_listview.setOnItemClickListener(new AdapterView.OnItemClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.7
            @Override // android.widget.AdapterView.OnItemClickListener
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                AttPhotoActivity.this.m_editorDesc.setText((CharSequence) AttPhotoActivity.this.mPhotoArray.get(position));
                AttPhotoActivity.this.adapter.setSelectedItem(position);
                AttPhotoActivity.this.adapter.notifyDataSetInvalidated();
            }
        });
        MakeDictButtton(this.itemList);
    }

    @Override // com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickOK() {
        GdbAttributesMap<String, Object> data = getAttributeMap();
        WorkArea area = getWorkArea();
        updateAttributeMap(this.itemList);
        String kX = getResources().getString(R.string.RGMAP_FLDNAME_PXX);
        String kY = getResources().getString(R.string.RGMAP_FLDNAME_PYY);
        getAttributeMap().put(kX, Double.valueOf(getX()));
        getAttributeMap().put(kY, Double.valueOf(getY()));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LONGITUDE, Double.valueOf(this.mdJwd[0]));
        getAttributeMap().put(AttributeExtActivity.PARAM_STRING_LATITUDE, Double.valueOf(this.mdJwd[1]));
        area.setNamedAttributes(getGeometryId(), data);
        String strPath = AoRGMapActivity.getCurrentMap().getMapName();
        int lastIndex = strPath.lastIndexOf(47);
        if (lastIndex <= 0) {
            lastIndex = strPath.lastIndexOf(92);
        }
        if (lastIndex <= 0) {
            Toast toast = DisplayHelper.getCommonToast();
            toast.setText("路径不正确:" + strPath);
            toast.show();
            finish();
            return;
        }
        String strPath2 = strPath.substring(0, lastIndex + 1) + "Images/";
        File f = new File(strPath2);
        if (!f.exists() && !f.mkdirs()) {
            Toast toast2 = DisplayHelper.getCommonToast();
            toast2.setText("创建文件夹失败：" + strPath2);
            toast2.show();
            finish();
            return;
        }
        String xmlFileName = strPath2 + this.m_itemGPointNo.getEditableText().toString() + "_" + this.m_PhotoNo.getText().toString() + ".xml";
        if (this.m_PhotoNo.getText().toString().length() <= 0) {
            xmlFileName = strPath2 + this.m_itemGPointNo.getEditableText().toString() + ".xml";
        }
        try {
            DocumentBuilderFactory docBuilderFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder docBuilder = docBuilderFactory.newDocumentBuilder();
            Document doc = docBuilder.newDocument();
            Element root = doc.createElement("PhotoDes");
            doc.appendChild(root);
            Iterator<String> it = this.mPhotoArray.iterator();
            while (it.hasNext()) {
                String str = it.next();
                Element node = doc.createElement("Note");
                node.setTextContent(str);
                root.appendChild(node);
            }
            FileOutputStream outStream = new FileOutputStream(xmlFileName);
            TransformerFactory factory = TransformerFactory.newInstance();
            Transformer transformer = factory.newTransformer();
            Properties outFormat = new Properties();
            outFormat.setProperty("method", "xml");
            outFormat.setProperty("omit-xml-declaration", "no");
            outFormat.setProperty(ClientCookie.VERSION_ATTR, "1.0");
            outFormat.setProperty("encoding", "GB2312");
            transformer.setOutputProperties(outFormat);
            DOMSource domSource = new DOMSource(doc.getDocumentElement());
            StreamResult result = new StreamResult(outStream);
            transformer.transform(domSource, result);
        } catch (Exception e) {
        } finally {
        }
        if (getAttributeMap().containsKey("ROUTECODE")) {
            String strCode = getAttributeMap().get("ROUTECODE").toString();
            RGMapApplication.getCurrentApp().getCurrentGlobal().TryUpdateRouteCode(strCode);
        }
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity, com.AoGIS.ui.AoGISUIActivity
    public void onClickCancel() {
        finish();
    }

    @Override // com.AoRGMap.prb.PrbAttributeActivity, com.AoDevBase.ui.AttributeActivity
    public void onClickSave() {
    }

    private void initMainView(ViewGroup container) {
        RGMapApplication app = (RGMapApplication) getApplication();
        GlobalState gstate = app.getCurrentGlobal();
        Map<String, ?> map = getAttributeMap();
        AttributeGroup.AttributeGroupParams groupParam = new AttributeGroup.AttributeGroupParams();
        groupParam.title = getResources().getString(R.string.menu_main_prb_photo);
        AttributeGroup group = new AttributeGroup(this, groupParam, container);
        String defData = gstate.GetCurrentRouteCode();
        AttributeItem item = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PROUTECODE, R.string.rg_route_no, EditTextEditorInfo.textEditor(), map, defData);
        this.itemList.add(item);
        this.itemRouteCode = item;
        String defData2 = gstate.GetCurrentPointCode();
        AttributeItem item2 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PGEOPOINT, R.string.rg_gpoint_no, EditTextEditorInfo.textEditor(), map, defData2);
        this.itemList.add(item2);
        this.m_itemGPointNo = item2.getEditorEditText();
        String strData = String.format("%f, %f", Double.valueOf(getX()), Double.valueOf(getY()));
        String defData3 = getResources().getString(R.string.nodata);
        AttributeItem item3 = AttributeUIHelper.createItem(group, (String) null, R.string.rg_xy, EditTextEditorInfo.textEditor(), strData, defData3);
        item3.getEditorEditText().setEnabled(false);
        this.itemXY = item3;
        this.mdJwd = ConverXyToJwd(getX(), getY());
        String strData2 = String.format("%.2f, %.2f", Double.valueOf(this.mdJwd[0]), Double.valueOf(this.mdJwd[1]));
        String defData4 = getResources().getString(R.string.nodata);
        AttributeItem item4 = AttributeUIHelper.createItem(group, (String) null, R.string.RGMAP_PROMPT_JWCOORD, EditTextEditorInfo.textEditor(), strData2, defData4);
        item4.getEditorEditText().setEnabled(false);
        this.itemJW = item4;
        String defData5 = Integer.toString(gstate.GetCurrentRouteingCode() - 1);
        AttributeItem item5 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PR_CODE, R.string.photo_rcode, EditTextEditorInfo.unsignedNumberEditor(), map, defData5);
        this.itemList.add(item5);
        this.itemRCode = item5;
        AttributeItem item6 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PDESCRIBE, R.string.photo_content, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item6);
        this.itemDesc = item6;
        AttributeItem item7 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PCODE, R.string.photo_no, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemCode = item7;
        this.itemList.add(item7);
        if (isNewFlag()) {
            int[] ids = getWorkArea().getGeometryIdListByAtt(String.format(" ROUTECODE = '%s' AND GEOPOINT = '%s' ", gstate.GetCurrentRouteCode(), gstate.GetCurrentPointCode()));
            int maxid = -1;
            for (int i = 0; ids != null && i < ids.length; i++) {
                if (maxid < ids[i]) {
                    maxid = ids[i];
                }
            }
            if (maxid > 0) {
                try {
                    if (getWorkArea().getNamedAttributes(maxid).get("CODE").toString().trim().length() > 0) {
                        int newid = Integer.parseInt(getWorkArea().getNamedAttributes(maxid).get("CODE").toString());
                        item7.setItemData(Integer.toString(newid + 1));
                    }
                } catch (RuntimeException e) {
                }
            }
            item7.setItemData(Integer.toString(1));
        }
        this.m_PhotoNo = item7.getEditorEditText();
        final int mitype = gstate.getCamaraType();
        AttributeItem item8 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PNUMBER, R.string.photo_seq, EditTextEditorInfo.textEditor(), map, (String) null);
        item8.makeDesc(getResources().getString(R.string.photo_seq_desc));
        item8.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.8
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent intent;
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemphoto.getEditorEditText().getText().toString();
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                String imgname = AttPhotoActivity.this.m_itemGPointNo.getText().toString() + "_" + AttPhotoActivity.this.m_PhotoNo.getText().toString();
                File vFilepath = new File(AttPhotoActivity.imgFolder);
                int photolv = AttPhotoActivity.this.getPhotolv(imgname, vFilepath);
                String imgname2 = imgname + "_" + String.valueOf(photolv);
                if ("".equals(AttPhotoActivity.this.imgcodeString)) {
                    AttPhotoActivity.this.imgcodeString = imgname2;
                } else {
                    AttPhotoActivity.this.imgcodeString += "," + imgname2;
                }
                AttPhotoActivity.imgPath = AttPhotoActivity.imgFolder + imgname2 + ".jpg";
                File vFile = new File(AttPhotoActivity.imgPath);
                if (!vFile.exists()) {
                    File vDirPath = vFile.getParentFile();
                    vDirPath.mkdirs();
                    try {
                        vFile.createNewFile();
                    } catch (IOException e2) {
                        e2.printStackTrace();
                    }
                }
                if (mitype == 0) {
                    AttPhotoActivity.uri = Uri.fromFile(vFile);
                    intent = new Intent("android.media.action.IMAGE_CAPTURE");
                    intent.putExtra("output", AttPhotoActivity.uri);
                    intent.putExtra("isNew", true);
                } else {
                    intent = new Intent(AttPhotoActivity.this, (Class<?>) CameraTaker2Activity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("JPGPANTH", AttPhotoActivity.imgPath);
                    intent.putExtras(bundle);
                }
                try {
                    AttPhotoActivity.this.startActivityForResult(intent, 1);
                } catch (Exception e3) {
                    Toast.makeText(AttPhotoActivity.this, R.string.RGMAP_BITMAP_NODECEVICE, 1).show();
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_camera)));
        item8.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.9
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemphoto.getEditorEditText().getText().toString();
                if (!"".equals(AttPhotoActivity.this.imgcodeString)) {
                    Intent intentimg = new Intent(AttPhotoActivity.this, (Class<?>) MyImageView1Activity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", AttPhotoActivity.this.imgcodeString);
                    bundle.putString("FolderATH", AttPhotoActivity.imgFolder);
                    intentimg.putExtras(bundle);
                    AttPhotoActivity.this.startActivity(intentimg);
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_play)));
        this.itemList.add(item8);
        this.itemphoto = item8;
        String defData6 = String.valueOf(0);
        AttributeItem item9 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PAMOUNT, R.string.photo_num, EditTextEditorInfo.unsignedNumberEditor(), map, defData6);
        this.itemList.add(item9);
        this.itemPhotoNum = item9;
        AttributeItem item10 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PDIRECTION, R.string.photo_direction, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item10);
        this.itemPhotoAngle = item10;
        item10.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.10
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Intent intent = new Intent();
                Bundle bundle = new Bundle();
                if (AttPhotoActivity.this.mGState.getCompassType() == 0) {
                    intent.setClass(AttPhotoActivity.this, AoCompassActivity.class);
                    bundle.putFloat("CPJ", AttPhotoActivity.this.mGState.GetCpj());
                } else {
                    intent.setClass(AttPhotoActivity.this, ExtOrientationActivity.class);
                    bundle.putFloat("CPJ", AttPhotoActivity.this.mGState.GetCpj());
                }
                intent.putExtras(bundle);
                AttPhotoActivity.this.startActivityForResult(intent, 6);
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_compass)));
        AttributeItem item11 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PSOUND_ID, R.string.photo_sound, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item11);
        this.itemsound = item11;
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.11
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemsound.getEditorEditText().getText().toString();
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                File vFilepath = new File(AttPhotoActivity.imgFolder);
                String soundname = AttPhotoActivity.this.m_itemGPointNo.getText().toString() + "_" + AttPhotoActivity.this.m_PhotoNo.getText().toString();
                int photolv = AttPhotoActivity.this.getSoundlv(soundname, vFilepath);
                String soundname2 = soundname + "_" + String.valueOf(photolv);
                if ("".equals(AttPhotoActivity.this.imgcodeString)) {
                    AttPhotoActivity.this.imgcodeString = soundname2;
                } else {
                    AttPhotoActivity.this.imgcodeString += "," + soundname2;
                }
                AttPhotoActivity.imgPath = AttPhotoActivity.imgFolder + soundname2 + ".wav";
                try {
                    if (AttPhotoActivity.this.mGState.getAudioType() == 0) {
                        Intent intent = new Intent("android.provider.MediaStore.RECORD_SOUND");
                        intent.putExtra("isNew", true);
                        AttPhotoActivity.this.startActivityForResult(intent, 3);
                    } else {
                        Intent intent2 = new Intent(AttPhotoActivity.this, (Class<?>) ExtAudioRecorderActivity.class);
                        intent2.putExtra(ExtAudioRecorderActivity.AUDIOSAVEPATH, AttPhotoActivity.imgFolder);
                        String fileName = soundname2 + ".wav";
                        intent2.putExtra(ExtAudioRecorderActivity.AUDIOFILENAME, fileName);
                        AttPhotoActivity.this.startActivityForResult(intent2, 3);
                    }
                } catch (Exception e2) {
                    Toast.makeText(AttPhotoActivity.this, R.string.RGMAP_BITMAP_NODECEVICE, 1).show();
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_btn_speak_now)));
        item11.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.12
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemsound.getEditorEditText().getText().toString();
                if (!"".equals(AttPhotoActivity.this.imgcodeString)) {
                    Intent intentsound = new Intent(AttPhotoActivity.this, (Class<?>) MySoundViewActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", AttPhotoActivity.this.imgcodeString);
                    bundle.putString("FolderATH", AttPhotoActivity.imgFolder);
                    intentsound.putExtras(bundle);
                    AttPhotoActivity.this.startActivity(intentsound);
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_play)));
        AttributeItem item12 = AttributeUIHelper.createItem(group, R.string.RGMAP_FLDNAME_PMPG_ID, R.string.photo_identity, EditTextEditorInfo.textEditor(), map, (String) null);
        this.itemList.add(item12);
        this.itemRecord = item12;
        item12.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.13
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemRecord.getEditorEditText().getText().toString();
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                File vFilepath = new File(AttPhotoActivity.imgFolder);
                String recordname = AttPhotoActivity.this.m_itemGPointNo.getText().toString() + "_" + AttPhotoActivity.this.m_PhotoNo.getText().toString();
                int photolv = AttPhotoActivity.this.getRecordlv(recordname, vFilepath);
                String recordname2 = recordname + "_" + String.valueOf(photolv);
                if ("".equals(AttPhotoActivity.this.imgcodeString)) {
                    AttPhotoActivity.this.imgcodeString = recordname2;
                } else {
                    AttPhotoActivity.this.imgcodeString += "," + recordname2;
                }
                AttPhotoActivity.videoPath = AttPhotoActivity.imgFolder + recordname2 + ".mp4";
                File vFile = new File(AttPhotoActivity.videoPath);
                if (!vFile.exists()) {
                    File vDirPath = vFile.getParentFile();
                    vDirPath.mkdirs();
                }
                AttPhotoActivity.uri = Uri.fromFile(vFile);
                Intent intent = new Intent("android.media.action.VIDEO_CAPTURE");
                intent.putExtra("android.intent.extra.videoQuality", 1);
                intent.putExtra("isNew", true);
                try {
                    AttPhotoActivity.this.startActivityForResult(intent, 5);
                } catch (Exception e2) {
                    Toast.makeText(AttPhotoActivity.this, R.string.RGMAP_BITMAP_NODECEVICE, 1).show();
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_menu_camera)));
        item12.addButton(new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.14
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                String rootPath = AoRGMapActivity.getCurrentMapPath();
                AttPhotoActivity.imgFolder = rootPath + "/images/";
                AttPhotoActivity.this.imgcodeString = AttPhotoActivity.this.itemRecord.getEditorEditText().getText().toString();
                if (!"".equals(AttPhotoActivity.this.imgcodeString)) {
                    Intent intentvideo = new Intent(AttPhotoActivity.this, (Class<?>) MyVideoViewActivity.class);
                    Bundle bundle = new Bundle();
                    bundle.putString("VIDE0PATH", AttPhotoActivity.this.imgcodeString);
                    bundle.putString("FolderATH", AttPhotoActivity.imgFolder);
                    intentvideo.putExtras(bundle);
                    AttPhotoActivity.this.startActivity(intentvideo);
                }
            }
        }, getResources().getDrawable(android.R.drawable.ic_media_play)));
        AttributeItem.AttributeItemParams picDesc = new AttributeItem.AttributeItemParams();
        picDesc.label = getResources().getString(R.string.photo_btn_desc);
        picDesc.clickable = new AttributeButton(new View.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.15
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                AttPhotoActivity.this.ensureShowView(1);
                AttPhotoActivity.this.m_editorDesc.clearFocus();
            }
        }, null);
        picDesc.describe = getResources().getString(R.string.RGMAP_PROMPT_IMGDESC);
        group.addItem(picDesc);
        AttributeItem.AttributeItemParams upLoadVIPItem = new AttributeItem.AttributeItemParams();
        upLoadVIPItem.label = "典型地质现象点上传";
        upLoadVIPItem.clickable = new AttributeButton(new AnonymousClass16(), null);
        upLoadVIPItem.describe = "典型地质现象点上传";
        group.addItem(upLoadVIPItem);
        container.addView(group.getInnerView());
        if (!isNewFlag()) {
            String strPath = AoRGMapActivity.getCurrentMap().getMapName();
            int lastIndex = strPath.lastIndexOf(47);
            if (lastIndex <= 0) {
                lastIndex = strPath.lastIndexOf(92);
            }
            if (lastIndex <= 0) {
                Toast toast = DisplayHelper.getCommonToast();
                toast.setText(getResources().getString(R.string.RGMAP_PROMPT_ERRPATH) + strPath);
                toast.show();
                finish();
                return;
            }
            String strPath2 = strPath.substring(0, lastIndex + 1) + "Images/";
            File f = new File(strPath2);
            if (!f.exists() && !f.mkdirs()) {
                Toast toast2 = DisplayHelper.getCommonToast();
                toast2.setText(getResources().getString(R.string.RGMAP_PROMPT_CREATEFLODER) + strPath2);
                toast2.show();
                finish();
                return;
            }
            String xmlFileName = strPath2 + this.m_itemGPointNo.getEditableText().toString() + "_" + this.m_PhotoNo.getText().toString() + ".xml";
            if (this.m_PhotoNo.getText().toString().length() <= 0) {
                xmlFileName = strPath2 + this.m_itemGPointNo.getEditableText().toString() + ".xml";
            }
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            try {
                DocumentBuilder db = dbf.newDocumentBuilder();
                try {
                    InputStream stream = new FileInputStream(xmlFileName);
                    Document doc = db.parse(stream);
                    stream.close();
                    Element root = doc.getDocumentElement();
                    NodeList photos = root.getElementsByTagName("Note");
                    for (int i2 = 0; i2 < photos.getLength(); i2++) {
                        Element desc = (Element) photos.item(i2);
                        this.mPhotoArray.add(desc.getTextContent());
                    }
                } catch (IOException e2) {
                } catch (DOMException e3) {
                } catch (SAXException e4) {
                }
            } catch (ParserConfigurationException e5) {
            }
        }
    }

    /* renamed from: com.AoRGMap.prb.AttPhotoActivity$16, reason: invalid class name */
    class AnonymousClass16 implements View.OnClickListener {
        AnonymousClass16() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View v) {
            if (!AttPhotoActivity.this.IsNetworkConnected(AttPhotoActivity.this).booleanValue()) {
                Toast.makeText(AttPhotoActivity.this, "请检查网络连接", 0).show();
                return;
            }
            AlertDialog.Builder customizeDialog = new AlertDialog.Builder(AttPhotoActivity.this);
            View dialogView = LayoutInflater.from(AttPhotoActivity.this).inflate(R.layout.dlg_userpassword, (ViewGroup) null);
            final EditText editUser = (EditText) dialogView.findViewById(R.id.id_user);
            final EditText editPassWord = (EditText) dialogView.findViewById(R.id.id_password);
            CheckBox editCheckBox = (CheckBox) dialogView.findViewById(R.id.id_checkbox);
            TextView textView = (TextView) dialogView.findViewById(R.id.id_ssfb);
            editCheckBox.setVisibility(8);
            textView.setVisibility(8);
            GlobalState unused = AttPhotoActivity.this.mGState;
            String loginInfo = GlobalState.getLoginInfo();
            if (loginInfo != null && !loginInfo.equals("")) {
                editUser.setText(loginInfo.split(",")[0]);
                editPassWord.setText(loginInfo.split(",")[1]);
            }
            customizeDialog.setTitle("请输入用户名和密码");
            customizeDialog.setView(dialogView);
            customizeDialog.setPositiveButton("确定", new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.16.1
                /* JADX WARN: Type inference failed for: r4v15, types: [com.AoRGMap.prb.AttPhotoActivity$16$1$1] */
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialog, int which) {
                    final String user = editUser.getText().toString();
                    final String passWord = editPassWord.getText().toString();
                    if (user == null || TextUtils.isEmpty(user) || passWord == null || TextUtils.isEmpty(passWord)) {
                        Toast.makeText(AttPhotoActivity.this, "请输入正确的用户名和密码", 0).show();
                        try {
                            Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                            field.setAccessible(true);
                            field.set(dialog, false);
                            return;
                        } catch (Exception e) {
                            return;
                        }
                    }
                    try {
                        new Thread() { // from class: com.AoRGMap.prb.AttPhotoActivity.16.1.1
                            @Override // java.lang.Thread, java.lang.Runnable
                            public void run() {
                                HttpUploadUtil uploadUtil = new HttpUploadUtil();
                                int result = uploadUtil.checkLogin(user, passWord);
                                switch (result) {
                                    case -2:
                                        AttPhotoActivity.this.mHandler.sendEmptyMessage(8885);
                                        break;
                                    case -1:
                                        AttPhotoActivity.this.mHandler.sendEmptyMessage(8886);
                                        break;
                                    case 0:
                                        GlobalState unused2 = AttPhotoActivity.this.mGState;
                                        GlobalState.setLoginInfo(user + "," + passWord);
                                        String vipPath = AttPhotoActivity.this.ZipPhotoPnt();
                                        if (vipPath.equals("") || uploadUtil.uploadGeoMediaPoint(vipPath, uploadUtil.getUcid()) != 1) {
                                            AttPhotoActivity.this.mHandler.sendEmptyMessage(8887);
                                            break;
                                        } else {
                                            AttPhotoActivity.this.mHandler.sendEmptyMessage(8888);
                                            break;
                                        }
                                    case 1:
                                        AttPhotoActivity.this.mHandler.sendEmptyMessage(8884);
                                        break;
                                }
                            }
                        }.start();
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            customizeDialog.setCancelable(false);
            customizeDialog.setNegativeButton("取消", new DialogInterface.OnClickListener() { // from class: com.AoRGMap.prb.AttPhotoActivity.16.2
                @Override // android.content.DialogInterface.OnClickListener
                public void onClick(DialogInterface dialog, int which) {
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, true);
                    } catch (Exception e) {
                    }
                    dialog.dismiss();
                }
            });
            customizeDialog.show();
        }
    }

    protected void prompt(String str) {
        Toast.makeText(getBaseContext(), str, 0).show();
    }

    @Override // android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == -1) {
            switch (requestCode) {
                case 1:
                    this.itemphoto.getEditorEditText().setText(this.imgcodeString);
                    String strnum = this.itemPhotoNum.getItemDataString();
                    if (strnum.isEmpty() || strnum == null || strnum.equals("")) {
                        this.iPhotoNum = 1;
                    } else {
                        this.iPhotoNum = Integer.valueOf(strnum).intValue() + 1;
                    }
                    this.itemPhotoNum.getEditorEditText().setText(String.valueOf(this.iPhotoNum));
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
                case 3:
                    if (this.mGState.getAudioType() == 0) {
                        Uri uriRecord = data.getData();
                        String[] proj = {"_data"};
                        Cursor actualRecordCursor = managedQuery(uriRecord, proj, null, null, null);
                        if (actualRecordCursor == null) {
                            Toast.makeText(this, "请在【系统设置】中设置录音方式为用户录音机", 0).show();
                            break;
                        } else {
                            int actual_record_column_index = actualRecordCursor.getColumnIndexOrThrow("_data");
                            actualRecordCursor.moveToFirst();
                            String recordPath = actualRecordCursor.getString(actual_record_column_index);
                            moveFile(recordPath, imgPath);
                        }
                    }
                    this.itemsound.getEditorEditText().setText(this.imgcodeString);
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
                case 4:
                case 6:
                    float resultTrend = data.getExtras().getFloat("Azimuth");
                    String str = String.format("%.0f", Float.valueOf(resultTrend));
                    String Angle = this.itemPhotoAngle.getEditorEditText().getText().toString();
                    if (Angle.equals("")) {
                        this.itemPhotoAngle.setItemData(str);
                        break;
                    } else {
                        this.itemPhotoAngle.setItemData(Angle + "," + str);
                        break;
                    }
                case 5:
                    Uri uriRecord2 = data.getData();
                    String[] proj2 = {"_data"};
                    Cursor actualRecordCursor2 = managedQuery(uriRecord2, proj2, null, null, null);
                    int actual_record_column_index2 = actualRecordCursor2.getColumnIndexOrThrow("_data");
                    actualRecordCursor2.moveToFirst();
                    String recordPath2 = actualRecordCursor2.getString(actual_record_column_index2);
                    moveFile(recordPath2, videoPath);
                    this.itemRecord.getEditorEditText().setText(this.imgcodeString);
                    Toast.makeText(this, R.string.RGMAP_PROMPT_SAVEOK, 1).show();
                    break;
            }
        }
    }

    public void moveFile(String oldPath, String newPath) {
        int bytesum = 0;
        try {
            File oldFile = new File(oldPath);
            File newFile = new File(newPath);
            if (!newFile.exists()) {
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                while (true) {
                    int byteread = inStream.read(buffer);
                    if (byteread != -1) {
                        bytesum += byteread;
                        System.out.println(bytesum);
                        fs.write(buffer, 0, byteread);
                    } else {
                        inStream.close();
                        oldFile.delete();
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getPhotolv(String str) {
        if ("".equals(str.trim())) {
            return 1;
        }
        char cLast = str.charAt(str.length() - 1);
        String strlast = String.valueOf(cLast);
        int Photolv = Integer.valueOf(strlast).intValue();
        return Photolv + 1;
    }

    public int getPhotolv(String keyword, File filepath) {
        File[] files;
        int Photolv = 1;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        Photolv = getPhotolv(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf("jpg") > -1) {
                        Photolv++;
                    }
                }
            }
        }
        return Photolv;
    }

    public int getRecordlv(String keyword, File filepath) {
        File[] files;
        int Photolv = 1;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        Photolv = getPhotolv(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf("mp4") > -1) {
                        Photolv++;
                    }
                }
            }
        }
        return Photolv;
    }

    public int getSoundlv(String keyword, File filepath) {
        File[] files;
        int Photolv = 1;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (file.canRead()) {
                        Photolv = getPhotolv(keyword, file);
                    }
                } else {
                    String strname = file.getName();
                    if ((strname.indexOf(keyword) > -1 || strname.indexOf(keyword.toUpperCase()) > -1) && strname.indexOf("wav") > -1) {
                        Photolv++;
                    }
                }
            }
        }
        return Photolv;
    }

    private static class PhotoDescAdapter extends BaseAdapter {
        private Context context;
        private List<String> data;
        private int selectedItem = -1;

        public static class Holder {
            TextView v_desc;
            TextView v_id;
        }

        public PhotoDescAdapter(Context context, List<String> data) {
            this.context = context;
            this.data = data;
        }

        @Override // android.widget.Adapter
        public int getCount() {
            return this.data.size();
        }

        @Override // android.widget.Adapter
        public Object getItem(int position) {
            return this.data.get(position);
        }

        @Override // android.widget.Adapter
        public long getItemId(int position) {
            return position;
        }

        @Override // android.widget.Adapter
        public View getView(int position, View convertView, ViewGroup parent) {
            Holder holder;
            if (convertView == null) {
                convertView = View.inflate(this.context, R.layout.photo_desc_item, null);
                holder = new Holder();
                holder.v_id = (TextView) convertView.findViewById(R.id.txtId);
                holder.v_desc = (TextView) convertView.findViewById(R.id.txtDesc);
                convertView.setTag(holder);
            } else {
                holder = (Holder) convertView.getTag();
            }
            holder.v_id.setText(Integer.toString(position));
            holder.v_desc.setText(this.data.get(position));
            if (position == this.selectedItem) {
                convertView.setBackgroundColor(-7829368);
            } else {
                convertView.setBackgroundColor(0);
            }
            return convertView;
        }

        public void setSelectedItem(int selectItem) {
            this.selectedItem = selectItem;
        }

        public int getSelectedItem() {
            return this.selectedItem;
        }
    }

    private double[] ConverXyToJwd(double dx, double dy) {
        WorkAreaParams.ProjectionType PrjType = this.mGState.getProjectionType();
        WorkAreaParams MapParam = this.mGState.getWorkAreaParams();
        WorkAreaParams.EarthType EarthType = MapParam.getEarthType();
        double dCenterLon = getRadFromDeg(MapParam.lon);
        double[] dJwd = new double[2];
        if (PrjType == WorkAreaParams.ProjectionType.Gauss) {
            double[] dPos = ProjectionHelper.GaussRev(dCenterLon, dx, dy, EarthType);
            if (dPos == null) {
                dPos = new double[]{0.0d, 0.0d};
            }
            String szTemp = String.format("%.8f", Double.valueOf((dPos[0] * 180.0d) / 3.1415926d));
            double dTemp = Double.valueOf(szTemp).doubleValue();
            dJwd[0] = dTemp;
            String szTemp2 = String.format("%.8f", Double.valueOf((dPos[1] * 180.0d) / 3.1415926d));
            double dTemp2 = Double.valueOf(szTemp2).doubleValue();
            dJwd[1] = dTemp2;
        } else if (PrjType == WorkAreaParams.ProjectionType.UTM) {
            double[] dPos2 = ProjectionHelper.UTMRev(dCenterLon, dx, dy, EarthType, MapParam.dx, MapParam.dy);
            if (dPos2 == null) {
                dPos2 = new double[]{0.0d, 0.0d};
            }
            String szTemp3 = String.format("%.8f", Double.valueOf((dPos2[0] * 180.0d) / 3.1415926d));
            double dTemp3 = Double.valueOf(szTemp3).doubleValue();
            dJwd[0] = dTemp3;
            String szTemp4 = String.format("%.8f", Double.valueOf((dPos2[1] * 180.0d) / 3.1415926d));
            double dTemp4 = Double.valueOf(szTemp4).doubleValue();
            dJwd[1] = dTemp4;
        } else if (PrjType == WorkAreaParams.ProjectionType.Lambert) {
            double[] dPos3 = ProjectionHelper.LambertRev(getRadFromDeg(MapParam.lat1), getRadFromDeg(MapParam.lat2), dCenterLon, getRadFromDeg(MapParam.lat), dx, dy, EarthType, MapParam.dx, MapParam.dy);
            if (dPos3 == null) {
                dPos3 = new double[]{0.0d, 0.0d};
            }
            String szTemp5 = String.format("%.8f", Double.valueOf((dPos3[0] * 180.0d) / 3.1415926d));
            double dTemp5 = Double.valueOf(szTemp5).doubleValue();
            dJwd[0] = dTemp5;
            String szTemp6 = String.format("%.8f", Double.valueOf((dPos3[1] * 180.0d) / 3.1415926d));
            double dTemp6 = Double.valueOf(szTemp6).doubleValue();
            dJwd[1] = dTemp6;
        } else if (PrjType == WorkAreaParams.ProjectionType.WebMercator) {
            double[] dPos4 = ProjectionHelper.WebMercatorRev(dx, dy, this.mGState.getbInChina());
            if (dPos4 == null) {
                dPos4 = new double[]{0.0d, 0.0d};
            }
            String szTemp7 = String.format("%.8f", Double.valueOf((dPos4[0] * 180.0d) / 3.1415926d));
            double dTemp7 = Double.valueOf(szTemp7).doubleValue();
            dJwd[0] = dTemp7;
            String szTemp8 = String.format("%.8f", Double.valueOf((dPos4[1] * 180.0d) / 3.1415926d));
            double dTemp8 = Double.valueOf(szTemp8).doubleValue();
            dJwd[1] = dTemp8;
        } else {
            dJwd[0] = 0.0d;
            dJwd[1] = 0.0d;
        }
        dJwd[0] = ConverDtoDFM(dJwd[0]);
        dJwd[1] = ConverDtoDFM(dJwd[1]);
        return dJwd;
    }

    private double getRadFromDeg(double dDeg) {
        double dx = dDeg / 10000.0d;
        double dRad = (int) dx;
        return (3.141592653589793d * ((dRad + (((int) r2) / 60.0d)) + ((((dx - ((int) dx)) * 100.0d) - ((int) r2)) / 0.0036d))) / 180.0d;
    }

    private double ConverDtoDFM(double dd) {
        int d = (int) dd;
        int f = (int) ((dd - d) * 60.0d);
        double m = (((dd - d) * 60.0d) - f) * 60.0d;
        double ddfm = (d * 100 * 100) + (f * 100) + m;
        return ddfm;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public String ZipPhotoPnt() {
        String zipPath = "";
        String mapFlord = AoRGMapActivity.getCurrentMap().getMapName();
        int lastIndex = mapFlord.lastIndexOf(47);
        if (lastIndex <= 0) {
            lastIndex = mapFlord.lastIndexOf(92);
        }
        if (lastIndex <= 0) {
            return "";
        }
        String mapFlord2 = mapFlord.substring(0, lastIndex + 1);
        String fromDir = mapFlord2 + "Images";
        File fromDirFile = new File(fromDir);
        if (!fromDirFile.exists()) {
            fromDirFile.mkdir();
        }
        String toDir = mapFlord2 + "GEOVIP";
        File toDirFile = new File(toDir);
        if (!toDirFile.exists()) {
            toDirFile.mkdir();
        } else {
            try {
                deleteFolderFile(toDir, false);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (this.m_PhotoNo.getText().toString().length() <= 0) {
            return "";
        }
        String xmlFileName = toDir + File.separator + this.m_itemGPointNo.getEditableText().toString() + "_" + this.m_PhotoNo.getText().toString() + "_VIP.xml";
        String photoStrings = "";
        String soundStrings = "";
        String vedioStrings = "";
        try {
            DocumentBuilderFactory docBuilderFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder docBuilder = docBuilderFactory.newDocumentBuilder();
            Document doc = docBuilder.newDocument();
            Element root = doc.createElement("PHOTOINFO");
            doc.appendChild(root);
            String itemValue = this.itemRouteCode.getEditorEditText().getText().toString();
            Element node = doc.createElement("ROUTECODE");
            node.setTextContent(itemValue);
            root.appendChild(node);
            String itemValue2 = this.m_itemGPointNo.getText().toString();
            Element node2 = doc.createElement("GEOPOINT");
            node2.setTextContent(itemValue2);
            root.appendChild(node2);
            String itemValue3 = this.itemXY.getEditorEditText().getText().toString();
            Element node3 = doc.createElement("XY");
            node3.setTextContent(itemValue3);
            root.appendChild(node3);
            String itemValue4 = this.itemJW.getEditorEditText().getText().toString();
            Element node4 = doc.createElement("JW");
            node4.setTextContent(itemValue4);
            root.appendChild(node4);
            String itemValue5 = this.itemRCode.getEditorEditText().getText().toString();
            Element node5 = doc.createElement("RCODE");
            node5.setTextContent(itemValue5);
            root.appendChild(node5);
            String itemValue6 = this.itemDesc.getEditorEditText().getText().toString();
            Element node6 = doc.createElement("DESCRIBE");
            node6.setTextContent(itemValue6);
            root.appendChild(node6);
            String itemValue7 = this.itemCode.getEditorEditText().getText().toString();
            Element node7 = doc.createElement("CODE");
            node7.setTextContent(itemValue7);
            root.appendChild(node7);
            String itemValue8 = this.itemphoto.getEditorEditText().getText().toString();
            Element node8 = doc.createElement("NUMBER");
            node8.setTextContent(itemValue8);
            root.appendChild(node8);
            photoStrings = itemValue8;
            String itemValue9 = this.itemPhotoNum.getEditorEditText().getText().toString();
            Element node9 = doc.createElement("AMOUNT");
            node9.setTextContent(itemValue9);
            root.appendChild(node9);
            String itemValue10 = this.itemPhotoAngle.getEditorEditText().getText().toString();
            Element node10 = doc.createElement("DIRECTION");
            node10.setTextContent(itemValue10);
            root.appendChild(node10);
            String itemValue11 = this.itemsound.getEditorEditText().getText().toString();
            Element node11 = doc.createElement("SOUNDID");
            node11.setTextContent(itemValue11);
            root.appendChild(node11);
            soundStrings = itemValue11;
            String itemValue12 = this.itemRecord.getEditorEditText().getText().toString();
            Element node12 = doc.createElement("MPGID");
            node12.setTextContent(itemValue12);
            root.appendChild(node12);
            vedioStrings = itemValue12;
            FileOutputStream outStream = new FileOutputStream(xmlFileName);
            TransformerFactory factory = TransformerFactory.newInstance();
            Transformer transformer = factory.newTransformer();
            Properties outFormat = new Properties();
            outFormat.setProperty("method", "xml");
            outFormat.setProperty("omit-xml-declaration", "no");
            outFormat.setProperty(ClientCookie.VERSION_ATTR, "1.0");
            outFormat.setProperty("encoding", "GB2312");
            transformer.setOutputProperties(outFormat);
            DOMSource domSource = new DOMSource(doc.getDocumentElement());
            StreamResult result = new StreamResult(outStream);
            transformer.transform(domSource, result);
        } catch (Exception e2) {
        } finally {
        }
        if (!photoStrings.equals("")) {
            if (photoStrings.contains(",")) {
                String[] photoStringVec = photoStrings.split(",");
                for (String photoTemp : photoStringVec) {
                    String fromFile = fromDir + File.separator + photoTemp + ".jpg";
                    String toFile = toDir + File.separator + photoTemp + ".jpg";
                    CopySdcardFile(fromFile, toFile);
                }
            } else {
                String photoTemp2 = photoStrings;
                String fromFile2 = fromDir + File.separator + photoTemp2 + ".jpg";
                String toFile2 = toDir + File.separator + photoTemp2 + ".jpg";
                CopySdcardFile(fromFile2, toFile2);
            }
        }
        if (!soundStrings.equals("")) {
            if (soundStrings.contains(",")) {
                String[] soundStringVec = soundStrings.split(",");
                for (String soundTemp : soundStringVec) {
                    String fromFile3 = fromDir + File.separator + soundTemp + ".wav";
                    String toFile3 = toDir + File.separator + soundTemp + ".wav";
                    CopySdcardFile(fromFile3, toFile3);
                }
            } else {
                String soundTemp2 = soundStrings;
                String fromFile4 = fromDir + File.separator + soundTemp2 + ".wav";
                String toFile4 = toDir + File.separator + soundTemp2 + ".wav";
                CopySdcardFile(fromFile4, toFile4);
            }
        }
        if (!vedioStrings.equals("")) {
            if (vedioStrings.contains(",")) {
                String[] vedioStringVec = vedioStrings.split(",");
                for (String vedioTemp : vedioStringVec) {
                    String fromFile5 = fromDir + File.separator + vedioTemp + ".mp4";
                    String toFile5 = toDir + File.separator + vedioTemp + ".mp4";
                    CopySdcardFile(fromFile5, toFile5);
                }
            } else {
                String vedioTemp2 = vedioStrings;
                String fromFile6 = fromDir + File.separator + vedioTemp2 + ".mp4";
                String toFile6 = toDir + File.separator + vedioTemp2 + ".mp4";
                CopySdcardFile(fromFile6, toFile6);
            }
        }
        String Zip = mapFlord2 + "GEOVIP.zip";
        File fielzip = new File(Zip);
        fielzip.delete();
        if (!fielzip.exists()) {
            try {
                fielzip.createNewFile();
            } catch (IOException e3) {
                e3.printStackTrace();
            }
        }
        new ZipUtils();
        ArrayList<File> resFileList = new ArrayList<>();
        File file = new File(toDir);
        resFileList.add(file);
        try {
            ZipUtils.zipFiles(resFileList, fielzip);
            zipPath = Zip;
        } catch (IOException e4) {
            e4.printStackTrace();
        }
        return zipPath;
    }

    private int CopySdcardFile(String fromFile, String toFile) {
        try {
            InputStream fosfrom = new FileInputStream(fromFile);
            OutputStream fosto = new FileOutputStream(toFile);
            byte[] bt = new byte[1024];
            while (true) {
                int c = fosfrom.read(bt);
                if (c > 0) {
                    fosto.write(bt, 0, c);
                } else {
                    fosfrom.close();
                    fosto.close();
                    return 0;
                }
            }
        } catch (Exception e) {
            return -1;
        }
    }

    public void deleteFolderFile(String filePath, boolean deleteThisPath) throws IOException {
        if (!TextUtils.isEmpty(filePath)) {
            File file = new File(filePath);
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File file2 : files) {
                    deleteFolderFile(file2.getAbsolutePath(), true);
                }
            }
            if (deleteThisPath) {
                if (!file.isDirectory()) {
                    file.delete();
                } else if (file.listFiles().length == 0) {
                    file.delete();
                }
            }
        }
    }

    public Boolean IsNetworkConnected(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            if (mNetworkInfo != null) {
                return Boolean.valueOf(mNetworkInfo.isAvailable());
            }
        }
        return false;
    }
}
