package com.AoRGMap;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import com.AoGIS.base.GeoRect;
import com.AoGIS.database.AoMap;
import com.AoGIS.database.GdbDataType;
import com.AoGIS.database.GdbFieldInfo;
import com.AoGIS.database.WorkArea;
import com.AoGIS.util.DisplayHelper;
import com.AoGIS.util.GdbAttributesMap;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.apache.commons.io.IOUtils;

/* loaded from: classes.dex */
public class RouteSumActivity extends Activity {
    private static final AoMap NULL = null;
    protected Button m_btnOK = null;
    protected Button m_btnCancel = null;
    protected EditText m_editDesc = null;
    WorkArea GrouteArea = null;
    short index = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strGRoute);
    private final int MENU_WORK_SUM = 1;

    @Override // android.app.Activity
    protected Dialog onCreateDialog(int id, Bundle args) {
        return super.onCreateDialog(id, args);
    }

    @Override // android.app.Activity
    protected Dialog onCreateDialog(int id) {
        return super.onCreateDialog(id);
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        String str = getResources().getString(R.string.RGMAP_PROMPT_WORKSUM);
        menu.addSubMenu(0, 1, 0, str);
        super.onCreateOptionsMenu(menu);
        return true;
    }

    @Override // android.app.Activity
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case 1:
                String StrLine = TJWorkSum();
                String RouteDesc1 = this.m_editDesc.getEditableText().toString();
                this.m_editDesc.setText(StrLine + RouteDesc1);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override // android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.routesum);
        this.m_btnOK = (Button) findViewById(R.id.routesum_button_ok);
        this.m_btnCancel = (Button) findViewById(R.id.routesum_button_cancel);
        this.m_editDesc = (EditText) findViewById(R.id.editText_routesum);
        if (this.index < 0) {
            Toast toast = DisplayHelper.getCommonToast();
            toast.setText(R.string.RGMAP_PROMPT_OPENMAP);
            toast.show();
            return;
        }
        boolean updated = false;
        AoMap.GeoItemState state = AoRGMapActivity.getCurrentMap().getItemState(this.index);
        if (state != AoMap.GeoItemState.EDIT && state != AoMap.GeoItemState.CUREDIT) {
            AoRGMapActivity.getCurrentMap().setItemState(this.index, AoMap.GeoItemState.EDIT);
        }
        this.GrouteArea = AoRGMapActivity.getCurrentMap().getItemWorkArea(this.index);
        GdbFieldInfo[] flds = this.GrouteArea.getAttributeSchemaManager().getFieldInfos();
        for (GdbFieldInfo gdbFieldInfo : flds) {
            if (gdbFieldInfo.getFieldname().compareToIgnoreCase("geo_desc") == 0) {
                updated = true;
            }
        }
        if (!updated) {
            Log.d("Str", "AttModi");
            GdbFieldInfo info = new GdbFieldInfo();
            info.setFieldname("geo_desc");
            info.setDisplayLength((short) 10);
            info.setDecimalScale((byte) 0);
            info.setFieldtype(GdbDataType.STRING);
            info.setPrintFlag((byte) 0);
            info.setMskLeng((short) -1);
            info.setIsEditable((short) 1);
            this.GrouteArea.getAttributeSchemaManager().appendField(info);
        }
        if (updated) {
            Map<String, Object> attRoute = this.GrouteArea.getNamedAttributeStrings(1);
            String RouteDesc = attRoute.get("geo_desc").toString();
            this.m_editDesc.setText(RouteDesc);
        }
        this.m_btnOK.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.RouteSumActivity.1
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                Object RouteDesc1 = RouteSumActivity.this.m_editDesc.getEditableText().toString();
                GdbAttributesMap<String, Object> RouteAttMap = RouteSumActivity.this.GrouteArea.getNamedAttributeStrings(1);
                RouteAttMap.put("geo_desc", RouteDesc1);
                RouteSumActivity.this.GrouteArea.setNamedAttributes(1, RouteAttMap);
                RouteSumActivity.this.GrouteArea.SaveArea();
                RouteSumActivity.this.finish();
            }
        });
        this.m_btnCancel.setOnClickListener(new View.OnClickListener() { // from class: com.AoRGMap.RouteSumActivity.2
            @Override // android.view.View.OnClickListener
            public void onClick(View v) {
                RouteSumActivity.this.GrouteArea.SaveArea();
                RouteSumActivity.this.finish();
            }
        });
    }

    public String TJWorkSum() {
        GeoRect rect;
        double DRouteLen = 0.0d;
        int SampleSize = 0;
        short index = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strRouting);
        WorkArea GrouteArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index);
        if (GrouteArea != null && (rect = GrouteArea.getRect()) != null) {
            int[] gidarr = GrouteArea.getGeometryIdListByRect(rect);
            for (int i : gidarr) {
                GdbAttributesMap<String, Object> RouteAttMap = GrouteArea.getNamedAttributeStrings(i);
                if (RouteAttMap != null) {
                    String szLen = RouteAttMap.get("DISTANCE").toString();
                    double dLen = Double.valueOf(szLen).doubleValue();
                    DRouteLen += dLen;
                }
            }
        }
        short index2 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strBoundary);
        WorkArea BoundaryArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index2);
        int boundrySize = BoundaryArea != null ? BoundaryArea.getPhysicalCount() - 1 : 0;
        short index3 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strGPoint);
        WorkArea GPointArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index3);
        int GpointSize = GPointArea != null ? GPointArea.getPhysicalCount() - 1 : 0;
        short index4 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strAttitude);
        WorkArea AttitudeArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index4);
        int AttitudeSize = AttitudeArea != null ? AttitudeArea.getPhysicalCount() - 1 : 0;
        Map<String, Integer> atttj = new HashMap<>();
        short index5 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSample);
        WorkArea SampleArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index5);
        if (SampleArea != null) {
            SampleSize = SampleArea.getPhysicalCount() - 1;
            for (int i2 = 1; i2 <= SampleSize; i2++) {
                GdbAttributesMap<String, Object> AttMap = SampleArea.getNamedAttributeStrings(i2);
                if (AttMap != null) {
                    String stttype = AttMap.get("TYPE").toString();
                    if (atttj.containsKey(stttype)) {
                        Integer isize = atttj.get(stttype);
                        atttj.put(stttype, Integer.valueOf(isize.intValue() + 1));
                    } else {
                        atttj.put(stttype, 1);
                    }
                }
            }
        }
        short index6 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSketch);
        WorkArea SketchArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index6);
        int SketchSize = SketchArea != null ? SketchArea.getPhysicalCount() - 1 : 0;
        short index7 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strPhoto);
        WorkArea PhotoArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index7);
        int PhotoSize = PhotoArea != null ? PhotoArea.getPhysicalCount() - 1 : 0;
        String rootPath = AoRGMapActivity.getCurrentMapPath();
        String imgFolder = rootPath + "/images/";
        File vFilepath = new File(imgFolder);
        int soundSize = getSoundlv(vFilepath);
        int videoSize = getVideolv(vFilepath);
        String str = ((String) null) + "一、" + getResources().getString(R.string.RGMAP_PROMPT_ROUTELEN);
        String szLine = getResources().getString(R.string.RGMAP_PROMPT_ROUTELEN) + ":" + String.valueOf(DRouteLen) + IOUtils.LINE_SEPARATOR_UNIX;
        if (boundrySize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_BOUNDARYNUM) + ":" + String.valueOf(boundrySize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (GpointSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_POINTNUM) + ":" + String.valueOf(GpointSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (AttitudeSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_SAMPLENUM) + ":" + String.valueOf(AttitudeSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (SampleSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_ATTINUM) + ":" + String.valueOf(SampleSize) + IOUtils.LINE_SEPARATOR_UNIX;
            Set setAtt = atttj.entrySet();
            for (Map.Entry<String, Integer> entAtt : setAtt) {
                szLine = szLine + entAtt.getKey() + ":" + String.valueOf(entAtt.getValue()) + IOUtils.LINE_SEPARATOR_UNIX;
            }
        }
        if (SketchSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_SKETCHNUM) + ":" + String.valueOf(SketchSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (PhotoSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_PHOTONUM) + ":" + String.valueOf(PhotoSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (soundSize > 0) {
            szLine = szLine + getResources().getString(R.string.RGMAP_PROMPT_SOUNDNUM) + ":" + String.valueOf(soundSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        if (videoSize > 0) {
            return szLine + getResources().getString(R.string.RGMAP_PROMPT_VIDEONUM) + ":" + String.valueOf(videoSize) + IOUtils.LINE_SEPARATOR_UNIX;
        }
        return szLine;
    }

    public int getSoundlv(File filepath) {
        File[] files;
        int soundsum = 0;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (!file.isDirectory()) {
                    String strname = file.getName();
                    if (strname.indexOf("wav") > -1 || strname.indexOf("WAV") > -1) {
                        soundsum++;
                    }
                }
            }
        }
        return soundsum;
    }

    public int getVideolv(File filepath) {
        File[] files;
        int videosum = 0;
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (!file.isDirectory()) {
                    String strname = file.getName();
                    if (strname.indexOf("mp4") > -1 || strname.indexOf("MP4") > -1) {
                        videosum++;
                    }
                }
            }
        }
        return videosum;
    }
}
