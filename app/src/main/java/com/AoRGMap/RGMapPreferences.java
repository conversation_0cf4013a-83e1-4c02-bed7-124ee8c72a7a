package com.AoRGMap;

import android.content.Context;
import android.content.SharedPreferences;

/* loaded from: classes.dex */
public class RGMapPreferences {
    public static final String mAoGISDicPath = "GisDicPath";
    public static final String mAoGISLibPath = "GisLibPath";
    public static final String mAoLanguage = "AoLanguage";
    public static final String mAttitudeSubSize = "AttitudeSubSize";
    public static final String mEditMode = "EditMode";
    public static final String mGlobalAudioType = "GlobalAudioType";
    public static final String mGlobalBoundaryNo = "GlobalBoundaryNo";
    public static final String mGlobalCameraType = "GlobalCameraType";
    public static final String mGlobalCommonMenu = "GlobalCommonMenu";
    public static final String mGlobalCompassType = "GlobalCompassType";
    public static final String mGlobalCounterFileAndFld = "GlobalCounterFileAndFld";
    public static final String mGlobalCpj = "GlobalCpj";
    public static final String mGlobalDigtalSize = "GlobalDigtalSize";
    public static final String mGlobalGeochemMenu = "GlobalGeochemMenu";
    public static final String mGlobalKTXCode = "GlobalKTXCode";
    public static final String mGlobalKcanbCode = "GlobalKcanbCode";
    public static final String mGlobalKcanbName = "GlobalKcanbName";
    public static final String mGlobalLoginInfo = "GlobalLoginInfo";
    public static final String mGlobalOnlineDataPack = "GlobalOnlineDataPack";
    public static final String mGlobalPointNo = "GlobalPointNo";
    public static final String mGlobalRouteNo = "GlobalRouteNo";
    public static final String mGlobalRoutingMinValue = "GlobalRoutingMinValue";
    public static final String mGlobalRoutingNo = "GlobalRoutingNo";
    public static final String mGlobalRoutingStartNo = "GlobalRoutingStartNo";
    public static final String mGlobalSamLayer = "GlobalSamLayer";
    public static final String mGlobalUserDefXml = "GlobalUserDefXml";
    public static final String mGlobalVoicePrompt = "GlobalVoicePrompt";
    public static final String mGlobalboundaryStartNo = "GlobalboundaryStartNo";
    public static final String mGpsAutoRecordInterval = "GpsAutoRecordInterval";
    public static final String mGpsAutoRecordType = "GpsAutoRecordType";
    public static final String mGpsRectifyX = "GpsRectifyX";
    public static final String mGpsRectifyY = "GpsRectifyY";
    public static final String mGpsRectifyZ = "GpsRectifyZ";
    public static final String mLbaleSize = "LbaleSize";
    public static final String mMapName = "MapName";
    public static final String mMapScale = "MapScale";
    public static final String mMapX = "MapX";
    public static final String mMapY = "MapY";
    public static final String mPRBSubSize = "PRBSubSize";
    public static final String mPointPrefix = "PointPrefix";
    private static final String mPreName = "AoRGMap";
    public static final String mRoutePrefix = "RoutePrefix";
    public static final String mSecPath = "SectionPath";
    public static final String mTMSOfflineFileName = "TMSOfflineFileName";
    public static final String mTMSUseOfflineFlag = "TMSUseOfflineFlag";
    private Context mContext;
    private SharedPreferences mSP = null;
    private SharedPreferences.Editor mEditor = null;

    public RGMapPreferences(Context context) {
        this.mContext = null;
        this.mContext = context;
    }

    public boolean openForRead() {
        this.mSP = this.mContext.getSharedPreferences(mPreName, 0);
        return this.mSP != null;
    }

    public boolean closeForRead() {
        return true;
    }

    public String getString(String key, String defValue) {
        return this.mSP == null ? defValue : this.mSP.getString(key, defValue);
    }

    public boolean getBoolean(String key, boolean defValue) {
        return this.mSP == null ? defValue : this.mSP.getBoolean(key, defValue);
    }

    public int getInt(String key, int defValue) {
        return this.mSP == null ? defValue : this.mSP.getInt(key, defValue);
    }

    public float getFloat(String key, float defValue) {
        return this.mSP == null ? defValue : this.mSP.getFloat(key, defValue);
    }

    public long getLong(String key, long defValue) {
        return this.mSP == null ? defValue : this.mSP.getLong(key, defValue);
    }

    public boolean openForWrite() {
        this.mSP = this.mContext.getSharedPreferences(mPreName, 0);
        if (this.mSP == null) {
            return false;
        }
        this.mEditor = this.mSP.edit();
        return this.mEditor != null;
    }

    public boolean closeForWrite() {
        if (this.mEditor == null) {
            return false;
        }
        return this.mEditor.commit();
    }

    public boolean putString(String key, String value) {
        if (this.mEditor == null) {
            return false;
        }
        this.mEditor.putString(key, value);
        return true;
    }

    public boolean putBoolean(String key, boolean value) {
        if (this.mEditor == null) {
            return false;
        }
        this.mEditor.putBoolean(key, value);
        return true;
    }

    public boolean putInt(String key, int value) {
        if (this.mEditor == null) {
            return false;
        }
        this.mEditor.putInt(key, value);
        return true;
    }

    public boolean putFloat(String key, float value) {
        if (this.mEditor == null) {
            return false;
        }
        this.mEditor.putFloat(key, value);
        return true;
    }

    public boolean putLong(String key, long value) {
        if (this.mEditor == null) {
            return false;
        }
        this.mEditor.putLong(key, value);
        return true;
    }
}
