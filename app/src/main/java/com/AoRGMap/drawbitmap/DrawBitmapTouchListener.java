package com.AoRGMap.drawbitmap;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.view.MotionEvent;
import com.AoGIS.edit.ComplexEditTouchListener;
import com.AoGIS.edit.gesture.DragGesture;
import com.AoGIS.edit.gesture.IFixedFingerGesture;
import com.AoGIS.edit.gesture.ZoomGesture;
import com.AoGIS.edit.pad.HoldingTouchPad;
import com.AoGIS.util.RelativeRect;
import com.AoRGMap.R;
import com.AoRGMap.RGMapApplication;

/* loaded from: classes.dex */
public class DrawBitmapTouchListener extends BaseDrawBitmapTouchListener {
    private int mGid;
    private HoldingTouchPad mPadDraw;
    private PadListener mPadListener;
    Paint mPaint;
    private int m_curFingerNum;
    private IFixedFingerGesture m_handler;
    int width;

    @Override // com.AoGIS.render.BaseTouchListener
    public void onBitmapDraw(Bitmap bitmap) {
        if (this.m_handler instanceof DrawBitmapLineGesture) {
            Path path = ((DrawBitmapLineGesture) this.m_handler).getPath();
            if (!path.isEmpty()) {
                Canvas canvas = new Canvas(bitmap);
                canvas.drawPath(path, this.mPaint);
            }
        }
        super.onBitmapDraw(bitmap);
    }

    public DrawBitmapTouchListener(BitmapEditView bitmapEditView) {
        super(bitmapEditView);
        this.width = 3;
        this.mPaint = null;
        this.mPadListener = new PadListener();
        this.m_curFingerNum = 0;
        this.mGid = 0;
        this.mPadDraw = new HoldingTouchPad(bitmapEditView, new RelativeRect(RelativeRect.VerticalBase.BOTTOM, RelativeRect.HorizontalBase.LEFT, 10, 10, 50, 50));
        addSpecialTouchPad(this.mPadDraw);
        this.mPadDraw.setListener(this.mPadListener);
        getView().invalidate();
        this.mPaint = new Paint();
        this.mPaint.setAntiAlias(true);
        this.mPaint.setColor(-16776961);
        this.mPaint.setStyle(Paint.Style.STROKE);
        this.mPaint.setStrokeJoin(Paint.Join.ROUND);
        this.mPaint.setStrokeCap(Paint.Cap.SQUARE);
        this.mPaint.setStrokeWidth(this.width);
    }

    public DrawBitmapTouchListener(BitmapEditView v, int width) {
        super(v);
        this.width = 3;
        this.mPaint = null;
        this.mPadListener = new PadListener();
        this.m_curFingerNum = 0;
        this.mGid = 0;
        this.width = width;
        this.mPadDraw = new HoldingTouchPad(v, new RelativeRect(RelativeRect.VerticalBase.BOTTOM, RelativeRect.HorizontalBase.LEFT, 10, 10, 50, 50));
        addSpecialTouchPad(this.mPadDraw);
        this.mPadDraw.setListener(this.mPadListener);
        getView().invalidate();
        this.mPaint = new Paint();
        this.mPaint.setAntiAlias(true);
        this.mPaint.setColor(-16776961);
        this.mPaint.setStyle(Paint.Style.STROKE);
        this.mPaint.setStrokeJoin(Paint.Join.ROUND);
        this.mPaint.setStrokeCap(Paint.Cap.SQUARE);
        this.mPaint.setStrokeWidth(width);
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public String[] getExtendFunctions() {
        String strbt1 = RGMapApplication.getCurrentApp().getResources().getString(R.string.RGMAP_BITMAP_UNDO);
        String strbt2 = RGMapApplication.getCurrentApp().getResources().getString(R.string.RGMAP_BITMAP_REDRAW);
        String strbt3 = RGMapApplication.getCurrentApp().getResources().getString(R.string.RGMAP_PROMPT_CLEAR);
        String strbt4 = RGMapApplication.getCurrentApp().getResources().getString(R.string.RGMAP_PROMPT_RESET);
        return new String[]{strbt1, strbt2, strbt3, strbt4};
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public boolean callFunction(int i, Object param) {
        switch (i) {
            case 0:
                getView().BackPath();
                break;
            case 1:
                getView().RestorePath();
                break;
            case 2:
                getView().ClearBitmap();
                break;
            case 3:
                getView().RestoreSize();
                break;
        }
        return true;
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public boolean onEditOk() {
        if (this.m_handler instanceof DrawBitmapLineGesture) {
            Path path = ((DrawBitmapLineGesture) this.m_handler).getPath();
            if (!path.isEmpty()) {
                getView().AddPath(path);
                getView().invalidate();
            }
        }
        return super.onEditOk();
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public boolean onEditCancel() {
        return super.onEditCancel();
    }

    @Override // com.AoGIS.render.BaseTouchListener
    public BitmapEditView getView() {
        return (BitmapEditView) super.getView();
    }

    private class PadListener implements HoldingTouchPad.HoldingTouchPadListener {
        private PadListener() {
        }

        @Override // com.AoGIS.edit.pad.HoldingTouchPad.HoldingTouchPadListener
        public void onPadStateChanged(boolean bPressingOld, boolean bPressing, boolean bHoldingOld, boolean bHolding, MotionEvent event) {
            DrawBitmapTouchListener.this.dealFingerChanged(DrawBitmapTouchListener.this.m_curFingerNum, DrawBitmapTouchListener.this.m_curFingerNum, DrawBitmapTouchListener.this.getClassAStates(), event);
        }
    }

    @SuppressLint({"NewApi"})
    public void dealFingerChanged(int oldCount, int newCount, ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        this.m_curFingerNum = newCount;
        if (newCount != oldCount || newCount == 1) {
            if (oldCount < 2 || newCount < 2) {
                if (newCount == -1) {
                    if (this.m_handler != null) {
                        this.m_handler.doCancel(event);
                    }
                    this.m_handler = null;
                    this.m_curFingerNum = 0;
                    return;
                }
                if (this.m_handler != null) {
                    this.m_handler.doEnd(pointers, event);
                }
                switch (newCount) {
                    case 0:
                        this.m_handler = null;
                        break;
                    case 1:
                        if (this.mPadDraw != null && this.mPadDraw.isPressing()) {
                            this.m_handler = new DrawBitmapLineGesture(this, getView().getMatrix());
                            break;
                        } else {
                            this.m_handler = new DragGesture(getView());
                            break;
                        }
                    case 2:
                        this.m_handler = new ZoomGesture(getView());
                        break;
                }
                if (this.m_handler != null) {
                    this.m_handler.doStart(pointers, event);
                }
            }
        }
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureStart(ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        dealFingerChanged(0, 1, new ComplexEditTouchListener.PointerState[]{pointer}, event);
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureEnd(ComplexEditTouchListener.PointerState pointer, MotionEvent event) {
        dealFingerChanged(1, 0, new ComplexEditTouchListener.PointerState[]{pointer}, event);
        getView().invalidate();
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureFingerChanged(ComplexEditTouchListener.PointerState[] pointers, ComplexEditTouchListener.PointerState changedOne, boolean isAdded, MotionEvent event) {
        int curFingers = pointers.length;
        int oldFingers = pointers.length - (isAdded ? 1 : -1);
        dealFingerChanged(oldFingers, curFingers, pointers, event);
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureMove(ComplexEditTouchListener.PointerState[] pointers, MotionEvent event) {
        if (this.m_handler != null) {
            this.m_handler.doTouchMove(pointers, event);
        }
        getView().invalidate();
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener
    public boolean onGestureCancel(MotionEvent event) {
        dealFingerChanged(-1, -1, null, event);
        return false;
    }

    @Override // com.AoGIS.edit.ComplexEditTouchListener, com.AoGIS.render.BaseTouchListener
    public void onCanvasDraw(Canvas canvas) {
        super.onCanvasDraw(canvas);
    }
}
