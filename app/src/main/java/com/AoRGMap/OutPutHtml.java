package com.AoRGMap;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;
import android.webkit.WebView;
import com.AoDevBase.ui.AttributeExtActivity;
import com.AoGIS.base.GeoLine;
import com.AoGIS.base.GeoPoint;
import com.AoGIS.base.GeoRect;
import com.AoGIS.database.AoMap;
import com.AoGIS.database.GdbFieldInfo;
import com.AoGIS.database.WorkArea;
import com.AoGIS.database.WorkAreaParams;
import com.AoGIS.database.WorkSpace;
import com.AoGIS.render.AoRender;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Vector;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;

/* loaded from: classes.dex */
public class OutPutHtml {
    public static final String m_strFile = AoRGMapActivity.getCurrentMapPath();
    private String htmlPath;
    private Context mContext;
    private File myFile;
    private FileOutputStream output;
    private String picturePath;
    private String videoPath;
    private WebView view;
    private String voicePath;
    String palypath = "/data/data/com.AoRGMap/AoGIS/play.jpg";
    String palypath1 = "/data/data/com.AoRGMap/AoGIS/play1.jpg";
    private String fileName = "summary";
    String routeCode = null;
    RgMapSort mapsort = new RgMapSort();

    public OutPutHtml(Context context) {
        this.mContext = context;
    }

    public String writeMulRouteHtml() {
        boolean blnezreo;
        String strTemp;
        String strTemp2;
        String strTemp3;
        String strTemp4;
        String strTemp5;
        String strTemp6;
        String strTemp7;
        RGMapApplication app = RGMapApplication.getCurrentApp();
        double unit = 1.0d;
        try {
            new WorkSpace();
            makeFile();
            this.myFile = new File(this.htmlPath);
            this.output = new FileOutputStream(this.myFile);
            Vector<String> VecRouteCode = new Vector<>();
            short index = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strGPoint);
            WorkArea gpointArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index);
            short index2 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strAttitude);
            WorkArea attitudeArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index2);
            short index3 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSketch);
            WorkArea sketchArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index3);
            short index4 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strPhoto);
            WorkArea photoArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index4);
            short index5 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strFossil);
            WorkArea fossilArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index5);
            short index6 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSample);
            WorkArea sampleArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index6);
            short index7 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strBoundary);
            WorkArea boundaryArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index7);
            short index8 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strRouting);
            WorkArea routingArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index8);
            int[] GeoPointIDArr = gpointArea.getGeometryIdListByAtt("ROUTECODE IS NOT NULL");
            for (int i : GeoPointIDArr) {
                Map<String, Object> GeoPointAtt = gpointArea.getNamedAttributeStrings(i);
                String strCodeString = GeoPointAtt.get("ROUTECODE").toString();
                if (!VecRouteCode.contains(strCodeString)) {
                    VecRouteCode.addElement(strCodeString);
                }
            }
            gpointArea.SaveArea();
            WorkAreaParams mapInfo = gpointArea.getWorkAreaParamsClone();
            if (mapInfo.getCoordType() != WorkAreaParams.LengthType.Meter) {
                unit = 1000.0d;
            }
            for (int i2 = 0; i2 < VecRouteCode.size(); i2++) {
                String RouteCode = VecRouteCode.get(i2);
                this.output.write("<p>".getBytes());
                String strTemp8 = app.getResources().getString(R.string.RGMAP_PROMPT_ROUTEDATA);
                writeContent(RouteCode + strTemp8, true);
                this.output.write("</p>".getBytes());
                int[] SelGeoPoint = gpointArea.getGeometryIdListByAtt("ROUTECODE = '" + RouteCode + "'");
                for (int iGPoint = 0; iGPoint < SelGeoPoint.length; iGPoint++) {
                    Map<String, Object> attGPoint = gpointArea.getNamedAttributeStrings(SelGeoPoint[iGPoint]);
                    GeoPoint GeoPointpnt = (GeoPoint) gpointArea.getGeometry(SelGeoPoint[iGPoint]);
                    this.output.write("<p>".getBytes());
                    String strTemp9 = app.getResources().getString(R.string.gpo_geopoint);
                    String strTempValue = attGPoint.get("GEOPOINT").toString();
                    if (strTempValue.equals("")) {
                        strTempValue = "?";
                    }
                    writeContent(strTemp9 + strTempValue, true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp10 = app.getResources().getString(R.string.gpo_xx);
                    writeContent(strTemp10 + attGPoint.get(AttributeExtActivity.PARAM_STRING_XX), true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp11 = app.getResources().getString(R.string.gpo_yy);
                    writeContent(strTemp11 + attGPoint.get(AttributeExtActivity.PARAM_STRING_YY), true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp12 = app.getResources().getString(R.string.gpo_outcrop);
                    String strTempValue2 = attGPoint.get("OUTCROP").toString();
                    if (strTempValue2.equals("")) {
                        strTempValue2 = "?";
                    }
                    writeContent(strTemp12 + strTempValue2, true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp13 = app.getResources().getString(R.string.gpo_type);
                    String strTempValue3 = attGPoint.get("TYPE").toString();
                    if (strTempValue3.equals("")) {
                        strTempValue3 = "?";
                    }
                    writeContent(strTemp13 + strTempValue3, true);
                    Boolean bhaqsyx = false;
                    String strTempValue4 = attGPoint.get("STRAPHA").toString();
                    if (!strTempValue4.equals("")) {
                        writeContent("(" + strTempValue4, true);
                        bhaqsyx = true;
                    }
                    String strTempValue5 = attGPoint.get("STRAPHB").toString();
                    if (!strTempValue5.equals("")) {
                        writeContent("and" + strTempValue5, true);
                    }
                    String strTempValue6 = attGPoint.get("STRAPHC").toString();
                    if (!strTempValue6.equals("")) {
                        writeContent("and " + strTempValue6, true);
                    }
                    if (bhaqsyx.booleanValue()) {
                        writeContent(")", true);
                    }
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp14 = app.getResources().getString(R.string.gpo_geomorph);
                    String strTempValue7 = attGPoint.get("GEOMORPH").toString();
                    if (strTempValue7.equals("")) {
                        strTempValue7 = "?";
                    }
                    writeContent(strTemp14 + strTempValue7, true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    String strTemp15 = app.getResources().getString(R.string.gpo_weathing);
                    String strTempValue8 = attGPoint.get("WEATHING").toString();
                    if (strTempValue8.equals("")) {
                        strTempValue8 = "?";
                    }
                    writeContent(strTemp15 + strTempValue8, true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    writeContent(attGPoint.get("geo_desc").toString(), true);
                    this.output.write("</p>".getBytes());
                    String sql = "GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')";
                    for (int i3 : attitudeArea.getGeometryIdListByAtt(sql)) {
                        Map<String, Object> attAttitude = attitudeArea.getNamedAttributeStrings(i3);
                        this.output.write("<p>".getBytes());
                        String strTemp16 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        if (!attAttitude.get("TYPE").toString().equals("")) {
                            strTemp16 = strTemp16 + attAttitude.get("TYPE") + app.getResources().getString(R.string.menu_main_prb_att) + ":";
                        }
                        String strLine = attAttitude.get("DIP").toString();
                        if (!strLine.equals("")) {
                            strTemp16 = strTemp16 + strLine + "∠" + attAttitude.get("DIP_ANG") + ";";
                        }
                        writeContent(strTemp16, false);
                        this.output.write("</p>".getBytes());
                    }
                    for (int i4 : photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')")) {
                        Map<String, Object> attPhoto = photoArea.getNamedAttributeStrings(i4);
                        this.output.write("<p>".getBytes());
                        String strTemp17 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        String strLine2 = attPhoto.get("DESCRIBE").toString();
                        String strTemp1 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCPHOTO);
                        if (!strLine2.contains(strTemp1)) {
                            strLine2 = strLine2 + strTemp1;
                        }
                        String strTemp18 = strTemp17 + strLine2;
                        String strLine3 = attPhoto.get("AMOUNT").toString();
                        if (!strLine3.equals("")) {
                            strTemp18 = strTemp18 + strLine3 + app.getResources().getString(R.string.RGMAP_PROMPT_PAGE);
                        }
                        String strLine4 = attPhoto.get("DIRECTION").toString();
                        if (strLine4.equals("")) {
                            strLine4 = "?";
                        }
                        writeContent(strTemp18 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_DIRECTION) + ":" + strLine4 + ",", false);
                        String strkeyword = attGPoint.get("GEOPOINT").toString() + "_" + attPhoto.get("CODE").toString();
                        String imgpath = m_strFile + File.separator + "images/";
                        File vFilepath = new File(imgpath);
                        File[] files = vFilepath.listFiles();
                        if (files != null && files.length > 0) {
                            for (File file : files) {
                                if (file.isFile()) {
                                    String strname = file.getName();
                                    if ((strname.indexOf(strkeyword) > -1 || strname.indexOf(strkeyword.toUpperCase()) > -1) && strname.indexOf("jpg") > -1) {
                                        this.output.write("<p>".getBytes());
                                        String imgfILEpath = m_strFile + File.separator + "images/" + strname;
                                        writePicture(imgfILEpath);
                                        this.output.write("</p>".getBytes());
                                    }
                                }
                            }
                        }
                        if (files != null && files.length > 0) {
                            for (File file2 : files) {
                                if (file2.isFile()) {
                                    String strname2 = file2.getName();
                                    if ((strname2.indexOf(strkeyword) > -1 || strname2.indexOf(strkeyword.toUpperCase()) > -1) && (strname2.indexOf("mp4") > -1 || strname2.indexOf("MP4") > -1)) {
                                        this.output.write("<p>".getBytes());
                                        String imgfILEpath2 = m_strFile + File.separator + "images/" + strname2;
                                        writeContent(strname2, false);
                                        writeVideo(imgfILEpath2, this.palypath);
                                        this.output.write("</p>".getBytes());
                                    }
                                }
                            }
                        }
                        if (files != null && files.length > 0) {
                            for (File file3 : files) {
                                if (file3.isFile()) {
                                    String strname3 = file3.getName();
                                    if ((strname3.indexOf(strkeyword) > -1 || strname3.indexOf(strkeyword.toUpperCase()) > -1) && (strname3.indexOf("wav") > -1 || strname3.indexOf("WAV") > -1)) {
                                        this.output.write("<p>".getBytes());
                                        String imgfILEpath3 = m_strFile + File.separator + "images/" + strname3;
                                        writeContent(strname3, false);
                                        writeVoice(imgfILEpath3, this.palypath1);
                                        this.output.write("</p>".getBytes());
                                    }
                                }
                            }
                        }
                        this.output.write("</p>".getBytes());
                    }
                    for (int i5 : sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                        Map<String, Object> attSketch = sketchArea.getNamedAttributeStrings(i5);
                        this.output.write("<p>".getBytes());
                        String strTemp19 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        String strLine5 = attSketch.get("TITLE").toString();
                        if (!strLine5.equals("")) {
                            String strTemp110 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                            String strTemp22 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCSEC);
                            if (!strLine5.contains(strTemp110) && !strLine5.contains(strTemp22)) {
                                strLine5 = strLine5 + strTemp110;
                            }
                        } else {
                            strLine5 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                        }
                        String strTemp20 = strTemp19 + strLine5;
                        String strLine6 = attSketch.get("SCALE").toString();
                        if (!strLine6.equals("")) {
                            strTemp20 = strTemp20 + ("," + app.getResources().getString(R.string.RGMAP_PROMPT_SCALE) + "：1：" + strLine6);
                        }
                        writeContent(strTemp20, false);
                        String SketchSpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".GPJ";
                        File vFile = new File(SketchSpath);
                        if (vFile.exists()) {
                            AoMap m_Map1 = AoMap.openMap(SketchSpath);
                            Bitmap bitmap = Bitmap.createBitmap(800, HttpStatus.SC_MULTIPLE_CHOICES, Bitmap.Config.ARGB_8888);
                            bitmap.eraseColor(-1);
                            AoRender render = new AoRender(bitmap);
                            render.setRenderSize(800, HttpStatus.SC_MULTIPLE_CHOICES);
                            GeoRect rect = new GeoRect();
                            m_Map1.getMapRect(rect);
                            double sx = 800.0d / (rect.getXmax() - rect.getXmin());
                            double sy = 300.0d / (rect.getYmax() - rect.getYmin());
                            double scale = sx < sy ? sx : sy;
                            render.setDrawParam(rect.getXmin(), rect.getYmin(), scale);
                            render.drawMap(m_Map1);
                            String SketchIMGpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".jpg";
                            render.saveBitmap(bitmap, SketchIMGpath, Bitmap.CompressFormat.JPEG, 75);
                            render.deleteRender();
                            m_Map1.closeMap();
                            this.output.write("<p>".getBytes());
                            writePicture(SketchIMGpath);
                            this.output.write("</p>".getBytes());
                        }
                        String SketchSpath1 = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".PNG";
                        File vFile1 = new File(SketchSpath1);
                        if (vFile1.exists()) {
                            this.output.write("<p>".getBytes());
                            writePicture(SketchSpath1);
                            this.output.write("</p>".getBytes());
                        }
                        this.output.write("</p>".getBytes());
                    }
                    for (int i6 : fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                        Map<String, Object> attFossil = fossilArea.getNamedAttributeStrings(i6);
                        this.output.write("<p>".getBytes());
                        String strTemp21 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        String strLine7 = attFossil.get("NAME").toString();
                        if (!strLine7.equals("")) {
                            strTemp7 = strTemp21 + strLine7 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                        } else {
                            strTemp7 = strTemp21 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                        }
                        String strLine8 = attFossil.get("CODE").toString();
                        if (!strLine8.equals("")) {
                            strTemp7 = strTemp7 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine8;
                        }
                        String strLine9 = attFossil.get("PNAME").toString();
                        if (!strLine9.equals("")) {
                            strTemp7 = strTemp7 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILELAYE) + ":" + strLine9;
                        }
                        writeContent(strTemp7, false);
                        this.output.write("</p>".getBytes());
                    }
                    for (int i7 : sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                        Map<String, Object> attSample = sampleArea.getNamedAttributeStrings(i7);
                        this.output.write("<p>".getBytes());
                        String strTemp23 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        String strLine10 = attSample.get("TYPE").toString();
                        if (!strLine10.equals("")) {
                            strTemp23 = strTemp23 + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLE) + "(" + strLine10 + ")";
                        }
                        String strLine11 = attSample.get("CODE").toString();
                        if (!strLine11.equals("")) {
                            strTemp23 = strTemp23 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine11;
                        }
                        String strLine12 = attSample.get("NAME").toString();
                        if (!strLine12.equals("")) {
                            strTemp23 = strTemp23 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLELAYE) + ":" + strLine12;
                        }
                        writeContent(strTemp23, false);
                        this.output.write("</p>".getBytes());
                    }
                    int[] boundaryID = boundaryArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')");
                    for (int i8 : boundaryID) {
                        Map<String, Object> attboundary = boundaryArea.getNamedAttributeStrings(i8);
                        this.output.write("<p>".getBytes());
                        String strTemp24 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                        String strLine13 = attboundary.get("SUBPOINT").toString();
                        if (!strLine13.equals("")) {
                            strTemp24 = strTemp24 + strLine13 + ":";
                        }
                        String strLine14 = attboundary.get("geo_desc").toString();
                        if (!strLine14.equals("")) {
                            strTemp24 = strTemp24 + strLine14;
                        }
                        writeContent(strTemp24, false);
                        this.output.write("</p>".getBytes());
                    }
                    double dRouteLen = 0.0d;
                    int[] routingID = routingArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' ");
                    for (int iRouting = 0; iRouting < routingID.length; iRouting++) {
                        Map<String, Object> attRouting = routingArea.getNamedAttributeStrings(routingID[iRouting]);
                        this.mapsort.reset();
                        GeoLine geoLine = (GeoLine) routingArea.getGeometry(routingID[iRouting]);
                        getpntLegth(geoLine);
                        String StrTitle = attRouting.get("geo_desc").toString();
                        String routecode = attRouting.get("R_CODE").toString();
                        String strDesc = StrTitle;
                        String strm = app.getResources().getString(R.string.RGMAP_PROMPT_DESCM);
                        if (StrTitle.indexOf(routecode) == 0) {
                            int idx = StrTitle.indexOf(44);
                            strDesc = StrTitle.substring(StrTitle.indexOf(strm, idx) + 1);
                        }
                        int[] attitudeID = attitudeArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                        for (int iAttitude = 0; iAttitude < attitudeID.length; iAttitude++) {
                            GeoPoint pnt = (GeoPoint) attitudeArea.getGeometry(attitudeID[iAttitude]);
                            if (pnt != null) {
                                this.mapsort.insert(((dRouteLen + GetPntToLin(pnt, geoLine)) * mapInfo.rate) / unit, 1, attitudeID[iAttitude], getpntAngle(GeoPointpnt, pnt));
                            }
                        }
                        int[] sketchID = sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                        for (int iSketch = 0; iSketch < sketchID.length; iSketch++) {
                            GeoPoint pnt2 = (GeoPoint) sketchArea.getGeometry(sketchID[iSketch]);
                            if (pnt2 != null) {
                                this.mapsort.insert(((dRouteLen + GetPntToLin(pnt2, geoLine)) * mapInfo.rate) / unit, 2, sketchID[iSketch], getpntAngle(GeoPointpnt, pnt2));
                            }
                        }
                        int[] photoID = photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                        for (int iPhoto = 0; iPhoto < photoID.length; iPhoto++) {
                            GeoPoint pnt3 = (GeoPoint) photoArea.getGeometry(photoID[iPhoto]);
                            if (pnt3 != null) {
                                this.mapsort.insert(((dRouteLen + GetPntToLin(pnt3, geoLine)) * mapInfo.rate) / unit, 3, photoID[iPhoto], getpntAngle(GeoPointpnt, pnt3));
                            }
                        }
                        int[] fossilID = fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                        for (int iFossil = 0; iFossil < fossilID.length; iFossil++) {
                            GeoPoint pnt4 = (GeoPoint) fossilArea.getGeometry(fossilID[iFossil]);
                            if (pnt4 != null) {
                                this.mapsort.insert(((dRouteLen + GetPntToLin(pnt4, geoLine)) * mapInfo.rate) / unit, 4, fossilID[iFossil], getpntAngle(GeoPointpnt, pnt4));
                            }
                        }
                        int[] sampleID = sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                        for (int iSample = 0; iSample < sampleID.length; iSample++) {
                            GeoPoint pnt5 = (GeoPoint) sampleArea.getGeometry(sampleID[iSample]);
                            if (pnt5 != null) {
                                this.mapsort.insert(((dRouteLen + GetPntToLin(pnt5, geoLine)) * mapInfo.rate) / unit, 5, sampleID[iSample], getpntAngle(GeoPointpnt, pnt5));
                            }
                        }
                        int itrans = 0;
                        this.output.write("<p>".getBytes());
                        String strTemp25 = app.getResources().getString(R.string.RGMAP_PROMPT_PRDESC);
                        writeContent(strTemp25, true);
                        this.output.write("</p>".getBytes());
                        if (iRouting == 0) {
                            String sAng = attRouting.get("DIRECTION").toString();
                            String SZlen = app.getResources().getString(R.string.RGMAP_PROMPT_POINT) + app.getResources().getString(R.string.RGMAP_PROMPT_TO) + getpntAngle(Double.valueOf(sAng).doubleValue()) + attRouting.get("DISTANCE").toString() + "m";
                            this.output.write("<p>".getBytes());
                            writeContent(SZlen, false);
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(strDesc, false);
                            this.output.write("</p>".getBytes());
                        } else {
                            String sAng2 = attRouting.get("DIRECTION").toString();
                            String sAng3 = getpntAngle(Double.valueOf(sAng2).doubleValue());
                            double dlenTemp = (mapInfo.rate * dRouteLen) / unit;
                            String SZlen2 = String.format("%.0fm", Double.valueOf(dlenTemp)) + app.getResources().getString(R.string.RGMAP_PROMPT_TO) + sAng3 + attRouting.get("DISTANCE").toString() + "m";
                            this.output.write("<p>".getBytes());
                            writeContent(SZlen2, false);
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(strDesc, false);
                            this.output.write("</p>".getBytes());
                        }
                        String[] strdataArr = this.mapsort.getStr();
                        for (int idex = 0; idex < this.mapsort.getCount(); idex++) {
                            String[] dataArr = strdataArr[idex].split(",");
                            int itype = Integer.valueOf(dataArr[1]).intValue();
                            int lid = Integer.valueOf(dataArr[2]).intValue();
                            String szAng = dataArr[3];
                            double dlensTemp = Double.valueOf(dataArr[0]).doubleValue();
                            String dlen = String.format("%.0fm", Double.valueOf(dlensTemp));
                            if (dlen.equals("0m")) {
                                blnezreo = true;
                            } else {
                                blnezreo = false;
                                itrans++;
                            }
                            switch (itype) {
                                case 1:
                                    Map<String, Object> attAttitude2 = attitudeArea.getNamedAttributeStrings(lid);
                                    this.output.write("<p>".getBytes());
                                    if (blnezreo) {
                                        strTemp6 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                    } else {
                                        strTemp6 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                    }
                                    String strTemp26 = strTemp6 + attAttitude2.get("TYPE") + app.getResources().getString(R.string.menu_main_prb_att) + ":";
                                    String strLine15 = attAttitude2.get("DIP").toString();
                                    if (!strLine15.equals("")) {
                                        strTemp26 = strTemp26 + strLine15 + "∠" + attAttitude2.get("DIP_ANG") + ";";
                                    }
                                    writeContent(strTemp26, false);
                                    this.output.write("</p>".getBytes());
                                    break;
                                case 2:
                                    Map<String, Object> attSketch2 = sketchArea.getNamedAttributeStrings(lid);
                                    this.output.write("<p>".getBytes());
                                    if (blnezreo) {
                                        strTemp5 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                    } else {
                                        strTemp5 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                    }
                                    String strLine16 = attSketch2.get("TITLE").toString();
                                    if (!strLine16.equals("")) {
                                        String strTemp111 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                        String strTemp27 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCSEC);
                                        if (!strLine16.contains(strTemp111) && !strLine16.contains(strTemp27)) {
                                            strLine16 = strLine16 + app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                        }
                                    } else {
                                        strLine16 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                    }
                                    String strTemp28 = strTemp5 + strLine16;
                                    String strLine17 = attSketch2.get("SCALE").toString();
                                    if (!strLine17.equals("")) {
                                        strTemp28 = strTemp28 + ("，" + app.getResources().getString(R.string.RGMAP_PROMPT_SCALE) + "1：" + strLine17);
                                    }
                                    writeContent(strTemp28, false);
                                    String SketchSpath2 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".GPJ";
                                    File vFile2 = new File(SketchSpath2);
                                    if (vFile2.exists()) {
                                        AoMap m_Map12 = AoMap.openMap(SketchSpath2);
                                        Bitmap bitmap2 = Bitmap.createBitmap(800, HttpStatus.SC_MULTIPLE_CHOICES, Bitmap.Config.ARGB_8888);
                                        bitmap2.eraseColor(-1);
                                        AoRender render2 = new AoRender(bitmap2);
                                        render2.setRenderSize(800, HttpStatus.SC_MULTIPLE_CHOICES);
                                        GeoRect rect2 = new GeoRect();
                                        m_Map12.getMapRect(rect2);
                                        double sx2 = 800.0d / (rect2.getXmax() - rect2.getXmin());
                                        double sy2 = 300.0d / (rect2.getYmax() - rect2.getYmin());
                                        double scale2 = sx2 < sy2 ? sx2 : sy2;
                                        render2.setDrawParam(rect2.getXmin(), rect2.getYmin(), scale2);
                                        render2.drawMap(m_Map12);
                                        String SketchIMGpath2 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".jpg";
                                        render2.saveBitmap(bitmap2, SketchIMGpath2, Bitmap.CompressFormat.JPEG, 75);
                                        render2.deleteRender();
                                        m_Map12.closeMap();
                                        this.output.write("<p>".getBytes());
                                        writePicture(SketchIMGpath2);
                                        this.output.write("</p>".getBytes());
                                    }
                                    String SketchSpath12 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".PNG";
                                    File vFile12 = new File(SketchSpath12);
                                    if (vFile12.exists()) {
                                        this.output.write("<p>".getBytes());
                                        writePicture(SketchSpath12);
                                        this.output.write("</p>".getBytes());
                                    }
                                    this.output.write("</p>".getBytes());
                                    break;
                                case 3:
                                    Map<String, Object> attPhoto2 = photoArea.getNamedAttributeStrings(lid);
                                    this.output.write("<p>".getBytes());
                                    if (blnezreo) {
                                        strTemp4 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                    } else {
                                        strTemp4 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                    }
                                    String strLine18 = attPhoto2.get("DESCRIBE").toString();
                                    String strTemp112 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCPHOTO);
                                    boolean b = strLine18.contains(strTemp112);
                                    if (!b) {
                                        strLine18 = strLine18 + strTemp112;
                                    }
                                    String strTemp29 = strTemp4 + strLine18;
                                    String strLine19 = attPhoto2.get("AMOUNT").toString();
                                    if (!strLine19.equals("")) {
                                        strTemp29 = strTemp29 + strLine19 + app.getResources().getString(R.string.RGMAP_PROMPT_PAGE);
                                    }
                                    String strLine20 = attPhoto2.get("DIRECTION").toString();
                                    if (strLine20.equals("")) {
                                        strLine20 = "?";
                                    }
                                    writeContent(strTemp29 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_DIRECTION) + ":" + strLine20, false);
                                    String strkeyword2 = attGPoint.get("GEOPOINT").toString() + "_" + attPhoto2.get("CODE").toString();
                                    String imgpath2 = m_strFile + File.separator + "images/";
                                    File vFilepath2 = new File(imgpath2);
                                    File[] files2 = vFilepath2.listFiles();
                                    if (files2 != null && files2.length > 0) {
                                        for (File file4 : files2) {
                                            if (file4.isFile()) {
                                                String strname4 = file4.getName();
                                                if ((strname4.indexOf(strkeyword2) > -1 || strname4.indexOf(strkeyword2.toUpperCase()) > -1) && (strname4.indexOf("jpg") > -1 || strname4.indexOf("JPG") > -1)) {
                                                    this.output.write("<p>".getBytes());
                                                    String imgfILEpath4 = m_strFile + File.separator + "images/" + strname4;
                                                    writePicture(imgfILEpath4);
                                                    this.output.write("</p>".getBytes());
                                                }
                                            }
                                        }
                                    }
                                    if (files2 != null && files2.length > 0) {
                                        for (File file5 : files2) {
                                            if (file5.isFile()) {
                                                String strname5 = file5.getName();
                                                if ((strname5.indexOf(strkeyword2) > -1 || strname5.indexOf(strkeyword2.toUpperCase()) > -1) && (strname5.indexOf("mp4") > -1 || strname5.indexOf("MP4") > -1)) {
                                                    this.output.write("<p>".getBytes());
                                                    String imgfILEpath5 = m_strFile + File.separator + "images/" + strname5;
                                                    writeContent(strname5, false);
                                                    writeVideo(imgfILEpath5, this.palypath);
                                                    this.output.write("</p>".getBytes());
                                                }
                                            }
                                        }
                                    }
                                    if (files2 != null && files2.length > 0) {
                                        for (File file6 : files2) {
                                            if (file6.isFile()) {
                                                String strname6 = file6.getName();
                                                if ((strname6.indexOf(strkeyword2) > -1 || strname6.indexOf(strkeyword2.toUpperCase()) > -1) && (strname6.indexOf("wav") > -1 || strname6.indexOf("WAV") > -1)) {
                                                    this.output.write("<p>".getBytes());
                                                    String imgfILEpath6 = m_strFile + File.separator + "images/" + strname6;
                                                    writeContent(strname6, false);
                                                    writeVoice(imgfILEpath6, this.palypath1);
                                                    this.output.write("</p>".getBytes());
                                                }
                                            }
                                        }
                                    }
                                    this.output.write("</p>".getBytes());
                                    break;
                                case 4:
                                    Map<String, Object> attFossil2 = fossilArea.getNamedAttributeStrings(lid);
                                    this.output.write("<p>".getBytes());
                                    if (blnezreo) {
                                        strTemp2 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                    } else {
                                        strTemp2 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                    }
                                    String strLine21 = attFossil2.get("NAME").toString();
                                    if (!strLine21.equals("")) {
                                        strTemp3 = strTemp2 + strLine21 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                                    } else {
                                        strTemp3 = strTemp2 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                                    }
                                    String strLine22 = attFossil2.get("CODE").toString();
                                    if (!strLine22.equals("")) {
                                        strTemp3 = strTemp3 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine22;
                                    }
                                    String strLine23 = attFossil2.get("PNAME").toString();
                                    if (!strLine23.equals("")) {
                                        strTemp3 = strTemp3 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILELAYE) + ":" + strLine23;
                                    }
                                    writeContent(strTemp3, false);
                                    this.output.write("</p>".getBytes());
                                    break;
                                case 5:
                                    this.output.write("<p>".getBytes());
                                    Map<String, Object> attSample2 = sampleArea.getNamedAttributeStrings(lid);
                                    if (blnezreo) {
                                        strTemp = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                    } else {
                                        strTemp = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                    }
                                    String strLine24 = attSample2.get("TYPE").toString();
                                    if (!strLine24.equals("")) {
                                        strTemp = strTemp + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLE) + "(" + strLine24 + ")";
                                    }
                                    String strLine25 = attSample2.get("CODE").toString();
                                    if (!strLine25.equals("")) {
                                        strTemp = strTemp + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine25;
                                    }
                                    String strLine26 = attSample2.get("NAME").toString();
                                    if (!strLine26.equals("")) {
                                        strTemp = strTemp + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLELAYE) + ":" + strLine26;
                                    }
                                    writeContent(strTemp, false);
                                    this.output.write("</p>".getBytes());
                                    break;
                            }
                        }
                        dRouteLen += getpntLegth(geoLine);
                    }
                }
            }
            attitudeArea.SaveArea();
            sketchArea.SaveArea();
            photoArea.SaveArea();
            fossilArea.SaveArea();
            sampleArea.SaveArea();
            boundaryArea.SaveArea();
            routingArea.SaveArea();
            gpointArea.SaveArea();
            this.output.write("</body></html>".getBytes());
            this.output.close();
        } catch (Exception e) {
            System.out.println("ReadAndWrite Exception");
        }
        return this.htmlPath;
    }

    public String writeHtml(WorkArea routeArea) {
        boolean blnezreo;
        String strTemp;
        String strTemp2;
        String strTemp3;
        String strTemp4;
        String strTemp5;
        String strTemp6;
        String strTemp7;
        RGMapApplication app = RGMapApplication.getCurrentApp();
        double unit = 1.0d;
        boolean bRouteSum = false;
        String RouteDesc = "";
        try {
            Map<String, Object> attRoute = routeArea.getNamedAttributeStrings(1);
            this.routeCode = (String) attRoute.get("ROUTECODE");
            new WorkSpace();
            makeFile();
            this.myFile = new File(this.htmlPath);
            this.output = new FileOutputStream(this.myFile);
            this.output.write("<html><body>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp8 = app.getResources().getString(R.string.REM_ROUTECODE);
            String strTempValue = attRoute.get("ROUTECODE").toString();
            if (strTempValue.equals("")) {
                strTempValue = "?";
            }
            writeContent(strTemp8 + strTempValue, true);
            String Routecode = attRoute.get("ROUTECODE").toString();
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp9 = app.getResources().getString(R.string.RGMAP_PROMPT_ROUTEDESC);
            String strTempValue2 = attRoute.get("DESCRIBE").toString();
            if (strTempValue2.equals("")) {
                strTempValue2 = "?";
            }
            writeContent(strTemp9 + strTempValue2, true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp10 = app.getResources().getString(R.string.RGMAP_PROMPT_ROUTETASK);
            String strTempValue3 = attRoute.get("TASK").toString();
            if (strTempValue3.equals("")) {
                strTempValue3 = "?";
            }
            writeContent(strTemp10 + strTempValue3, true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp11 = app.getResources().getString(R.string.REM_MAPCODE);
            String strTempValue4 = attRoute.get(AttributeExtActivity.PARAM_STRING_MAPCODE).toString();
            if (strTempValue4.equals("")) {
                strTempValue4 = "?";
            }
            writeContent(strTemp11 + strTempValue4, true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp12 = app.getResources().getString(R.string.RGMAP_PROMPT_RECORDER);
            String strTempValue5 = attRoute.get("RECORDER").toString();
            if (strTempValue5.equals("")) {
                strTempValue5 = "?";
            }
            writeContent(strTemp12 + strTempValue5, true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp13 = app.getResources().getString(R.string.RGMAP_PROMPT_FLLOWER);
            String strTempValue6 = attRoute.get("CAMERAMAN").toString();
            if (strTempValue6.equals("")) {
                strTempValue6 = "?";
            }
            writeContent(strTemp13 + strTempValue6, true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp14 = app.getResources().getString(R.string.orecheck_ore_mine_date);
            String strTempValue7 = attRoute.get(AttributeExtActivity.PARAM_STRING_DATE).toString();
            if (strTempValue7.equals("")) {
                strTempValue7 = "?";
            }
            writeContent(strTemp14 + strTempValue7, true);
            this.output.write("</p>".getBytes());
            GdbFieldInfo[] flds = routeArea.getAttributeSchemaManager().getFieldInfos();
            for (GdbFieldInfo info : flds) {
                if (info.getFieldname().compareToIgnoreCase("geo_desc") == 0) {
                    bRouteSum = true;
                }
            }
            if (bRouteSum) {
                RouteDesc = attRoute.get("geo_desc").toString();
            }
            this.output.write("<br>".getBytes());
            this.output.write("</br>".getBytes());
            short index = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strGPoint);
            WorkArea gpointArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index);
            WorkAreaParams mapInfo = gpointArea.getWorkAreaParamsClone();
            if (mapInfo.getCoordType() != WorkAreaParams.LengthType.Meter) {
                unit = 1000.0d;
            }
            int[] gpointID = gpointArea.getGeometryIdListByAtt("ROUTECODE = '" + this.routeCode + "'");
            for (int iGPoint = 0; iGPoint < gpointID.length; iGPoint++) {
                Map<String, Object> attGPoint = gpointArea.getNamedAttributeStrings(gpointID[iGPoint]);
                GeoPoint GeoPointpnt = (GeoPoint) gpointArea.getGeometry(gpointID[iGPoint]);
                this.output.write("<p>".getBytes());
                String strTemp15 = app.getResources().getString(R.string.gpo_geopoint);
                String strTempValue8 = attGPoint.get("GEOPOINT").toString();
                if (strTempValue8.equals("")) {
                    strTempValue8 = "?";
                }
                writeContent(strTemp15 + strTempValue8, true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp16 = app.getResources().getString(R.string.gpo_xx);
                writeContent(strTemp16 + attGPoint.get(AttributeExtActivity.PARAM_STRING_XX), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp17 = app.getResources().getString(R.string.gpo_yy);
                writeContent(strTemp17 + attGPoint.get(AttributeExtActivity.PARAM_STRING_YY), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp18 = app.getResources().getString(R.string.gpo_outcrop);
                String strTempValue9 = attGPoint.get("OUTCROP").toString();
                if (strTempValue9.equals("")) {
                    strTempValue9 = "?";
                }
                writeContent(strTemp18 + strTempValue9, true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp19 = app.getResources().getString(R.string.gpo_type);
                if (attGPoint.get("TYPE").toString().equals("")) {
                }
                writeContent(strTemp19 + attGPoint.get("TYPE"), true);
                Boolean bhaqsyx = false;
                String strTempValue10 = attGPoint.get("STRAPHA").toString();
                if (!strTempValue10.equals("")) {
                    writeContent("(" + strTempValue10, true);
                    bhaqsyx = true;
                }
                String strTempValue11 = attGPoint.get("STRAPHB").toString();
                if (!strTempValue11.equals("")) {
                    writeContent("And" + strTempValue11, true);
                }
                String strTempValue12 = attGPoint.get("STRAPHC").toString();
                if (!strTempValue12.equals("")) {
                    writeContent("And " + strTempValue12, true);
                }
                if (bhaqsyx.booleanValue()) {
                    writeContent(")", true);
                }
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp20 = app.getResources().getString(R.string.gpo_geomorph);
                String strTempValue13 = attGPoint.get("GEOMORPH").toString();
                if (strTempValue13.equals("")) {
                    strTempValue13 = "?";
                }
                writeContent(strTemp20 + strTempValue13, true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp21 = app.getResources().getString(R.string.gpo_weathing);
                String strTempValue14 = attGPoint.get("WEATHING").toString();
                if (strTempValue14.equals("")) {
                    strTempValue14 = "?";
                }
                writeContent(strTemp21 + strTempValue14, true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                writeContent(attGPoint.get("geo_desc").toString(), true);
                this.output.write("</p>".getBytes());
                String geopointmulmediaString = strTempValue8 + "_p";
                String pimgpath = m_strFile + File.separator + "images/";
                File vpFilepath = new File(pimgpath);
                File[] pfiles = vpFilepath.listFiles();
                if (pfiles != null && pfiles.length > 0) {
                    for (File file : pfiles) {
                        if (file.isFile()) {
                            String strname = file.getName();
                            if ((strname.indexOf(geopointmulmediaString) > -1 || strname.indexOf(geopointmulmediaString.toUpperCase()) > -1) && strname.indexOf("jpg") > -1) {
                                this.output.write("<p>".getBytes());
                                String imgfILEpath = m_strFile + File.separator + "images/" + strname;
                                writePicture(imgfILEpath);
                                this.output.write("</p>".getBytes());
                            }
                        }
                    }
                }
                if (pfiles != null && pfiles.length > 0) {
                    for (File file2 : pfiles) {
                        if (file2.isFile()) {
                            String strname2 = file2.getName();
                            if ((strname2.indexOf(geopointmulmediaString) > -1 || strname2.indexOf(geopointmulmediaString.toUpperCase()) > -1) && (strname2.indexOf("mp4") > -1 || strname2.indexOf("MP4") > -1)) {
                                this.output.write("<p>".getBytes());
                                String imgfILEpath2 = m_strFile + File.separator + "images/" + strname2;
                                writeContent(strname2, false);
                                writeVideo(imgfILEpath2, this.palypath);
                                this.output.write("</p>".getBytes());
                            }
                        }
                    }
                }
                if (pfiles != null && pfiles.length > 0) {
                    for (File file3 : pfiles) {
                        if (file3.isFile()) {
                            String strname3 = file3.getName();
                            if ((strname3.indexOf(geopointmulmediaString) > -1 || strname3.indexOf(geopointmulmediaString.toUpperCase()) > -1) && (strname3.indexOf("wav") > -1 || strname3.indexOf("WAV") > -1)) {
                                this.output.write("<p>".getBytes());
                                String imgfILEpath3 = m_strFile + File.separator + "images/" + strname3;
                                writeContent(strname3, false);
                                writeVoice(imgfILEpath3, this.palypath1);
                                this.output.write("</p>".getBytes());
                            }
                        }
                    }
                }
                short index2 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strAttitude);
                WorkArea attitudeArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index2);
                short index3 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSketch);
                WorkArea sketchArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index3);
                short index4 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strPhoto);
                WorkArea photoArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index4);
                short index5 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strFossil);
                WorkArea fossilArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index5);
                short index6 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strSample);
                WorkArea sampleArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index6);
                short index7 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strBoundary);
                WorkArea boundaryArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index7);
                short index8 = AoRGMapActivity.getCurrentMap().getItemIndex(PRBAreas.m_strRouting);
                WorkArea routingArea = AoRGMapActivity.getCurrentMap().getItemWorkAreaMustEdit(index8);
                String sql = "GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')";
                for (int i : attitudeArea.getGeometryIdListByAtt(sql)) {
                    Map<String, Object> attAttitude = attitudeArea.getNamedAttributeStrings(i);
                    this.output.write("<p>".getBytes());
                    String strTemp22 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    if (!attAttitude.get("TYPE").toString().equals("")) {
                        strTemp22 = strTemp22 + attAttitude.get("TYPE") + app.getResources().getString(R.string.menu_main_prb_att) + ":";
                    }
                    String strLine = attAttitude.get("DIP").toString();
                    if (!strLine.equals("")) {
                        strTemp22 = strTemp22 + strLine + "∠" + attAttitude.get("DIP_ANG") + ";";
                    }
                    writeContent(strTemp22, false);
                    this.output.write("</p>".getBytes());
                }
                for (int i2 : photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attPhoto = photoArea.getNamedAttributeStrings(i2);
                    this.output.write("<p>".getBytes());
                    String strTemp23 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    String strLine2 = attPhoto.get("DESCRIBE").toString();
                    String strTemp1 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCPHOTO);
                    if (!strLine2.contains(strTemp1)) {
                        strLine2 = strLine2 + strTemp1;
                    }
                    String strTemp24 = strTemp23 + strLine2;
                    String strLine3 = attPhoto.get("AMOUNT").toString();
                    if (!strLine3.equals("")) {
                        strTemp24 = strTemp24 + strLine3 + app.getResources().getString(R.string.RGMAP_PROMPT_PAGE);
                    }
                    String strLine4 = attPhoto.get("DIRECTION").toString();
                    if (strLine4.equals("")) {
                        strLine4 = "?";
                    }
                    writeContent(strTemp24 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_DIRECTION) + ":" + strLine4 + ",", false);
                    String strkeyword = attGPoint.get("GEOPOINT").toString() + "_" + attPhoto.get("CODE").toString();
                    String imgpath = m_strFile + File.separator + "images/";
                    File vFilepath = new File(imgpath);
                    File[] files = vFilepath.listFiles();
                    if (files != null && files.length > 0) {
                        for (File file4 : files) {
                            if (file4.isFile()) {
                                String strname4 = file4.getName();
                                if ((strname4.indexOf(strkeyword) > -1 || strname4.indexOf(strkeyword.toUpperCase()) > -1) && strname4.indexOf("jpg") > -1) {
                                    this.output.write("<p>".getBytes());
                                    String imgfILEpath4 = m_strFile + File.separator + "images/" + strname4;
                                    writePicture(imgfILEpath4);
                                    this.output.write("</p>".getBytes());
                                }
                            }
                        }
                    }
                    if (files != null && files.length > 0) {
                        for (File file5 : files) {
                            if (file5.isFile()) {
                                String strname5 = file5.getName();
                                if ((strname5.indexOf(strkeyword) > -1 || strname5.indexOf(strkeyword.toUpperCase()) > -1) && (strname5.indexOf("mp4") > -1 || strname5.indexOf("MP4") > -1)) {
                                    this.output.write("<p>".getBytes());
                                    String imgfILEpath5 = m_strFile + File.separator + "images/" + strname5;
                                    writeContent(strname5, false);
                                    writeVideo(imgfILEpath5, this.palypath);
                                    this.output.write("</p>".getBytes());
                                }
                            }
                        }
                    }
                    if (files != null && files.length > 0) {
                        for (File file6 : files) {
                            if (file6.isFile()) {
                                String strname6 = file6.getName();
                                if ((strname6.indexOf(strkeyword) > -1 || strname6.indexOf(strkeyword.toUpperCase()) > -1) && (strname6.indexOf("wav") > -1 || strname6.indexOf("WAV") > -1)) {
                                    this.output.write("<p>".getBytes());
                                    String imgfILEpath6 = m_strFile + File.separator + "images/" + strname6;
                                    writeContent(strname6, false);
                                    writeVoice(imgfILEpath6, this.palypath1);
                                    this.output.write("</p>".getBytes());
                                }
                            }
                        }
                    }
                    this.output.write("</p>".getBytes());
                }
                for (int i3 : sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attSketch = sketchArea.getNamedAttributeStrings(i3);
                    this.output.write("<p>".getBytes());
                    String strTemp25 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    String strLine5 = attSketch.get("TITLE").toString();
                    if (!strLine5.equals("")) {
                        String strTemp110 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                        String strTemp26 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCSEC);
                        if (!strLine5.contains(strTemp110) && !strLine5.contains(strTemp26)) {
                            strLine5 = strLine5 + strTemp110;
                        }
                    } else {
                        strLine5 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                    }
                    String strTemp27 = strTemp25 + strLine5;
                    String strLine6 = attSketch.get("SCALE").toString();
                    if (!strLine6.equals("")) {
                        strTemp27 = strTemp27 + ("," + app.getResources().getString(R.string.RGMAP_PROMPT_SCALE) + "：1：" + strLine6);
                    }
                    writeContent(strTemp27, false);
                    String SketchSpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".GPJ";
                    File vFile = new File(SketchSpath);
                    if (vFile.exists()) {
                        AoMap m_Map1 = AoMap.openMap(SketchSpath);
                        Bitmap bitmap = Bitmap.createBitmap(800, HttpStatus.SC_MULTIPLE_CHOICES, Bitmap.Config.ARGB_8888);
                        bitmap.eraseColor(-1);
                        AoRender render = new AoRender(bitmap);
                        render.setRenderSize(800, HttpStatus.SC_MULTIPLE_CHOICES);
                        GeoRect rect = new GeoRect();
                        m_Map1.getMapRect(rect);
                        double sx = 800.0d / (rect.getXmax() - rect.getXmin());
                        double sy = 300.0d / (rect.getYmax() - rect.getYmin());
                        double scale = sx < sy ? sx : sy;
                        render.setDrawParam(rect.getXmin(), rect.getYmin(), scale);
                        render.drawMap(m_Map1);
                        String SketchIMGpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + "_vec.jpg";
                        render.saveBitmap(bitmap, SketchIMGpath, Bitmap.CompressFormat.JPEG, 75);
                        render.deleteRender();
                        m_Map1.closeMap();
                        this.output.write("<p>".getBytes());
                        writePicture(SketchIMGpath);
                        this.output.write("</p>".getBytes());
                    }
                    String SketchSpath1 = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".jpg";
                    File vFile1 = new File(SketchSpath1);
                    if (vFile1.exists()) {
                        this.output.write("<p>".getBytes());
                        writePicture(SketchSpath1);
                        this.output.write("</p>".getBytes());
                    }
                    this.output.write("</p>".getBytes());
                }
                for (int i4 : fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attFossil = fossilArea.getNamedAttributeStrings(i4);
                    this.output.write("<p>".getBytes());
                    String strTemp28 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    String strLine7 = attFossil.get("NAME").toString();
                    if (!strLine7.equals("")) {
                        strTemp7 = strTemp28 + strLine7 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                    } else {
                        strTemp7 = strTemp28 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                    }
                    String strLine8 = attFossil.get("CODE").toString();
                    if (!strLine8.equals("")) {
                        strTemp7 = strTemp7 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine8;
                    }
                    String strLine9 = attFossil.get("PNAME").toString();
                    if (!strLine9.equals("")) {
                        strTemp7 = strTemp7 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILELAYE) + ":" + strLine9;
                    }
                    writeContent(strTemp7, false);
                    this.output.write("</p>".getBytes());
                }
                for (int i5 : sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attSample = sampleArea.getNamedAttributeStrings(i5);
                    this.output.write("<p>".getBytes());
                    String strTemp29 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    String strLine10 = attSample.get("TYPE").toString();
                    if (!strLine10.equals("")) {
                        strTemp29 = strTemp29 + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLE) + "(" + strLine10 + ")";
                    }
                    String strLine11 = attSample.get("CODE").toString();
                    if (!strLine11.equals("")) {
                        strTemp29 = strTemp29 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine11;
                    }
                    String strLine12 = attSample.get("NAME").toString();
                    if (!strLine12.equals("")) {
                        strTemp29 = strTemp29 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLELAYE) + ":" + strLine12;
                    }
                    writeContent(strTemp29, false);
                    this.output.write("</p>".getBytes());
                }
                int[] boundaryID = boundaryArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')");
                for (int i6 : boundaryID) {
                    Map<String, Object> attboundary = boundaryArea.getNamedAttributeStrings(i6);
                    this.output.write("<p>".getBytes());
                    String strTemp30 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                    String strLine13 = attboundary.get("SUBPOINT").toString();
                    if (!strLine13.equals("")) {
                        strTemp30 = strTemp30 + strLine13 + ":";
                    }
                    String strLine14 = attboundary.get("geo_desc").toString();
                    if (!strLine14.equals("")) {
                        strTemp30 = strTemp30 + strLine14;
                    }
                    writeContent(strTemp30, false);
                    this.output.write("</p>".getBytes());
                }
                double dRouteLen = 0.0d;
                int[] routingID = routingArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' ");
                for (int iRouting = 0; iRouting < routingID.length; iRouting++) {
                    Map<String, Object> attRouting = routingArea.getNamedAttributeStrings(routingID[iRouting]);
                    this.mapsort.reset();
                    GeoLine geoLine = (GeoLine) routingArea.getGeometry(routingID[iRouting]);
                    getpntLegth(geoLine);
                    String StrTitle = attRouting.get("geo_desc").toString();
                    String routecode = attRouting.get("R_CODE").toString();
                    String strDesc = StrTitle;
                    String strm = app.getResources().getString(R.string.RGMAP_PROMPT_DESCM);
                    if (StrTitle.indexOf(routecode) == 0) {
                        int idx = StrTitle.indexOf(44);
                        strDesc = StrTitle.substring(StrTitle.indexOf(strm, idx) + 1);
                    }
                    int[] attitudeID = attitudeArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iAttitude = 0; iAttitude < attitudeID.length; iAttitude++) {
                        GeoPoint pnt = (GeoPoint) attitudeArea.getGeometry(attitudeID[iAttitude]);
                        if (pnt != null) {
                            this.mapsort.insert(((dRouteLen + GetPntToLin(pnt, geoLine)) * mapInfo.rate) / unit, 1, attitudeID[iAttitude], getpntAngle(GeoPointpnt, pnt));
                        }
                    }
                    int[] sketchID = sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iSketch = 0; iSketch < sketchID.length; iSketch++) {
                        GeoPoint pnt2 = (GeoPoint) sketchArea.getGeometry(sketchID[iSketch]);
                        if (pnt2 != null) {
                            this.mapsort.insert(((dRouteLen + GetPntToLin(pnt2, geoLine)) * mapInfo.rate) / unit, 2, sketchID[iSketch], getpntAngle(GeoPointpnt, pnt2));
                        }
                    }
                    int[] photoID = photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iPhoto = 0; iPhoto < photoID.length; iPhoto++) {
                        GeoPoint pnt3 = (GeoPoint) photoArea.getGeometry(photoID[iPhoto]);
                        if (pnt3 != null) {
                            this.mapsort.insert(((dRouteLen + GetPntToLin(pnt3, geoLine)) * mapInfo.rate) / unit, 3, photoID[iPhoto], getpntAngle(GeoPointpnt, pnt3));
                        }
                    }
                    int[] fossilID = fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iFossil = 0; iFossil < fossilID.length; iFossil++) {
                        GeoPoint pnt4 = (GeoPoint) fossilArea.getGeometry(fossilID[iFossil]);
                        if (pnt4 != null) {
                            this.mapsort.insert(((dRouteLen + GetPntToLin(pnt4, geoLine)) * mapInfo.rate) / unit, 4, fossilID[iFossil], getpntAngle(GeoPointpnt, pnt4));
                        }
                    }
                    int[] sampleID = sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iSample = 0; iSample < sampleID.length; iSample++) {
                        GeoPoint pnt5 = (GeoPoint) sampleArea.getGeometry(sampleID[iSample]);
                        if (pnt5 != null) {
                            this.mapsort.insert(((dRouteLen + GetPntToLin(pnt5, geoLine)) * mapInfo.rate) / unit, 5, sampleID[iSample], getpntAngle(GeoPointpnt, pnt5));
                        }
                    }
                    int itrans = 0;
                    this.output.write("<p>".getBytes());
                    String strTemp31 = app.getResources().getString(R.string.RGMAP_PROMPT_PRDESC);
                    writeContent(strTemp31, true);
                    this.output.write("</p>".getBytes());
                    if (iRouting == 0) {
                        String sAng = attRouting.get("DIRECTION").toString();
                        String SZlen = app.getResources().getString(R.string.RGMAP_PROMPT_POINT) + app.getResources().getString(R.string.RGMAP_PROMPT_TO) + getpntAngle(Double.valueOf(sAng).doubleValue()) + attRouting.get("DISTANCE").toString() + "m";
                        this.output.write("<p>".getBytes());
                        writeContent(SZlen, false);
                        this.output.write("</p>".getBytes());
                        this.output.write("<p>".getBytes());
                        writeContent(strDesc, false);
                        this.output.write("</p>".getBytes());
                    } else {
                        String sAng2 = attRouting.get("DIRECTION").toString();
                        String sAng3 = getpntAngle(Double.valueOf(sAng2).doubleValue());
                        double dlenTemp = (mapInfo.rate * dRouteLen) / unit;
                        String SZlen2 = String.format("%.0fm", Double.valueOf(dlenTemp)) + app.getResources().getString(R.string.RGMAP_PROMPT_TO) + sAng3 + attRouting.get("DISTANCE").toString() + "m";
                        this.output.write("<p>".getBytes());
                        writeContent(SZlen2, false);
                        this.output.write("</p>".getBytes());
                        this.output.write("<p>".getBytes());
                        writeContent(strDesc, false);
                        this.output.write("</p>".getBytes());
                    }
                    String[] strdataArr = this.mapsort.getStr();
                    for (int idex = 0; idex < this.mapsort.getCount(); idex++) {
                        String[] dataArr = strdataArr[idex].split(",");
                        int itype = Integer.valueOf(dataArr[1]).intValue();
                        int lid = Integer.valueOf(dataArr[2]).intValue();
                        String szAng = dataArr[3];
                        double dlensTemp = Double.valueOf(dataArr[0]).doubleValue();
                        String dlen = String.format("%.0fm", Double.valueOf(dlensTemp));
                        if (dlen.equals("0m")) {
                            blnezreo = true;
                        } else {
                            blnezreo = false;
                            itrans++;
                        }
                        switch (itype) {
                            case 1:
                                Map<String, Object> attAttitude2 = attitudeArea.getNamedAttributeStrings(lid);
                                this.output.write("<p>".getBytes());
                                if (blnezreo) {
                                    strTemp6 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                } else {
                                    strTemp6 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                }
                                String strTemp32 = strTemp6 + attAttitude2.get("TYPE") + app.getResources().getString(R.string.menu_main_prb_att) + ":";
                                String strLine15 = attAttitude2.get("DIP").toString();
                                if (!strLine15.equals("")) {
                                    strTemp32 = strTemp32 + strLine15 + "∠" + attAttitude2.get("DIP_ANG") + ";";
                                }
                                writeContent(strTemp32, false);
                                this.output.write("</p>".getBytes());
                                break;
                            case 2:
                                Map<String, Object> attSketch2 = sketchArea.getNamedAttributeStrings(lid);
                                this.output.write("<p>".getBytes());
                                if (blnezreo) {
                                    strTemp5 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                } else {
                                    strTemp5 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                }
                                String strLine16 = attSketch2.get("TITLE").toString();
                                if (!strLine16.equals("")) {
                                    String strTemp111 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                    String strTemp210 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCSEC);
                                    if (!strLine16.contains(strTemp111) && !strLine16.contains(strTemp210)) {
                                        strLine16 = strLine16 + app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                    }
                                } else {
                                    strLine16 = app.getResources().getString(R.string.RGMAP_PROMPT_SKETCH);
                                }
                                String strTemp33 = strTemp5 + strLine16;
                                String strLine17 = attSketch2.get("SCALE").toString();
                                if (!strLine17.equals("")) {
                                    strTemp33 = strTemp33 + ("，" + app.getResources().getString(R.string.RGMAP_PROMPT_SCALE) + "1：" + strLine17);
                                }
                                writeContent(strTemp33, false);
                                String SketchSpath2 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".GPJ";
                                File vFile2 = new File(SketchSpath2);
                                if (vFile2.exists()) {
                                    AoMap m_Map12 = AoMap.openMap(SketchSpath2);
                                    Bitmap bitmap2 = Bitmap.createBitmap(800, HttpStatus.SC_MULTIPLE_CHOICES, Bitmap.Config.ARGB_8888);
                                    bitmap2.eraseColor(-1);
                                    AoRender render2 = new AoRender(bitmap2);
                                    render2.setRenderSize(800, HttpStatus.SC_MULTIPLE_CHOICES);
                                    GeoRect rect2 = new GeoRect();
                                    m_Map12.getMapRect(rect2);
                                    double sx2 = 800.0d / (rect2.getXmax() - rect2.getXmin());
                                    double sy2 = 300.0d / (rect2.getYmax() - rect2.getYmin());
                                    double scale2 = sx2 < sy2 ? sx2 : sy2;
                                    render2.setDrawParam(rect2.getXmin(), rect2.getYmin(), scale2);
                                    render2.drawMap(m_Map12);
                                    String SketchIMGpath2 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".jpg";
                                    render2.saveBitmap(bitmap2, SketchIMGpath2, Bitmap.CompressFormat.JPEG, 75);
                                    render2.deleteRender();
                                    m_Map12.closeMap();
                                    this.output.write("<p>".getBytes());
                                    writePicture(SketchIMGpath2);
                                    this.output.write("</p>".getBytes());
                                }
                                String SketchSpath12 = m_strFile + File.separator + "sketch/" + attSketch2.get("GEOPOINT") + "_" + attSketch2.get("CODE") + ".PNG";
                                File vFile12 = new File(SketchSpath12);
                                if (vFile12.exists()) {
                                    this.output.write("<p>".getBytes());
                                    writePicture(SketchSpath12);
                                    this.output.write("</p>".getBytes());
                                }
                                this.output.write("</p>".getBytes());
                                break;
                            case 3:
                                Map<String, Object> attPhoto2 = photoArea.getNamedAttributeStrings(lid);
                                this.output.write("<p>".getBytes());
                                if (blnezreo) {
                                    strTemp4 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                } else {
                                    strTemp4 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                }
                                String strLine18 = attPhoto2.get("DESCRIBE").toString();
                                String strTemp112 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCPHOTO);
                                boolean b = strLine18.contains(strTemp112);
                                if (!b) {
                                    strLine18 = strLine18 + strTemp112;
                                }
                                String strTemp34 = strTemp4 + strLine18;
                                String strLine19 = attPhoto2.get("AMOUNT").toString();
                                if (!strLine19.equals("")) {
                                    strTemp34 = strTemp34 + strLine19 + app.getResources().getString(R.string.RGMAP_PROMPT_PAGE);
                                }
                                String strLine20 = attPhoto2.get("DIRECTION").toString();
                                if (strLine20.equals("")) {
                                    strLine20 = "?";
                                }
                                writeContent(strTemp34 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_DIRECTION) + ":" + strLine20, false);
                                String strkeyword2 = attGPoint.get("GEOPOINT").toString() + "_" + attPhoto2.get("CODE").toString();
                                String imgpath2 = m_strFile + File.separator + "images/";
                                File vFilepath2 = new File(imgpath2);
                                File[] files2 = vFilepath2.listFiles();
                                if (files2 != null && files2.length > 0) {
                                    for (File file7 : files2) {
                                        if (file7.isFile()) {
                                            String strname7 = file7.getName();
                                            if ((strname7.indexOf(strkeyword2) > -1 || strname7.indexOf(strkeyword2.toUpperCase()) > -1) && (strname7.indexOf("jpg") > -1 || strname7.indexOf("JPG") > -1)) {
                                                this.output.write("<p>".getBytes());
                                                String imgfILEpath7 = m_strFile + File.separator + "images/" + strname7;
                                                writePicture(imgfILEpath7);
                                                this.output.write("</p>".getBytes());
                                            }
                                        }
                                    }
                                }
                                if (files2 != null && files2.length > 0) {
                                    for (File file8 : files2) {
                                        if (file8.isFile()) {
                                            String strname8 = file8.getName();
                                            if ((strname8.indexOf(strkeyword2) > -1 || strname8.indexOf(strkeyword2.toUpperCase()) > -1) && (strname8.indexOf("mp4") > -1 || strname8.indexOf("MP4") > -1)) {
                                                this.output.write("<p>".getBytes());
                                                String imgfILEpath8 = m_strFile + File.separator + "images/" + strname8;
                                                writeContent(strname8, false);
                                                writeVideo(imgfILEpath8, this.palypath);
                                                this.output.write("</p>".getBytes());
                                            }
                                        }
                                    }
                                }
                                if (files2 != null && files2.length > 0) {
                                    for (File file9 : files2) {
                                        if (file9.isFile()) {
                                            String strname9 = file9.getName();
                                            if ((strname9.indexOf(strkeyword2) > -1 || strname9.indexOf(strkeyword2.toUpperCase()) > -1) && (strname9.indexOf("wav") > -1 || strname9.indexOf("WAV") > -1)) {
                                                this.output.write("<p>".getBytes());
                                                String imgfILEpath9 = m_strFile + File.separator + "images/" + strname9;
                                                writeContent(strname9, false);
                                                writeVoice(imgfILEpath9, this.palypath1);
                                                this.output.write("</p>".getBytes());
                                            }
                                        }
                                    }
                                }
                                this.output.write("</p>".getBytes());
                                break;
                            case 4:
                                Map<String, Object> attFossil2 = fossilArea.getNamedAttributeStrings(lid);
                                this.output.write("<p>".getBytes());
                                if (blnezreo) {
                                    strTemp2 = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                } else {
                                    strTemp2 = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                }
                                String strLine21 = attFossil2.get("NAME").toString();
                                if (!strLine21.equals("")) {
                                    strTemp3 = strTemp2 + strLine21 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                                } else {
                                    strTemp3 = strTemp2 + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILE);
                                }
                                String strLine22 = attFossil2.get("CODE").toString();
                                if (!strLine22.equals("")) {
                                    strTemp3 = strTemp3 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine22;
                                }
                                String strLine23 = attFossil2.get("PNAME").toString();
                                if (!strLine23.equals("")) {
                                    strTemp3 = strTemp3 + "," + app.getResources().getString(R.string.RGMAP_PROMPT_FOSSILELAYE) + ":" + strLine23;
                                }
                                writeContent(strTemp3, false);
                                this.output.write("</p>".getBytes());
                                break;
                            case 5:
                                this.output.write("<p>".getBytes());
                                Map<String, Object> attSample2 = sampleArea.getNamedAttributeStrings(lid);
                                if (blnezreo) {
                                    strTemp = app.getResources().getString(R.string.RGMAP_PROMPT_POINT);
                                } else {
                                    strTemp = szAng + dlen + app.getResources().getString(R.string.RGMAP_PROMPT_CHU);
                                }
                                String strLine24 = attSample2.get("TYPE").toString();
                                if (!strLine24.equals("")) {
                                    strTemp = strTemp + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLE) + "(" + strLine24 + ")";
                                }
                                String strLine25 = attSample2.get("CODE").toString();
                                if (!strLine25.equals("")) {
                                    strTemp = strTemp + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLECODE) + ":" + attGPoint.get("GEOPOINT").toString() + "-" + strLine25;
                                }
                                String strLine26 = attSample2.get("NAME").toString();
                                if (!strLine26.equals("")) {
                                    strTemp = strTemp + "," + app.getResources().getString(R.string.RGMAP_PROMPT_SAMPLELAYE) + ":" + strLine26;
                                }
                                writeContent(strTemp, false);
                                this.output.write("</p>".getBytes());
                                break;
                        }
                    }
                    dRouteLen += getpntLegth(geoLine);
                }
                attitudeArea.SaveArea();
                sketchArea.SaveArea();
                photoArea.SaveArea();
                fossilArea.SaveArea();
                sampleArea.SaveArea();
                boundaryArea.SaveArea();
                routingArea.SaveArea();
            }
            if (!RouteDesc.equals("")) {
                this.output.write("<p>".getBytes());
                String Str = app.getResources().getString(R.string.menu_main_file_routesum);
                writeContent(Str, false);
                writeContent(":", false);
                writeContent("<br>", false);
                writeContent("</br>", false);
                String strResult = RouteDesc.replace(IOUtils.LINE_SEPARATOR_UNIX, "<br>");
                writeContent(strResult, false);
                this.output.write("</p>".getBytes());
            }
            String imgPRBSketch = m_strFile + File.separator + "sketch/" + Routecode + ".GPJ";
            File vFile3 = new File(imgPRBSketch);
            if (vFile3.exists()) {
                AoMap m_Map13 = AoMap.openMap(imgPRBSketch);
                Bitmap bitmap3 = Bitmap.createBitmap(800, HttpStatus.SC_MULTIPLE_CHOICES, Bitmap.Config.ARGB_8888);
                bitmap3.eraseColor(-1);
                AoRender render3 = new AoRender(bitmap3);
                render3.setRenderSize(800, HttpStatus.SC_MULTIPLE_CHOICES);
                GeoRect rect3 = new GeoRect();
                m_Map13.getMapRect(rect3);
                double sx3 = 800.0d / (rect3.getXmax() - rect3.getXmin());
                double sy3 = 300.0d / (rect3.getYmax() - rect3.getYmin());
                double scale3 = sx3 < sy3 ? sx3 : sy3;
                render3.setDrawParam(rect3.getXmin(), rect3.getYmin(), scale3);
                render3.drawMap(m_Map13);
                String SketchIMGpath3 = m_strFile + File.separator + "sketch/" + Routecode + ".jpg";
                render3.saveBitmap(bitmap3, SketchIMGpath3, Bitmap.CompressFormat.JPEG, 75);
                render3.deleteRender();
                m_Map13.closeMap();
                String strTemp113 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCROUTE);
                String strTemp211 = app.getResources().getString(R.string.RGMAP_PROMPT_DESCHANDMAP);
                this.output.write("<p>".getBytes());
                String Temp = strTemp113 + Routecode + strTemp211 + IOUtils.LINE_SEPARATOR_UNIX;
                writeContent(Temp, false);
                writePicture(SketchIMGpath3);
                this.output.write("</p>".getBytes());
            }
            gpointArea.SaveArea();
            this.output.write("</body></html>".getBytes());
            this.output.close();
        } catch (Exception e) {
            System.out.println("ReadAndWrite Exception");
        }
        return this.htmlPath;
    }

    public String writeHtml1(WorkArea routeArea) {
        RGMapApplication app = RGMapApplication.getCurrentApp();
        double unit = 1.0d;
        try {
            Map<String, Object> attRoute = routeArea.getNamedAttributeStrings(1);
            this.routeCode = (String) attRoute.get("ROUTECODE");
            WorkSpace space = new WorkSpace();
            makeFile();
            this.myFile = new File(this.htmlPath);
            this.output = new FileOutputStream(this.myFile);
            this.output.write("<html><body>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp = app.getResources().getString(R.string.REM_ROUTECODE);
            writeContent(strTemp + attRoute.get("ROUTECODE"), true);
            this.output.write("</p>".getBytes());
            String strTemp2 = app.getResources().getString(R.string.RGMAP_PROMPT_ROUTEDESC);
            this.output.write("<p>".getBytes());
            writeContent(strTemp2 + attRoute.get("DESCRIBE"), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp3 = app.getResources().getString(R.string.RGMAP_PROMPT_ROUTETASK);
            writeContent(strTemp3 + attRoute.get("TASK"), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp4 = app.getResources().getString(R.string.REM_MAPNAME);
            writeContent(strTemp4 + attRoute.get("MAPNAME"), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp5 = app.getResources().getString(R.string.REM_MAPCODE);
            writeContent(strTemp5 + attRoute.get(AttributeExtActivity.PARAM_STRING_MAPCODE), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp6 = app.getResources().getString(R.string.RGMAP_HYDPOINT_RECORDER);
            writeContent(strTemp6 + attRoute.get("RECORDER"), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp7 = app.getResources().getString(R.string.RGMAP_PROMPT_FLLOWER);
            writeContent(strTemp7 + attRoute.get("CAMERAMAN"), true);
            this.output.write("</p>".getBytes());
            this.output.write("<p>".getBytes());
            String strTemp8 = app.getResources().getString(R.string.orecheck_ore_mine_date);
            writeContent(strTemp8 + attRoute.get(AttributeExtActivity.PARAM_STRING_DATE), true);
            this.output.write("</p>".getBytes());
            this.output.write("<br>".getBytes());
            this.output.write("</br>".getBytes());
            WorkArea gpointArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strGPoint);
            WorkAreaParams mapInfo = gpointArea.getWorkAreaParamsClone();
            if (mapInfo.getCoordType() != WorkAreaParams.LengthType.Meter) {
                unit = 1000.0d;
            }
            int[] gpointID = gpointArea.getGeometryIdListByAtt("ROUTECODE = '" + this.routeCode + "'");
            for (int iGPoint = 0; iGPoint < gpointID.length; iGPoint++) {
                Map<String, Object> attGPoint = gpointArea.getNamedAttributeStrings(gpointID[iGPoint]);
                GeoPoint GeoPointpnt = (GeoPoint) gpointArea.getGeometry(gpointID[iGPoint]);
                this.output.write("<p>".getBytes());
                String strTemp9 = app.getResources().getString(R.string.gpo_geopoint);
                writeContent(strTemp9 + attGPoint.get("GEOPOINT"), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp10 = app.getResources().getString(R.string.gpo_xx);
                writeContent(strTemp10 + attGPoint.get(AttributeExtActivity.PARAM_STRING_XX), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp11 = app.getResources().getString(R.string.gpo_yy);
                writeContent(strTemp11 + attGPoint.get(AttributeExtActivity.PARAM_STRING_YY), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp12 = app.getResources().getString(R.string.gpo_outcrop);
                writeContent(strTemp12 + attGPoint.get("OUTCROP"), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp13 = app.getResources().getString(R.string.gpo_type);
                writeContent(strTemp13 + attGPoint.get("TYPE"), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp14 = app.getResources().getString(R.string.gpo_geomorph);
                writeContent(strTemp14 + attGPoint.get("GEOMORPH"), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp15 = app.getResources().getString(R.string.gpo_weathing);
                writeContent(strTemp15 + attGPoint.get("WEATHING"), true);
                this.output.write("</p>".getBytes());
                this.output.write("<p>".getBytes());
                String strTemp16 = app.getResources().getString(R.string.gpo_gpodescribe);
                writeContent(strTemp16 + attGPoint.get("geo_desc"), true);
                this.output.write("</p>".getBytes());
                WorkArea attitudeArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strAttitude);
                WorkArea sketchArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strSketch);
                WorkArea photoArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strPhoto);
                WorkArea fossilArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strFossil);
                WorkArea sampleArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strSample);
                WorkArea boundaryArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strBoundary);
                WorkArea routingArea = space.OpenWorkArea(m_strFile + File.separator + PRBAreas.m_strRouting);
                this.output.write("<p>".getBytes());
                String strTemp17 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTATTI);
                writeContent(strTemp17, true);
                this.output.write("</p>".getBytes());
                String sql = "GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')";
                for (int i : attitudeArea.getGeometryIdListByAtt(sql)) {
                    Map<String, Object> attAttitude = attitudeArea.getNamedAttributeStrings(i);
                    this.output.write("<p>".getBytes());
                    String strTemp18 = app.getResources().getString(R.string.seca_code);
                    writeContent(strTemp18 + attAttitude.get("GEOPOINT") + "-" + attAttitude.get("CODE") + ";", false);
                    String strTemp19 = app.getResources().getString(R.string.REM_LOCATION);
                    writeContent(strTemp19 + attAttitude.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attAttitude.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                    String strTemp20 = app.getResources().getString(R.string.RGMAP_PROMPT_ANGLE);
                    writeContent(strTemp20 + attAttitude.get("DIP") + "∠" + attAttitude.get("DIP_ANG") + ";", false);
                    String strTemp21 = app.getResources().getString(R.string.attitude_type);
                    writeContent(strTemp21 + attAttitude.get("TYPE"), false);
                    this.output.write("</p>".getBytes());
                }
                this.output.write("<p>".getBytes());
                String strTemp22 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTPHOTO);
                writeContent(strTemp22, true);
                this.output.write("</p>".getBytes());
                for (int i2 : photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attPhoto = photoArea.getNamedAttributeStrings(i2);
                    this.output.write("<p>".getBytes());
                    String strTemp23 = app.getResources().getString(R.string.photo_no);
                    writeContent(strTemp23 + attPhoto.get("GEOPOINT") + "-" + attPhoto.get("CODE") + ";", false);
                    String strTemp24 = app.getResources().getString(R.string.REM_LOCATION);
                    writeContent(strTemp24 + attPhoto.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attPhoto.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                    String strTemp25 = app.getResources().getString(R.string.photo_num);
                    writeContent(strTemp25 + attPhoto.get("AMOUNT") + ";", false);
                    String strTemp26 = app.getResources().getString(R.string.photo_content);
                    writeContent(strTemp26 + attPhoto.get("DESCRIBE") + ";", false);
                    String strTemp27 = app.getResources().getString(R.string.photo_seq);
                    writeContent(strTemp27 + attPhoto.get("NUMBER"), false);
                    String szImgNum = (String) attPhoto.get("NUMBER");
                    if (!szImgNum.equals("")) {
                        for (String str : szImgNum.split(",")) {
                            this.output.write("<p>".getBytes());
                            String imgpath = m_strFile + File.separator + "images/" + str + ".jpg";
                            writePicture(imgpath);
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(imgpath, false);
                            this.output.write("</p>".getBytes());
                        }
                    }
                    String szvdoNum = (String) attPhoto.get("MPG_ID");
                    if (!szvdoNum.equals("")) {
                        String[] vdopathArr = szvdoNum.split(",");
                        for (String str2 : vdopathArr) {
                            this.output.write("<p>".getBytes());
                            String imgpath2 = m_strFile + File.separator + "images/" + str2 + ".mp4";
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(imgpath2, false);
                            this.output.write("</p>".getBytes());
                        }
                    }
                    String szatoNum = (String) attPhoto.get("SOUND_ID");
                    if (!szatoNum.equals("")) {
                        String[] atopathArr = szatoNum.split(",");
                        for (String str3 : atopathArr) {
                            this.output.write("<p>".getBytes());
                            String imgpath3 = m_strFile + File.separator + "images/" + str3 + ".wav";
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(imgpath3, false);
                            this.output.write("</p>".getBytes());
                        }
                    }
                    this.output.write("</p>".getBytes());
                }
                this.output.write("<p>".getBytes());
                String strTemp28 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTSKETCH);
                writeContent(strTemp28, true);
                this.output.write("</p>".getBytes());
                for (int i3 : sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attSketch = sketchArea.getNamedAttributeStrings(i3);
                    this.output.write("<p>".getBytes());
                    String strTemp29 = app.getResources().getString(R.string.sketch_no);
                    writeContent(strTemp29 + attSketch.get("GEOPOINT") + "-" + attSketch.get("CODE") + ";", false);
                    String strTemp30 = app.getResources().getString(R.string.REM_LOCATION);
                    writeContent(strTemp30 + attSketch.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attSketch.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                    String strTemp31 = app.getResources().getString(R.string.sketch_strech);
                    writeContent(strTemp31 + attSketch.get("SCALE") + ";", false);
                    String strTemp32 = app.getResources().getString(R.string.sketch_name);
                    writeContent(strTemp32 + attSketch.get("TITLE"), false);
                    String SketchSpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".GPJ";
                    File vFile = new File(SketchSpath);
                    if (vFile.exists()) {
                        AoMap m_Map1 = AoMap.openMap(SketchSpath);
                        Bitmap bitmap = Bitmap.createBitmap(800, 600, Bitmap.Config.ARGB_8888);
                        bitmap.eraseColor(-1);
                        AoRender render = new AoRender(bitmap);
                        render.setRenderSize(800, 600);
                        GeoRect rect = new GeoRect();
                        m_Map1.getMapRect(rect);
                        double sx = 800.0d / (rect.getXmax() - rect.getXmin());
                        double sy = 600.0d / (rect.getYmax() - rect.getYmin());
                        double scale = sx < sy ? sx : sy;
                        render.setDrawParam(rect.getXmin(), rect.getYmin(), scale);
                        render.drawMap(m_Map1);
                        String SketchIMGpath = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".jpg";
                        render.saveBitmap(bitmap, SketchIMGpath, Bitmap.CompressFormat.JPEG, 75);
                        render.deleteRender();
                        m_Map1.closeMap();
                        this.output.write("<p>".getBytes());
                        writePicture(SketchIMGpath);
                        this.output.write("</p>".getBytes());
                        this.output.write("<p>".getBytes());
                        writeContent(SketchIMGpath, false);
                        this.output.write("</p>".getBytes());
                    }
                    String SketchSpath1 = m_strFile + File.separator + "sketch/" + attSketch.get("GEOPOINT") + "_" + attSketch.get("CODE") + ".PNG";
                    File vFile1 = new File(SketchSpath1);
                    if (vFile1.exists()) {
                        this.output.write("<p>".getBytes());
                        writePicture(SketchSpath1);
                        this.output.write("</p>".getBytes());
                        this.output.write("<p>".getBytes());
                        writeContent(SketchSpath1, false);
                        this.output.write("</p>".getBytes());
                    }
                    this.output.write("</p>".getBytes());
                }
                this.output.write("<p>".getBytes());
                String strTemp33 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTFOS);
                writeContent(strTemp33, true);
                this.output.write("</p>".getBytes());
                for (int i4 : fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attFossil = fossilArea.getNamedAttributeStrings(i4);
                    this.output.write("<p>".getBytes());
                    String strTemp34 = app.getResources().getString(R.string.fossil_sample_no);
                    writeContent(strTemp34 + attFossil.get("GEOPOINT") + "-" + attFossil.get("CODE") + ";", false);
                    String strTemp35 = app.getResources().getString(R.string.REM_LOCATION);
                    writeContent(strTemp35 + attFossil.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attFossil.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                    String strTemp36 = app.getResources().getString(R.string.fos_name);
                    writeContent(strTemp36 + attFossil.get("NAME") + ";", false);
                    String strTemp37 = app.getResources().getString(R.string.fos_geounit);
                    writeContent(strTemp37 + attFossil.get("AGE") + ";", false);
                    String strTemp38 = app.getResources().getString(R.string.fos_analyse);
                    writeContent(strTemp38 + attFossil.get("PNAME"), false);
                    this.output.write("</p>".getBytes());
                }
                this.output.write("<p>".getBytes());
                String strTemp39 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTSAMPLE);
                writeContent(strTemp39, true);
                this.output.write("</p>".getBytes());
                for (int i5 : sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')")) {
                    Map<String, Object> attSample = sampleArea.getNamedAttributeStrings(i5);
                    this.output.write("<p>".getBytes());
                    String strTemp40 = app.getResources().getString(R.string.sample_no);
                    writeContent(strTemp40 + attSample.get("GEOPOINT") + "-" + attSample.get("CODE") + ";", false);
                    String strTemp41 = app.getResources().getString(R.string.REM_LOCATION);
                    writeContent(strTemp41 + attSample.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attSample.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                    String strTemp42 = app.getResources().getString(R.string.sample_class);
                    writeContent(strTemp42 + attSample.get("TYPE") + ";", false);
                    String strTemp43 = app.getResources().getString(R.string.sample_yanxing);
                    writeContent(strTemp43 + attSample.get("NAME") + ";", false);
                    this.output.write("</p>".getBytes());
                }
                this.output.write("<p>".getBytes());
                String strTemp44 = app.getResources().getString(R.string.RGMAP_PROMPT_POINTBOUNDARY);
                writeContent(strTemp44, true);
                this.output.write("</p>".getBytes());
                int[] boundaryID = boundaryArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND (R_CODE = '0' OR R_CODE ='')");
                for (int i6 : boundaryID) {
                    Map<String, Object> attboundary = boundaryArea.getNamedAttributeStrings(i6);
                    this.output.write("<p>".getBytes());
                    String strTemp45 = app.getResources().getString(R.string.gboundary_bcode);
                    writeContent(strTemp45 + attboundary.get("GEOPOINT") + "-" + attboundary.get("SUBPOINT") + ";", false);
                    String strTemp46 = app.getResources().getString(R.string.gboundary_btype);
                    writeContent(strTemp46 + attboundary.get("TYPE") + ";", false);
                    String strTemp47 = app.getResources().getString(R.string.gboundary_relation);
                    writeContent(strTemp47 + attboundary.get("RELATION") + ";", false);
                    String strTemp48 = app.getResources().getString(R.string.gboundary_leftbody);
                    writeContent(strTemp48 + attboundary.get("LEFT_BODY") + ";", false);
                    String strTemp49 = app.getResources().getString(R.string.gboundary_rightbody);
                    writeContent(strTemp49 + attboundary.get("RIGHT_BODY"), false);
                    String strTemp50 = app.getResources().getString(R.string.routing_dizhi);
                    writeContent(strTemp50 + attboundary.get("geo_desc"), false);
                    this.output.write("</p>".getBytes());
                }
                double dRouteLen = 0.0d;
                int[] routingID = routingArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "' ");
                int j = 1;
                for (int iRouting = 0; iRouting < routingID.length; iRouting++) {
                    Map<String, Object> attRouting = routingArea.getNamedAttributeStrings(routingID[iRouting]);
                    this.output.write("<p>".getBytes());
                    String strTemp51 = app.getResources().getString(R.string.RGMAP_PROMPT_PRDESC);
                    writeContent(strTemp51, true);
                    this.output.write("</p>".getBytes());
                    this.output.write("<p>".getBytes());
                    writeContent(j + "：" + attRouting.get("geo_desc"), false);
                    this.output.write("</p>".getBytes());
                    j++;
                    GeoLine geoLine = (GeoLine) routingArea.getGeometry(routingID[iRouting]);
                    int[] attitudeID = attitudeArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iAttitude = 0; iAttitude < attitudeID.length; iAttitude++) {
                        Map<String, Object> attAttitude2 = attitudeArea.getNamedAttributeStrings(attitudeID[iAttitude]);
                        this.output.write("<p>".getBytes());
                        GeoPoint pnt = (GeoPoint) attitudeArea.getGeometry(attitudeID[iAttitude]);
                        double dlen = GetPntToLin(pnt, geoLine);
                        String strTemp52 = String.format("%.2f", Double.valueOf(((dRouteLen + dlen) * mapInfo.rate) / unit));
                        writeContent(getpntAngle(GeoPointpnt, pnt) + strTemp52 + "m", false);
                        String strTemp53 = app.getResources().getString(R.string.seca_code);
                        writeContent(strTemp53 + attAttitude2.get("GEOPOINT") + "-" + attAttitude2.get("CODE") + ";", false);
                        String strTemp54 = app.getResources().getString(R.string.REM_LOCATION);
                        writeContent(strTemp54 + attAttitude2.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attAttitude2.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                        String strTemp55 = app.getResources().getString(R.string.RGMAP_PROMPT_ANGLE);
                        writeContent(strTemp55 + attAttitude2.get("DIP") + "∠" + attAttitude2.get("DIP_ANG") + ";", false);
                        String strTemp56 = app.getResources().getString(R.string.attitude_type);
                        writeContent(strTemp56 + attAttitude2.get("TYPE"), false);
                        this.output.write("</p>".getBytes());
                    }
                    int[] sketchID = sketchArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iSketch = 0; iSketch < sketchID.length; iSketch++) {
                        Map<String, Object> attSketch2 = sketchArea.getNamedAttributeStrings(sketchID[iSketch]);
                        this.output.write("<p>".getBytes());
                        GeoPoint pnt2 = (GeoPoint) sketchArea.getGeometry(sketchID[iSketch]);
                        double dlen2 = GetPntToLin(pnt2, geoLine);
                        String strTemp57 = String.format("%.2f", Double.valueOf(((dRouteLen + dlen2) * mapInfo.rate) / unit));
                        writeContent(getpntAngle(GeoPointpnt, pnt2) + strTemp57 + "m", false);
                        String strTemp58 = app.getResources().getString(R.string.sketch_no);
                        writeContent(strTemp58 + attSketch2.get("GEOPOINT") + "-" + attSketch2.get("CODE") + ";", false);
                        String strTemp59 = app.getResources().getString(R.string.REM_LOCATION);
                        writeContent(strTemp59 + attSketch2.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attSketch2.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                        String strTemp60 = app.getResources().getString(R.string.sketch_strech);
                        writeContent(strTemp60 + attSketch2.get("SCALE") + ";", false);
                        String strTemp61 = app.getResources().getString(R.string.sketch_name);
                        writeContent(strTemp61 + attSketch2.get("TITLE"), false);
                        this.output.write("</p>".getBytes());
                    }
                    int[] photoID = photoArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iPhoto = 0; iPhoto < photoID.length; iPhoto++) {
                        Map<String, Object> attPhoto2 = photoArea.getNamedAttributeStrings(photoID[iPhoto]);
                        this.output.write("<p>".getBytes());
                        GeoPoint pnt3 = (GeoPoint) photoArea.getGeometry(photoID[iPhoto]);
                        double dlen3 = GetPntToLin(pnt3, geoLine);
                        String strTemp62 = String.format("%.2f", Double.valueOf(((dRouteLen + dlen3) * mapInfo.rate) / unit));
                        writeContent(getpntAngle(GeoPointpnt, pnt3) + strTemp62 + "M", false);
                        String strTemp63 = app.getResources().getString(R.string.photo_no);
                        writeContent(strTemp63 + attPhoto2.get("GEOPOINT") + "-" + attPhoto2.get("CODE") + ";", false);
                        String strTemp64 = app.getResources().getString(R.string.REM_LOCATION);
                        writeContent(strTemp64 + attPhoto2.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attPhoto2.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                        String strTemp65 = app.getResources().getString(R.string.photo_num);
                        writeContent(strTemp65 + attPhoto2.get("AMOUNT") + ";", false);
                        String strTemp66 = app.getResources().getString(R.string.photo_content);
                        writeContent(strTemp66 + attPhoto2.get("DESCRIBE") + ";", false);
                        String strTemp67 = app.getResources().getString(R.string.photo_seq);
                        writeContent(strTemp67 + attPhoto2.get("NUMBER"), false);
                        String[] IMGpathArr = ((String) attPhoto2.get("NUMBER")).split(",");
                        for (String str4 : IMGpathArr) {
                            this.output.write("<p>".getBytes());
                            String imgpath4 = m_strFile + File.separator + "images/" + str4 + ".jpg";
                            writePicture(imgpath4);
                            this.output.write("</p>".getBytes());
                            this.output.write("<p>".getBytes());
                            writeContent(imgpath4, false);
                            this.output.write("</p>".getBytes());
                        }
                        this.output.write("</p>".getBytes());
                    }
                    int[] fossilID = fossilArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iFossil = 0; iFossil < fossilID.length; iFossil++) {
                        Map<String, Object> attFossil2 = fossilArea.getNamedAttributeStrings(fossilID[iFossil]);
                        this.output.write("<p>".getBytes());
                        GeoPoint pnt4 = (GeoPoint) fossilArea.getGeometry(fossilID[iFossil]);
                        double dlen4 = GetPntToLin(pnt4, geoLine);
                        String strTemp68 = String.format("%.2f", Double.valueOf(((dRouteLen + dlen4) * mapInfo.rate) / unit));
                        writeContent(getpntAngle(GeoPointpnt, pnt4) + strTemp68 + "M", false);
                        String strTemp69 = app.getResources().getString(R.string.fossil_sample_no);
                        writeContent(strTemp69 + attFossil2.get("GEOPOINT") + "-" + attFossil2.get("CODE") + ";", false);
                        String strTemp70 = app.getResources().getString(R.string.REM_LOCATION);
                        writeContent(strTemp70 + attFossil2.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attFossil2.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                        String strTemp71 = app.getResources().getString(R.string.fos_name);
                        writeContent(strTemp71 + attFossil2.get("NAME") + ";", false);
                        String strTemp72 = app.getResources().getString(R.string.fos_geounit);
                        writeContent(strTemp72 + attFossil2.get("AGE") + ";", false);
                        String strTemp73 = app.getResources().getString(R.string.fos_analyse);
                        writeContent(strTemp73 + attFossil2.get("PNAME"), false);
                        this.output.write("</p>".getBytes());
                    }
                    int[] sampleID = sampleArea.getGeometryIdListByAtt("GEOPOINT = '" + attGPoint.get("GEOPOINT") + "'AND R_CODE = '" + attRouting.get("R_CODE") + "'");
                    for (int iSample = 0; iSample < sampleID.length; iSample++) {
                        Map<String, Object> attSample2 = sampleArea.getNamedAttributeStrings(sampleID[iSample]);
                        this.output.write("<p>".getBytes());
                        GeoPoint pnt5 = (GeoPoint) sampleArea.getGeometry(sampleID[iSample]);
                        double dlen5 = GetPntToLin(pnt5, geoLine);
                        String strTemp74 = String.format("%.2f", Double.valueOf(((dRouteLen + dlen5) * mapInfo.rate) / unit));
                        writeContent(getpntAngle(GeoPointpnt, pnt5) + strTemp74 + "M", false);
                        String strTemp75 = app.getResources().getString(R.string.sample_no);
                        writeContent(strTemp75 + attSample2.get("GEOPOINT") + "-" + attSample2.get("CODE") + ";", false);
                        String strTemp76 = app.getResources().getString(R.string.REM_LOCATION);
                        writeContent(strTemp76 + attSample2.get(AttributeExtActivity.PARAM_STRING_XX) + "," + attSample2.get(AttributeExtActivity.PARAM_STRING_YY) + ";", false);
                        String strTemp77 = app.getResources().getString(R.string.sample_class);
                        writeContent(strTemp77 + attSample2.get("TYPE") + ";", false);
                        String strTemp78 = app.getResources().getString(R.string.sample_yanxing);
                        writeContent(strTemp78 + attSample2.get("NAME") + ";", false);
                        this.output.write("</p>".getBytes());
                    }
                    dRouteLen += getpntLegth(geoLine);
                }
            }
            this.output.write("</body></html>".getBytes());
            this.output.close();
        } catch (Exception e) {
            System.out.println("ReadAndWrite Exception");
        }
        return this.htmlPath;
    }

    public void writeContent(String text, Boolean isBold) {
        try {
            this.output.write("<font size=\"3\">".getBytes());
            this.output.write("<font color=\"#000000\">".getBytes());
            if (isBold.booleanValue()) {
                this.output.write("<b>".getBytes());
            }
            this.output.write(text.getBytes());
            if (isBold.booleanValue()) {
                this.output.write("</b>".getBytes());
            }
            this.output.write("</font>".getBytes());
            this.output.write("</font>".getBytes());
        } catch (Exception e) {
            System.out.println("Write File Exception");
        }
    }

    public void writePicture(String url) {
        this.picturePath = url;
        String imageString = "<img src=\"" + this.picturePath + "\" width=\"800\" height=\"600\"";
        try {
            this.output.write((imageString + ">").getBytes());
        } catch (Exception e) {
            System.out.println("output Exception");
        }
    }

    public void writeVideo(String url, String urlimg) {
        this.videoPath = url;
        String videoString = "<a href=\"" + this.videoPath + "\"><img  src=\"" + urlimg + " \" width=\"50\" height=\"50\"></a>";
        try {
            this.output.write(videoString.getBytes());
        } catch (Exception e) {
            System.out.println("output Exception");
        }
    }

    public void writeVoice(String url, String urlimg) {
        this.voicePath = url;
        String voiceString = "<a href=\"" + this.voicePath + "\"><img  src=\"" + urlimg + " \"width=\"50\" height=\"50\"></a>";
        try {
            this.output.write(voiceString.getBytes());
        } catch (Exception e) {
            System.out.println("output Exception");
        }
    }

    public void makeFile() {
        String sdStateString = Environment.getExternalStorageState();
        if (sdStateString.equals("mounted")) {
            try {
                String path = m_strFile + File.separator + this.fileName;
                String temp = path + File.separator + this.routeCode + ".html";
                File dirFile = new File(path);
                if (!dirFile.exists()) {
                    dirFile.mkdir();
                }
                File myFile = new File(temp);
                if (!myFile.exists()) {
                    myFile.createNewFile();
                }
                this.htmlPath = myFile.getAbsolutePath();
            } catch (Exception e) {
            }
        }
    }

    public double getpntLegth(GeoPoint lindot0, GeoPoint lindot1) {
        double deltX = lindot1.getX() - lindot0.getX();
        double deltY = lindot1.getY() - lindot0.getY();
        double len = Math.sqrt((deltX * deltX) + (deltY * deltY));
        return len;
    }

    public double getpntLegth(GeoLine line) {
        double len = 0.0d;
        int isize = line.getPointCount();
        for (int i = 1; i < isize; i++) {
            double dBeginx = line.getX(i - 1);
            double dBeginy = line.getY(i - 1);
            double dEndx = line.getX(i);
            double dEndy = line.getY(i);
            len += getpntLegth(dBeginx, dBeginy, dEndx, dEndy);
        }
        return len;
    }

    public double getpntLegth(double lindot0x, double lindot0y, double lindot1x, double lindot1y) {
        double deltX = lindot1x - lindot0x;
        double deltY = lindot1y - lindot0y;
        double len = Math.sqrt((deltX * deltX) + (deltY * deltY));
        return len;
    }

    public String getpntAngle(GeoPoint lindot0, GeoPoint lindot1) {
        RGMapApplication app = RGMapApplication.getCurrentApp();
        String StrTemp = null;
        double deltX = lindot1.getX() - lindot0.getX();
        double deltY = lindot1.getY() - lindot0.getY();
        if (deltX > 0.0d && deltY > 0.0d) {
            StrTemp = app.getResources().getString(R.string.RGMAP_PROMPT_EN);
        }
        if (deltX > 0.0d && deltY < 0.0d) {
            StrTemp = app.getResources().getString(R.string.RGMAP_PROMPT_ES);
        }
        if (deltX < 0.0d && deltY < 0.0d) {
            StrTemp = app.getResources().getString(R.string.RGMAP_PROMPT_WS);
        }
        if (deltX < 0.0d && deltY > 0.0d) {
            return app.getResources().getString(R.string.RGMAP_PROMPT_WN);
        }
        return StrTemp;
    }

    public String getpntAngle(double dAng) {
        String StrTemp = null;
        if (dAng > 0.0d && dAng <= 45.0d) {
            StrTemp = "NNE ";
        }
        if (dAng > 45.0d && dAng < 90.0d) {
            StrTemp = "NEE ";
        }
        if (dAng > 90.0d && dAng <= 135.0d) {
            StrTemp = "SEE ";
        }
        if (dAng > 135.0d && dAng < 180.0d) {
            StrTemp = "SSE ";
        }
        if (dAng > 180.0d && dAng <= 225.0d) {
            StrTemp = "SSW ";
        }
        if (dAng > 225.0d && dAng < 270.0d) {
            StrTemp = "SWW ";
        }
        if (dAng > 270.0d && dAng <= 315.0d) {
            StrTemp = "NWW ";
        }
        if (dAng > 315.0d && dAng < 360.0d) {
            StrTemp = "NNW ";
        }
        if (Math.abs(dAng - 0.0d) < 1.0E-7d) {
            StrTemp = "N ";
        }
        if (Math.abs(dAng - 90.0d) < 1.0E-7d) {
            StrTemp = "E ";
        }
        if (Math.abs(dAng - 180.0d) < 1.0E-7d) {
            StrTemp = "S ";
        }
        if (Math.abs(dAng - 270.0d) < 1.0E-7d) {
            return "W ";
        }
        return StrTemp;
    }

    public GeoPoint GetVerticalPntToLin(GeoPoint pnt, GeoPoint lindot0, GeoPoint lindot1) {
        GeoPoint resPnt;
        double deltX = lindot1.getX() - lindot0.getX();
        double deltY = lindot1.getY() - lindot0.getY();
        double len = Math.sqrt((deltX * deltX) + (deltY * deltY));
        if (len < 0.0d) {
            return null;
        }
        double A1 = deltY / len;
        double B1 = (-deltX) / len;
        double C1 = -((lindot0.getX() * A1) + (lindot0.getY() * B1));
        if (Math.abs(B1) < 0.001d) {
            resPnt = new GeoPoint();
            resPnt.setXY(lindot0.getX(), pnt.getY());
        } else if (Math.abs(A1) < 0.001d) {
            resPnt = new GeoPoint();
            resPnt.setXY(pnt.getX(), lindot0.getY());
        } else {
            double d = ((-1.0d) * A1) / B1;
            double K2 = B1 / A1;
            double C2 = pnt.getY() - (pnt.getX() * K2);
            if (Math.abs((A1 * (-1.0d)) - (K2 * B1)) < 0.001d) {
                return null;
            }
            GeoPoint resPnt2 = new GeoPoint();
            resPnt2.setXY(((C1 * (-1.0d)) - (C2 * B1)) / ((K2 * B1) - (A1 * (-1.0d))), ((K2 * C1) - (A1 * C2)) / ((A1 * (-1.0d)) - (K2 * B1)));
            return resPnt2;
        }
        return resPnt;
    }

    public short GetVerticalPntToLin(GeoPoint pnt, double lindot0x, double lindot0y, double lindot1x, double lindot1y, GeoPoint reternPnt) {
        double deltX = lindot1x - lindot0x;
        double deltY = lindot1y - lindot0y;
        double len = Math.sqrt((deltX * deltX) + (deltY * deltY));
        if (len < 0.0d) {
            return (short) 0;
        }
        double A1 = deltY / len;
        double B1 = (-deltX) / len;
        double C1 = -((A1 * lindot0x) + (B1 * lindot0y));
        if (Math.abs(B1) < 0.001d) {
            reternPnt.setX(lindot0x);
            reternPnt.setY(pnt.getY());
        } else if (Math.abs(A1) < 0.001d) {
            reternPnt.setX(pnt.getX());
            reternPnt.setY(lindot0y);
        } else {
            double d = ((-1.0d) * A1) / B1;
            double K2 = B1 / A1;
            double C2 = pnt.getY() - (pnt.getX() * K2);
            if (Math.abs((A1 * (-1.0d)) - (K2 * B1)) >= 0.001d) {
                reternPnt.setX(((C1 * (-1.0d)) - (C2 * B1)) / ((K2 * B1) - (A1 * (-1.0d))));
                reternPnt.setY(((K2 * C1) - (A1 * C2)) / ((A1 * (-1.0d)) - (K2 * B1)));
            } else {
                return (short) 0;
            }
        }
        double minx = lindot0x < lindot1x ? lindot0x : lindot1x;
        double maxx = lindot0x > lindot1x ? lindot0x : lindot1x;
        double miny = lindot0y < lindot1y ? lindot0y : lindot1y;
        double maxy = lindot0y > lindot1y ? lindot0y : lindot1y;
        if (reternPnt.getX() > minx && reternPnt.getX() < maxx && reternPnt.getY() > miny && reternPnt.getY() < maxy) {
            return (short) 1;
        }
        return (short) 0;
    }

    public double GetPntToLin(GeoPoint pnt, GeoLine line) {
        double dlen = 0.0d;
        double dlen1 = 0.0d;
        double dlast = 9.999999999999E7d;
        GeoPoint VerticalPnt = new GeoPoint();
        int isize = line.getPointCount();
        for (int i = 1; i < isize; i++) {
            double dBeginx = line.getX(i - 1);
            double dBeginy = line.getY(i - 1);
            double dEndx = line.getX(i);
            double dEndy = line.getY(i);
            short ire = GetVerticalPntToLin(pnt, dBeginx, dBeginy, dEndx, dEndy, VerticalPnt);
            if (ire > 0) {
                double dlen2 = getpntLegth(dBeginx, dBeginy, VerticalPnt.getX(), VerticalPnt.getY());
                double dlen3 = getpntLegth(pnt, VerticalPnt);
                if (dlast > dlen3) {
                    dlast = dlen3;
                    dlen = dlen1 + dlen2;
                }
            } else {
                double a1 = getpntLegth(dBeginx, dBeginy, pnt.getX(), pnt.getY());
                double a2 = getpntLegth(dEndx, dEndy, pnt.getX(), pnt.getY());
                if (a1 < a2) {
                    if (dlast > a1) {
                        dlast = a1;
                        dlen = dlen1 + 0.0d;
                    }
                } else if (dlast > a2) {
                    dlast = a2;
                    dlen = dlen1 + getpntLegth(dBeginx, dBeginy, dEndx, dEndy);
                }
            }
            dlen1 += getpntLegth(dBeginx, dBeginy, dEndx, dEndy);
        }
        return dlen;
    }

    public void showPhotovideo(String path, String Keyword) {
        File[] files;
        File filepath = new File(path);
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (!file.isDirectory()) {
                    String strname = file.getName();
                    if ((strname.indexOf(Keyword) > -1 || strname.indexOf(Keyword.toUpperCase()) > -1) && (strname.indexOf(".mp4") > -1 || strname.indexOf(Keyword.toUpperCase()) > -1)) {
                        try {
                            this.output.write("<p>".getBytes());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        String str = path + strname;
                        try {
                            this.output.write("</p>".getBytes());
                        } catch (IOException e2) {
                            e2.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    public void showPhotoAtuo(String path, String Keyword) {
        File[] files;
        File filepath = new File(path);
        if (Environment.getExternalStorageState().equals("mounted") && (files = filepath.listFiles()) != null && files.length > 0) {
            for (File file : files) {
                if (!file.isDirectory()) {
                    String strname = file.getName();
                    if ((strname.indexOf(Keyword) > -1 || strname.indexOf(Keyword.toUpperCase()) > -1) && (strname.indexOf(".mp3") > -1 || strname.indexOf(Keyword.toUpperCase()) > -1)) {
                        try {
                            this.output.write("<p>".getBytes());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        String str = path + strname;
                        try {
                            this.output.write("</p>".getBytes());
                        } catch (IOException e2) {
                            e2.printStackTrace();
                        }
                    }
                }
            }
        }
    }
}
