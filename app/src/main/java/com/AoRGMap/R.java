package com.AoRGMap;

/* loaded from: classes.dex */
public final class R {

    public static final class anim {
        public static final int abc_fade_in = 0x7f010000;
        public static final int abc_fade_out = 0x7f010001;
        public static final int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static final int abc_popup_enter = 0x7f010003;
        public static final int abc_popup_exit = 0x7f010004;
        public static final int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static final int abc_slide_in_bottom = 0x7f010006;
        public static final int abc_slide_in_top = 0x7f010007;
        public static final int abc_slide_out_bottom = 0x7f010008;
        public static final int abc_slide_out_top = 0x7f010009;
    }

    public static final class array {
        public static final int AxLenTypes = 0x7f020000;
        public static final int AxProjTypes = 0x7f020001;
        public static final int AxTypes = 0x7f020002;
        public static final int Component_array = 0x7f020003;
        public static final int _model_list = 0x7f020004;
        public static final int decoder_list = 0x7f020005;
        public static final int engpoint_array = 0x7f020006;
        public static final int gravel_array = 0x7f020007;
        public static final int hydpoint_array = 0x7f020008;
        public static final int infile_list = 0x7f020009;
        public static final int language = 0x7f02000a;
        public static final int nlu_list = 0x7f02000b;
        public static final int prop_list = 0x7f02000c;
        public static final int section_array = 0x7f02000d;
        public static final int vad_list = 0x7f02000e;
        public static final int vad_timeout_list = 0x7f02000f;
    }

    public static final class attr {
        public static final int actionBarDivider = 0x7f030000;
        public static final int actionBarItemBackground = 0x7f030001;
        public static final int actionBarPopupTheme = 0x7f030002;
        public static final int actionBarSize = 0x7f030003;
        public static final int actionBarSplitStyle = 0x7f030004;
        public static final int actionBarStyle = 0x7f030005;
        public static final int actionBarTabBarStyle = 0x7f030006;
        public static final int actionBarTabStyle = 0x7f030007;
        public static final int actionBarTabTextStyle = 0x7f030008;
        public static final int actionBarTheme = 0x7f030009;
        public static final int actionBarWidgetTheme = 0x7f03000a;
        public static final int actionButtonStyle = 0x7f03000b;
        public static final int actionDropDownStyle = 0x7f03000c;
        public static final int actionLayout = 0x7f03000d;
        public static final int actionMenuTextAppearance = 0x7f03000e;
        public static final int actionMenuTextColor = 0x7f03000f;
        public static final int actionModeBackground = 0x7f030010;
        public static final int actionModeCloseButtonStyle = 0x7f030011;
        public static final int actionModeCloseDrawable = 0x7f030012;
        public static final int actionModeCopyDrawable = 0x7f030013;
        public static final int actionModeCutDrawable = 0x7f030014;
        public static final int actionModeFindDrawable = 0x7f030015;
        public static final int actionModePasteDrawable = 0x7f030016;
        public static final int actionModePopupWindowStyle = 0x7f030017;
        public static final int actionModeSelectAllDrawable = 0x7f030018;
        public static final int actionModeShareDrawable = 0x7f030019;
        public static final int actionModeSplitBackground = 0x7f03001a;
        public static final int actionModeStyle = 0x7f03001b;
        public static final int actionModeWebSearchDrawable = 0x7f03001c;
        public static final int actionOverflowButtonStyle = 0x7f03001d;
        public static final int actionOverflowMenuStyle = 0x7f03001e;
        public static final int actionProviderClass = 0x7f03001f;
        public static final int actionViewClass = 0x7f030020;
        public static final int activityChooserViewStyle = 0x7f030021;
        public static final int alertDialogButtonGroupStyle = 0x7f030022;
        public static final int alertDialogCenterButtons = 0x7f030023;
        public static final int alertDialogStyle = 0x7f030024;
        public static final int alertDialogTheme = 0x7f030025;
        public static final int allowStacking = 0x7f030026;
        public static final int arrowHeadLength = 0x7f030027;
        public static final int arrowShaftLength = 0x7f030028;
        public static final int autoCompleteTextViewStyle = 0x7f030029;
        public static final int background = 0x7f03002a;
        public static final int backgroundSplit = 0x7f03002b;
        public static final int backgroundStacked = 0x7f03002c;
        public static final int backgroundTint = 0x7f03002d;
        public static final int backgroundTintMode = 0x7f03002e;
        public static final int barLength = 0x7f03002f;
        public static final int borderlessButtonStyle = 0x7f030030;
        public static final int buttonBarButtonStyle = 0x7f030031;
        public static final int buttonBarNegativeButtonStyle = 0x7f030032;
        public static final int buttonBarNeutralButtonStyle = 0x7f030033;
        public static final int buttonBarPositiveButtonStyle = 0x7f030034;
        public static final int buttonBarStyle = 0x7f030035;
        public static final int buttonPanelSideLayout = 0x7f030036;
        public static final int buttonStyle = 0x7f030037;
        public static final int buttonStyleSmall = 0x7f030038;
        public static final int buttonTint = 0x7f030039;
        public static final int buttonTintMode = 0x7f03003a;
        public static final int checkboxStyle = 0x7f03003b;
        public static final int checkedTextViewStyle = 0x7f03003c;
        public static final int closeIcon = 0x7f03003d;
        public static final int closeItemLayout = 0x7f03003e;
        public static final int collapseContentDescription = 0x7f03003f;
        public static final int collapseIcon = 0x7f030040;
        public static final int color = 0x7f030041;
        public static final int colorAccent = 0x7f030042;
        public static final int colorButtonNormal = 0x7f030043;
        public static final int colorControlActivated = 0x7f030044;
        public static final int colorControlHighlight = 0x7f030045;
        public static final int colorControlNormal = 0x7f030046;
        public static final int colorPrimary = 0x7f030047;
        public static final int colorPrimaryDark = 0x7f030048;
        public static final int colorSwitchThumbNormal = 0x7f030049;
        public static final int commitIcon = 0x7f03004a;
        public static final int contentInsetEnd = 0x7f03004b;
        public static final int contentInsetLeft = 0x7f03004c;
        public static final int contentInsetRight = 0x7f03004d;
        public static final int contentInsetStart = 0x7f03004e;
        public static final int controlBackground = 0x7f03004f;
        public static final int customNavigationLayout = 0x7f030050;
        public static final int defaultQueryHint = 0x7f030051;
        public static final int default_screen = 0x7f030052;
        public static final int dialogPreferredPadding = 0x7f030053;
        public static final int dialogTheme = 0x7f030054;
        public static final int displayOptions = 0x7f030055;
        public static final int divider = 0x7f030056;
        public static final int dividerHorizontal = 0x7f030057;
        public static final int dividerPadding = 0x7f030058;
        public static final int dividerVertical = 0x7f030059;
        public static final int drawableSize = 0x7f03005a;
        public static final int drawerArrowStyle = 0x7f03005b;
        public static final int dropDownListViewStyle = 0x7f03005c;
        public static final int dropdownListPreferredItemHeight = 0x7f03005d;
        public static final int editTextBackground = 0x7f03005e;
        public static final int editTextColor = 0x7f03005f;
        public static final int editTextStyle = 0x7f030060;
        public static final int elevation = 0x7f030061;
        public static final int expandActivityOverflowButtonDrawable = 0x7f030062;
        public static final int gapBetweenBars = 0x7f030063;
        public static final int goIcon = 0x7f030064;
        public static final int height = 0x7f030065;
        public static final int hideOnContentScroll = 0x7f030066;
        public static final int homeAsUpIndicator = 0x7f030067;
        public static final int homeLayout = 0x7f030068;
        public static final int icon = 0x7f030069;
        public static final int iconifiedByDefault = 0x7f03006a;
        public static final int imageButtonStyle = 0x7f03006b;
        public static final int indeterminateProgressStyle = 0x7f03006c;
        public static final int initialActivityCount = 0x7f03006d;
        public static final int isLightTheme = 0x7f03006e;
        public static final int itemPadding = 0x7f03006f;
        public static final int layout = 0x7f030070;
        public static final int listChoiceBackgroundIndicator = 0x7f030071;
        public static final int listDividerAlertDialog = 0x7f030072;
        public static final int listItemLayout = 0x7f030073;
        public static final int listLayout = 0x7f030074;
        public static final int listPopupWindowStyle = 0x7f030075;
        public static final int listPreferredItemHeight = 0x7f030076;
        public static final int listPreferredItemHeightLarge = 0x7f030077;
        public static final int listPreferredItemHeightSmall = 0x7f030078;
        public static final int listPreferredItemPaddingLeft = 0x7f030079;
        public static final int listPreferredItemPaddingRight = 0x7f03007a;
        public static final int logo = 0x7f03007b;
        public static final int logoDescription = 0x7f03007c;
        public static final int maxButtonHeight = 0x7f03007d;
        public static final int measureWithLargestChild = 0x7f03007e;
        public static final int multiChoiceItemLayout = 0x7f03007f;
        public static final int navigationContentDescription = 0x7f030080;
        public static final int navigationIcon = 0x7f030081;
        public static final int navigationMode = 0x7f030082;
        public static final int overlapAnchor = 0x7f030083;
        public static final int paddingEnd = 0x7f030084;
        public static final int paddingStart = 0x7f030085;
        public static final int panelBackground = 0x7f030086;
        public static final int panelMenuListTheme = 0x7f030087;
        public static final int panelMenuListWidth = 0x7f030088;
        public static final int popupMenuStyle = 0x7f030089;
        public static final int popupTheme = 0x7f03008a;
        public static final int popupWindowStyle = 0x7f03008b;
        public static final int position = 0x7f03008c;
        public static final int preserveIconSpacing = 0x7f03008d;
        public static final int progressBarPadding = 0x7f03008e;
        public static final int progressBarStyle = 0x7f03008f;
        public static final int queryBackground = 0x7f030090;
        public static final int queryHint = 0x7f030091;
        public static final int radioButtonStyle = 0x7f030092;
        public static final int radius = 0x7f030093;
        public static final int ratingBarStyle = 0x7f030094;
        public static final int ratingBarStyleIndicator = 0x7f030095;
        public static final int ratingBarStyleSmall = 0x7f030096;
        public static final int searchHintIcon = 0x7f030097;
        public static final int searchIcon = 0x7f030098;
        public static final int searchViewStyle = 0x7f030099;
        public static final int seekBarStyle = 0x7f03009a;
        public static final int selectableItemBackground = 0x7f03009b;
        public static final int selectableItemBackgroundBorderless = 0x7f03009c;
        public static final int showAsAction = 0x7f03009d;
        public static final int showDividers = 0x7f03009e;
        public static final int showText = 0x7f03009f;
        public static final int singleChoiceItemLayout = 0x7f0300a0;
        public static final int spinBars = 0x7f0300a1;
        public static final int spinnerDropDownItemStyle = 0x7f0300a2;
        public static final int spinnerStyle = 0x7f0300a3;
        public static final int splitTrack = 0x7f0300a4;
        public static final int srcCompat = 0x7f0300a5;
        public static final int state_above_anchor = 0x7f0300a6;
        public static final int submitBackground = 0x7f0300a7;
        public static final int subtitle = 0x7f0300a8;
        public static final int subtitleTextAppearance = 0x7f0300a9;
        public static final int subtitleTextColor = 0x7f0300aa;
        public static final int subtitleTextStyle = 0x7f0300ab;
        public static final int suggestionRowLayout = 0x7f0300ac;
        public static final int switchMinWidth = 0x7f0300ad;
        public static final int switchPadding = 0x7f0300ae;
        public static final int switchStyle = 0x7f0300af;
        public static final int switchTextAppearance = 0x7f0300b0;
        public static final int textAllCaps = 0x7f0300b1;
        public static final int textAppearanceLargePopupMenu = 0x7f0300b2;
        public static final int textAppearanceListItem = 0x7f0300b3;
        public static final int textAppearanceListItemSmall = 0x7f0300b4;
        public static final int textAppearanceSearchResultSubtitle = 0x7f0300b5;
        public static final int textAppearanceSearchResultTitle = 0x7f0300b6;
        public static final int textAppearanceSmallPopupMenu = 0x7f0300b7;
        public static final int textColorAlertDialogListItem = 0x7f0300b8;
        public static final int textColorSearchUrl = 0x7f0300b9;
        public static final int theme = 0x7f0300ba;
        public static final int thickness = 0x7f0300bb;
        public static final int thumbTextPadding = 0x7f0300bc;
        public static final int title = 0x7f0300bd;
        public static final int titleMarginBottom = 0x7f0300be;
        public static final int titleMarginEnd = 0x7f0300bf;
        public static final int titleMarginStart = 0x7f0300c0;
        public static final int titleMarginTop = 0x7f0300c1;
        public static final int titleMargins = 0x7f0300c2;
        public static final int titleTextAppearance = 0x7f0300c3;
        public static final int titleTextColor = 0x7f0300c4;
        public static final int titleTextStyle = 0x7f0300c5;
        public static final int toolbarNavigationButtonStyle = 0x7f0300c6;
        public static final int toolbarStyle = 0x7f0300c7;
        public static final int track = 0x7f0300c8;
        public static final int voiceIcon = 0x7f0300c9;
        public static final int windowActionBar = 0x7f0300ca;
        public static final int windowActionBarOverlay = 0x7f0300cb;
        public static final int windowActionModeOverlay = 0x7f0300cc;
        public static final int windowFixedHeightMajor = 0x7f0300cd;
        public static final int windowFixedHeightMinor = 0x7f0300ce;
        public static final int windowFixedWidthMajor = 0x7f0300cf;
        public static final int windowFixedWidthMinor = 0x7f0300d0;
        public static final int windowMinWidthMajor = 0x7f0300d1;
        public static final int windowMinWidthMinor = 0x7f0300d2;
        public static final int windowNoTitle = 0x7f0300d3;
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 0x7f040000;
        public static final int abc_action_bar_embed_tabs_pre_jb = 0x7f040001;
        public static final int abc_action_bar_expanded_action_views_exclusive = 0x7f040002;
        public static final int abc_allow_stacked_button_bar = 0x7f040003;
        public static final int abc_config_actionMenuItemAllCaps = 0x7f040004;
        public static final int abc_config_allowActionMenuItemTextWithIcon = 0x7f040005;
        public static final int abc_config_closeDialogWhenTouchOutside = 0x7f040006;
        public static final int abc_config_showMenuShortcutsWhenKeyboardPresent = 0x7f040007;
    }

    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 0x7f050000;
        public static final int abc_background_cache_hint_selector_material_light = 0x7f050001;
        public static final int abc_color_highlight_material = 0x7f050002;
        public static final int abc_input_method_navigation_guard = 0x7f050003;
        public static final int abc_primary_text_disable_only_material_dark = 0x7f050004;
        public static final int abc_primary_text_disable_only_material_light = 0x7f050005;
        public static final int abc_primary_text_material_dark = 0x7f050006;
        public static final int abc_primary_text_material_light = 0x7f050007;
        public static final int abc_search_url_text = 0x7f050008;
        public static final int abc_search_url_text_normal = 0x7f050009;
        public static final int abc_search_url_text_pressed = 0x7f05000a;
        public static final int abc_search_url_text_selected = 0x7f05000b;
        public static final int abc_secondary_text_material_dark = 0x7f05000c;
        public static final int abc_secondary_text_material_light = 0x7f05000d;
        public static final int accent_material_dark = 0x7f05000e;
        public static final int accent_material_light = 0x7f05000f;
        public static final int ao_back_color = 0x7f050010;
        public static final int ao_black = 0x7f050011;
        public static final int ao_blue = 0x7f050012;
        public static final int ao_half_transparent = 0x7f050013;
        public static final int ao_red = 0x7f050014;
        public static final int back_color = 0x7f050015;
        public static final int background_floating_material_dark = 0x7f050016;
        public static final int background_floating_material_light = 0x7f050017;
        public static final int background_material_dark = 0x7f050018;
        public static final int background_material_light = 0x7f050019;
        public static final int black = 0x7f05001a;
        public static final int bright_foreground_disabled_material_dark = 0x7f05001b;
        public static final int bright_foreground_disabled_material_light = 0x7f05001c;
        public static final int bright_foreground_inverse_material_dark = 0x7f05001d;
        public static final int bright_foreground_inverse_material_light = 0x7f05001e;
        public static final int bright_foreground_material_dark = 0x7f05001f;
        public static final int bright_foreground_material_light = 0x7f050020;
        public static final int btn_color = 0x7f050021;
        public static final int btn_color1 = 0x7f050022;
        public static final int button_material_dark = 0x7f050023;
        public static final int button_material_light = 0x7f050024;
        public static final int colorAccent = 0x7f050025;
        public static final int colorPrimary = 0x7f050026;
        public static final int colorPrimaryDark = 0x7f050027;
        public static final int dim_foreground_disabled_material_dark = 0x7f050028;
        public static final int dim_foreground_disabled_material_light = 0x7f050029;
        public static final int dim_foreground_material_dark = 0x7f05002a;
        public static final int dim_foreground_material_light = 0x7f05002b;
        public static final int foreground_material_dark = 0x7f05002c;
        public static final int foreground_material_light = 0x7f05002d;
        public static final int highlighted_text_material_dark = 0x7f05002e;
        public static final int highlighted_text_material_light = 0x7f05002f;
        public static final int hint_foreground_material_dark = 0x7f050030;
        public static final int hint_foreground_material_light = 0x7f050031;
        public static final int map_catlog = 0x7f050032;
        public static final int map_item = 0x7f050033;
        public static final int material_blue_grey_800 = 0x7f050034;
        public static final int material_blue_grey_900 = 0x7f050035;
        public static final int material_blue_grey_950 = 0x7f050036;
        public static final int material_deep_teal_200 = 0x7f050037;
        public static final int material_deep_teal_500 = 0x7f050038;
        public static final int material_grey_100 = 0x7f050039;
        public static final int material_grey_300 = 0x7f05003a;
        public static final int material_grey_50 = 0x7f05003b;
        public static final int material_grey_600 = 0x7f05003c;
        public static final int material_grey_800 = 0x7f05003d;
        public static final int material_grey_850 = 0x7f05003e;
        public static final int material_grey_900 = 0x7f05003f;
        public static final int primary_dark_material_dark = 0x7f050040;
        public static final int primary_dark_material_light = 0x7f050041;
        public static final int primary_material_dark = 0x7f050042;
        public static final int primary_material_light = 0x7f050043;
        public static final int primary_text_default_material_dark = 0x7f050044;
        public static final int primary_text_default_material_light = 0x7f050045;
        public static final int primary_text_disabled_material_dark = 0x7f050046;
        public static final int primary_text_disabled_material_light = 0x7f050047;
        public static final int ripple_material_dark = 0x7f050048;
        public static final int ripple_material_light = 0x7f050049;
        public static final int secondary_text_default_material_dark = 0x7f05004a;
        public static final int secondary_text_default_material_light = 0x7f05004b;
        public static final int secondary_text_disabled_material_dark = 0x7f05004c;
        public static final int secondary_text_disabled_material_light = 0x7f05004d;
        public static final int switch_thumb_disabled_material_dark = 0x7f05004e;
        public static final int switch_thumb_disabled_material_light = 0x7f05004f;
        public static final int switch_thumb_material_dark = 0x7f050050;
        public static final int switch_thumb_material_light = 0x7f050051;
        public static final int switch_thumb_normal_material_dark = 0x7f050052;
        public static final int switch_thumb_normal_material_light = 0x7f050053;
        public static final int title_color = 0x7f050054;
        public static final int view_mask = 0x7f050055;
    }

    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 0x7f060000;
        public static final int abc_action_bar_default_height_material = 0x7f060001;
        public static final int abc_action_bar_default_padding_end_material = 0x7f060002;
        public static final int abc_action_bar_default_padding_start_material = 0x7f060003;
        public static final int abc_action_bar_icon_vertical_padding_material = 0x7f060004;
        public static final int abc_action_bar_overflow_padding_end_material = 0x7f060005;
        public static final int abc_action_bar_overflow_padding_start_material = 0x7f060006;
        public static final int abc_action_bar_progress_bar_size = 0x7f060007;
        public static final int abc_action_bar_stacked_max_height = 0x7f060008;
        public static final int abc_action_bar_stacked_tab_max_width = 0x7f060009;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 0x7f06000a;
        public static final int abc_action_bar_subtitle_top_margin_material = 0x7f06000b;
        public static final int abc_action_button_min_height_material = 0x7f06000c;
        public static final int abc_action_button_min_width_material = 0x7f06000d;
        public static final int abc_action_button_min_width_overflow_material = 0x7f06000e;
        public static final int abc_alert_dialog_button_bar_height = 0x7f06000f;
        public static final int abc_button_inset_horizontal_material = 0x7f060010;
        public static final int abc_button_inset_vertical_material = 0x7f060011;
        public static final int abc_button_padding_horizontal_material = 0x7f060012;
        public static final int abc_button_padding_vertical_material = 0x7f060013;
        public static final int abc_config_prefDialogWidth = 0x7f060014;
        public static final int abc_control_corner_material = 0x7f060015;
        public static final int abc_control_inset_material = 0x7f060016;
        public static final int abc_control_padding_material = 0x7f060017;
        public static final int abc_dialog_fixed_height_major = 0x7f060018;
        public static final int abc_dialog_fixed_height_minor = 0x7f060019;
        public static final int abc_dialog_fixed_width_major = 0x7f06001a;
        public static final int abc_dialog_fixed_width_minor = 0x7f06001b;
        public static final int abc_dialog_list_padding_vertical_material = 0x7f06001c;
        public static final int abc_dialog_min_width_major = 0x7f06001d;
        public static final int abc_dialog_min_width_minor = 0x7f06001e;
        public static final int abc_dialog_padding_material = 0x7f06001f;
        public static final int abc_dialog_padding_top_material = 0x7f060020;
        public static final int abc_disabled_alpha_material_dark = 0x7f060021;
        public static final int abc_disabled_alpha_material_light = 0x7f060022;
        public static final int abc_dropdownitem_icon_width = 0x7f060023;
        public static final int abc_dropdownitem_text_padding_left = 0x7f060024;
        public static final int abc_dropdownitem_text_padding_right = 0x7f060025;
        public static final int abc_edit_text_inset_bottom_material = 0x7f060026;
        public static final int abc_edit_text_inset_horizontal_material = 0x7f060027;
        public static final int abc_edit_text_inset_top_material = 0x7f060028;
        public static final int abc_floating_window_z = 0x7f060029;
        public static final int abc_list_item_padding_horizontal_material = 0x7f06002a;
        public static final int abc_panel_menu_list_width = 0x7f06002b;
        public static final int abc_search_view_preferred_width = 0x7f06002c;
        public static final int abc_search_view_text_min_width = 0x7f06002d;
        public static final int abc_seekbar_track_background_height_material = 0x7f06002e;
        public static final int abc_seekbar_track_progress_height_material = 0x7f06002f;
        public static final int abc_select_dialog_padding_start_material = 0x7f060030;
        public static final int abc_switch_padding = 0x7f060031;
        public static final int abc_text_size_body_1_material = 0x7f060032;
        public static final int abc_text_size_body_2_material = 0x7f060033;
        public static final int abc_text_size_button_material = 0x7f060034;
        public static final int abc_text_size_caption_material = 0x7f060035;
        public static final int abc_text_size_display_1_material = 0x7f060036;
        public static final int abc_text_size_display_2_material = 0x7f060037;
        public static final int abc_text_size_display_3_material = 0x7f060038;
        public static final int abc_text_size_display_4_material = 0x7f060039;
        public static final int abc_text_size_headline_material = 0x7f06003a;
        public static final int abc_text_size_large_material = 0x7f06003b;
        public static final int abc_text_size_medium_material = 0x7f06003c;
        public static final int abc_text_size_menu_material = 0x7f06003d;
        public static final int abc_text_size_small_material = 0x7f06003e;
        public static final int abc_text_size_subhead_material = 0x7f06003f;
        public static final int abc_text_size_subtitle_material_toolbar = 0x7f060040;
        public static final int abc_text_size_title_material = 0x7f060041;
        public static final int abc_text_size_title_material_toolbar = 0x7f060042;
        public static final int activity_horizontal_margin = 0x7f060043;
        public static final int activity_vertical_margin = 0x7f060044;
        public static final int disabled_alpha_material_dark = 0x7f060045;
        public static final int disabled_alpha_material_light = 0x7f060046;
        public static final int highlight_alpha_material_colored = 0x7f060047;
        public static final int highlight_alpha_material_dark = 0x7f060048;
        public static final int highlight_alpha_material_light = 0x7f060049;
        public static final int notification_large_icon_height = 0x7f06004a;
        public static final int notification_large_icon_width = 0x7f06004b;
        public static final int notification_subtext_size = 0x7f06004c;
    }

    public static final class drawable {
        public static final int a32 = 0x7f070000;
        public static final int a321 = 0x7f070001;
        public static final int a322 = 0x7f070002;
        public static final int a35 = 0x7f070003;
        public static final int a3o = 0x7f070004;
        public static final int abc_ab_share_pack_mtrl_alpha = 0x7f070005;
        public static final int abc_action_bar_item_background_material = 0x7f070006;
        public static final int abc_btn_borderless_material = 0x7f070007;
        public static final int abc_btn_check_material = 0x7f070008;
        public static final int abc_btn_check_to_on_mtrl_000 = 0x7f070009;
        public static final int abc_btn_check_to_on_mtrl_015 = 0x7f07000a;
        public static final int abc_btn_colored_material = 0x7f07000b;
        public static final int abc_btn_default_mtrl_shape = 0x7f07000c;
        public static final int abc_btn_radio_material = 0x7f07000d;
        public static final int abc_btn_radio_to_on_mtrl_000 = 0x7f07000e;
        public static final int abc_btn_radio_to_on_mtrl_015 = 0x7f07000f;
        public static final int abc_btn_rating_star_off_mtrl_alpha = 0x7f070010;
        public static final int abc_btn_rating_star_on_mtrl_alpha = 0x7f070011;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 0x7f070012;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 0x7f070013;
        public static final int abc_cab_background_internal_bg = 0x7f070014;
        public static final int abc_cab_background_top_material = 0x7f070015;
        public static final int abc_cab_background_top_mtrl_alpha = 0x7f070016;
        public static final int abc_control_background_material = 0x7f070017;
        public static final int abc_dialog_material_background_dark = 0x7f070018;
        public static final int abc_dialog_material_background_light = 0x7f070019;
        public static final int abc_edit_text_material = 0x7f07001a;
        public static final int abc_ic_ab_back_mtrl_am_alpha = 0x7f07001b;
        public static final int abc_ic_clear_mtrl_alpha = 0x7f07001c;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 0x7f07001d;
        public static final int abc_ic_go_search_api_mtrl_alpha = 0x7f07001e;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001f;
        public static final int abc_ic_menu_cut_mtrl_alpha = 0x7f070020;
        public static final int abc_ic_menu_moreoverflow_mtrl_alpha = 0x7f070021;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 0x7f070022;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 0x7f070023;
        public static final int abc_ic_menu_share_mtrl_alpha = 0x7f070024;
        public static final int abc_ic_search_api_mtrl_alpha = 0x7f070025;
        public static final int abc_ic_star_black_16dp = 0x7f070026;
        public static final int abc_ic_star_black_36dp = 0x7f070027;
        public static final int abc_ic_star_half_black_16dp = 0x7f070028;
        public static final int abc_ic_star_half_black_36dp = 0x7f070029;
        public static final int abc_ic_voice_search_api_mtrl_alpha = 0x7f07002a;
        public static final int abc_item_background_holo_dark = 0x7f07002b;
        public static final int abc_item_background_holo_light = 0x7f07002c;
        public static final int abc_list_divider_mtrl_alpha = 0x7f07002d;
        public static final int abc_list_focused_holo = 0x7f07002e;
        public static final int abc_list_longpressed_holo = 0x7f07002f;
        public static final int abc_list_pressed_holo_dark = 0x7f070030;
        public static final int abc_list_pressed_holo_light = 0x7f070031;
        public static final int abc_list_selector_background_transition_holo_dark = 0x7f070032;
        public static final int abc_list_selector_background_transition_holo_light = 0x7f070033;
        public static final int abc_list_selector_disabled_holo_dark = 0x7f070034;
        public static final int abc_list_selector_disabled_holo_light = 0x7f070035;
        public static final int abc_list_selector_holo_dark = 0x7f070036;
        public static final int abc_list_selector_holo_light = 0x7f070037;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 0x7f070038;
        public static final int abc_popup_background_mtrl_mult = 0x7f070039;
        public static final int abc_ratingbar_full_material = 0x7f07003a;
        public static final int abc_ratingbar_indicator_material = 0x7f07003b;
        public static final int abc_ratingbar_small_material = 0x7f07003c;
        public static final int abc_scrubber_control_off_mtrl_alpha = 0x7f07003d;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07003e;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07003f;
        public static final int abc_scrubber_primary_mtrl_alpha = 0x7f070040;
        public static final int abc_scrubber_track_mtrl_alpha = 0x7f070041;
        public static final int abc_seekbar_thumb_material = 0x7f070042;
        public static final int abc_seekbar_track_material = 0x7f070043;
        public static final int abc_spinner_mtrl_am_alpha = 0x7f070044;
        public static final int abc_spinner_textfield_background_material = 0x7f070045;
        public static final int abc_switch_thumb_material = 0x7f070046;
        public static final int abc_switch_track_mtrl_alpha = 0x7f070047;
        public static final int abc_tab_indicator_material = 0x7f070048;
        public static final int abc_tab_indicator_mtrl_alpha = 0x7f070049;
        public static final int abc_text_cursor_material = 0x7f07004a;
        public static final int abc_textfield_activated_mtrl_alpha = 0x7f07004b;
        public static final int abc_textfield_default_mtrl_alpha = 0x7f07004c;
        public static final int abc_textfield_search_activated_mtrl_alpha = 0x7f07004d;
        public static final int abc_textfield_search_default_mtrl_alpha = 0x7f07004e;
        public static final int abc_textfield_search_material = 0x7f07004f;
        public static final int amp1 = 0x7f070050;
        public static final int amp2 = 0x7f070051;
        public static final int amp3 = 0x7f070052;
        public static final int amp4 = 0x7f070053;
        public static final int amp5 = 0x7f070054;
        public static final int amp6 = 0x7f070055;
        public static final int amp7 = 0x7f070056;
        public static final int ao_attribute_cell = 0x7f070057;
        public static final int ao_bkcolor = 0x7f070058;
        public static final int ao_compass = 0x7f070059;
        public static final int ao_folder = 0x7f07005a;
        public static final int ao_imagebutton = 0x7f07005b;
        public static final int ao_level = 0x7f07005c;
        public static final int ao_load = 0x7f07005d;
        public static final int ao_screen_pot = 0x7f07005e;
        public static final int ao_screen_pot_selected = 0x7f07005f;
        public static final int ao_titlebutton = 0x7f070060;
        public static final int ao_txt = 0x7f070061;
        public static final int ao_uponelevel = 0x7f070062;
        public static final int app_icon = 0x7f070063;
        public static final int apply = 0x7f070064;
        public static final int apply_button = 0x7f070065;
        public static final int apply_button1 = 0x7f070066;
        public static final int attribute_cell = 0x7f070067;
        public static final int b = 0x7f070068;
        public static final int bd_asr_popup_bg = 0x7f070069;
        public static final int bdspeech_btn_greendeep_normal = 0x7f07006a;
        public static final int bdspeech_btn_greendeep_pressed = 0x7f07006b;
        public static final int bdspeech_btn_greenlight_normal = 0x7f07006c;
        public static final int bdspeech_btn_greenlight_pressed = 0x7f07006d;
        public static final int bdspeech_btn_normal = 0x7f07006e;
        public static final int bdspeech_btn_orangedeep_normal = 0x7f07006f;
        public static final int bdspeech_btn_orangedeep_pressed = 0x7f070070;
        public static final int bdspeech_btn_orangelight_normal = 0x7f070071;
        public static final int bdspeech_btn_orangelight_pressed = 0x7f070072;
        public static final int bdspeech_btn_pressed = 0x7f070073;
        public static final int bdspeech_btn_recognizing = 0x7f070074;
        public static final int bdspeech_btn_recognizing_deep = 0x7f070075;
        public static final int bdspeech_btn_reddeep_normal = 0x7f070076;
        public static final int bdspeech_btn_reddeep_pressed = 0x7f070077;
        public static final int bdspeech_btn_redlight_normal = 0x7f070078;
        public static final int bdspeech_btn_redlight_pressed = 0x7f070079;
        public static final int bdspeech_digital_bg = 0x7f07007a;
        public static final int bdspeech_digital_deep_bg = 0x7f07007b;
        public static final int bdspeech_left_deep_normal = 0x7f07007c;
        public static final int bdspeech_left_deep_pressed = 0x7f07007d;
        public static final int bdspeech_left_normal = 0x7f07007e;
        public static final int bdspeech_left_pressed = 0x7f07007f;
        public static final int bdspeech_right_greendeep_normal = 0x7f070080;
        public static final int bdspeech_right_greendeep_pressed = 0x7f070081;
        public static final int bdspeech_right_greenlight_normal = 0x7f070082;
        public static final int bdspeech_right_greenlight_pressed = 0x7f070083;
        public static final int bdspeech_right_normal = 0x7f070084;
        public static final int bdspeech_right_orangedeep_normal = 0x7f070085;
        public static final int bdspeech_right_orangedeep_pressed = 0x7f070086;
        public static final int bdspeech_right_orangelight_normal = 0x7f070087;
        public static final int bdspeech_right_orangelight_pressed = 0x7f070088;
        public static final int bdspeech_right_pressed = 0x7f070089;
        public static final int bdspeech_right_reddeep_normal = 0x7f07008a;
        public static final int bdspeech_right_reddeep_pressed = 0x7f07008b;
        public static final int bdspeech_right_redlight_normal = 0x7f07008c;
        public static final int bdspeech_right_redlight_pressed = 0x7f07008d;
        public static final int bk_layoutshape = 0x7f07008e;
        public static final int bkframe = 0x7f07008f;
        public static final int bkshape = 0x7f070090;
        public static final int bottom_att_link = 0x7f070091;
        public static final int bottom_copy_point = 0x7f070092;
        public static final int bottom_delete = 0x7f070093;
        public static final int bottom_delete_line = 0x7f070094;
        public static final int bottom_edit_att = 0x7f070095;
        public static final int bottom_edit_base = 0x7f070096;
        public static final int bottom_edit_info = 0x7f070097;
        public static final int bottom_edit_infolin = 0x7f070098;
        public static final int bottom_edit_lib = 0x7f070099;
        public static final int bottom_edit_line = 0x7f07009a;
        public static final int bottom_geochemistry = 0x7f07009b;
        public static final int bottom_map = 0x7f07009c;
        public static final int bottom_map1 = 0x7f07009d;
        public static final int bottom_move = 0x7f07009e;
        public static final int bottom_move_line = 0x7f07009f;
        public static final int bottom_new = 0x7f0700a0;
        public static final int bottom_new_line1 = 0x7f0700a1;
        public static final int bottom_new_line2 = 0x7f0700a2;
        public static final int bottom_new_line3 = 0x7f0700a3;
        public static final int bottom_new_note = 0x7f0700a4;
        public static final int bottom_new_section = 0x7f0700a5;
        public static final int bottom_point_add = 0x7f0700a6;
        public static final int bottom_point_del = 0x7f0700a7;
        public static final int bottom_point_move = 0x7f0700a8;
        public static final int bottom_proj = 0x7f0700a9;
        public static final int bottom_route = 0x7f0700aa;
        public static final int bottom_section = 0x7f0700ab;
        public static final int btn_radio_on_disabled_holo_light = 0x7f0700ac;
        public static final int btn_radio_on_holo_dark = 0x7f0700ad;
        public static final int buttoncircle = 0x7f0700ae;
        public static final int camera_mode_button = 0x7f0700af;
        public static final int camera_mode_shutter_press = 0x7f0700b0;
        public static final int camera_save = 0x7f0700b1;
        public static final int camera_snapshot = 0x7f0700b2;
        public static final int cancel = 0x7f0700b3;
        public static final int cancel_button = 0x7f0700b4;
        public static final int cancel_button1 = 0x7f0700b5;
        public static final int close = 0x7f0700b6;
        public static final int close_compass = 0x7f0700b7;
        public static final int compass = 0x7f0700b8;
        public static final int compass1 = 0x7f0700b9;
        public static final int compass2 = 0x7f0700ba;
        public static final int composer_button = 0x7f0700bb;
        public static final int composer_icn_plus = 0x7f0700bc;
        public static final int composer_lssc = 0x7f0700bd;
        public static final int composer_place = 0x7f0700be;
        public static final int composer_sjjm = 0x7f0700bf;
        public static final int composer_sjwj = 0x7f0700c0;
        public static final int composer_sleep = 0x7f0700c1;
        public static final int composer_thought = 0x7f0700c2;
        public static final int composer_with = 0x7f0700c3;
        public static final int composer_xspm = 0x7f0700c4;
        public static final int composer_yksb = 0x7f0700c5;
        public static final int composer_ywsb = 0x7f0700c6;
        public static final int cunchu = 0x7f0700c7;
        public static final int da = 0x7f0700c8;
        public static final int dlg_background = 0x7f0700c9;
        public static final int dsk = 0x7f0700ca;
        public static final int e1 = 0x7f0700cb;
        public static final int e2 = 0x7f0700cc;
        public static final int e3 = 0x7f0700cd;
        public static final int e4 = 0x7f0700ce;
        public static final int e5 = 0x7f0700cf;
        public static final int e6 = 0x7f0700d0;
        public static final int e7 = 0x7f0700d1;
        public static final int editviewshaple = 0x7f0700d2;
        public static final int endinfo = 0x7f0700d3;
        public static final int erase = 0x7f0700d4;
        public static final int f = 0x7f0700d5;
        public static final int frame = 0x7f0700d6;
        public static final int googleimage = 0x7f0700d7;
        public static final int googlemap = 0x7f0700d8;
        public static final int home_btn_bg = 0x7f0700d9;
        public static final int home_btn_bg_d = 0x7f0700da;
        public static final int home_btn_bg_n = 0x7f0700db;
        public static final int home_btn_bg_s = 0x7f0700dc;
        public static final int html = 0x7f0700dd;
        public static final int ic_1 = 0x7f0700de;
        public static final int ic_2 = 0x7f0700df;
        public static final int ic_3 = 0x7f0700e0;
        public static final int ic_4 = 0x7f0700e1;
        public static final int ic_5 = 0x7f0700e2;
        public static final int ic_6 = 0x7f0700e3;
        public static final int ic_launcher = 0x7f0700e4;
        public static final int ic_main = 0x7f0700e5;
        public static final int icon_1_n = 0x7f0700e6;
        public static final int icon_2_n = 0x7f0700e7;
        public static final int icon_3_n = 0x7f0700e8;
        public static final int icon_4_n = 0x7f0700e9;
        public static final int icon_5_n = 0x7f0700ea;
        public static final int image_zyml = 0x7f0700eb;
        public static final int image_zyml1 = 0x7f0700ec;
        public static final int image_zyml2 = 0x7f0700ed;
        public static final int img_wj = 0x7f0700ee;
        public static final int img_wjj = 0x7f0700ef;
        public static final int info = 0x7f0700f0;
        public static final int info1 = 0x7f0700f1;
        public static final int info2 = 0x7f0700f2;
        public static final int intab = 0x7f0700f3;
        public static final int loc = 0x7f0700f4;
        public static final int loc1 = 0x7f0700f5;
        public static final int logo = 0x7f0700f6;
        public static final int logo1 = 0x7f0700f7;
        public static final int logorgmap = 0x7f0700f8;
        public static final int maintab_toolbar_bg = 0x7f0700f9;
        public static final int maptype0 = 0x7f0700fa;
        public static final int maptype1 = 0x7f0700fb;
        public static final int maptype2 = 0x7f0700fc;
        public static final int maptype3 = 0x7f0700fd;
        public static final int med = 0x7f0700fe;
        public static final int menu_line_copy = 0x7f0700ff;
        public static final int menu_more = 0x7f070100;
        public static final int menu_select1 = 0x7f070101;
        public static final int menu_select2 = 0x7f070102;
        public static final int notification_template_icon_bg = 0x7f070103;
        public static final int p = 0x7f070104;
        public static final int path = 0x7f070105;
        public static final int playwav = 0x7f070106;
        public static final int psk = 0x7f070107;
        public static final int r = 0x7f070108;
        public static final int route = 0x7f070109;
        public static final int search = 0x7f07010a;
        public static final int sight_jd = 0x7f07010b;
        public static final int sketcha = 0x7f07010c;
        public static final int sketchb = 0x7f07010d;
        public static final int sketchbk = 0x7f07010e;
        public static final int sketchcolor = 0x7f07010f;
        public static final int sketcheraser = 0x7f070110;
        public static final int sketchlinewidth = 0x7f070111;
        public static final int sketchpen = 0x7f070112;
        public static final int sketchsave = 0x7f070113;
        public static final int sketchtextpen = 0x7f070114;
        public static final int stopwav = 0x7f070115;
        public static final int switch_button_left_checked = 0x7f070116;
        public static final int tab_weixin_pressed = 0x7f070117;
        public static final int titlebar_lightgray_bg = 0x7f070118;
        public static final int tree_ec = 0x7f070119;
        public static final int tree_ex = 0x7f07011a;
        public static final int urlnavigation_itemicon_default = 0x7f07011b;
        public static final int userdef_audio_background = 0x7f07011c;
        public static final int userdef_audio_rekam = 0x7f07011d;
        public static final int userdef_audio_return = 0x7f07011e;
        public static final int userdef_audio_stop = 0x7f07011f;
        public static final int voice_rcd_hint = 0x7f070120;
        public static final int vsk = 0x7f070121;
        public static final int wg_sh = 0x7f070122;
    }

    public static final class id {
        public static final int CPJView = 0x7f080000;
        public static final int LAYER = 0x7f080001;
        public static final int LINE = 0x7f080002;
        public static final int LinearLayout_InputGPSRectify = 0x7f080003;
        public static final int LinearLayout_InputGeoNum = 0x7f080004;
        public static final int SECCODE = 0x7f080005;
        public static final int XangView = 0x7f080006;
        public static final int ZView = 0x7f080007;
        public static final int action0 = 0x7f080008;
        public static final int action_bar = 0x7f080009;
        public static final int action_bar_activity_content = 0x7f08000a;
        public static final int action_bar_container = 0x7f08000b;
        public static final int action_bar_root = 0x7f08000c;
        public static final int action_bar_spinner = 0x7f08000d;
        public static final int action_bar_subtitle = 0x7f08000e;
        public static final int action_bar_title = 0x7f08000f;
        public static final int action_context_bar = 0x7f080010;
        public static final int action_divider = 0x7f080011;
        public static final int action_menu_divider = 0x7f080012;
        public static final int action_menu_presenter = 0x7f080013;
        public static final int action_mode_bar = 0x7f080014;
        public static final int action_mode_bar_stub = 0x7f080015;
        public static final int action_mode_close_button = 0x7f080016;
        public static final int activity_chooser_view_content = 0x7f080017;
        public static final int alertTitle = 0x7f080018;
        public static final int always = 0x7f080019;
        public static final int ao_att_table_header = 0x7f08001a;
        public static final int ao_attribute_button = 0x7f08001b;
        public static final int ao_attribute_content = 0x7f08001c;
        public static final int ao_attribute_editline = 0x7f08001d;
        public static final int ao_attribute_editor = 0x7f08001e;
        public static final int ao_attribute_group = 0x7f08001f;
        public static final int ao_attribute_group_title = 0x7f080020;
        public static final int ao_attribute_item = 0x7f080021;
        public static final int ao_attribute_label = 0x7f080022;
        public static final int ao_attribute_list = 0x7f080023;
        public static final int ao_attribute_prop_dictName = 0x7f080024;
        public static final int ao_attribute_prop_dictType = 0x7f080025;
        public static final int ao_attributes_container_scroll = 0x7f080026;
        public static final int ao_attributes_container_view = 0x7f080027;
        public static final int ao_btnCancel = 0x7f080028;
        public static final int ao_btnOK = 0x7f080029;
        public static final int ao_config_container = 0x7f08002a;
        public static final int ao_dlg_dic_hlist = 0x7f08002b;
        public static final int ao_dlg_dic_listview = 0x7f08002c;
        public static final int ao_float_panel = 0x7f08002d;
        public static final int ao_gpsFloat = 0x7f08002e;
        public static final int ao_gps_bar_dragger = 0x7f08002f;
        public static final int ao_gps_btnOnOff = 0x7f080030;
        public static final int ao_gps_btnSwitchDisp = 0x7f080031;
        public static final int ao_gps_editor_x = 0x7f080032;
        public static final int ao_gps_editor_y = 0x7f080033;
        public static final int ao_lbl_toolbar_edit_Area = 0x7f080034;
        public static final int ao_mainRelativeLayout = 0x7f080035;
        public static final int ao_main_view = 0x7f080036;
        public static final int ao_orientation = 0x7f080037;
        public static final int ao_orientation_x = 0x7f080038;
        public static final int ao_orientation_y = 0x7f080039;
        public static final int ao_orientation_z = 0x7f08003a;
        public static final int ao_screen_indicator = 0x7f08003b;
        public static final int ao_scroll_screen = 0x7f08003c;
        public static final int ao_test_scroll_view = 0x7f08003d;
        public static final int ao_toolbar_edit_comm = 0x7f08003e;
        public static final int att_table_header = 0x7f08003f;
        public static final int atti_DIP = 0x7f080040;
        public static final int atti_DIP_ANG = 0x7f080041;
        public static final int atti_ORDER = 0x7f080042;
        public static final int atti_REMAARK = 0x7f080043;
        public static final int atti_TREND = 0x7f080044;
        public static final int atti_TYPE = 0x7f080045;
        public static final int atti_listSurvey = 0x7f080046;
        public static final int atti_sortid = 0x7f080047;
        public static final int attribute_list = 0x7f080048;
        public static final int autoCompleteTextView1 = 0x7f080049;
        public static final int bdinfoBtn = 0x7f08004a;
        public static final int bdinfo_battery = 0x7f08004b;
        public static final int bdinfo_bdtime = 0x7f08004c;
        public static final int bdinfo_cardnum = 0x7f08004d;
        public static final int bdinfo_satellite1 = 0x7f08004e;
        public static final int bdinfo_satellite2 = 0x7f08004f;
        public static final int bdinfo_satellite3 = 0x7f080050;
        public static final int bdinfo_satellite4 = 0x7f080051;
        public static final int bdinfo_satellite5 = 0x7f080052;
        public static final int bdinfo_satellite6 = 0x7f080053;
        public static final int bdinfo_xcoord = 0x7f080054;
        public static final int bdinfo_ycoord = 0x7f080055;
        public static final int beginning = 0x7f080056;
        public static final int bigdataservice_del = 0x7f080057;
        public static final int bigdataservice_query = 0x7f080058;
        public static final int bigdataservice_resultlistView = 0x7f080059;
        public static final int bigdataservice_search = 0x7f08005a;
        public static final int bluetoothBtn = 0x7f08005b;
        public static final int bluetoothStateTv = 0x7f08005c;
        public static final int btimgnext = 0x7f08005d;
        public static final int btimgpre = 0x7f08005e;
        public static final int btn = 0x7f08005f;
        public static final int btnGPSRectify_TableRow = 0x7f080060;
        public static final int btn_add = 0x7f080061;
        public static final int btn_delete = 0x7f080062;
        public static final int btn_edit = 0x7f080063;
        public static final int btn_insert = 0x7f080064;
        public static final int btn_look = 0x7f080065;
        public static final int btn_stop = 0x7f080066;
        public static final int btn_table = 0x7f080067;
        public static final int btn_toolbar_att = 0x7f080068;
        public static final int btn_toolbar_cancel = 0x7f080069;
        public static final int btn_toolbar_entity_att = 0x7f08006a;
        public static final int btn_toolbar_entity_delete = 0x7f08006b;
        public static final int btn_toolbar_entity_info = 0x7f08006c;
        public static final int btn_toolbar_entity_new = 0x7f08006d;
        public static final int btn_toolbar_info = 0x7f08006e;
        public static final int btn_toolbar_new = 0x7f08006f;
        public static final int btn_toolbar_ok = 0x7f080070;
        public static final int btn_toolbar_return = 0x7f080071;
        public static final int btn_update = 0x7f080072;
        public static final int btsoundnext = 0x7f080073;
        public static final int btsoundpause = 0x7f080074;
        public static final int btsoundplay = 0x7f080075;
        public static final int btsoundpre = 0x7f080076;
        public static final int btsoundstop = 0x7f080077;
        public static final int btvideonext = 0x7f080078;
        public static final int btvideopre = 0x7f080079;
        public static final int button1 = 0x7f08007a;
        public static final int button2 = 0x7f08007b;
        public static final int button3 = 0x7f08007c;
        public static final int buttonCancel = 0x7f08007d;
        public static final int buttonOK = 0x7f08007e;
        public static final int buttonPanel = 0x7f08007f;
        public static final int buttonSelect = 0x7f080080;
        public static final int buttonSet = 0x7f080081;
        public static final int button_cancel = 0x7f080082;
        public static final int button_mpxjcancel = 0x7f080083;
        public static final int button_ok = 0x7f080084;
        public static final int button_pmxjumok = 0x7f080085;
        public static final int cameraSurfaceView = 0x7f080086;
        public static final int cancel_action = 0x7f080087;
        public static final int checkBox1 = 0x7f080088;
        public static final int checkbox = 0x7f080089;
        public static final int chronometer = 0x7f08008a;
        public static final int closeBluetoothActivity = 0x7f08008b;
        public static final int closeBluetoothBt = 0x7f08008c;
        public static final int collapseActionView = 0x7f08008d;
        public static final int config_container = 0x7f08008e;
        public static final int content = 0x7f08008f;
        public static final int contentPanel = 0x7f080090;
        public static final int curTime = 0x7f080091;
        public static final int custom = 0x7f080092;
        public static final int customPanel = 0x7f080093;
        public static final int dataquery_radiogroup = 0x7f080094;
        public static final int decor_content_parent = 0x7f080095;
        public static final int default_activity_button = 0x7f080096;
        public static final int des_label1 = 0x7f080097;
        public static final int deviceListLayout = 0x7f080098;
        public static final int deviceListTv = 0x7f080099;
        public static final int dict_edit = 0x7f08009a;
        public static final int dict_edit_layout = 0x7f08009b;
        public static final int disConnectBt = 0x7f08009c;
        public static final int disableHome = 0x7f08009d;
        public static final int dlg_gps_rectify_dx = 0x7f08009e;
        public static final int dlg_gps_rectify_dy = 0x7f08009f;
        public static final int dlg_gps_rectify_dz = 0x7f0800a0;
        public static final int editText = 0x7f0800a1;
        public static final int editTextDot = 0x7f0800a2;
        public static final int editTextLine = 0x7f0800a3;
        public static final int editText_pmtj = 0x7f0800a4;
        public static final int editText_routesum = 0x7f0800a5;
        public static final int edit_query = 0x7f0800a6;
        public static final int edtInput = 0x7f0800a7;
        public static final int end = 0x7f0800a8;
        public static final int end_padder = 0x7f0800a9;
        public static final int engpointlist = 0x7f0800aa;
        public static final int ep_attitjpoint = 0x7f0800ab;
        public static final int ep_code = 0x7f0800ac;
        public static final int ep_dip = 0x7f0800ad;
        public static final int ep_dip_ang = 0x7f0800ae;
        public static final int ep_feature = 0x7f0800af;
        public static final int ep_gap = 0x7f0800b0;
        public static final int ep_gpoint = 0x7f0800b1;
        public static final int ep_length = 0x7f0800b2;
        public static final int ep_linecode = 0x7f0800b3;
        public static final int ep_pack = 0x7f0800b4;
        public static final int ep_packtype = 0x7f0800b5;
        public static final int ep_roughness = 0x7f0800b6;
        public static final int ep_sortid = 0x7f0800b7;
        public static final int ep_span = 0x7f0800b8;
        public static final int ep_trend = 0x7f0800b9;
        public static final int ep_type = 0x7f0800ba;
        public static final int ep_water = 0x7f0800bb;
        public static final int ep_width = 0x7f0800bc;
        public static final int expand_activities_button = 0x7f0800bd;
        public static final int expanded_menu = 0x7f0800be;
        public static final int flipper = 0x7f0800bf;
        public static final int floatBtnAddBound = 0x7f0800c0;
        public static final int floatBtnAddE1 = 0x7f0800c1;
        public static final int floatBtnAddE2 = 0x7f0800c2;
        public static final int floatBtnAddE3 = 0x7f0800c3;
        public static final int floatBtnAddE4 = 0x7f0800c4;
        public static final int floatBtnAddE5 = 0x7f0800c5;
        public static final int floatBtnAddE6 = 0x7f0800c6;
        public static final int floatBtnAddE7 = 0x7f0800c7;
        public static final int floatBtnAddGPoint = 0x7f0800c8;
        public static final int floatBtnAddRoute = 0x7f0800c9;
        public static final int float_panel = 0x7f0800ca;
        public static final int fos_analyse = 0x7f0800cb;
        public static final int fos_code = 0x7f0800cc;
        public static final int fos_geounit = 0x7f0800cd;
        public static final int fos_laycode = 0x7f0800ce;
        public static final int fos_name = 0x7f0800cf;
        public static final int fos_remark = 0x7f0800d0;
        public static final int fos_sampling = 0x7f0800d1;
        public static final int fos_secpoint = 0x7f0800d2;
        public static final int fos_slope_l = 0x7f0800d3;
        public static final int fos_sortid = 0x7f0800d4;
        public static final int fos_type = 0x7f0800d5;
        public static final int fosillist = 0x7f0800d6;
        public static final int foslist = 0x7f0800d7;
        public static final int free_p_radiogroup = 0x7f0800d8;
        public static final int free_p_radiogroup1 = 0x7f0800d9;
        public static final int free_p_radiogroup2 = 0x7f0800da;
        public static final int gallery1 = 0x7f0800db;
        public static final int gpo_geomorph = 0x7f0800dc;
        public static final int gpo_geopoint = 0x7f0800dd;
        public static final int gpo_laycode = 0x7f0800de;
        public static final int gpo_litho_A = 0x7f0800df;
        public static final int gpo_litho_B = 0x7f0800e0;
        public static final int gpo_litho_C = 0x7f0800e1;
        public static final int gpo_outcrop = 0x7f0800e2;
        public static final int gpo_position = 0x7f0800e3;
        public static final int gpo_secpoint = 0x7f0800e4;
        public static final int gpo_sortid = 0x7f0800e5;
        public static final int gpo_straphA = 0x7f0800e6;
        public static final int gpo_straphB = 0x7f0800e7;
        public static final int gpo_straphC = 0x7f0800e8;
        public static final int gpo_strarAB = 0x7f0800e9;
        public static final int gpo_strarAC = 0x7f0800ea;
        public static final int gpo_strarBC = 0x7f0800eb;
        public static final int gpo_type = 0x7f0800ec;
        public static final int gpo_weathing = 0x7f0800ed;
        public static final int gpointlist = 0x7f0800ee;
        public static final int gpsFloat = 0x7f0800ef;
        public static final int gps_btbgpslocation = 0x7f0800f0;
        public static final int gps_btnCenter = 0x7f0800f1;
        public static final int gps_btnDrawPoint = 0x7f0800f2;
        public static final int gps_btnOnOff = 0x7f0800f3;
        public static final int gps_btnSwitchDisp = 0x7f0800f4;
        public static final int gps_btnnavigation = 0x7f0800f5;
        public static final int gps_editor_x = 0x7f0800f6;
        public static final int gps_editor_y = 0x7f0800f7;
        public static final int gps_editor_z = 0x7f0800f8;
        public static final int gps_nnavigation_text = 0x7f0800f9;
        public static final int gravel_DIP = 0x7f0800fa;
        public static final int gravel_DIPANGLE = 0x7f0800fb;
        public static final int gravel_LONGAXIS = 0x7f0800fc;
        public static final int gravel_ROCKTYPE = 0x7f0800fd;
        public static final int gravel_SHORTAXIS = 0x7f0800fe;
        public static final int gravel_TYPE = 0x7f0800ff;
        public static final int gravel_ZAXIS = 0x7f080100;
        public static final int gravel_add = 0x7f080101;
        public static final int gravel_del = 0x7f080102;
        public static final int gravel_insert = 0x7f080103;
        public static final int gravel_layout = 0x7f080104;
        public static final int gravel_listSurvey = 0x7f080105;
        public static final int gravel_order = 0x7f080106;
        public static final int gravel_sortid = 0x7f080107;
        public static final int gravel_spinner = 0x7f080108;
        public static final int gravel_update = 0x7f080109;
        public static final int home = 0x7f08010a;
        public static final int homeAsUp = 0x7f08010b;
        public static final int hp_amount = 0x7f08010c;
        public static final int hp_code = 0x7f08010d;
        public static final int hp_depth = 0x7f08010e;
        public static final int hp_gpoint = 0x7f08010f;
        public static final int hp_hydrology = 0x7f080110;
        public static final int hp_linecode = 0x7f080111;
        public static final int hp_method = 0x7f080112;
        public static final int hp_proectant = 0x7f080113;
        public static final int hp_recorder = 0x7f080114;
        public static final int hp_sortid = 0x7f080115;
        public static final int hp_time = 0x7f080116;
        public static final int hp_x = 0x7f080117;
        public static final int hp_y = 0x7f080118;
        public static final int hp_z = 0x7f080119;
        public static final int icon = 0x7f08011a;
        public static final int id_Layer_end = 0x7f08011b;
        public static final int id_Layer_id = 0x7f08011c;
        public static final int id_Layer_soildesc = 0x7f08011d;
        public static final int id_Layer_soiltype = 0x7f08011e;
        public static final int id_Layer_start = 0x7f08011f;
        public static final int id_arcmenu = 0x7f080120;
        public static final int id_arcmenu_button = 0x7f080121;
        public static final int id_arcmenu_button1 = 0x7f080122;
        public static final int id_arcmenu_button2 = 0x7f080123;
        public static final int id_arcmenu_button3 = 0x7f080124;
        public static final int id_arcmenu_button4 = 0x7f080125;
        public static final int id_arcmenu_button5 = 0x7f080126;
        public static final int id_arcmenu_button6 = 0x7f080127;
        public static final int id_arcmenu_button7 = 0x7f080128;
        public static final int id_audio_Time = 0x7f080129;
        public static final int id_audio_apply = 0x7f08012a;
        public static final int id_audio_erase = 0x7f08012b;
        public static final int id_audio_pause = 0x7f08012c;
        public static final int id_audio_record = 0x7f08012d;
        public static final int id_audio_stop = 0x7f08012e;
        public static final int id_audio_text = 0x7f08012f;
        public static final int id_bottom_menu = 0x7f080130;
        public static final int id_bt_add = 0x7f080131;
        public static final int id_bt_play = 0x7f080132;
        public static final int id_button_layout = 0x7f080133;
        public static final int id_button_showbkatt = 0x7f080134;
        public static final int id_checkBox1 = 0x7f080135;
        public static final int id_checkBox10 = 0x7f080136;
        public static final int id_checkBox11 = 0x7f080137;
        public static final int id_checkBox12 = 0x7f080138;
        public static final int id_checkBox13 = 0x7f080139;
        public static final int id_checkBox14 = 0x7f08013a;
        public static final int id_checkBox2 = 0x7f08013b;
        public static final int id_checkBox3 = 0x7f08013c;
        public static final int id_checkBox4 = 0x7f08013d;
        public static final int id_checkBox5 = 0x7f08013e;
        public static final int id_checkBox6 = 0x7f08013f;
        public static final int id_checkBox7 = 0x7f080140;
        public static final int id_checkBox8 = 0x7f080141;
        public static final int id_checkBox9 = 0x7f080142;
        public static final int id_checkbox = 0x7f080143;
        public static final int id_dataquery_Type = 0x7f080144;
        public static final int id_dataquery_headType = 0x7f080145;
        public static final int id_dataquery_poplist = 0x7f080146;
        public static final int id_dataqueryhead_field = 0x7f080147;
        public static final int id_dataqueryhead_value = 0x7f080148;
        public static final int id_dataqueryresult_field = 0x7f080149;
        public static final int id_dataqueryresult_value = 0x7f08014a;
        public static final int id_dataqueryresult_value1 = 0x7f08014b;
        public static final int id_dataqueryresult_value10 = 0x7f08014c;
        public static final int id_dataqueryresult_value2 = 0x7f08014d;
        public static final int id_dataqueryresult_value3 = 0x7f08014e;
        public static final int id_dataqueryresult_value4 = 0x7f08014f;
        public static final int id_dataqueryresult_value5 = 0x7f080150;
        public static final int id_dataqueryresult_value6 = 0x7f080151;
        public static final int id_dataqueryresult_value7 = 0x7f080152;
        public static final int id_dataqueryresult_value8 = 0x7f080153;
        public static final int id_dataqueryresult_value9 = 0x7f080154;
        public static final int id_dlg_mapview = 0x7f080155;
        public static final int id_dlgmaptype = 0x7f080156;
        public static final int id_edit_code = 0x7f080157;
        public static final int id_edit_compass = 0x7f080158;
        public static final int id_edit_direction = 0x7f080159;
        public static final int id_edit_fileno = 0x7f08015a;
        public static final int id_edit_note = 0x7f08015b;
        public static final int id_fgx = 0x7f08015c;
        public static final int id_google_type0 = 0x7f08015d;
        public static final int id_google_type1 = 0x7f08015e;
        public static final int id_google_type2 = 0x7f08015f;
        public static final int id_google_type3 = 0x7f080160;
        public static final int id_googlemap_type = 0x7f080161;
        public static final int id_guass_type0 = 0x7f080162;
        public static final int id_guass_type1 = 0x7f080163;
        public static final int id_guass_type2 = 0x7f080164;
        public static final int id_hisresource = 0x7f080165;
        public static final int id_htmlinfo_webView = 0x7f080166;
        public static final int id_imageView_amp = 0x7f080167;
        public static final int id_imageView_circle = 0x7f080168;
        public static final int id_imageView_compass = 0x7f080169;
        public static final int id_imageView_level = 0x7f08016a;
        public static final int id_imageView_takepic = 0x7f08016b;
        public static final int id_image_apply = 0x7f08016c;
        public static final int id_image_cancel = 0x7f08016d;
        public static final int id_image_orientationshow = 0x7f08016e;
        public static final int id_image_orientationtext = 0x7f08016f;
        public static final int id_image_orientationzoom = 0x7f080170;
        public static final int id_image_seekBar = 0x7f080171;
        public static final int id_info_listView = 0x7f080172;
        public static final int id_infopopwindow_field = 0x7f080173;
        public static final int id_infopopwindow_value = 0x7f080174;
        public static final int id_item_descpath = 0x7f080175;
        public static final int id_layout1 = 0x7f080176;
        public static final int id_layout_l1 = 0x7f080177;
        public static final int id_layout_l2 = 0x7f080178;
        public static final int id_layout_showatt = 0x7f080179;
        public static final int id_listview_geopoint_file_desc = 0x7f08017a;
        public static final int id_listview_geopointdesc = 0x7f08017b;
        public static final int id_listview_mulmedia = 0x7f08017c;
        public static final int id_location_checkBox = 0x7f08017d;
        public static final int id_location_editTextX = 0x7f08017e;
        public static final int id_location_editTextY = 0x7f08017f;
        public static final int id_location_radio0 = 0x7f080180;
        public static final int id_location_radio1 = 0x7f080181;
        public static final int id_location_radioGroup = 0x7f080182;
        public static final int id_main_query = 0x7f080183;
        public static final int id_map_orientationzoom = 0x7f080184;
        public static final int id_mapgroup_list = 0x7f080185;
        public static final int id_mapitem_list = 0x7f080186;
        public static final int id_maptype_layout = 0x7f080187;
        public static final int id_maptype_ok = 0x7f080188;
        public static final int id_mdl_data = 0x7f080189;
        public static final int id_mdl_desc = 0x7f08018a;
        public static final int id_mdl_loc = 0x7f08018b;
        public static final int id_mdl_name = 0x7f08018c;
        public static final int id_mdl_person = 0x7f08018d;
        public static final int id_mdlservice_list = 0x7f08018e;
        public static final int id_measureline = 0x7f08018f;
        public static final int id_measureline_close = 0x7f080190;
        public static final int id_media_audio = 0x7f080191;
        public static final int id_media_photo = 0x7f080192;
        public static final int id_media_vedio = 0x7f080193;
        public static final int id_menu_add = 0x7f080194;
        public static final int id_menu_add_file = 0x7f080195;
        public static final int id_menu_delete = 0x7f080196;
        public static final int id_menu_delete_file = 0x7f080197;
        public static final int id_menu_googleView = 0x7f080198;
        public static final int id_menu_gsView = 0x7f080199;
        public static final int id_menu_insert = 0x7f08019a;
        public static final int id_menu_play = 0x7f08019b;
        public static final int id_menu_section_atti = 0x7f08019c;
        public static final int id_menu_section_fossil = 0x7f08019d;
        public static final int id_menu_section_gpoint = 0x7f08019e;
        public static final int id_menu_section_more1 = 0x7f08019f;
        public static final int id_menu_section_more2 = 0x7f0801a0;
        public static final int id_menu_section_photo = 0x7f0801a1;
        public static final int id_menu_section_sample = 0x7f0801a2;
        public static final int id_menu_section_sketch = 0x7f0801a3;
        public static final int id_menu_section_slayer = 0x7f0801a4;
        public static final int id_menu_section_suery = 0x7f0801a5;
        public static final int id_menu_tdtView = 0x7f0801a6;
        public static final int id_menu_update = 0x7f0801a7;
        public static final int id_menu_viewtypelayout = 0x7f0801a8;
        public static final int id_model3d_list = 0x7f0801a9;
        public static final int id_model3d_mainactivity = 0x7f0801aa;
        public static final int id_model3d_mapview = 0x7f0801ab;
        public static final int id_mulmedia_direction = 0x7f0801ac;
        public static final int id_mulmedia_fileno = 0x7f0801ad;
        public static final int id_mulmedia_id = 0x7f0801ae;
        public static final int id_mulmedia_mainid = 0x7f0801af;
        public static final int id_mulmedia_mediaid = 0x7f0801b0;
        public static final int id_mulmedia_note = 0x7f0801b1;
        public static final int id_mulmedia_orderid = 0x7f0801b2;
        public static final int id_mulmedia_sampleid = 0x7f0801b3;
        public static final int id_net_dlgimage = 0x7f0801b4;
        public static final int id_net_imageView = 0x7f0801b5;
        public static final int id_password = 0x7f0801b6;
        public static final int id_photo_cancel = 0x7f0801b7;
        public static final int id_photo_info = 0x7f0801b8;
        public static final int id_photo_menu = 0x7f0801b9;
        public static final int id_photo_save = 0x7f0801ba;
        public static final int id_photo_surfaceView = 0x7f0801bb;
        public static final int id_radioGroup_file_menu = 0x7f0801bc;
        public static final int id_radioGroup_menu = 0x7f0801bd;
        public static final int id_radioGroup_menu1 = 0x7f0801be;
        public static final int id_radioGroup_selector = 0x7f0801bf;
        public static final int id_save_layout = 0x7f0801c0;
        public static final int id_sectionlib_group1 = 0x7f0801c1;
        public static final int id_sectionlib_group2 = 0x7f0801c2;
        public static final int id_selector_audio = 0x7f0801c3;
        public static final int id_selector_photo = 0x7f0801c4;
        public static final int id_selector_vedio = 0x7f0801c5;
        public static final int id_setgooglemaptype = 0x7f0801c6;
        public static final int id_showhisresource = 0x7f0801c7;
        public static final int id_splitscreen_mapview1 = 0x7f0801c8;
        public static final int id_splitscreen_mapview2 = 0x7f0801c9;
        public static final int id_splitscreen_mapview3 = 0x7f0801ca;
        public static final int id_splitscreen_mapview4 = 0x7f0801cb;
        public static final int id_ssfb = 0x7f0801cc;
        public static final int id_tab_View = 0x7f0801cd;
        public static final int id_tab_list = 0x7f0801ce;
        public static final int id_tab_list_img = 0x7f0801cf;
        public static final int id_tab_map = 0x7f0801d0;
        public static final int id_tab_map_img = 0x7f0801d1;
        public static final int id_textView_angle = 0x7f0801d2;
        public static final int id_top_menu = 0x7f0801d3;
        public static final int id_tree = 0x7f0801d4;
        public static final int id_treenode_icon = 0x7f0801d5;
        public static final int id_treenode_label = 0x7f0801d6;
        public static final int id_type_selector = 0x7f0801d7;
        public static final int id_update_progress = 0x7f0801d8;
        public static final int id_user = 0x7f0801d9;
        public static final int id_webView_bigdataservice = 0x7f0801da;
        public static final int id_webservice_webView = 0x7f0801db;
        public static final int id_yksb_progress = 0x7f0801dc;
        public static final int id_yksb_result1 = 0x7f0801dd;
        public static final int id_yksb_result2 = 0x7f0801de;
        public static final int id_yksb_result3 = 0x7f0801df;
        public static final int id_yksb_result4 = 0x7f0801e0;
        public static final int id_yksb_result5 = 0x7f0801e1;
        public static final int id_yksb_resultlyt = 0x7f0801e2;
        public static final int id_yksg_bdbk = 0x7f0801e3;
        public static final int id_yksg_result = 0x7f0801e4;
        public static final int ifRoom = 0x7f0801e5;
        public static final int image = 0x7f0801e6;
        public static final int imageButton1 = 0x7f0801e7;
        public static final int imageView = 0x7f0801e8;
        public static final int imageView1 = 0x7f0801e9;
        public static final int imageView2 = 0x7f0801ea;
        public static final int imageView3 = 0x7f0801eb;
        public static final int imageView4 = 0x7f0801ec;
        public static final int imageView5 = 0x7f0801ed;
        public static final int imageView6 = 0x7f0801ee;
        public static final int imageView7 = 0x7f0801ef;
        public static final int imageView_menu = 0x7f0801f0;
        public static final int imageView_return = 0x7f0801f1;
        public static final int imgViewpath = 0x7f0801f2;
        public static final int info = 0x7f0801f3;
        public static final int label1 = 0x7f0801f4;
        public static final int labelDx = 0x7f0801f5;
        public static final int labelDy = 0x7f0801f6;
        public static final int lay_laycode = 0x7f0801f7;
        public static final int lay_secpoint = 0x7f0801f8;
        public static final int lay_slope_l = 0x7f0801f9;
        public static final int lay_sortid = 0x7f0801fa;
        public static final int layerlist = 0x7f0801fb;
        public static final int layout_first = 0x7f0801fc;
        public static final int layout_hisdata = 0x7f0801fd;
        public static final int layout_hisdata_cataloglist = 0x7f0801fe;
        public static final int layout_hisdata_mapcontent = 0x7f0801ff;
        public static final int layout_hisdata_mapcontent_menu = 0x7f080200;
        public static final int layout_hisdata_mapcontent_menu_file = 0x7f080201;
        public static final int layout_hisdata_mapcontent_menu_line = 0x7f080202;
        public static final int layout_hisdata_mapcontent_menu_point = 0x7f080203;
        public static final int layout_hisdata_mapcontent_menu_search = 0x7f080204;
        public static final int layout_hisdata_mapcontent_title = 0x7f080205;
        public static final int layout_hisdata_mapcontent_title_menu = 0x7f080206;
        public static final int layout_hisdata_mapcontent_title_ok = 0x7f080207;
        public static final int layout_hisdata_mapcontent_title_text = 0x7f080208;
        public static final int layout_hisdata_mapcontent_view = 0x7f080209;
        public static final int layout_hisdata_mapcontent_viewlayout = 0x7f08020a;
        public static final int layout_hisdata_title = 0x7f08020b;
        public static final int layout_hisdata_title_imageView = 0x7f08020c;
        public static final int layout_hisdata_title_text = 0x7f08020d;
        public static final int lbl_toolbar_edit_Area = 0x7f08020e;
        public static final int left_bottom = 0x7f08020f;
        public static final int left_top = 0x7f080210;
        public static final int line1 = 0x7f080211;
        public static final int line3 = 0x7f080212;
        public static final int line_radiogroup = 0x7f080213;
        public static final int line_radiogroup1 = 0x7f080214;
        public static final int line_radiogroup2 = 0x7f080215;
        public static final int line_radiogroup3 = 0x7f080216;
        public static final int linearLayout1 = 0x7f080217;
        public static final int linearLayout2 = 0x7f080218;
        public static final int linearLayout3 = 0x7f080219;
        public static final int listEngPoint = 0x7f08021a;
        public static final int listFossil = 0x7f08021b;
        public static final int listGpoint = 0x7f08021c;
        public static final int listHydPoint = 0x7f08021d;
        public static final int listLayer = 0x7f08021e;
        public static final int listMode = 0x7f08021f;
        public static final int listPhoto = 0x7f080220;
        public static final int listSample = 0x7f080221;
        public static final int listSecatt = 0x7f080222;
        public static final int listSection = 0x7f080223;
        public static final int listSketch = 0x7f080224;
        public static final int listSurvey = 0x7f080225;
        public static final int listView1 = 0x7f080226;
        public static final int listView2 = 0x7f080227;
        public static final int listView_content = 0x7f080228;
        public static final int list_item = 0x7f080229;
        public static final int mainRelativeLayout = 0x7f08022a;
        public static final int main_radiogroup = 0x7f08022b;
        public static final int main_sketch_menu = 0x7f08022c;
        public static final int main_view = 0x7f08022d;
        public static final int map_group_tag = 0x7f08022e;
        public static final int map_group_title = 0x7f08022f;
        public static final int map_item_check = 0x7f080230;
        public static final int map_item_title = 0x7f080231;
        public static final int media_actions = 0x7f080232;
        public static final int menu = 0x7f080233;
        public static final int menu01 = 0x7f080234;
        public static final int menu02 = 0x7f080235;
        public static final int menu03 = 0x7f080236;
        public static final int menu04 = 0x7f080237;
        public static final int menu_dataquery_comm = 0x7f080238;
        public static final int menu_dataquery_largescale = 0x7f080239;
        public static final int menu_dataquery_list = 0x7f08023a;
        public static final int menu_dataquery_setting = 0x7f08023b;
        public static final int menu_free_p_copy = 0x7f08023c;
        public static final int menu_free_p_del = 0x7f08023d;
        public static final int menu_free_p_link_att = 0x7f08023e;
        public static final int menu_free_p_modi_param = 0x7f08023f;
        public static final int menu_free_p_more_1 = 0x7f080240;
        public static final int menu_free_p_more_2 = 0x7f080241;
        public static final int menu_free_p_move = 0x7f080242;
        public static final int menu_free_p_new_note = 0x7f080243;
        public static final int menu_free_p_new_sub = 0x7f080244;
        public static final int menu_geochem = 0x7f080245;
        public static final int menu_line_copy = 0x7f080246;
        public static final int menu_line_del = 0x7f080247;
        public static final int menu_line_edit = 0x7f080248;
        public static final int menu_line_link_att = 0x7f080249;
        public static final int menu_line_modi_att = 0x7f08024a;
        public static final int menu_line_modi_param = 0x7f08024b;
        public static final int menu_line_more_1 = 0x7f08024c;
        public static final int menu_line_more_2 = 0x7f08024d;
        public static final int menu_line_more_3 = 0x7f08024e;
        public static final int menu_line_move = 0x7f08024f;
        public static final int menu_line_new = 0x7f080250;
        public static final int menu_line_new1 = 0x7f080251;
        public static final int menu_line_new2 = 0x7f080252;
        public static final int menu_line_new3 = 0x7f080253;
        public static final int menu_map = 0x7f080254;
        public static final int menu_point_Addbygps = 0x7f080255;
        public static final int menu_point_copy = 0x7f080256;
        public static final int menu_point_del = 0x7f080257;
        public static final int menu_point_link_att = 0x7f080258;
        public static final int menu_point_modi_att = 0x7f080259;
        public static final int menu_point_modi_param = 0x7f08025a;
        public static final int menu_point_more_1 = 0x7f08025b;
        public static final int menu_point_more_2 = 0x7f08025c;
        public static final int menu_point_move = 0x7f08025d;
        public static final int menu_point_new = 0x7f08025e;
        public static final int menu_route = 0x7f08025f;
        public static final int menu_section = 0x7f080260;
        public static final int menu_section_del = 0x7f080261;
        public static final int menu_section_del_innertbl = 0x7f080262;
        public static final int menu_section_link_att = 0x7f080263;
        public static final int menu_section_modi_att = 0x7f080264;
        public static final int menu_section_modi_att_innertbl = 0x7f080265;
        public static final int menu_section_modi_lib = 0x7f080266;
        public static final int menu_section_more_1 = 0x7f080267;
        public static final int menu_section_more_2 = 0x7f080268;
        public static final int menu_section_move = 0x7f080269;
        public static final int menu_section_new = 0x7f08026a;
        public static final int menu_section_new_innertbl = 0x7f08026b;
        public static final int menu_section_proj = 0x7f08026c;
        public static final int menu_section_store_path = 0x7f08026d;
        public static final int menu_service_bigdata = 0x7f08026e;
        public static final int menu_service_list = 0x7f08026f;
        public static final int menu_service_load = 0x7f080270;
        public static final int menu_sketch_Eraser = 0x7f080271;
        public static final int menu_sketch_file = 0x7f080272;
        public static final int menu_sketch_line = 0x7f080273;
        public static final int menu_sketch_point = 0x7f080274;
        public static final int menu_sketch_save = 0x7f080275;
        public static final int menu_sketch_search = 0x7f080276;
        public static final int menu_sketch_setbk = 0x7f080277;
        public static final int menu_sketch_setcolor = 0x7f080278;
        public static final int menu_sketch_setpen = 0x7f080279;
        public static final int menu_sketch_setwidth = 0x7f08027a;
        public static final int menuset_button_cancel = 0x7f08027b;
        public static final int menuset_button_ok = 0x7f08027c;
        public static final int middle = 0x7f08027d;
        public static final int multiply = 0x7f08027e;
        public static final int mydlg = 0x7f08027f;
        public static final int mydlg_spinner1 = 0x7f080280;
        public static final int mydlg_spinner2 = 0x7f080281;
        public static final int mydlg_spinner3 = 0x7f080282;
        public static final int mydlg_textView1 = 0x7f080283;
        public static final int mydlg_textView2 = 0x7f080284;
        public static final int mydlg_textView3 = 0x7f080285;
        public static final int never = 0x7f080286;
        public static final int newSec_OK = 0x7f080287;
        public static final int newSec_cancel = 0x7f080288;
        public static final int newSec_text = 0x7f080289;
        public static final int newSeccode = 0x7f08028a;
        public static final int none = 0x7f08028b;
        public static final int normal = 0x7f08028c;
        public static final int parentPanel = 0x7f08028d;
        public static final int pho_describe = 0x7f08028e;
        public static final int pho_direction = 0x7f08028f;
        public static final int pho_laycode = 0x7f080290;
        public static final int pho_number = 0x7f080291;
        public static final int pho_phocode = 0x7f080292;
        public static final int pho_secpoint = 0x7f080293;
        public static final int pho_slope_l = 0x7f080294;
        public static final int pho_sortid = 0x7f080295;
        public static final int photo_btnDelete = 0x7f080296;
        public static final int photo_btnInsert = 0x7f080297;
        public static final int photo_btnModify = 0x7f080298;
        public static final int photo_editor_desc = 0x7f080299;
        public static final int photo_list = 0x7f08029a;
        public static final int photo_relativeLayout = 0x7f08029b;
        public static final int photo_text_remark = 0x7f08029c;
        public static final int photolist = 0x7f08029d;
        public static final int point_add = 0x7f08029e;
        public static final int point_del = 0x7f08029f;
        public static final int point_insert = 0x7f0802a0;
        public static final int point_layout = 0x7f0802a1;
        public static final int point_radiogroup = 0x7f0802a2;
        public static final int point_radiogroup1 = 0x7f0802a3;
        public static final int point_radiogroup2 = 0x7f0802a4;
        public static final int point_radiogroup_1 = 0x7f0802a5;
        public static final int point_radiogroup_2 = 0x7f0802a6;
        public static final int point_spinner = 0x7f0802a7;
        public static final int point_update = 0x7f0802a8;
        public static final int popTextView = 0x7f0802a9;
        public static final int progress_circular = 0x7f0802aa;
        public static final int progress_horizontal = 0x7f0802ab;
        public static final int radio = 0x7f0802ac;
        public static final int receivemsgBtn = 0x7f0802ad;
        public static final int rectOnCamera = 0x7f0802ae;
        public static final int relativeLayout1 = 0x7f0802af;
        public static final int right_bottom = 0x7f0802b0;
        public static final int right_top = 0x7f0802b1;
        public static final int rocktypedlg = 0x7f0802b2;
        public static final int routesum_button_cancel = 0x7f0802b3;
        public static final int routesum_button_ok = 0x7f0802b4;
        public static final int sam_analyse = 0x7f0802b5;
        public static final int sam_code = 0x7f0802b6;
        public static final int sam_geounit = 0x7f0802b7;
        public static final int sam_laycode = 0x7f0802b8;
        public static final int sam_name = 0x7f0802b9;
        public static final int sam_sampling = 0x7f0802ba;
        public static final int sam_secpoint = 0x7f0802bb;
        public static final int sam_slope_l = 0x7f0802bc;
        public static final int sam_sortid = 0x7f0802bd;
        public static final int sam_type = 0x7f0802be;
        public static final int samplelist = 0x7f0802bf;
        public static final int scanBluetoothBt = 0x7f0802c0;
        public static final int screen = 0x7f0802c1;
        public static final int scrollIndicatorDown = 0x7f0802c2;
        public static final int scrollIndicatorUp = 0x7f0802c3;
        public static final int scrollView = 0x7f0802c4;
        public static final int scrollView1 = 0x7f0802c5;
        public static final int search_badge = 0x7f0802c6;
        public static final int search_bar = 0x7f0802c7;
        public static final int search_button = 0x7f0802c8;
        public static final int search_close_btn = 0x7f0802c9;
        public static final int search_edit_frame = 0x7f0802ca;
        public static final int search_go_btn = 0x7f0802cb;
        public static final int search_mag_icon = 0x7f0802cc;
        public static final int search_plate = 0x7f0802cd;
        public static final int search_src_text = 0x7f0802ce;
        public static final int search_voice_btn = 0x7f0802cf;
        public static final int sec_behind = 0x7f0802d0;
        public static final int sec_camera = 0x7f0802d1;
        public static final int sec_date = 0x7f0802d2;
        public static final int sec_date_end = 0x7f0802d3;
        public static final int sec_date_sta = 0x7f0802d4;
        public static final int sec_devider = 0x7f0802d5;
        public static final int sec_direction = 0x7f0802d6;
        public static final int sec_former = 0x7f0802d7;
        public static final int sec_gasurvey = 0x7f0802d8;
        public static final int sec_height1 = 0x7f0802d9;
        public static final int sec_height2 = 0x7f0802da;
        public static final int sec_latitude1 = 0x7f0802db;
        public static final int sec_latitude2 = 0x7f0802dc;
        public static final int sec_length = 0x7f0802dd;
        public static final int sec_longitude1 = 0x7f0802de;
        public static final int sec_longitude2 = 0x7f0802df;
        public static final int sec_mapcode = 0x7f0802e0;
        public static final int sec_mapname = 0x7f0802e1;
        public static final int sec_photoer = 0x7f0802e2;
        public static final int sec_recorder = 0x7f0802e3;
        public static final int sec_sampling = 0x7f0802e4;
        public static final int sec_scale = 0x7f0802e5;
        public static final int sec_seccode = 0x7f0802e6;
        public static final int sec_secname = 0x7f0802e7;
        public static final int sec_verify = 0x7f0802e8;
        public static final int sec_xx1 = 0x7f0802e9;
        public static final int sec_xx2 = 0x7f0802ea;
        public static final int sec_yy1 = 0x7f0802eb;
        public static final int sec_yy2 = 0x7f0802ec;
        public static final int seca_code = 0x7f0802ed;
        public static final int seca_dip = 0x7f0802ee;
        public static final int seca_dip_ang = 0x7f0802ef;
        public static final int seca_laycode = 0x7f0802f0;
        public static final int seca_secpoint = 0x7f0802f1;
        public static final int seca_slope_l = 0x7f0802f2;
        public static final int seca_sortid = 0x7f0802f3;
        public static final int seca_trend = 0x7f0802f4;
        public static final int seca_type = 0x7f0802f5;
        public static final int secattlist = 0x7f0802f6;
        public static final int section_att_radiogroup = 0x7f0802f7;
        public static final int section_att_radiogroup_innertbl = 0x7f0802f8;
        public static final int section_radiogroup = 0x7f0802f9;
        public static final int section_radiogroup1 = 0x7f0802fa;
        public static final int section_radiogroup2 = 0x7f0802fb;
        public static final int sectionlist = 0x7f0802fc;
        public static final int sectionwebView = 0x7f0802fd;
        public static final int select_dialog_listview = 0x7f0802fe;
        public static final int sendmsgBtn = 0x7f0802ff;
        public static final int service_query_radiogroup = 0x7f080300;
        public static final int setting = 0x7f080301;
        public static final int shortcut = 0x7f080302;
        public static final int show = 0x7f080303;
        public static final int showCustom = 0x7f080304;
        public static final int showHome = 0x7f080305;
        public static final int showTitle = 0x7f080306;
        public static final int simple_item_0 = 0x7f080307;
        public static final int simple_item_1 = 0x7f080308;
        public static final int ske_code = 0x7f080309;
        public static final int ske_describe = 0x7f08030a;
        public static final int ske_geopoint = 0x7f08030b;
        public static final int ske_laycode = 0x7f08030c;
        public static final int ske_scale = 0x7f08030d;
        public static final int ske_secpoint = 0x7f08030e;
        public static final int ske_slope_l = 0x7f08030f;
        public static final int ske_sortid = 0x7f080310;
        public static final int ske_title = 0x7f080311;
        public static final int skecth_button_cancel = 0x7f080312;
        public static final int sketch_btnReturn = 0x7f080313;
        public static final int sketch_btnReturn1 = 0x7f080314;
        public static final int sketch_button_ok = 0x7f080315;
        public static final int sketch_title = 0x7f080316;
        public static final int sketch_title1 = 0x7f080317;
        public static final int slidingLayout = 0x7f080318;
        public static final int sms_content = 0x7f080319;
        public static final int sms_sent = 0x7f08031a;
        public static final int sms_sent_to_num = 0x7f08031b;
        public static final int soundViewpath = 0x7f08031c;
        public static final int spacer = 0x7f08031d;
        public static final int split_action_bar = 0x7f08031e;
        public static final int src_atop = 0x7f08031f;
        public static final int src_in = 0x7f080320;
        public static final int src_over = 0x7f080321;
        public static final int startup_btnCancel = 0x7f080322;
        public static final int startup_btnOK = 0x7f080323;
        public static final int status_bar_latest_event_content = 0x7f080324;
        public static final int submit_area = 0x7f080325;
        public static final int sur_azimuth = 0x7f080326;
        public static final int sur_from_x = 0x7f080327;
        public static final int sur_from_y = 0x7f080328;
        public static final int sur_from_z = 0x7f080329;
        public static final int sur_grade = 0x7f08032a;
        public static final int sur_line_code = 0x7f08032b;
        public static final int sur_secpoint = 0x7f08032c;
        public static final int sur_slope_l = 0x7f08032d;
        public static final int sur_sortid = 0x7f08032e;
        public static final int sur_to_x = 0x7f08032f;
        public static final int sur_to_y = 0x7f080330;
        public static final int sur_to_z = 0x7f080331;
        public static final int surveylist = 0x7f080332;
        public static final int sys_config_scrollview = 0x7f080333;
        public static final int tabMode = 0x7f080334;
        public static final int takePic = 0x7f080335;
        public static final int text = 0x7f080336;
        public static final int text2 = 0x7f080337;
        public static final int textSpacerNoButtons = 0x7f080338;
        public static final int textView = 0x7f080339;
        public static final int textView1 = 0x7f08033a;
        public static final int textView10 = 0x7f08033b;
        public static final int textView11 = 0x7f08033c;
        public static final int textView111 = 0x7f08033d;
        public static final int textView12 = 0x7f08033e;
        public static final int textView13 = 0x7f08033f;
        public static final int textView14 = 0x7f080340;
        public static final int textView141 = 0x7f080341;
        public static final int textView15 = 0x7f080342;
        public static final int textView16 = 0x7f080343;
        public static final int textView2 = 0x7f080344;
        public static final int textView3 = 0x7f080345;
        public static final int textView4 = 0x7f080346;
        public static final int textView5 = 0x7f080347;
        public static final int textView6 = 0x7f080348;
        public static final int textView7 = 0x7f080349;
        public static final int textView8 = 0x7f08034a;
        public static final int textView9 = 0x7f08034b;
        public static final int textView_path = 0x7f08034c;
        public static final int textView_text = 0x7f08034d;
        public static final int textView_title = 0x7f08034e;
        public static final int time = 0x7f08034f;
        public static final int title = 0x7f080350;
        public static final int title_template = 0x7f080351;
        public static final int toolbar_edit_comm = 0x7f080352;
        public static final int toolbar_edit_entity = 0x7f080353;
        public static final int toolbar_edit_point = 0x7f080354;
        public static final int toolbar_info_query = 0x7f080355;
        public static final int topPanel = 0x7f080356;
        public static final int txtDesc = 0x7f080357;
        public static final int txtId = 0x7f080358;
        public static final int txtLog = 0x7f080359;
        public static final int txtResult = 0x7f08035a;
        public static final int up = 0x7f08035b;
        public static final int useLogo = 0x7f08035c;
        public static final int videoView = 0x7f08035d;
        public static final int videoView1 = 0x7f08035e;
        public static final int warpper_hor1 = 0x7f08035f;
        public static final int withText = 0x7f080360;
        public static final int wrap_content = 0x7f080361;
        public static final int xView = 0x7f080362;
        public static final int xorgView = 0x7f080363;
        public static final int yView = 0x7f080364;
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 0x7f090000;
        public static final int abc_config_activityShortDur = 0x7f090001;
        public static final int abc_max_action_buttons = 0x7f090002;
        public static final int cancel_button_image_alpha = 0x7f090003;
        public static final int status_bar_notification_info_maxnum = 0x7f090004;
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 0x7f0a0000;
        public static final int abc_action_bar_up_container = 0x7f0a0001;
        public static final int abc_action_bar_view_list_nav_layout = 0x7f0a0002;
        public static final int abc_action_menu_item_layout = 0x7f0a0003;
        public static final int abc_action_menu_layout = 0x7f0a0004;
        public static final int abc_action_mode_bar = 0x7f0a0005;
        public static final int abc_action_mode_close_item_material = 0x7f0a0006;
        public static final int abc_activity_chooser_view = 0x7f0a0007;
        public static final int abc_activity_chooser_view_list_item = 0x7f0a0008;
        public static final int abc_alert_dialog_button_bar_material = 0x7f0a0009;
        public static final int abc_alert_dialog_material = 0x7f0a000a;
        public static final int abc_dialog_title_material = 0x7f0a000b;
        public static final int abc_expanded_menu_layout = 0x7f0a000c;
        public static final int abc_list_menu_item_checkbox = 0x7f0a000d;
        public static final int abc_list_menu_item_icon = 0x7f0a000e;
        public static final int abc_list_menu_item_layout = 0x7f0a000f;
        public static final int abc_list_menu_item_radio = 0x7f0a0010;
        public static final int abc_popup_menu_item_layout = 0x7f0a0011;
        public static final int abc_screen_content_include = 0x7f0a0012;
        public static final int abc_screen_simple = 0x7f0a0013;
        public static final int abc_screen_simple_overlay_action_mode = 0x7f0a0014;
        public static final int abc_screen_toolbar = 0x7f0a0015;
        public static final int abc_search_dropdown_item_icons_2line = 0x7f0a0016;
        public static final int abc_search_view = 0x7f0a0017;
        public static final int abc_select_dialog_material = 0x7f0a0018;
        public static final int acticity_splitscreen = 0x7f0a0019;
        public static final int activity_bddemo = 0x7f0a001a;
        public static final int activity_bdsinfo = 0x7f0a001b;
        public static final int activity_bluetooth = 0x7f0a001c;
        public static final int activity_recievemsg = 0x7f0a001d;
        public static final int activity_sendmsg = 0x7f0a001e;
        public static final int activity_top = 0x7f0a001f;
        public static final int ao_att_list_cell = 0x7f0a0020;
        public static final int ao_att_list_item = 0x7f0a0021;
        public static final int ao_attribute_activity = 0x7f0a0022;
        public static final int ao_attribute_button = 0x7f0a0023;
        public static final int ao_attribute_desc = 0x7f0a0024;
        public static final int ao_attribute_editor = 0x7f0a0025;
        public static final int ao_attribute_group = 0x7f0a0026;
        public static final int ao_attribute_item = 0x7f0a0027;
        public static final int ao_attribute_seperator = 0x7f0a0028;
        public static final int ao_attribute_static_text = 0x7f0a0029;
        public static final int ao_attributes_container = 0x7f0a002a;
        public static final int ao_dicedittype = 0x7f0a002b;
        public static final int ao_dlg_attribute_link = 0x7f0a002c;
        public static final int ao_dlg_dict_edit = 0x7f0a002d;
        public static final int ao_dlg_dictionary = 0x7f0a002e;
        public static final int ao_dlg_extdictionary = 0x7f0a002f;
        public static final int ao_dlg_gps_settings = 0x7f0a0030;
        public static final int ao_dlg_orientation = 0x7f0a0031;
        public static final int ao_dlg_orientation1 = 0x7f0a0032;
        public static final int ao_draw_sketch = 0x7f0a0033;
        public static final int ao_screen_content = 0x7f0a0034;
        public static final int ao_sys_config = 0x7f0a0035;
        public static final int ao_toolbar_edit_comm = 0x7f0a0036;
        public static final int ao_vscroll_view = 0x7f0a0037;
        public static final int aqfewqefwqefe = 0x7f0a0038;
        public static final int att_atti = 0x7f0a0039;
        public static final int att_attilist = 0x7f0a003a;
        public static final int att_geopoint_desc = 0x7f0a003b;
        public static final int att_geopoint_desclist = 0x7f0a003c;
        public static final int att_geopoint_file_desc = 0x7f0a003d;
        public static final int att_gravel = 0x7f0a003e;
        public static final int att_gravellist = 0x7f0a003f;
        public static final int att_list_cell = 0x7f0a0040;
        public static final int att_list_item = 0x7f0a0041;
        public static final int bigdatawebserviceview = 0x7f0a0042;
        public static final int bottom_data_query = 0x7f0a0043;
        public static final int bottom_free_p_edit = 0x7f0a0044;
        public static final int bottom_free_p_edit1 = 0x7f0a0045;
        public static final int bottom_free_p_edit2 = 0x7f0a0046;
        public static final int bottom_line_edit = 0x7f0a0047;
        public static final int bottom_line_edit1 = 0x7f0a0048;
        public static final int bottom_line_edit2 = 0x7f0a0049;
        public static final int bottom_line_edit3 = 0x7f0a004a;
        public static final int bottom_main = 0x7f0a004b;
        public static final int bottom_model3d_menu = 0x7f0a004c;
        public static final int bottom_point_edit = 0x7f0a004d;
        public static final int bottom_point_edit1 = 0x7f0a004e;
        public static final int bottom_point_edit2 = 0x7f0a004f;
        public static final int bottom_point_edit_1 = 0x7f0a0050;
        public static final int bottom_point_edit_2 = 0x7f0a0051;
        public static final int bottom_seclib_edit1 = 0x7f0a0052;
        public static final int bottom_seclib_edit2 = 0x7f0a0053;
        public static final int bottom_section_att_edit = 0x7f0a0054;
        public static final int bottom_section_att_edit_for_inner_table = 0x7f0a0055;
        public static final int bottom_section_edit = 0x7f0a0056;
        public static final int bottom_section_edit1 = 0x7f0a0057;
        public static final int bottom_section_edit2 = 0x7f0a0058;
        public static final int bottom_service_query = 0x7f0a0059;
        public static final int common = 0x7f0a005a;
        public static final int common_mini = 0x7f0a005b;
        public static final int common_without_setting = 0x7f0a005c;
        public static final int copyroutedlg = 0x7f0a005d;
        public static final int data_query_result = 0x7f0a005e;
        public static final int data_queryresult_popwindow = 0x7f0a005f;
        public static final int dataquerylist = 0x7f0a0060;
        public static final int dataqueryresult_headerview = 0x7f0a0061;
        public static final int dlg_attribute_link = 0x7f0a0062;
        public static final int dlg_bigdataservice = 0x7f0a0063;
        public static final int dlg_gps_rectify = 0x7f0a0064;
        public static final int dlg_guass_maptype = 0x7f0a0065;
        public static final int dlg_listservice = 0x7f0a0066;
        public static final int dlg_maptype = 0x7f0a0067;
        public static final int dlg_mapview = 0x7f0a0068;
        public static final int dlg_modelservice = 0x7f0a0069;
        public static final int dlg_online_maptype = 0x7f0a006a;
        public static final int dlg_ptotosketch = 0x7f0a006b;
        public static final int dlg_smartspace_map = 0x7f0a006c;
        public static final int dlg_userpassword = 0x7f0a006d;
        public static final int dlg_webbigdataservice = 0x7f0a006e;
        public static final int draw_bitmap = 0x7f0a006f;
        public static final int draw_sketch = 0x7f0a0070;
        public static final int drawbitmap = 0x7f0a0071;
        public static final int drawbitmapskecth = 0x7f0a0072;
        public static final int drawvectorsketch = 0x7f0a0073;
        public static final int engpoint = 0x7f0a0074;
        public static final int engpointlist = 0x7f0a0075;
        public static final int ext_dlg_hisdataview = 0x7f0a0076;
        public static final int ext_dlg_listservice = 0x7f0a0077;
        public static final int extphotocamera = 0x7f0a0078;
        public static final int flipper = 0x7f0a0079;
        public static final int fossil = 0x7f0a007a;
        public static final int fossillist = 0x7f0a007b;
        public static final int geochem_mulsoil_layer_headerview = 0x7f0a007c;
        public static final int geochem_mulsoil_layer_itemview = 0x7f0a007d;
        public static final int geochem_mulsoil_layerlist = 0x7f0a007e;
        public static final int gislibpath = 0x7f0a007f;
        public static final int gpoint = 0x7f0a0080;
        public static final int gpointlist = 0x7f0a0081;
        public static final int gravel = 0x7f0a0082;
        public static final int html_info_webview = 0x7f0a0083;
        public static final int hydpoint = 0x7f0a0084;
        public static final int hydpointlist = 0x7f0a0085;
        public static final int image_view = 0x7f0a0086;
        public static final int imageview = 0x7f0a0087;
        public static final int imageview1 = 0x7f0a0088;
        public static final int infopopwindow = 0x7f0a0089;
        public static final int infopopwindowvaluelist = 0x7f0a008a;
        public static final int layer = 0x7f0a008b;
        public static final int layerlist = 0x7f0a008c;
        public static final int layout_baidu_voice = 0x7f0a008d;
        public static final int list_item = 0x7f0a008e;
        public static final int listitem = 0x7f0a008f;
        public static final int listservice_result = 0x7f0a0090;
        public static final int locationdlg = 0x7f0a0091;
        public static final int main = 0x7f0a0092;
        public static final int media_geochem_25w = 0x7f0a0093;
        public static final int medialist_geochem_25w = 0x7f0a0094;
        public static final int medialist_headerview = 0x7f0a0095;
        public static final int model3d_layout_view01 = 0x7f0a0096;
        public static final int model3d_layout_view02 = 0x7f0a0097;
        public static final int model3d_mianactivity = 0x7f0a0098;
        public static final int mydialog = 0x7f0a0099;
        public static final int myearthdialog = 0x7f0a009a;
        public static final int navigation = 0x7f0a009b;
        public static final int net_web_imageview = 0x7f0a009c;
        public static final int newsection = 0x7f0a009d;
        public static final int notification_media_action = 0x7f0a009e;
        public static final int notification_media_cancel_action = 0x7f0a009f;
        public static final int notification_template_big_media = 0x7f0a00a0;
        public static final int notification_template_big_media_narrow = 0x7f0a00a1;
        public static final int notification_template_lines = 0x7f0a00a2;
        public static final int notification_template_media = 0x7f0a00a3;
        public static final int notification_template_part_chronometer = 0x7f0a00a4;
        public static final int notification_template_part_time = 0x7f0a00a5;
        public static final int orientation = 0x7f0a00a6;
        public static final int orientation_1 = 0x7f0a00a7;
        public static final int photo = 0x7f0a00a8;
        public static final int photo_desc = 0x7f0a00a9;
        public static final int photo_desc_item = 0x7f0a00aa;
        public static final int photocamera = 0x7f0a00ab;
        public static final int photocamera1 = 0x7f0a00ac;
        public static final int photolist = 0x7f0a00ad;
        public static final int pmxj = 0x7f0a00ae;
        public static final int point = 0x7f0a00af;
        public static final int pop_window = 0x7f0a00b0;
        public static final int rocktype = 0x7f0a00b1;
        public static final int routesum = 0x7f0a00b2;
        public static final int sample = 0x7f0a00b3;
        public static final int samplelist = 0x7f0a00b4;
        public static final int sdk2_api = 0x7f0a00b5;
        public static final int sdview = 0x7f0a00b6;
        public static final int secatt = 0x7f0a00b7;
        public static final int secattlist = 0x7f0a00b8;
        public static final int section = 0x7f0a00b9;
        public static final int sectionlist = 0x7f0a00ba;
        public static final int sectionmain = 0x7f0a00bb;
        public static final int sectionmanage = 0x7f0a00bc;
        public static final int secwebview = 0x7f0a00bd;
        public static final int select_dialog_item_material = 0x7f0a00be;
        public static final int select_dialog_multichoice_material = 0x7f0a00bf;
        public static final int select_dialog_singlechoice_material = 0x7f0a00c0;
        public static final int setting_menu = 0x7f0a00c1;
        public static final int simple_list_item_for_autocomplete = 0x7f0a00c2;
        public static final int simple_list_item_for_bigdataquery = 0x7f0a00c3;
        public static final int simple_list_item_for_listservicecontent = 0x7f0a00c4;
        public static final int simple_list_item_for_listserviceroot = 0x7f0a00c5;
        public static final int simple_list_item_for_mapcatlog = 0x7f0a00c6;
        public static final int simple_list_item_for_mapitem = 0x7f0a00c7;
        public static final int simple_list_item_for_modellist = 0x7f0a00c8;
        public static final int simple_list_item_for_textgallery = 0x7f0a00c9;
        public static final int sketch = 0x7f0a00ca;
        public static final int sketchlist = 0x7f0a00cb;
        public static final int softupdate_progress = 0x7f0a00cc;
        public static final int soundview = 0x7f0a00cd;
        public static final int startup = 0x7f0a00ce;
        public static final int support_simple_spinner_dropdown_item = 0x7f0a00cf;
        public static final int survey = 0x7f0a00d0;
        public static final int surveylist = 0x7f0a00d1;
        public static final int sys_config = 0x7f0a00d2;
        public static final int testlayout = 0x7f0a00d3;
        public static final int toolbar_addpoint = 0x7f0a00d4;
        public static final int toolbar_edit_comm = 0x7f0a00d5;
        public static final int toolbar_edit_comm1 = 0x7f0a00d6;
        public static final int toolbar_edit_entity = 0x7f0a00d7;
        public static final int toolbar_info_query = 0x7f0a00d8;
        public static final int twegf = 0x7f0a00d9;
        public static final int userdef_audio = 0x7f0a00da;
        public static final int video = 0x7f0a00db;
        public static final int videoview = 0x7f0a00dc;
        public static final int webserviceview = 0x7f0a00dd;
        public static final int webview = 0x7f0a00de;
        public static final int yksb_camera = 0x7f0a00df;
        public static final int yksb_result_activity = 0x7f0a00e0;
    }

    public static final class mipmap {
        public static final int ic_launcher = 0x7f0b0000;
    }

    public static final class raw {
        public static final int bdspeech_recognition_cancel = 0x7f0c0000;
        public static final int bdspeech_recognition_error = 0x7f0c0001;
        public static final int bdspeech_recognition_start = 0x7f0c0002;
        public static final int bdspeech_recognition_success = 0x7f0c0003;
        public static final int bdspeech_speech_end = 0x7f0c0004;
        public static final int droidsansfallback = 0x7f0c0005;
        public static final int geochemdict25w = 0x7f0c0006;
        public static final int geochemdict5w = 0x7f0c0007;
        public static final int geochemmul = 0x7f0c0008;
        public static final int geocolor = 0x7f0c0009;
        public static final int geopoint = 0x7f0c000a;
        public static final int geotszf = 0x7f0c000b;
        public static final int music1 = 0x7f0c000c;
        public static final int play = 0x7f0c000d;
        public static final int play1 = 0x7f0c000e;
        public static final int video1 = 0x7f0c000f;
    }

    public static final class string {
        public static final int ATTI_DIP = 0x7f0d0000;
        public static final int ATTI_DIPANGLE = 0x7f0d0001;
        public static final int ATTI_FLDNAME_DIP = 0x7f0d0002;
        public static final int ATTI_FLDNAME_DIPANGLE = 0x7f0d0003;
        public static final int ATTI_FLDNAME_GPOINT = 0x7f0d0004;
        public static final int ATTI_FLDNAME_LINECODE = 0x7f0d0005;
        public static final int ATTI_FLDNAME_ORDER = 0x7f0d0006;
        public static final int ATTI_FLDNAME_REMAARK = 0x7f0d0007;
        public static final int ATTI_FLDNAME_TREND = 0x7f0d0008;
        public static final int ATTI_FLDNAME_TYPE = 0x7f0d0009;
        public static final int ATTI_GPOINT = 0x7f0d000a;
        public static final int ATTI_LINECODE = 0x7f0d000b;
        public static final int ATTI_ORDER = 0x7f0d000c;
        public static final int ATTI_REMAARK = 0x7f0d000d;
        public static final int ATTI_TREND = 0x7f0d000e;
        public static final int ATTI_TYPE = 0x7f0d000f;
        public static final int Button_edit = 0x7f0d0010;
        public static final int DATAQUERY_MENU_COMMQUERY = 0x7f0d0011;
        public static final int DATAQUERY_MENU_LARGEMAPQUERY = 0x7f0d0012;
        public static final int DATAQUERY_MENU_LISTQUERY = 0x7f0d0013;
        public static final int DATAQUERY_MENU_SAMQUERY = 0x7f0d0014;
        public static final int DATAQUERY_MENU_SECLAYERQUERY = 0x7f0d0015;
        public static final int DATAQUERY_MENU_SETTING = 0x7f0d0016;
        public static final int DATASERVICE_POSSERVICE_PROMPT1 = 0x7f0d0017;
        public static final int DATASERVICE_POSSERVICE_PROMPT2 = 0x7f0d0018;
        public static final int DATASERVICE_POSSERVICE_PROMPT3 = 0x7f0d0019;
        public static final int DATASERVICE_POSSERVICE_PROMPT4 = 0x7f0d001a;
        public static final int DATASERVICE_POSSERVICE_PROMPT5 = 0x7f0d001b;
        public static final int DATASERVICE_UPDATA_PROMPT1 = 0x7f0d001c;
        public static final int DATASERVICE_UPDATA_PROMPT2 = 0x7f0d001d;
        public static final int EARTH_25ROCK_ADMCODE = 0x7f0d001e;
        public static final int EARTH_25ROCK_CHAHNV = 0x7f0d001f;
        public static final int EARTH_25ROCK_CHAHUN = 0x7f0d0020;
        public static final int EARTH_25ROCK_DDAEBA = 0x7f0d0021;
        public static final int EARTH_25ROCK_DDAEBB = 0x7f0d0022;
        public static final int EARTH_25ROCK_HTCYNO = 0x7f0d0023;
        public static final int EARTH_25ROCK_HTCYWZ = 0x7f0d0024;
        public static final int EARTH_25ROCK_HTDEM = 0x7f0d0025;
        public static final int EARTH_25ROCK_HTKHTP = 0x7f0d0026;
        public static final int EARTH_25ROCK_HTKWCF = 0x7f0d0027;
        public static final int EARTH_25ROCK_HTMDTP = 0x7f0d0028;
        public static final int EARTH_25ROCK_HTMDYS = 0x7f0d0029;
        public static final int EARTH_25ROCK_HTNOTE = 0x7f0d002a;
        public static final int EARTH_25ROCK_HTRKCL = 0x7f0d002b;
        public static final int EARTH_25ROCK_HTSBTP = 0x7f0d002c;
        public static final int EARTH_25ROCK_HTSXINX = 0x7f0d002d;
        public static final int EARTH_25ROCK_HTWRNM = 0x7f0d002e;
        public static final int EARTH_25ROCK_HTYCJL = 0x7f0d002f;
        public static final int EARTH_25ROCK_HTYCQJ = 0x7f0d0030;
        public static final int EARTH_25ROCK_HTYCQX = 0x7f0d0031;
        public static final int EARTH_25ROCK_HTYPNO = 0x7f0d0032;
        public static final int EARTH_25ROCK_MAPCODE = 0x7f0d0033;
        public static final int EARTH_25ROCK_QDAEC = 0x7f0d0034;
        public static final int EARTH_25ROCK_QDAJ = 0x7f0d0035;
        public static final int EARTH_25ROCK_ROUTE_CODE = 0x7f0d0036;
        public static final int EARTH_25ROCK_TKBEAL = 0x7f0d0037;
        public static final int EARTH_25ROCK_YRTP = 0x7f0d0038;
        public static final int EARTH_25ROCK_YSEB = 0x7f0d0039;
        public static final int EARTH_25SOIL_ADMCODE = 0x7f0d003a;
        public static final int EARTH_25SOIL_CHAHNV = 0x7f0d003b;
        public static final int EARTH_25SOIL_CHAHUN = 0x7f0d003c;
        public static final int EARTH_25SOIL_DDAEBA = 0x7f0d003d;
        public static final int EARTH_25SOIL_DDAEBB = 0x7f0d003e;
        public static final int EARTH_25SOIL_HTCYCW = 0x7f0d003f;
        public static final int EARTH_25SOIL_HTCYSD = 0x7f0d0040;
        public static final int EARTH_25SOIL_HTCYWZ = 0x7f0d0041;
        public static final int EARTH_25SOIL_HTDEM = 0x7f0d0042;
        public static final int EARTH_25SOIL_HTMDTP = 0x7f0d0043;
        public static final int EARTH_25SOIL_HTMDYS = 0x7f0d0044;
        public static final int EARTH_25SOIL_HTMZFLT = 0x7f0d0045;
        public static final int EARTH_25SOIL_HTMZFXS = 0x7f0d0046;
        public static final int EARTH_25SOIL_HTMZFYX = 0x7f0d0047;
        public static final int EARTH_25SOIL_HTNOTE = 0x7f0d0048;
        public static final int EARTH_25SOIL_HTSXINX = 0x7f0d0049;
        public static final int EARTH_25SOIL_HTWRNM = 0x7f0d004a;
        public static final int EARTH_25SOIL_HTWRQK = 0x7f0d004b;
        public static final int EARTH_25SOIL_HTYPNO = 0x7f0d004c;
        public static final int EARTH_25SOIL_MAPCODE = 0x7f0d004d;
        public static final int EARTH_25SOIL_QDAEC = 0x7f0d004e;
        public static final int EARTH_25SOIL_QDAJ = 0x7f0d004f;
        public static final int EARTH_25SOIL_ROUTE_CODE = 0x7f0d0050;
        public static final int EARTH_25SOIL_TKBEAL = 0x7f0d0051;
        public static final int EARTH_25SOIL_YRTP = 0x7f0d0052;
        public static final int EARTH_25SOIL_YSEB = 0x7f0d0053;
        public static final int EARTH_25STREAM_ADMCODE = 0x7f0d0054;
        public static final int EARTH_25STREAM_CHAHNV = 0x7f0d0055;
        public static final int EARTH_25STREAM_CHAHUN = 0x7f0d0056;
        public static final int EARTH_25STREAM_DDAEBA = 0x7f0d0057;
        public static final int EARTH_25STREAM_DDAEBB = 0x7f0d0058;
        public static final int EARTH_25STREAM_HTCYBW = 0x7f0d0059;
        public static final int EARTH_25STREAM_HTDEM = 0x7f0d005a;
        public static final int EARTH_25STREAM_HTMZFCS = 0x7f0d005b;
        public static final int EARTH_25STREAM_HTMZFXS = 0x7f0d005c;
        public static final int EARTH_25STREAM_HTMZFYJ = 0x7f0d005d;
        public static final int EARTH_25STREAM_HTNOTE = 0x7f0d005e;
        public static final int EARTH_25STREAM_HTPHV = 0x7f0d005f;
        public static final int EARTH_25STREAM_HTSTRMT = 0x7f0d0060;
        public static final int EARTH_25STREAM_HTSXINX = 0x7f0d0061;
        public static final int EARTH_25STREAM_HTWRNM = 0x7f0d0062;
        public static final int EARTH_25STREAM_HTWRQK = 0x7f0d0063;
        public static final int EARTH_25STREAM_HTYPNO = 0x7f0d0064;
        public static final int EARTH_25STREAM_HTZBFG = 0x7f0d0065;
        public static final int EARTH_25STREAM_HTZBTP = 0x7f0d0066;
        public static final int EARTH_25STREAM_MAPCODE = 0x7f0d0067;
        public static final int EARTH_25STREAM_QDAEC = 0x7f0d0068;
        public static final int EARTH_25STREAM_QDAJ = 0x7f0d0069;
        public static final int EARTH_25STREAM_ROUTE_CODE = 0x7f0d006a;
        public static final int EARTH_25STREAM_TKBEAL = 0x7f0d006b;
        public static final int EARTH_25STREAM_YRTP = 0x7f0d006c;
        public static final int EARTH_25STREAM_YSEB = 0x7f0d006d;
        public static final int EARTH_25WATER_ADMCODE = 0x7f0d006e;
        public static final int EARTH_25WATER_CHAHNV = 0x7f0d006f;
        public static final int EARTH_25WATER_CHAHUN = 0x7f0d0070;
        public static final int EARTH_25WATER_DDAEBA = 0x7f0d0071;
        public static final int EARTH_25WATER_DDAEBB = 0x7f0d0072;
        public static final int EARTH_25WATER_HTCYWZ = 0x7f0d0073;
        public static final int EARTH_25WATER_HTDEM = 0x7f0d0074;
        public static final int EARTH_25WATER_HTNOTE = 0x7f0d0075;
        public static final int EARTH_25WATER_HTSXINX = 0x7f0d0076;
        public static final int EARTH_25WATER_HTWRNM = 0x7f0d0077;
        public static final int EARTH_25WATER_HTWTDP = 0x7f0d0078;
        public static final int EARTH_25WATER_HTWTPH = 0x7f0d0079;
        public static final int EARTH_25WATER_HTWTSM = 0x7f0d007a;
        public static final int EARTH_25WATER_HTWTTP = 0x7f0d007b;
        public static final int EARTH_25WATER_HTWTWD = 0x7f0d007c;
        public static final int EARTH_25WATER_HTWTWR = 0x7f0d007d;
        public static final int EARTH_25WATER_HTWTYS = 0x7f0d007e;
        public static final int EARTH_25WATER_HTWYNO = 0x7f0d007f;
        public static final int EARTH_25WATER_MAPCODE = 0x7f0d0080;
        public static final int EARTH_25WATER_QDAEC = 0x7f0d0081;
        public static final int EARTH_25WATER_QDAJ = 0x7f0d0082;
        public static final int EARTH_25WATER_ROUTE_CODE = 0x7f0d0083;
        public static final int EARTH_25WATER_TKBEAL = 0x7f0d0084;
        public static final int EARTH_25WATER_YSEB = 0x7f0d0085;
        public static final int EARTH_CHECK = 0x7f0d0086;
        public static final int EARTH_CHECK_NAME = 0x7f0d0087;
        public static final int EARTH_DATE_STA = 0x7f0d0088;
        public static final int EARTH_DEPTH = 0x7f0d0089;
        public static final int EARTH_DESCRIBE = 0x7f0d008a;
        public static final int EARTH_FLDNAME_25ROCK_ADMCODE = 0x7f0d008b;
        public static final int EARTH_FLDNAME_25ROCK_CHAHAA = 0x7f0d008c;
        public static final int EARTH_FLDNAME_25ROCK_CHAHAB = 0x7f0d008d;
        public static final int EARTH_FLDNAME_25ROCK_CHAHNV = 0x7f0d008e;
        public static final int EARTH_FLDNAME_25ROCK_CHAHUN = 0x7f0d008f;
        public static final int EARTH_FLDNAME_25ROCK_DDAEBA = 0x7f0d0090;
        public static final int EARTH_FLDNAME_25ROCK_DDAEBB = 0x7f0d0091;
        public static final int EARTH_FLDNAME_25ROCK_HTCYNO = 0x7f0d0092;
        public static final int EARTH_FLDNAME_25ROCK_HTCYWZ = 0x7f0d0093;
        public static final int EARTH_FLDNAME_25ROCK_HTDEM = 0x7f0d0094;
        public static final int EARTH_FLDNAME_25ROCK_HTKHTP = 0x7f0d0095;
        public static final int EARTH_FLDNAME_25ROCK_HTKWCF = 0x7f0d0096;
        public static final int EARTH_FLDNAME_25ROCK_HTMDTP = 0x7f0d0097;
        public static final int EARTH_FLDNAME_25ROCK_HTMDYS = 0x7f0d0098;
        public static final int EARTH_FLDNAME_25ROCK_HTNOTE = 0x7f0d0099;
        public static final int EARTH_FLDNAME_25ROCK_HTRKCL = 0x7f0d009a;
        public static final int EARTH_FLDNAME_25ROCK_HTSBTP = 0x7f0d009b;
        public static final int EARTH_FLDNAME_25ROCK_HTSXINX = 0x7f0d009c;
        public static final int EARTH_FLDNAME_25ROCK_HTWRNM = 0x7f0d009d;
        public static final int EARTH_FLDNAME_25ROCK_HTYCJL = 0x7f0d009e;
        public static final int EARTH_FLDNAME_25ROCK_HTYCQJ = 0x7f0d009f;
        public static final int EARTH_FLDNAME_25ROCK_HTYCQX = 0x7f0d00a0;
        public static final int EARTH_FLDNAME_25ROCK_HTYPNO = 0x7f0d00a1;
        public static final int EARTH_FLDNAME_25ROCK_MAPCODE = 0x7f0d00a2;
        public static final int EARTH_FLDNAME_25ROCK_QDAEC = 0x7f0d00a3;
        public static final int EARTH_FLDNAME_25ROCK_QDAJ = 0x7f0d00a4;
        public static final int EARTH_FLDNAME_25ROCK_ROUTE_CODE = 0x7f0d00a5;
        public static final int EARTH_FLDNAME_25ROCK_TKBEAL = 0x7f0d00a6;
        public static final int EARTH_FLDNAME_25ROCK_YRTP = 0x7f0d00a7;
        public static final int EARTH_FLDNAME_25ROCK_YSEB = 0x7f0d00a8;
        public static final int EARTH_FLDNAME_25SOIL_ADMCODE = 0x7f0d00a9;
        public static final int EARTH_FLDNAME_25SOIL_CHAHNV = 0x7f0d00aa;
        public static final int EARTH_FLDNAME_25SOIL_CHAHUN = 0x7f0d00ab;
        public static final int EARTH_FLDNAME_25SOIL_DDAEBA = 0x7f0d00ac;
        public static final int EARTH_FLDNAME_25SOIL_DDAEBB = 0x7f0d00ad;
        public static final int EARTH_FLDNAME_25SOIL_HTCYCW = 0x7f0d00ae;
        public static final int EARTH_FLDNAME_25SOIL_HTCYSD = 0x7f0d00af;
        public static final int EARTH_FLDNAME_25SOIL_HTCYWZ = 0x7f0d00b0;
        public static final int EARTH_FLDNAME_25SOIL_HTDEM = 0x7f0d00b1;
        public static final int EARTH_FLDNAME_25SOIL_HTMDTP = 0x7f0d00b2;
        public static final int EARTH_FLDNAME_25SOIL_HTMDYS = 0x7f0d00b3;
        public static final int EARTH_FLDNAME_25SOIL_HTMZFLT = 0x7f0d00b4;
        public static final int EARTH_FLDNAME_25SOIL_HTMZFXS = 0x7f0d00b5;
        public static final int EARTH_FLDNAME_25SOIL_HTMZFYX = 0x7f0d00b6;
        public static final int EARTH_FLDNAME_25SOIL_HTNOTE = 0x7f0d00b7;
        public static final int EARTH_FLDNAME_25SOIL_HTSXINX = 0x7f0d00b8;
        public static final int EARTH_FLDNAME_25SOIL_HTWRNM = 0x7f0d00b9;
        public static final int EARTH_FLDNAME_25SOIL_HTWRQK = 0x7f0d00ba;
        public static final int EARTH_FLDNAME_25SOIL_HTYPNO = 0x7f0d00bb;
        public static final int EARTH_FLDNAME_25SOIL_MAPCODE = 0x7f0d00bc;
        public static final int EARTH_FLDNAME_25SOIL_QDAEC = 0x7f0d00bd;
        public static final int EARTH_FLDNAME_25SOIL_QDAJ = 0x7f0d00be;
        public static final int EARTH_FLDNAME_25SOIL_ROUTE_CODE = 0x7f0d00bf;
        public static final int EARTH_FLDNAME_25SOIL_TKBEAL = 0x7f0d00c0;
        public static final int EARTH_FLDNAME_25SOIL_YRTP = 0x7f0d00c1;
        public static final int EARTH_FLDNAME_25SOIL_YSEB = 0x7f0d00c2;
        public static final int EARTH_FLDNAME_25STREAM_ADMCODE = 0x7f0d00c3;
        public static final int EARTH_FLDNAME_25STREAM_CHAHNV = 0x7f0d00c4;
        public static final int EARTH_FLDNAME_25STREAM_CHAHUN = 0x7f0d00c5;
        public static final int EARTH_FLDNAME_25STREAM_DDAEBA = 0x7f0d00c6;
        public static final int EARTH_FLDNAME_25STREAM_DDAEBB = 0x7f0d00c7;
        public static final int EARTH_FLDNAME_25STREAM_HTCYBW = 0x7f0d00c8;
        public static final int EARTH_FLDNAME_25STREAM_HTDEM = 0x7f0d00c9;
        public static final int EARTH_FLDNAME_25STREAM_HTMZFCS = 0x7f0d00ca;
        public static final int EARTH_FLDNAME_25STREAM_HTMZFXS = 0x7f0d00cb;
        public static final int EARTH_FLDNAME_25STREAM_HTMZFYJ = 0x7f0d00cc;
        public static final int EARTH_FLDNAME_25STREAM_HTNOTE = 0x7f0d00cd;
        public static final int EARTH_FLDNAME_25STREAM_HTPHV = 0x7f0d00ce;
        public static final int EARTH_FLDNAME_25STREAM_HTSTRMT = 0x7f0d00cf;
        public static final int EARTH_FLDNAME_25STREAM_HTSXINX = 0x7f0d00d0;
        public static final int EARTH_FLDNAME_25STREAM_HTWRNM = 0x7f0d00d1;
        public static final int EARTH_FLDNAME_25STREAM_HTWRQK = 0x7f0d00d2;
        public static final int EARTH_FLDNAME_25STREAM_HTYPNO = 0x7f0d00d3;
        public static final int EARTH_FLDNAME_25STREAM_HTZBFG = 0x7f0d00d4;
        public static final int EARTH_FLDNAME_25STREAM_HTZBTP = 0x7f0d00d5;
        public static final int EARTH_FLDNAME_25STREAM_MAPCODE = 0x7f0d00d6;
        public static final int EARTH_FLDNAME_25STREAM_QDAEC = 0x7f0d00d7;
        public static final int EARTH_FLDNAME_25STREAM_QDAJ = 0x7f0d00d8;
        public static final int EARTH_FLDNAME_25STREAM_ROUTE_CODE = 0x7f0d00d9;
        public static final int EARTH_FLDNAME_25STREAM_TKBEAL = 0x7f0d00da;
        public static final int EARTH_FLDNAME_25STREAM_YRTP = 0x7f0d00db;
        public static final int EARTH_FLDNAME_25STREAM_YSEB = 0x7f0d00dc;
        public static final int EARTH_FLDNAME_25WATER_ADMCODE = 0x7f0d00dd;
        public static final int EARTH_FLDNAME_25WATER_CHAHNV = 0x7f0d00de;
        public static final int EARTH_FLDNAME_25WATER_CHAHUN = 0x7f0d00df;
        public static final int EARTH_FLDNAME_25WATER_DDAEBA = 0x7f0d00e0;
        public static final int EARTH_FLDNAME_25WATER_DDAEBB = 0x7f0d00e1;
        public static final int EARTH_FLDNAME_25WATER_HTCYWZ = 0x7f0d00e2;
        public static final int EARTH_FLDNAME_25WATER_HTDEM = 0x7f0d00e3;
        public static final int EARTH_FLDNAME_25WATER_HTNOTE = 0x7f0d00e4;
        public static final int EARTH_FLDNAME_25WATER_HTSXINX = 0x7f0d00e5;
        public static final int EARTH_FLDNAME_25WATER_HTWRNM = 0x7f0d00e6;
        public static final int EARTH_FLDNAME_25WATER_HTWTDP = 0x7f0d00e7;
        public static final int EARTH_FLDNAME_25WATER_HTWTPH = 0x7f0d00e8;
        public static final int EARTH_FLDNAME_25WATER_HTWTSM = 0x7f0d00e9;
        public static final int EARTH_FLDNAME_25WATER_HTWTTP = 0x7f0d00ea;
        public static final int EARTH_FLDNAME_25WATER_HTWTWD = 0x7f0d00eb;
        public static final int EARTH_FLDNAME_25WATER_HTWTWR = 0x7f0d00ec;
        public static final int EARTH_FLDNAME_25WATER_HTWTYS = 0x7f0d00ed;
        public static final int EARTH_FLDNAME_25WATER_HTWYNO = 0x7f0d00ee;
        public static final int EARTH_FLDNAME_25WATER_MAPCODE = 0x7f0d00ef;
        public static final int EARTH_FLDNAME_25WATER_QDAEC = 0x7f0d00f0;
        public static final int EARTH_FLDNAME_25WATER_QDAJ = 0x7f0d00f1;
        public static final int EARTH_FLDNAME_25WATER_ROUTE_CODE = 0x7f0d00f2;
        public static final int EARTH_FLDNAME_25WATER_TKBEAL = 0x7f0d00f3;
        public static final int EARTH_FLDNAME_25WATER_YSEB = 0x7f0d00f4;
        public static final int EARTH_FLDNAME_CHECK = 0x7f0d00f5;
        public static final int EARTH_FLDNAME_CHECK_NAME = 0x7f0d00f6;
        public static final int EARTH_FLDNAME_DATE_STA = 0x7f0d00f7;
        public static final int EARTH_FLDNAME_DEPTH = 0x7f0d00f8;
        public static final int EARTH_FLDNAME_DESCRIBE = 0x7f0d00f9;
        public static final int EARTH_FLDNAME_GEOROCK_AGE = 0x7f0d00fa;
        public static final int EARTH_FLDNAME_GEOROCK_BAGCODE = 0x7f0d00fb;
        public static final int EARTH_FLDNAME_GEOROCK_CHECKDATE = 0x7f0d00fc;
        public static final int EARTH_FLDNAME_GEOROCK_DESCRIBE = 0x7f0d00fd;
        public static final int EARTH_FLDNAME_GEOROCK_KCANBCODE = 0x7f0d00fe;
        public static final int EARTH_FLDNAME_GEOROCK_KCANBNAME = 0x7f0d00ff;
        public static final int EARTH_FLDNAME_GEOROCK_KTXCODE = 0x7f0d0100;
        public static final int EARTH_FLDNAME_GEOROCK_LATITUDE = 0x7f0d0101;
        public static final int EARTH_FLDNAME_GEOROCK_LINECODE = 0x7f0d0102;
        public static final int EARTH_FLDNAME_GEOROCK_LONGITUDE = 0x7f0d0103;
        public static final int EARTH_FLDNAME_GEOROCK_PNTCODE = 0x7f0d0104;
        public static final int EARTH_FLDNAME_GEOROCK_POSITION = 0x7f0d0105;
        public static final int EARTH_FLDNAME_GEOROCK_RECORDER = 0x7f0d0106;
        public static final int EARTH_FLDNAME_GEOROCK_REMARK = 0x7f0d0107;
        public static final int EARTH_FLDNAME_GEOROCK_ROCKNAME = 0x7f0d0108;
        public static final int EARTH_FLDNAME_GEOROCK_ROUTECODE = 0x7f0d0109;
        public static final int EARTH_FLDNAME_GEOROCK_SAMDATE = 0x7f0d010a;
        public static final int EARTH_FLDNAME_GEOROCK_SAMPLEING = 0x7f0d010b;
        public static final int EARTH_FLDNAME_GEOROCK_SAMPLER = 0x7f0d010c;
        public static final int EARTH_FLDNAME_GEOROCK_WEATHERING = 0x7f0d010d;
        public static final int EARTH_FLDNAME_GEOROCK_WORKPLACE = 0x7f0d010e;
        public static final int EARTH_FLDNAME_GEOROCK_X = 0x7f0d010f;
        public static final int EARTH_FLDNAME_GEOROCK_Y = 0x7f0d0110;
        public static final int EARTH_FLDNAME_GEOSOIL_BAGCODE = 0x7f0d0111;
        public static final int EARTH_FLDNAME_GEOSOIL_CHECKDATE = 0x7f0d0112;
        public static final int EARTH_FLDNAME_GEOSOIL_COMPONENT = 0x7f0d0113;
        public static final int EARTH_FLDNAME_GEOSOIL_DEPTH = 0x7f0d0114;
        public static final int EARTH_FLDNAME_GEOSOIL_DESCRIBE = 0x7f0d0115;
        public static final int EARTH_FLDNAME_GEOSOIL_KCANBCODE = 0x7f0d0116;
        public static final int EARTH_FLDNAME_GEOSOIL_KCANBNAME = 0x7f0d0117;
        public static final int EARTH_FLDNAME_GEOSOIL_KTXCODE = 0x7f0d0118;
        public static final int EARTH_FLDNAME_GEOSOIL_LATITUDE = 0x7f0d0119;
        public static final int EARTH_FLDNAME_GEOSOIL_LAYER = 0x7f0d011a;
        public static final int EARTH_FLDNAME_GEOSOIL_LINECODE = 0x7f0d011b;
        public static final int EARTH_FLDNAME_GEOSOIL_LONGITUDE = 0x7f0d011c;
        public static final int EARTH_FLDNAME_GEOSOIL_PNTCODE = 0x7f0d011d;
        public static final int EARTH_FLDNAME_GEOSOIL_POSITION = 0x7f0d011e;
        public static final int EARTH_FLDNAME_GEOSOIL_RECORDER = 0x7f0d011f;
        public static final int EARTH_FLDNAME_GEOSOIL_REMARK = 0x7f0d0120;
        public static final int EARTH_FLDNAME_GEOSOIL_ROUTECODE = 0x7f0d0121;
        public static final int EARTH_FLDNAME_GEOSOIL_SAMDATE = 0x7f0d0122;
        public static final int EARTH_FLDNAME_GEOSOIL_SAMPLER = 0x7f0d0123;
        public static final int EARTH_FLDNAME_GEOSOIL_WORKPLACE = 0x7f0d0124;
        public static final int EARTH_FLDNAME_GEOSOIL_X = 0x7f0d0125;
        public static final int EARTH_FLDNAME_GEOSOIL_Y = 0x7f0d0126;
        public static final int EARTH_FLDNAME_GEOWATER_BAGCODE = 0x7f0d0127;
        public static final int EARTH_FLDNAME_GEOWATER_CHECKDATE = 0x7f0d0128;
        public static final int EARTH_FLDNAME_GEOWATER_COMPONENT = 0x7f0d0129;
        public static final int EARTH_FLDNAME_GEOWATER_DESCRIBE = 0x7f0d012a;
        public static final int EARTH_FLDNAME_GEOWATER_KCANBCODE = 0x7f0d012b;
        public static final int EARTH_FLDNAME_GEOWATER_KCANBNAME = 0x7f0d012c;
        public static final int EARTH_FLDNAME_GEOWATER_KTXCODE = 0x7f0d012d;
        public static final int EARTH_FLDNAME_GEOWATER_LATITUDE = 0x7f0d012e;
        public static final int EARTH_FLDNAME_GEOWATER_LONGITUDE = 0x7f0d012f;
        public static final int EARTH_FLDNAME_GEOWATER_PH = 0x7f0d0130;
        public static final int EARTH_FLDNAME_GEOWATER_PNTCODE = 0x7f0d0131;
        public static final int EARTH_FLDNAME_GEOWATER_POSITION = 0x7f0d0132;
        public static final int EARTH_FLDNAME_GEOWATER_RECORDER = 0x7f0d0133;
        public static final int EARTH_FLDNAME_GEOWATER_REMARK = 0x7f0d0134;
        public static final int EARTH_FLDNAME_GEOWATER_ROUTECODE = 0x7f0d0135;
        public static final int EARTH_FLDNAME_GEOWATER_SAMDATE = 0x7f0d0136;
        public static final int EARTH_FLDNAME_GEOWATER_SAMPLER = 0x7f0d0137;
        public static final int EARTH_FLDNAME_GEOWATER_WATERSYSCODE = 0x7f0d0138;
        public static final int EARTH_FLDNAME_GEOWATER_WORKPLACE = 0x7f0d0139;
        public static final int EARTH_FLDNAME_GEOWATER_X = 0x7f0d013a;
        public static final int EARTH_FLDNAME_GEOWATER_Y = 0x7f0d013b;
        public static final int EARTH_FLDNAME_HEIGHT = 0x7f0d013c;
        public static final int EARTH_FLDNAME_KCANB_CODE = 0x7f0d013d;
        public static final int EARTH_FLDNAME_KCANB_NAME = 0x7f0d013e;
        public static final int EARTH_FLDNAME_KTX_CODE = 0x7f0d013f;
        public static final int EARTH_FLDNAME_LATITUDE = 0x7f0d0140;
        public static final int EARTH_FLDNAME_LITHO = 0x7f0d0141;
        public static final int EARTH_FLDNAME_LITHO_CODE = 0x7f0d0142;
        public static final int EARTH_FLDNAME_LONGITUDE = 0x7f0d0143;
        public static final int EARTH_FLDNAME_MORPHOLOGY = 0x7f0d0144;
        public static final int EARTH_FLDNAME_MULSOIL_BAG_CODE = 0x7f0d0145;
        public static final int EARTH_FLDNAME_MULSOIL_CARD_NO = 0x7f0d0146;
        public static final int EARTH_FLDNAME_MULSOIL_CAUSE = 0x7f0d0147;
        public static final int EARTH_FLDNAME_MULSOIL_CHECKER = 0x7f0d0148;
        public static final int EARTH_FLDNAME_MULSOIL_COLOR = 0x7f0d0149;
        public static final int EARTH_FLDNAME_MULSOIL_COMPONENT1 = 0x7f0d014a;
        public static final int EARTH_FLDNAME_MULSOIL_COMPONENT2 = 0x7f0d014b;
        public static final int EARTH_FLDNAME_MULSOIL_COMPONENT3 = 0x7f0d014c;
        public static final int EARTH_FLDNAME_MULSOIL_CROP = 0x7f0d014d;
        public static final int EARTH_FLDNAME_MULSOIL_CULTURIST = 0x7f0d014e;
        public static final int EARTH_FLDNAME_MULSOIL_DATE = 0x7f0d014f;
        public static final int EARTH_FLDNAME_MULSOIL_ERODE = 0x7f0d0150;
        public static final int EARTH_FLDNAME_MULSOIL_MAPCODE = 0x7f0d0151;
        public static final int EARTH_FLDNAME_MULSOIL_NEW_PRODUCT = 0x7f0d0152;
        public static final int EARTH_FLDNAME_MULSOIL_ORG_SAMCODE = 0x7f0d0153;
        public static final int EARTH_FLDNAME_MULSOIL_PICKLED = 0x7f0d0154;
        public static final int EARTH_FLDNAME_MULSOIL_POLLUTION = 0x7f0d0155;
        public static final int EARTH_FLDNAME_MULSOIL_POSITION = 0x7f0d0156;
        public static final int EARTH_FLDNAME_MULSOIL_PROVICE_CODE = 0x7f0d0157;
        public static final int EARTH_FLDNAME_MULSOIL_PROVICE_NAME = 0x7f0d0158;
        public static final int EARTH_FLDNAME_MULSOIL_RECORDER = 0x7f0d0159;
        public static final int EARTH_FLDNAME_MULSOIL_REGIONALISM = 0x7f0d015a;
        public static final int EARTH_FLDNAME_MULSOIL_REMARK = 0x7f0d015b;
        public static final int EARTH_FLDNAME_MULSOIL_ROCK_FEATURE = 0x7f0d015c;
        public static final int EARTH_FLDNAME_MULSOIL_ROUTH = 0x7f0d015d;
        public static final int EARTH_FLDNAME_MULSOIL_SAMED_UNIT = 0x7f0d015e;
        public static final int EARTH_FLDNAME_MULSOIL_SAMPER = 0x7f0d015f;
        public static final int EARTH_FLDNAME_MULSOIL_SAM_CODE = 0x7f0d0160;
        public static final int EARTH_FLDNAME_MULSOIL_SAM_DEPTH = 0x7f0d0161;
        public static final int EARTH_FLDNAME_MULSOIL_SAM_TYPE = 0x7f0d0162;
        public static final int EARTH_FLDNAME_MULSOIL_SOIL_USED = 0x7f0d0163;
        public static final int EARTH_FLDNAME_MULSOIL_WATER_LEVEL = 0x7f0d0164;
        public static final int EARTH_FLDNAME_MULSOIL_XX = 0x7f0d0165;
        public static final int EARTH_FLDNAME_MULSOIL_YY = 0x7f0d0166;
        public static final int EARTH_FLDNAME_MULSTREM_CARD_NO = 0x7f0d0167;
        public static final int EARTH_FLDNAME_MULSTREM_CHECKER = 0x7f0d0168;
        public static final int EARTH_FLDNAME_MULSTREM_COMPONENT_1 = 0x7f0d0169;
        public static final int EARTH_FLDNAME_MULSTREM_COMPONENT_2 = 0x7f0d016a;
        public static final int EARTH_FLDNAME_MULSTREM_COMPONENT_3 = 0x7f0d016b;
        public static final int EARTH_FLDNAME_MULSTREM_CULTURIST = 0x7f0d016c;
        public static final int EARTH_FLDNAME_MULSTREM_CUTICLE_COLOR1 = 0x7f0d016d;
        public static final int EARTH_FLDNAME_MULSTREM_DATE = 0x7f0d016e;
        public static final int EARTH_FLDNAME_MULSTREM_DEBRIS = 0x7f0d016f;
        public static final int EARTH_FLDNAME_MULSTREM_DESCRIBE = 0x7f0d0170;
        public static final int EARTH_FLDNAME_MULSTREM_HYDROBIOLOGY = 0x7f0d0171;
        public static final int EARTH_FLDNAME_MULSTREM_MAPCODE = 0x7f0d0172;
        public static final int EARTH_FLDNAME_MULSTREM_ORG_SAMCODE = 0x7f0d0173;
        public static final int EARTH_FLDNAME_MULSTREM_PHYSIOGNOMY = 0x7f0d0174;
        public static final int EARTH_FLDNAME_MULSTREM_POLLUTION = 0x7f0d0175;
        public static final int EARTH_FLDNAME_MULSTREM_POLLUTION_DES = 0x7f0d0176;
        public static final int EARTH_FLDNAME_MULSTREM_POSITION = 0x7f0d0177;
        public static final int EARTH_FLDNAME_MULSTREM_PROVICE_CODE = 0x7f0d0178;
        public static final int EARTH_FLDNAME_MULSTREM_PROVICE_NAME = 0x7f0d0179;
        public static final int EARTH_FLDNAME_MULSTREM_RECORDER = 0x7f0d017a;
        public static final int EARTH_FLDNAME_MULSTREM_REMARK = 0x7f0d017b;
        public static final int EARTH_FLDNAME_MULSTREM_ROUTH = 0x7f0d017c;
        public static final int EARTH_FLDNAME_MULSTREM_SAMED_DEPTH = 0x7f0d017d;
        public static final int EARTH_FLDNAME_MULSTREM_SAMPER = 0x7f0d017e;
        public static final int EARTH_FLDNAME_MULSTREM_SAM_BAGCODE = 0x7f0d017f;
        public static final int EARTH_FLDNAME_MULSTREM_SAM_CODE = 0x7f0d0180;
        public static final int EARTH_FLDNAME_MULSTREM_SAM_COLOR2 = 0x7f0d0181;
        public static final int EARTH_FLDNAME_MULSTREM_SAM_TYPE = 0x7f0d0182;
        public static final int EARTH_FLDNAME_MULSTREM_TAG_POS = 0x7f0d0183;
        public static final int EARTH_FLDNAME_MULSTREM_WATER_DEPTH = 0x7f0d0184;
        public static final int EARTH_FLDNAME_MULSTREM_XX = 0x7f0d0185;
        public static final int EARTH_FLDNAME_MULSTREM_YY = 0x7f0d0186;
        public static final int EARTH_FLDNAME_MULWATER_BEGRIME = 0x7f0d0187;
        public static final int EARTH_FLDNAME_MULWATER_CARD_NO = 0x7f0d0188;
        public static final int EARTH_FLDNAME_MULWATER_CHECKER = 0x7f0d0189;
        public static final int EARTH_FLDNAME_MULWATER_DATE = 0x7f0d018a;
        public static final int EARTH_FLDNAME_MULWATER_DIS_TYPE = 0x7f0d018b;
        public static final int EARTH_FLDNAME_MULWATER_HCI = 0x7f0d018c;
        public static final int EARTH_FLDNAME_MULWATER_HNO3 = 0x7f0d018d;
        public static final int EARTH_FLDNAME_MULWATER_K2CR207 = 0x7f0d018e;
        public static final int EARTH_FLDNAME_MULWATER_MAPCODE = 0x7f0d018f;
        public static final int EARTH_FLDNAME_MULWATER_NAOH = 0x7f0d0190;
        public static final int EARTH_FLDNAME_MULWATER_NEPHELOMETER = 0x7f0d0191;
        public static final int EARTH_FLDNAME_MULWATER_ORG_SAMCODE = 0x7f0d0192;
        public static final int EARTH_FLDNAME_MULWATER_PH = 0x7f0d0193;
        public static final int EARTH_FLDNAME_MULWATER_POLLUTION = 0x7f0d0194;
        public static final int EARTH_FLDNAME_MULWATER_PROVICE_CODE = 0x7f0d0195;
        public static final int EARTH_FLDNAME_MULWATER_PROVICE_NAME = 0x7f0d0196;
        public static final int EARTH_FLDNAME_MULWATER_PURPOSE = 0x7f0d0197;
        public static final int EARTH_FLDNAME_MULWATER_RECORDER = 0x7f0d0198;
        public static final int EARTH_FLDNAME_MULWATER_REGIONALISM = 0x7f0d0199;
        public static final int EARTH_FLDNAME_MULWATER_REMARK = 0x7f0d019a;
        public static final int EARTH_FLDNAME_MULWATER_ROUTH = 0x7f0d019b;
        public static final int EARTH_FLDNAME_MULWATER_SAMED_UNIT = 0x7f0d019c;
        public static final int EARTH_FLDNAME_MULWATER_SAMPER = 0x7f0d019d;
        public static final int EARTH_FLDNAME_MULWATER_SAM_CODE = 0x7f0d019e;
        public static final int EARTH_FLDNAME_MULWATER_SAM_TYPE = 0x7f0d019f;
        public static final int EARTH_FLDNAME_MULWATER_SAM_WATER1 = 0x7f0d01a0;
        public static final int EARTH_FLDNAME_MULWATER_SAM_WATER2 = 0x7f0d01a1;
        public static final int EARTH_FLDNAME_MULWATER_TAG_POS = 0x7f0d01a2;
        public static final int EARTH_FLDNAME_MULWATER_TEMPERATURE = 0x7f0d01a3;
        public static final int EARTH_FLDNAME_MULWATER_WATER_COLOR = 0x7f0d01a4;
        public static final int EARTH_FLDNAME_MULWATER_WATER_LEVEL1 = 0x7f0d01a5;
        public static final int EARTH_FLDNAME_MULWATER_WATER_LEVEL2 = 0x7f0d01a6;
        public static final int EARTH_FLDNAME_MULWATER_WATER_SMELL = 0x7f0d01a7;
        public static final int EARTH_FLDNAME_MULWATER_WELL_DEPTH = 0x7f0d01a8;
        public static final int EARTH_FLDNAME_MULWATER_XX = 0x7f0d01a9;
        public static final int EARTH_FLDNAME_MULWATER_YY = 0x7f0d01aa;
        public static final int EARTH_FLDNAME_ORI_WEIGHT = 0x7f0d01ab;
        public static final int EARTH_FLDNAME_POINT_CODE = 0x7f0d01ac;
        public static final int EARTH_FLDNAME_POSITION = 0x7f0d01ad;
        public static final int EARTH_FLDNAME_RECORDER = 0x7f0d01ae;
        public static final int EARTH_FLDNAME_ROCKSY = 0x7f0d01af;
        public static final int EARTH_FLDNAME_ROUTE_CODE = 0x7f0d01b0;
        public static final int EARTH_FLDNAME_ROUTE_DATE = 0x7f0d01b1;
        public static final int EARTH_FLDNAME_ROUTE_FELLOW = 0x7f0d01b2;
        public static final int EARTH_FLDNAME_ROUTE_KCANBCODE = 0x7f0d01b3;
        public static final int EARTH_FLDNAME_ROUTE_KCANBNAME = 0x7f0d01b4;
        public static final int EARTH_FLDNAME_ROUTE_KTXCODE = 0x7f0d01b5;
        public static final int EARTH_FLDNAME_ROUTE_LENGTH = 0x7f0d01b6;
        public static final int EARTH_FLDNAME_ROUTE_RECORDER = 0x7f0d01b7;
        public static final int EARTH_FLDNAME_ROUTE_REMARK = 0x7f0d01b8;
        public static final int EARTH_FLDNAME_ROUTE_ROUTE_CODE = 0x7f0d01b9;
        public static final int EARTH_FLDNAME_ROUTE_SAMPLER = 0x7f0d01ba;
        public static final int EARTH_FLDNAME_ROUTE_TASK = 0x7f0d01bb;
        public static final int EARTH_FLDNAME_ROUTE_UNIT = 0x7f0d01bc;
        public static final int EARTH_FLDNAME_ROUTE_WORKPLACE = 0x7f0d01bd;
        public static final int EARTH_FLDNAME_SEMI_TYPE = 0x7f0d01be;
        public static final int EARTH_FLDNAME_SOILSEDI_BAGCODE = 0x7f0d01bf;
        public static final int EARTH_FLDNAME_SOILSEDI_CHARACTER = 0x7f0d01c0;
        public static final int EARTH_FLDNAME_SOILSEDI_COLOR = 0x7f0d01c1;
        public static final int EARTH_FLDNAME_SOILSEDI_DEPTH = 0x7f0d01c2;
        public static final int EARTH_FLDNAME_SOILSEDI_GEOLOGY = 0x7f0d01c3;
        public static final int EARTH_FLDNAME_SOILSEDI_HUMIDITY = 0x7f0d01c4;
        public static final int EARTH_FLDNAME_SOILSEDI_KCANBCODE = 0x7f0d01c5;
        public static final int EARTH_FLDNAME_SOILSEDI_KCANBNAME = 0x7f0d01c6;
        public static final int EARTH_FLDNAME_SOILSEDI_KTXCODE = 0x7f0d01c7;
        public static final int EARTH_FLDNAME_SOILSEDI_LAY = 0x7f0d01c8;
        public static final int EARTH_FLDNAME_SOILSEDI_LINCODE = 0x7f0d01c9;
        public static final int EARTH_FLDNAME_SOILSEDI_METHOD = 0x7f0d01ca;
        public static final int EARTH_FLDNAME_SOILSEDI_PH = 0x7f0d01cb;
        public static final int EARTH_FLDNAME_SOILSEDI_PHYSIOGNOMY = 0x7f0d01cc;
        public static final int EARTH_FLDNAME_SOILSEDI_PNTCODE = 0x7f0d01cd;
        public static final int EARTH_FLDNAME_SOILSEDI_POSITION = 0x7f0d01ce;
        public static final int EARTH_FLDNAME_SOILSEDI_RECORDER = 0x7f0d01cf;
        public static final int EARTH_FLDNAME_SOILSEDI_ROUTECODE = 0x7f0d01d0;
        public static final int EARTH_FLDNAME_SOILSEDI_SAMDATE = 0x7f0d01d1;
        public static final int EARTH_FLDNAME_SOILSEDI_SAMPLER = 0x7f0d01d2;
        public static final int EARTH_FLDNAME_SOILSEDI_THICKNESS = 0x7f0d01d3;
        public static final int EARTH_FLDNAME_SOILSEDI_TYPE = 0x7f0d01d4;
        public static final int EARTH_FLDNAME_SOILSEDI_WEATHER = 0x7f0d01d5;
        public static final int EARTH_FLDNAME_SOILSEDI_X = 0x7f0d01d6;
        public static final int EARTH_FLDNAME_SOILSEDI_Y = 0x7f0d01d7;
        public static final int EARTH_FLDNAME_STREMSEDI_BAGCODE = 0x7f0d01d8;
        public static final int EARTH_FLDNAME_STREMSEDI_COLOR = 0x7f0d01d9;
        public static final int EARTH_FLDNAME_STREMSEDI_EH = 0x7f0d01da;
        public static final int EARTH_FLDNAME_STREMSEDI_GEOLOGY = 0x7f0d01db;
        public static final int EARTH_FLDNAME_STREMSEDI_GRANULARITY = 0x7f0d01dc;
        public static final int EARTH_FLDNAME_STREMSEDI_HUMIDITY = 0x7f0d01dd;
        public static final int EARTH_FLDNAME_STREMSEDI_KCANBCODE = 0x7f0d01de;
        public static final int EARTH_FLDNAME_STREMSEDI_KCANBNAME = 0x7f0d01df;
        public static final int EARTH_FLDNAME_STREMSEDI_KTXCODE = 0x7f0d01e0;
        public static final int EARTH_FLDNAME_STREMSEDI_LINCODE = 0x7f0d01e1;
        public static final int EARTH_FLDNAME_STREMSEDI_METHOD = 0x7f0d01e2;
        public static final int EARTH_FLDNAME_STREMSEDI_PH = 0x7f0d01e3;
        public static final int EARTH_FLDNAME_STREMSEDI_PHYSIOGNOMY = 0x7f0d01e4;
        public static final int EARTH_FLDNAME_STREMSEDI_PNTCODE = 0x7f0d01e5;
        public static final int EARTH_FLDNAME_STREMSEDI_POLLUTE = 0x7f0d01e6;
        public static final int EARTH_FLDNAME_STREMSEDI_POSITION = 0x7f0d01e7;
        public static final int EARTH_FLDNAME_STREMSEDI_RECORDER = 0x7f0d01e8;
        public static final int EARTH_FLDNAME_STREMSEDI_ROUTECODE = 0x7f0d01e9;
        public static final int EARTH_FLDNAME_STREMSEDI_SAMDATE = 0x7f0d01ea;
        public static final int EARTH_FLDNAME_STREMSEDI_SAMPLER = 0x7f0d01eb;
        public static final int EARTH_FLDNAME_STREMSEDI_TURBIDITY = 0x7f0d01ec;
        public static final int EARTH_FLDNAME_STREMSEDI_TYPE = 0x7f0d01ed;
        public static final int EARTH_FLDNAME_STREMSEDI_WEATHER = 0x7f0d01ee;
        public static final int EARTH_FLDNAME_STREMSEDI_X = 0x7f0d01ef;
        public static final int EARTH_FLDNAME_STREMSEDI_Y = 0x7f0d01f0;
        public static final int EARTH_FLDNAME_TASK = 0x7f0d01f1;
        public static final int EARTH_FLDNAME_VIEW_MINERAL = 0x7f0d01f2;
        public static final int EARTH_FLDNAME_WEATHER = 0x7f0d01f3;
        public static final int EARTH_FLDNAME_WEIGHT = 0x7f0d01f4;
        public static final int EARTH_FLDNAME_XX = 0x7f0d01f5;
        public static final int EARTH_FLDNAME_YY = 0x7f0d01f6;
        public static final int EARTH_GEOCHEM25W_ALTERATION = 0x7f0d01f7;
        public static final int EARTH_GEOCHEM25W_ALTITUDE = 0x7f0d01f8;
        public static final int EARTH_GEOCHEM25W_BAGNO = 0x7f0d01f9;
        public static final int EARTH_GEOCHEM25W_CHECKMAN = 0x7f0d01fa;
        public static final int EARTH_GEOCHEM25W_COLOR = 0x7f0d01fb;
        public static final int EARTH_GEOCHEM25W_COMPONENT = 0x7f0d01fc;
        public static final int EARTH_GEOCHEM25W_COORDX = 0x7f0d01fd;
        public static final int EARTH_GEOCHEM25W_COORDY = 0x7f0d01fe;
        public static final int EARTH_GEOCHEM25W_DEPTH = 0x7f0d01ff;
        public static final int EARTH_GEOCHEM25W_DIP = 0x7f0d0200;
        public static final int EARTH_GEOCHEM25W_DIPANG = 0x7f0d0201;
        public static final int EARTH_GEOCHEM25W_JOINT = 0x7f0d0202;
        public static final int EARTH_GEOCHEM25W_KARSTTYPE = 0x7f0d0203;
        public static final int EARTH_GEOCHEM25W_LATITUDE = 0x7f0d0204;
        public static final int EARTH_GEOCHEM25W_LONGITUDE = 0x7f0d0205;
        public static final int EARTH_GEOCHEM25W_MAINID = 0x7f0d0206;
        public static final int EARTH_GEOCHEM25W_MAPCODE = 0x7f0d0207;
        public static final int EARTH_GEOCHEM25W_MINERAL = 0x7f0d0208;
        public static final int EARTH_GEOCHEM25W_MINERALA = 0x7f0d0209;
        public static final int EARTH_GEOCHEM25W_MINERALAPRE = 0x7f0d020a;
        public static final int EARTH_GEOCHEM25W_MINERALB = 0x7f0d020b;
        public static final int EARTH_GEOCHEM25W_MINERALBPRE = 0x7f0d020c;
        public static final int EARTH_GEOCHEM25W_NEARPNTNUM = 0x7f0d020d;
        public static final int EARTH_GEOCHEM25W_OUTCRAP = 0x7f0d020e;
        public static final int EARTH_GEOCHEM25W_PH = 0x7f0d020f;
        public static final int EARTH_GEOCHEM25W_POLLUTION = 0x7f0d0210;
        public static final int EARTH_GEOCHEM25W_PROVINCE = 0x7f0d0211;
        public static final int EARTH_GEOCHEM25W_RECORDMAN = 0x7f0d0212;
        public static final int EARTH_GEOCHEM25W_REMARK = 0x7f0d0213;
        public static final int EARTH_GEOCHEM25W_ROCKTYPE = 0x7f0d0214;
        public static final int EARTH_GEOCHEM25W_ROUTECODE = 0x7f0d0215;
        public static final int EARTH_GEOCHEM25W_SAMAGE = 0x7f0d0216;
        public static final int EARTH_GEOCHEM25W_SAMCODE = 0x7f0d0217;
        public static final int EARTH_GEOCHEM25W_SAMEDATE = 0x7f0d0218;
        public static final int EARTH_GEOCHEM25W_SAMLAYER = 0x7f0d0219;
        public static final int EARTH_GEOCHEM25W_SAMMAN = 0x7f0d021a;
        public static final int EARTH_GEOCHEM25W_SAMORGCODE = 0x7f0d021b;
        public static final int EARTH_GEOCHEM25W_SAMPOS = 0x7f0d021c;
        public static final int EARTH_GEOCHEM25W_SAMTYPE = 0x7f0d021d;
        public static final int EARTH_GEOCHEM25W_STREAMORDER = 0x7f0d021e;
        public static final int EARTH_GEOCHEM25W_WORKAREA = 0x7f0d021f;
        public static final int EARTH_GEOCHEM25W_WORKUNIT = 0x7f0d0220;
        public static final int EARTH_GEOCHEM25W_WORKUNITC = 0x7f0d0221;
        public static final int EARTH_GEOCHEMROCK5W_ALTERATION = 0x7f0d0222;
        public static final int EARTH_GEOCHEMROCK5W_LITHOLOGY = 0x7f0d0223;
        public static final int EARTH_GEOCHEMROCK5W_MINECODE = 0x7f0d0224;
        public static final int EARTH_GEOCHEMROCK5W_MINECODEID = 0x7f0d0225;
        public static final int EARTH_GEOCHEMROCK5W_PHOTOCODE = 0x7f0d0226;
        public static final int EARTH_GEOCHEMROCK5W_ROCKTYPE = 0x7f0d0227;
        public static final int EARTH_GEOCHEMROCK5W_SAMTYPE = 0x7f0d0228;
        public static final int EARTH_GEOCHEMROCK5W_SKETCHCODE = 0x7f0d0229;
        public static final int EARTH_GEOCHEMROCK5W_STRUCTURE = 0x7f0d022a;
        public static final int EARTH_GEOCHEMROCK5W_WEATHERED = 0x7f0d022b;
        public static final int EARTH_GEOCHEMSOIL5W_CHECKMAN = 0x7f0d022c;
        public static final int EARTH_GEOCHEMSOIL5W_SAMDEPTH = 0x7f0d022d;
        public static final int EARTH_GEOCHEMSOIL5W_SOILAGE = 0x7f0d022e;
        public static final int EARTH_GEOCHEMSOIL5W_SOILGENETICTYPE = 0x7f0d022f;
        public static final int EARTH_GEOCHEMSOIL5W_SOILGENETICTYPEC = 0x7f0d0230;
        public static final int EARTH_GEOCHEMSTREAM5W_AGE = 0x7f0d0231;
        public static final int EARTH_GEOCHEMSTREAM5W_ALTERATION = 0x7f0d0232;
        public static final int EARTH_GEOCHEMSTREAM5W_ALTERATIONC = 0x7f0d0233;
        public static final int EARTH_GEOCHEMSTREAM5W_ALTITUDE = 0x7f0d0234;
        public static final int EARTH_GEOCHEMSTREAM5W_BAGNO = 0x7f0d0235;
        public static final int EARTH_GEOCHEMSTREAM5W_CHECKMAN = 0x7f0d0236;
        public static final int EARTH_GEOCHEMSTREAM5W_COLOR = 0x7f0d0237;
        public static final int EARTH_GEOCHEMSTREAM5W_COLORC = 0x7f0d0238;
        public static final int EARTH_GEOCHEMSTREAM5W_COMPONENT = 0x7f0d0239;
        public static final int EARTH_GEOCHEMSTREAM5W_COMPONENTC = 0x7f0d023a;
        public static final int EARTH_GEOCHEMSTREAM5W_COORDX = 0x7f0d023b;
        public static final int EARTH_GEOCHEMSTREAM5W_COORDY = 0x7f0d023c;
        public static final int EARTH_GEOCHEMSTREAM5W_GEOMORPHICTYPE = 0x7f0d023d;
        public static final int EARTH_GEOCHEMSTREAM5W_GEOMORPHICTYPEC = 0x7f0d023e;
        public static final int EARTH_GEOCHEMSTREAM5W_GPSFILENO = 0x7f0d023f;
        public static final int EARTH_GEOCHEMSTREAM5W_GPSID = 0x7f0d0240;
        public static final int EARTH_GEOCHEMSTREAM5W_KARSTTYPE = 0x7f0d0241;
        public static final int EARTH_GEOCHEMSTREAM5W_KARSTTYPEC = 0x7f0d0242;
        public static final int EARTH_GEOCHEMSTREAM5W_MAPCODE = 0x7f0d0243;
        public static final int EARTH_GEOCHEMSTREAM5W_MAPNAME = 0x7f0d0244;
        public static final int EARTH_GEOCHEMSTREAM5W_MARKPOS = 0x7f0d0245;
        public static final int EARTH_GEOCHEMSTREAM5W_PLANT = 0x7f0d0246;
        public static final int EARTH_GEOCHEMSTREAM5W_PLANTC = 0x7f0d0247;
        public static final int EARTH_GEOCHEMSTREAM5W_POLLUTION = 0x7f0d0248;
        public static final int EARTH_GEOCHEMSTREAM5W_POLLUTIONC = 0x7f0d0249;
        public static final int EARTH_GEOCHEMSTREAM5W_PROJECTNAME = 0x7f0d024a;
        public static final int EARTH_GEOCHEMSTREAM5W_RECORDMAN = 0x7f0d024b;
        public static final int EARTH_GEOCHEMSTREAM5W_REMARK = 0x7f0d024c;
        public static final int EARTH_GEOCHEMSTREAM5W_ROCKTYPE = 0x7f0d024d;
        public static final int EARTH_GEOCHEMSTREAM5W_ROUTECODE = 0x7f0d024e;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMCODE = 0x7f0d024f;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMDATE = 0x7f0d0250;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMMAN = 0x7f0d0251;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMORGCODE = 0x7f0d0252;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMPOS = 0x7f0d0253;
        public static final int EARTH_GEOCHEMSTREAM5W_SAMPOSC = 0x7f0d0254;
        public static final int EARTH_GEOCHEMSTREAM5W_STREAMORDER = 0x7f0d0255;
        public static final int EARTH_GEOCHEMSTREAM5W_STREAMORDERC = 0x7f0d0256;
        public static final int EARTH_GEOROCK_AGE = 0x7f0d0257;
        public static final int EARTH_GEOROCK_BAGCODE = 0x7f0d0258;
        public static final int EARTH_GEOROCK_CHECKDATE = 0x7f0d0259;
        public static final int EARTH_GEOROCK_DESCRIBE = 0x7f0d025a;
        public static final int EARTH_GEOROCK_KCANBCODE = 0x7f0d025b;
        public static final int EARTH_GEOROCK_KCANBNAME = 0x7f0d025c;
        public static final int EARTH_GEOROCK_KTXCODE = 0x7f0d025d;
        public static final int EARTH_GEOROCK_LATITUDE = 0x7f0d025e;
        public static final int EARTH_GEOROCK_LINECODE = 0x7f0d025f;
        public static final int EARTH_GEOROCK_LONGITUDE = 0x7f0d0260;
        public static final int EARTH_GEOROCK_PNTCODE = 0x7f0d0261;
        public static final int EARTH_GEOROCK_POSITION = 0x7f0d0262;
        public static final int EARTH_GEOROCK_RECORDER = 0x7f0d0263;
        public static final int EARTH_GEOROCK_REMARK = 0x7f0d0264;
        public static final int EARTH_GEOROCK_ROCKNAME = 0x7f0d0265;
        public static final int EARTH_GEOROCK_ROUTECODE = 0x7f0d0266;
        public static final int EARTH_GEOROCK_SAMDATE = 0x7f0d0267;
        public static final int EARTH_GEOROCK_SAMPLEING = 0x7f0d0268;
        public static final int EARTH_GEOROCK_SAMPLER = 0x7f0d0269;
        public static final int EARTH_GEOROCK_WEATHERING = 0x7f0d026a;
        public static final int EARTH_GEOROCK_WORKPLACE = 0x7f0d026b;
        public static final int EARTH_GEOROCK_X = 0x7f0d026c;
        public static final int EARTH_GEOROCK_Y = 0x7f0d026d;
        public static final int EARTH_GEOSOIL_BAGCODE = 0x7f0d026e;
        public static final int EARTH_GEOSOIL_CHECKDATE = 0x7f0d026f;
        public static final int EARTH_GEOSOIL_COMPONENT = 0x7f0d0270;
        public static final int EARTH_GEOSOIL_DEPTH = 0x7f0d0271;
        public static final int EARTH_GEOSOIL_DESCRIBE = 0x7f0d0272;
        public static final int EARTH_GEOSOIL_KCANBCODE = 0x7f0d0273;
        public static final int EARTH_GEOSOIL_KCANBNAME = 0x7f0d0274;
        public static final int EARTH_GEOSOIL_KTXCODE = 0x7f0d0275;
        public static final int EARTH_GEOSOIL_LATITUDE = 0x7f0d0276;
        public static final int EARTH_GEOSOIL_LAYER = 0x7f0d0277;
        public static final int EARTH_GEOSOIL_LINECODE = 0x7f0d0278;
        public static final int EARTH_GEOSOIL_LONGITUDE = 0x7f0d0279;
        public static final int EARTH_GEOSOIL_PNTCODE = 0x7f0d027a;
        public static final int EARTH_GEOSOIL_POSITION = 0x7f0d027b;
        public static final int EARTH_GEOSOIL_RECORDER = 0x7f0d027c;
        public static final int EARTH_GEOSOIL_REMARK = 0x7f0d027d;
        public static final int EARTH_GEOSOIL_ROUTECODE = 0x7f0d027e;
        public static final int EARTH_GEOSOIL_SAMDATE = 0x7f0d027f;
        public static final int EARTH_GEOSOIL_SAMPLER = 0x7f0d0280;
        public static final int EARTH_GEOSOIL_WORKPLACE = 0x7f0d0281;
        public static final int EARTH_GEOSOIL_X = 0x7f0d0282;
        public static final int EARTH_GEOSOIL_Y = 0x7f0d0283;
        public static final int EARTH_GEOWATER_BAGCODE = 0x7f0d0284;
        public static final int EARTH_GEOWATER_CHECKDATE = 0x7f0d0285;
        public static final int EARTH_GEOWATER_COMPONENT = 0x7f0d0286;
        public static final int EARTH_GEOWATER_DESCRIBE = 0x7f0d0287;
        public static final int EARTH_GEOWATER_KCANBCODE = 0x7f0d0288;
        public static final int EARTH_GEOWATER_KCANBNAME = 0x7f0d0289;
        public static final int EARTH_GEOWATER_KTXCODE = 0x7f0d028a;
        public static final int EARTH_GEOWATER_LATITUDE = 0x7f0d028b;
        public static final int EARTH_GEOWATER_LONGITUDE = 0x7f0d028c;
        public static final int EARTH_GEOWATER_PH = 0x7f0d028d;
        public static final int EARTH_GEOWATER_PNTCODE = 0x7f0d028e;
        public static final int EARTH_GEOWATER_POSITION = 0x7f0d028f;
        public static final int EARTH_GEOWATER_RECORDER = 0x7f0d0290;
        public static final int EARTH_GEOWATER_REMARK = 0x7f0d0291;
        public static final int EARTH_GEOWATER_ROUTECODE = 0x7f0d0292;
        public static final int EARTH_GEOWATER_SAMDATE = 0x7f0d0293;
        public static final int EARTH_GEOWATER_SAMPLER = 0x7f0d0294;
        public static final int EARTH_GEOWATER_WATERSYSCODE = 0x7f0d0295;
        public static final int EARTH_GEOWATER_WORKPLACE = 0x7f0d0296;
        public static final int EARTH_GEOWATER_X = 0x7f0d0297;
        public static final int EARTH_GEOWATER_Y = 0x7f0d0298;
        public static final int EARTH_HEIGHT = 0x7f0d0299;
        public static final int EARTH_KCANB_CODE = 0x7f0d029a;
        public static final int EARTH_KCANB_NAME = 0x7f0d029b;
        public static final int EARTH_KTX_CODE = 0x7f0d029c;
        public static final int EARTH_LATITUDE = 0x7f0d029d;
        public static final int EARTH_LITHO = 0x7f0d029e;
        public static final int EARTH_LITHO_CODE = 0x7f0d029f;
        public static final int EARTH_LONGITUDE = 0x7f0d02a0;
        public static final int EARTH_MORPHOLOGY = 0x7f0d02a1;
        public static final int EARTH_MULSOIL_BAG_CODE = 0x7f0d02a2;
        public static final int EARTH_MULSOIL_CARD_NO = 0x7f0d02a3;
        public static final int EARTH_MULSOIL_CAUSE = 0x7f0d02a4;
        public static final int EARTH_MULSOIL_CHECKER = 0x7f0d02a5;
        public static final int EARTH_MULSOIL_COLOR = 0x7f0d02a6;
        public static final int EARTH_MULSOIL_COMPONENT1 = 0x7f0d02a7;
        public static final int EARTH_MULSOIL_COMPONENT2 = 0x7f0d02a8;
        public static final int EARTH_MULSOIL_COMPONENT3 = 0x7f0d02a9;
        public static final int EARTH_MULSOIL_CROP = 0x7f0d02aa;
        public static final int EARTH_MULSOIL_CULTURIST = 0x7f0d02ab;
        public static final int EARTH_MULSOIL_DATE = 0x7f0d02ac;
        public static final int EARTH_MULSOIL_ERODE = 0x7f0d02ad;
        public static final int EARTH_MULSOIL_MAPCODE = 0x7f0d02ae;
        public static final int EARTH_MULSOIL_NEW_PRODUCT = 0x7f0d02af;
        public static final int EARTH_MULSOIL_ORG_SAMCODE = 0x7f0d02b0;
        public static final int EARTH_MULSOIL_PICKLED = 0x7f0d02b1;
        public static final int EARTH_MULSOIL_POLLUTION = 0x7f0d02b2;
        public static final int EARTH_MULSOIL_POSITION = 0x7f0d02b3;
        public static final int EARTH_MULSOIL_PROVICE_CODE = 0x7f0d02b4;
        public static final int EARTH_MULSOIL_PROVICE_NAME = 0x7f0d02b5;
        public static final int EARTH_MULSOIL_RECORDER = 0x7f0d02b6;
        public static final int EARTH_MULSOIL_REGIONALISM = 0x7f0d02b7;
        public static final int EARTH_MULSOIL_REMARK = 0x7f0d02b8;
        public static final int EARTH_MULSOIL_ROCK_FEATURE = 0x7f0d02b9;
        public static final int EARTH_MULSOIL_ROUTH = 0x7f0d02ba;
        public static final int EARTH_MULSOIL_SAMED_UNIT = 0x7f0d02bb;
        public static final int EARTH_MULSOIL_SAMPER = 0x7f0d02bc;
        public static final int EARTH_MULSOIL_SAM_CODE = 0x7f0d02bd;
        public static final int EARTH_MULSOIL_SAM_DEPTH = 0x7f0d02be;
        public static final int EARTH_MULSOIL_SAM_TYPE = 0x7f0d02bf;
        public static final int EARTH_MULSOIL_SOIL_USED = 0x7f0d02c0;
        public static final int EARTH_MULSOIL_WATER_LEVEL = 0x7f0d02c1;
        public static final int EARTH_MULSOIL_XX = 0x7f0d02c2;
        public static final int EARTH_MULSOIL_YY = 0x7f0d02c3;
        public static final int EARTH_MULSTREM_CARD_NO = 0x7f0d02c4;
        public static final int EARTH_MULSTREM_CHECKER = 0x7f0d02c5;
        public static final int EARTH_MULSTREM_COMPONENT_1 = 0x7f0d02c6;
        public static final int EARTH_MULSTREM_COMPONENT_2 = 0x7f0d02c7;
        public static final int EARTH_MULSTREM_COMPONENT_3 = 0x7f0d02c8;
        public static final int EARTH_MULSTREM_CULTURIST = 0x7f0d02c9;
        public static final int EARTH_MULSTREM_CUTICLE_COLOR1 = 0x7f0d02ca;
        public static final int EARTH_MULSTREM_DATE = 0x7f0d02cb;
        public static final int EARTH_MULSTREM_DEBRIS = 0x7f0d02cc;
        public static final int EARTH_MULSTREM_DESCRIBE = 0x7f0d02cd;
        public static final int EARTH_MULSTREM_HYDROBIOLOGY = 0x7f0d02ce;
        public static final int EARTH_MULSTREM_MAPCODE = 0x7f0d02cf;
        public static final int EARTH_MULSTREM_ORG_SAMCODE = 0x7f0d02d0;
        public static final int EARTH_MULSTREM_PHYSIOGNOMY = 0x7f0d02d1;
        public static final int EARTH_MULSTREM_POLLUTION = 0x7f0d02d2;
        public static final int EARTH_MULSTREM_POLLUTION_DES = 0x7f0d02d3;
        public static final int EARTH_MULSTREM_POSITION = 0x7f0d02d4;
        public static final int EARTH_MULSTREM_PROVICE_CODE = 0x7f0d02d5;
        public static final int EARTH_MULSTREM_PROVICE_NAME = 0x7f0d02d6;
        public static final int EARTH_MULSTREM_RECORDER = 0x7f0d02d7;
        public static final int EARTH_MULSTREM_REMARK = 0x7f0d02d8;
        public static final int EARTH_MULSTREM_ROUTH = 0x7f0d02d9;
        public static final int EARTH_MULSTREM_SAMED_DEPTH = 0x7f0d02da;
        public static final int EARTH_MULSTREM_SAMPER = 0x7f0d02db;
        public static final int EARTH_MULSTREM_SAM_BAGCODE = 0x7f0d02dc;
        public static final int EARTH_MULSTREM_SAM_CODE = 0x7f0d02dd;
        public static final int EARTH_MULSTREM_SAM_COLOR2 = 0x7f0d02de;
        public static final int EARTH_MULSTREM_SAM_TYPE = 0x7f0d02df;
        public static final int EARTH_MULSTREM_TAG_POS = 0x7f0d02e0;
        public static final int EARTH_MULSTREM_WATER_DEPTH = 0x7f0d02e1;
        public static final int EARTH_MULSTREM_XX = 0x7f0d02e2;
        public static final int EARTH_MULSTREM_YY = 0x7f0d02e3;
        public static final int EARTH_MULWATER_BEGRIME = 0x7f0d02e4;
        public static final int EARTH_MULWATER_CARD_NO = 0x7f0d02e5;
        public static final int EARTH_MULWATER_CHECKER = 0x7f0d02e6;
        public static final int EARTH_MULWATER_DATE = 0x7f0d02e7;
        public static final int EARTH_MULWATER_DIS_TYPE = 0x7f0d02e8;
        public static final int EARTH_MULWATER_HCI = 0x7f0d02e9;
        public static final int EARTH_MULWATER_HNO3 = 0x7f0d02ea;
        public static final int EARTH_MULWATER_K2CR207 = 0x7f0d02eb;
        public static final int EARTH_MULWATER_MAPCODE = 0x7f0d02ec;
        public static final int EARTH_MULWATER_NAOH = 0x7f0d02ed;
        public static final int EARTH_MULWATER_NEPHELOMETER = 0x7f0d02ee;
        public static final int EARTH_MULWATER_ORG_SAMCODE = 0x7f0d02ef;
        public static final int EARTH_MULWATER_PH = 0x7f0d02f0;
        public static final int EARTH_MULWATER_POLLUTION = 0x7f0d02f1;
        public static final int EARTH_MULWATER_PROVICE_CODE = 0x7f0d02f2;
        public static final int EARTH_MULWATER_PROVICE_NAME = 0x7f0d02f3;
        public static final int EARTH_MULWATER_PURPOSE = 0x7f0d02f4;
        public static final int EARTH_MULWATER_RECORDER = 0x7f0d02f5;
        public static final int EARTH_MULWATER_REGIONALISM = 0x7f0d02f6;
        public static final int EARTH_MULWATER_REMARK = 0x7f0d02f7;
        public static final int EARTH_MULWATER_ROUTH = 0x7f0d02f8;
        public static final int EARTH_MULWATER_SAMED_UNIT = 0x7f0d02f9;
        public static final int EARTH_MULWATER_SAMPER = 0x7f0d02fa;
        public static final int EARTH_MULWATER_SAM_CODE = 0x7f0d02fb;
        public static final int EARTH_MULWATER_SAM_TYPE = 0x7f0d02fc;
        public static final int EARTH_MULWATER_SAM_WATER1 = 0x7f0d02fd;
        public static final int EARTH_MULWATER_SAM_WATER2 = 0x7f0d02fe;
        public static final int EARTH_MULWATER_TAG_POS = 0x7f0d02ff;
        public static final int EARTH_MULWATER_TEMPERATURE = 0x7f0d0300;
        public static final int EARTH_MULWATER_WATER_COLOR = 0x7f0d0301;
        public static final int EARTH_MULWATER_WATER_LEVEL1 = 0x7f0d0302;
        public static final int EARTH_MULWATER_WATER_LEVEL2 = 0x7f0d0303;
        public static final int EARTH_MULWATER_WATER_SMELL = 0x7f0d0304;
        public static final int EARTH_MULWATER_WELL_DEPTH = 0x7f0d0305;
        public static final int EARTH_MULWATER_XX = 0x7f0d0306;
        public static final int EARTH_MULWATER_YY = 0x7f0d0307;
        public static final int EARTH_ORI_WEIGHT = 0x7f0d0308;
        public static final int EARTH_POINT_CODE = 0x7f0d0309;
        public static final int EARTH_POSITION = 0x7f0d030a;
        public static final int EARTH_RECORDER = 0x7f0d030b;
        public static final int EARTH_ROCKSY = 0x7f0d030c;
        public static final int EARTH_ROUTE_CODE = 0x7f0d030d;
        public static final int EARTH_ROUTE_DATE = 0x7f0d030e;
        public static final int EARTH_ROUTE_FELLOW = 0x7f0d030f;
        public static final int EARTH_ROUTE_KCANBCODE = 0x7f0d0310;
        public static final int EARTH_ROUTE_KCANBNAME = 0x7f0d0311;
        public static final int EARTH_ROUTE_KTXCODE = 0x7f0d0312;
        public static final int EARTH_ROUTE_LENGTH = 0x7f0d0313;
        public static final int EARTH_ROUTE_RECORDER = 0x7f0d0314;
        public static final int EARTH_ROUTE_REMARK = 0x7f0d0315;
        public static final int EARTH_ROUTE_ROUTE_CODE = 0x7f0d0316;
        public static final int EARTH_ROUTE_SAMPLER = 0x7f0d0317;
        public static final int EARTH_ROUTE_TASK = 0x7f0d0318;
        public static final int EARTH_ROUTE_UNIT = 0x7f0d0319;
        public static final int EARTH_ROUTE_WORKPLACE = 0x7f0d031a;
        public static final int EARTH_SEMI_TYPE = 0x7f0d031b;
        public static final int EARTH_SOILSEDI_BAGCODE = 0x7f0d031c;
        public static final int EARTH_SOILSEDI_CHARACTER = 0x7f0d031d;
        public static final int EARTH_SOILSEDI_COLOR = 0x7f0d031e;
        public static final int EARTH_SOILSEDI_DEPTH = 0x7f0d031f;
        public static final int EARTH_SOILSEDI_GEOLOGY = 0x7f0d0320;
        public static final int EARTH_SOILSEDI_HUMIDITY = 0x7f0d0321;
        public static final int EARTH_SOILSEDI_KCANBCODE = 0x7f0d0322;
        public static final int EARTH_SOILSEDI_KCANBNAME = 0x7f0d0323;
        public static final int EARTH_SOILSEDI_KTXCODE = 0x7f0d0324;
        public static final int EARTH_SOILSEDI_LAY = 0x7f0d0325;
        public static final int EARTH_SOILSEDI_LINCODE = 0x7f0d0326;
        public static final int EARTH_SOILSEDI_METHOD = 0x7f0d0327;
        public static final int EARTH_SOILSEDI_PH = 0x7f0d0328;
        public static final int EARTH_SOILSEDI_PHYSIOGNOMY = 0x7f0d0329;
        public static final int EARTH_SOILSEDI_PNTCODE = 0x7f0d032a;
        public static final int EARTH_SOILSEDI_POSITION = 0x7f0d032b;
        public static final int EARTH_SOILSEDI_RECORDER = 0x7f0d032c;
        public static final int EARTH_SOILSEDI_ROUTECODE = 0x7f0d032d;
        public static final int EARTH_SOILSEDI_SAMDATE = 0x7f0d032e;
        public static final int EARTH_SOILSEDI_SAMPLER = 0x7f0d032f;
        public static final int EARTH_SOILSEDI_THICKNESS = 0x7f0d0330;
        public static final int EARTH_SOILSEDI_TYPE = 0x7f0d0331;
        public static final int EARTH_SOILSEDI_WEATHER = 0x7f0d0332;
        public static final int EARTH_SOILSEDI_X = 0x7f0d0333;
        public static final int EARTH_SOILSEDI_Y = 0x7f0d0334;
        public static final int EARTH_STREMSEDI_BAGCODE = 0x7f0d0335;
        public static final int EARTH_STREMSEDI_COLOR = 0x7f0d0336;
        public static final int EARTH_STREMSEDI_EH = 0x7f0d0337;
        public static final int EARTH_STREMSEDI_GEOLOGY = 0x7f0d0338;
        public static final int EARTH_STREMSEDI_GRANULARITY = 0x7f0d0339;
        public static final int EARTH_STREMSEDI_HUMIDITY = 0x7f0d033a;
        public static final int EARTH_STREMSEDI_KCANBCODE = 0x7f0d033b;
        public static final int EARTH_STREMSEDI_KCANBNAME = 0x7f0d033c;
        public static final int EARTH_STREMSEDI_KTXCODE = 0x7f0d033d;
        public static final int EARTH_STREMSEDI_LINCODE = 0x7f0d033e;
        public static final int EARTH_STREMSEDI_METHOD = 0x7f0d033f;
        public static final int EARTH_STREMSEDI_PH = 0x7f0d0340;
        public static final int EARTH_STREMSEDI_PHYSIOGNOMY = 0x7f0d0341;
        public static final int EARTH_STREMSEDI_PNTCODE = 0x7f0d0342;
        public static final int EARTH_STREMSEDI_POLLUTE = 0x7f0d0343;
        public static final int EARTH_STREMSEDI_POSITION = 0x7f0d0344;
        public static final int EARTH_STREMSEDI_RECORDER = 0x7f0d0345;
        public static final int EARTH_STREMSEDI_ROUTECODE = 0x7f0d0346;
        public static final int EARTH_STREMSEDI_SAMDATE = 0x7f0d0347;
        public static final int EARTH_STREMSEDI_SAMPLER = 0x7f0d0348;
        public static final int EARTH_STREMSEDI_TURBIDITY = 0x7f0d0349;
        public static final int EARTH_STREMSEDI_TYPE = 0x7f0d034a;
        public static final int EARTH_STREMSEDI_WEATHER = 0x7f0d034b;
        public static final int EARTH_STREMSEDI_X = 0x7f0d034c;
        public static final int EARTH_STREMSEDI_Y = 0x7f0d034d;
        public static final int EARTH_TASK = 0x7f0d034e;
        public static final int EARTH_VIEW_MINERAL = 0x7f0d034f;
        public static final int EARTH_WEATHER = 0x7f0d0350;
        public static final int EARTH_WEIGHT = 0x7f0d0351;
        public static final int EARTH_XX = 0x7f0d0352;
        public static final int EARTH_YY = 0x7f0d0353;
        public static final int FOS_FLDNAME_AGE = 0x7f0d0354;
        public static final int FOS_FLDNAME_ANALYSE = 0x7f0d0355;
        public static final int FOS_FLDNAME_CODE = 0x7f0d0356;
        public static final int FOS_FLDNAME_DATE = 0x7f0d0357;
        public static final int FOS_FLDNAME_GBCODE = 0x7f0d0358;
        public static final int FOS_FLDNAME_GEOUNIT = 0x7f0d0359;
        public static final int FOS_FLDNAME_LAYCODE = 0x7f0d035a;
        public static final int FOS_FLDNAME_NAME = 0x7f0d035b;
        public static final int FOS_FLDNAME_REMARK = 0x7f0d035c;
        public static final int FOS_FLDNAME_SAMPLING = 0x7f0d035d;
        public static final int FOS_FLDNAME_SECCODE = 0x7f0d035e;
        public static final int FOS_FLDNAME_SECPOINT = 0x7f0d035f;
        public static final int FOS_FLDNAME_SLOPE_L = 0x7f0d0360;
        public static final int FOS_FLDNAME_TYPE = 0x7f0d0361;
        public static final int GEOCHEM25W_MULMEDIA_FLDNAME_SAMCODE = 0x7f0d0362;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_DEVICETYPE = 0x7f0d0363;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_LOCATION = 0x7f0d0364;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_LOCATIONMAPID = 0x7f0d0365;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_MAINID = 0x7f0d0366;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_PERSON = 0x7f0d0367;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_REMARK = 0x7f0d0368;
        public static final int GEOCHEM25W_MULMEDIA__FLDNAME_WORKPLACE = 0x7f0d0369;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_DEVICETYPE = 0x7f0d036a;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_DIRECTION = 0x7f0d036b;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_EXIST = 0x7f0d036c;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_FILENO = 0x7f0d036d;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_LOCATION = 0x7f0d036e;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_LOCATIONMAPID = 0x7f0d036f;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_MAINID = 0x7f0d0370;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_MEDIAID = 0x7f0d0371;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_NOTE = 0x7f0d0372;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_NULL = 0x7f0d0373;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_PERSON = 0x7f0d0374;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_REMARK = 0x7f0d0375;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_SAMCODE = 0x7f0d0376;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_TITLE = 0x7f0d0377;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_TOOL = 0x7f0d0378;
        public static final int GEOCHEM25W_MULMEDIA__PROMPT_WORKPLACE = 0x7f0d0379;
        public static final int GEOCHEMMULSEDIMENT_FIELD_LOCTION = 0x7f0d037a;
        public static final int GEOCHEMMULSEDIMENT_FIELD_SAMCOLOR = 0x7f0d037b;
        public static final int GEOCHEMMULSEDIMENT_FIELD_SAMDEPTH = 0x7f0d037c;
        public static final int GEOCHEMMULSEDIMENT_FIELD_SURFACECOLOR = 0x7f0d037d;
        public static final int GEOCHEMMULSEDIMENT_FIELD_WRECKAGE = 0x7f0d037e;
        public static final int GEOCHEMMULSOIL_DBFIELD_DESC = 0x7f0d037f;
        public static final int GEOCHEMMULSOIL_DBFIELD_ENDDEPTH = 0x7f0d0380;
        public static final int GEOCHEMMULSOIL_DBFIELD_ID = 0x7f0d0381;
        public static final int GEOCHEMMULSOIL_DBFIELD_PDESC = 0x7f0d0382;
        public static final int GEOCHEMMULSOIL_DBFIELD_PENDDEPTH = 0x7f0d0383;
        public static final int GEOCHEMMULSOIL_DBFIELD_PID = 0x7f0d0384;
        public static final int GEOCHEMMULSOIL_DBFIELD_PSAMCODE = 0x7f0d0385;
        public static final int GEOCHEMMULSOIL_DBFIELD_PSOILTYPE = 0x7f0d0386;
        public static final int GEOCHEMMULSOIL_DBFIELD_PSTARTDEPTH = 0x7f0d0387;
        public static final int GEOCHEMMULSOIL_DBFIELD_SAMCODE = 0x7f0d0388;
        public static final int GEOCHEMMULSOIL_DBFIELD_SOILTYPE = 0x7f0d0389;
        public static final int GEOCHEMMULSOIL_DBFIELD_STARTDEPTH = 0x7f0d038a;
        public static final int GEOCHEMMULSOIL_FIELD_ALTITUDE = 0x7f0d038b;
        public static final int GEOCHEMMULSOIL_FIELD_BAGCODE = 0x7f0d038c;
        public static final int GEOCHEMMULSOIL_FIELD_CAUSE = 0x7f0d038d;
        public static final int GEOCHEMMULSOIL_FIELD_CHECKER = 0x7f0d038e;
        public static final int GEOCHEMMULSOIL_FIELD_COLOR = 0x7f0d038f;
        public static final int GEOCHEMMULSOIL_FIELD_COMPONENT = 0x7f0d0390;
        public static final int GEOCHEMMULSOIL_FIELD_COORDX = 0x7f0d0391;
        public static final int GEOCHEMMULSOIL_FIELD_COORDY = 0x7f0d0392;
        public static final int GEOCHEMMULSOIL_FIELD_DEPTH = 0x7f0d0393;
        public static final int GEOCHEMMULSOIL_FIELD_DESC = 0x7f0d0394;
        public static final int GEOCHEMMULSOIL_FIELD_ERODE = 0x7f0d0395;
        public static final int GEOCHEMMULSOIL_FIELD_GPSFILEID = 0x7f0d0396;
        public static final int GEOCHEMMULSOIL_FIELD_GPSID = 0x7f0d0397;
        public static final int GEOCHEMMULSOIL_FIELD_GPSMAPFILE = 0x7f0d0398;
        public static final int GEOCHEMMULSOIL_FIELD_LANDFORM = 0x7f0d0399;
        public static final int GEOCHEMMULSOIL_FIELD_MAINID = 0x7f0d039a;
        public static final int GEOCHEMMULSOIL_FIELD_MAPCODE = 0x7f0d039b;
        public static final int GEOCHEMMULSOIL_FIELD_MAPNAME = 0x7f0d039c;
        public static final int GEOCHEMMULSOIL_FIELD_POLLUTION = 0x7f0d039d;
        public static final int GEOCHEMMULSOIL_FIELD_PROVINCE = 0x7f0d039e;
        public static final int GEOCHEMMULSOIL_FIELD_RECORDER = 0x7f0d039f;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK = 0x7f0d03a0;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK1 = 0x7f0d03a1;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK2 = 0x7f0d03a2;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK3 = 0x7f0d03a3;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK4 = 0x7f0d03a4;
        public static final int GEOCHEMMULSOIL_FIELD_REMARK5 = 0x7f0d03a5;
        public static final int GEOCHEMMULSOIL_FIELD_ROUTECODE = 0x7f0d03a6;
        public static final int GEOCHEMMULSOIL_FIELD_SALINIZATION = 0x7f0d03a7;
        public static final int GEOCHEMMULSOIL_FIELD_SAMCODE = 0x7f0d03a8;
        public static final int GEOCHEMMULSOIL_FIELD_SAMDATE = 0x7f0d03a9;
        public static final int GEOCHEMMULSOIL_FIELD_SAMORGCODE = 0x7f0d03aa;
        public static final int GEOCHEMMULSOIL_FIELD_SAMPLER = 0x7f0d03ab;
        public static final int GEOCHEMMULSOIL_FIELD_SOILUSE = 0x7f0d03ac;
        public static final int GEOCHEMMULSOIL_FIELD_STARTDEPTH = 0x7f0d03ad;
        public static final int GEOCHEMMULSOIL_FIELD_UNIT = 0x7f0d03ae;
        public static final int GEOCHEMMULWATER_FIELD_DEPTH = 0x7f0d03af;
        public static final int GEOCHEMMULWATER_FIELD_PH = 0x7f0d03b0;
        public static final int GEOCHEMMULWATER_FIELD_POLLUTION = 0x7f0d03b1;
        public static final int GEOCHEMMULWATER_FIELD_SEASON = 0x7f0d03b2;
        public static final int GEOCHEMMULWATER_FIELD_SEDIMENT = 0x7f0d03b3;
        public static final int GEOCHEMMULWATER_FIELD_SMELL = 0x7f0d03b4;
        public static final int GEOCHEMMULWATER_FIELD_TEMPERATURE = 0x7f0d03b5;
        public static final int GEOCHEMMULWATER_FIELD_TRUBIDNESS = 0x7f0d03b6;
        public static final int GEOCHEMMULWATER_FIELD_USE = 0x7f0d03b7;
        public static final int GEOCHEMMULWATER_FIELD_WATERTYPE = 0x7f0d03b8;
        public static final int GEOCHEMMULWATER_FIELD_WELLDEPTH = 0x7f0d03b9;
        public static final int GEOCHEMNATIONAL_PROMPT_CARDNO = 0x7f0d03ba;
        public static final int GEOCHEMNATIONAL_PROMPT_CoordX = 0x7f0d03bb;
        public static final int GEOCHEMNATIONAL_PROMPT_CoordY = 0x7f0d03bc;
        public static final int GEOCHEMNATIONAL_PROMPT_Country = 0x7f0d03bd;
        public static final int GEOCHEMNATIONAL_PROMPT_County = 0x7f0d03be;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamClay = 0x7f0d03bf;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamColor = 0x7f0d03c0;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamFinesand = 0x7f0d03c1;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamSand = 0x7f0d03c2;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamSilt = 0x7f0d03c3;
        public static final int GEOCHEMNATIONAL_PROMPT_DeepSamSize = 0x7f0d03c4;
        public static final int GEOCHEMNATIONAL_PROMPT_Desc = 0x7f0d03c5;
        public static final int GEOCHEMNATIONAL_PROMPT_Height = 0x7f0d03c6;
        public static final int GEOCHEMNATIONAL_PROMPT_LandSetting = 0x7f0d03c7;
        public static final int GEOCHEMNATIONAL_PROMPT_Leader = 0x7f0d03c8;
        public static final int GEOCHEMNATIONAL_PROMPT_MapSheet = 0x7f0d03c9;
        public static final int GEOCHEMNATIONAL_PROMPT_OrgSamNo = 0x7f0d03ca;
        public static final int GEOCHEMNATIONAL_PROMPT_PictureNo = 0x7f0d03cb;
        public static final int GEOCHEMNATIONAL_PROMPT_Province = 0x7f0d03cc;
        public static final int GEOCHEMNATIONAL_PROMPT_Registrar = 0x7f0d03cd;
        public static final int GEOCHEMNATIONAL_PROMPT_RouteCode = 0x7f0d03ce;
        public static final int GEOCHEMNATIONAL_PROMPT_SamDate = 0x7f0d03cf;
        public static final int GEOCHEMNATIONAL_PROMPT_SamNo = 0x7f0d03d0;
        public static final int GEOCHEMNATIONAL_PROMPT_SamPerson = 0x7f0d03d1;
        public static final int GEOCHEMNATIONAL_PROMPT_SamType = 0x7f0d03d2;
        public static final int GEOCHEMNATIONAL_PROMPT_StreamFlow = 0x7f0d03d3;
        public static final int GEOCHEMNATIONAL_PROMPT_StreamName = 0x7f0d03d4;
        public static final int GEOCHEMNATIONAL_PROMPT_StreamWidth = 0x7f0d03d5;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamClay = 0x7f0d03d6;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamColor = 0x7f0d03d7;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamFinesand = 0x7f0d03d8;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamSand = 0x7f0d03d9;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamSilt = 0x7f0d03da;
        public static final int GEOCHEMNATIONAL_PROMPT_TopSamSize = 0x7f0d03db;
        public static final int GEOCHEMNATIONAL_PROMPT_Topography = 0x7f0d03dc;
        public static final int GEOCHEMNATIONAL_PROMPT_Village = 0x7f0d03dd;
        public static final int GEOCHEMNATIONAL_PROMPT_Weather = 0x7f0d03de;
        public static final int GPO_FLDNAME_ALTITUDE = 0x7f0d03df;
        public static final int GPO_FLDNAME_DATE = 0x7f0d03e0;
        public static final int GPO_FLDNAME_GEOMORPH = 0x7f0d03e1;
        public static final int GPO_FLDNAME_GEOPOINT = 0x7f0d03e2;
        public static final int GPO_FLDNAME_GPODESCRIBE = 0x7f0d03e3;
        public static final int GPO_FLDNAME_LATITUDE = 0x7f0d03e4;
        public static final int GPO_FLDNAME_LAYCODE = 0x7f0d03e5;
        public static final int GPO_FLDNAME_LITHO_1 = 0x7f0d03e6;
        public static final int GPO_FLDNAME_LITHO_2 = 0x7f0d03e7;
        public static final int GPO_FLDNAME_LITHO_3 = 0x7f0d03e8;
        public static final int GPO_FLDNAME_LITHO_A = 0x7f0d03e9;
        public static final int GPO_FLDNAME_LITHO_B = 0x7f0d03ea;
        public static final int GPO_FLDNAME_LITHO_C = 0x7f0d03eb;
        public static final int GPO_FLDNAME_LOCATION = 0x7f0d03ec;
        public static final int GPO_FLDNAME_LONGITUDE = 0x7f0d03ed;
        public static final int GPO_FLDNAME_OUTCROP = 0x7f0d03ee;
        public static final int GPO_FLDNAME_SECCODE = 0x7f0d03ef;
        public static final int GPO_FLDNAME_SECPOINT = 0x7f0d03f0;
        public static final int GPO_FLDNAME_SLOPE_L = 0x7f0d03f1;
        public static final int GPO_FLDNAME_STRAPHA = 0x7f0d03f2;
        public static final int GPO_FLDNAME_STRAPHB = 0x7f0d03f3;
        public static final int GPO_FLDNAME_STRAPHC = 0x7f0d03f4;
        public static final int GPO_FLDNAME_STRARAB = 0x7f0d03f5;
        public static final int GPO_FLDNAME_STRARAC = 0x7f0d03f6;
        public static final int GPO_FLDNAME_STRARBC = 0x7f0d03f7;
        public static final int GPO_FLDNAME_TYPE = 0x7f0d03f8;
        public static final int GPO_FLDNAME_WEATHING = 0x7f0d03f9;
        public static final int GPO_FLDNAME_XX = 0x7f0d03fa;
        public static final int GPO_FLDNAME_YY = 0x7f0d03fb;
        public static final int GRAVEL_DIP = 0x7f0d03fc;
        public static final int GRAVEL_DIPANGLE = 0x7f0d03fd;
        public static final int GRAVEL_FLDNAME_DIP = 0x7f0d03fe;
        public static final int GRAVEL_FLDNAME_DIPANGLE = 0x7f0d03ff;
        public static final int GRAVEL_FLDNAME_GPOINT = 0x7f0d0400;
        public static final int GRAVEL_FLDNAME_LINECODE = 0x7f0d0401;
        public static final int GRAVEL_FLDNAME_LONGAXIS = 0x7f0d0402;
        public static final int GRAVEL_FLDNAME_ORDER = 0x7f0d0403;
        public static final int GRAVEL_FLDNAME_ROCKTYPE = 0x7f0d0404;
        public static final int GRAVEL_FLDNAME_SHORTAXIS = 0x7f0d0405;
        public static final int GRAVEL_FLDNAME_TYPE = 0x7f0d0406;
        public static final int GRAVEL_FLDNAME_ZAXIS = 0x7f0d0407;
        public static final int GRAVEL_GPOINT = 0x7f0d0408;
        public static final int GRAVEL_LINECODE = 0x7f0d0409;
        public static final int GRAVEL_LONGAXIS = 0x7f0d040a;
        public static final int GRAVEL_ORDER = 0x7f0d040b;
        public static final int GRAVEL_ROCKTYPE = 0x7f0d040c;
        public static final int GRAVEL_SHORTAXIS = 0x7f0d040d;
        public static final int GRAVEL_TYPE = 0x7f0d040e;
        public static final int GRAVEL_ZAXIS = 0x7f0d040f;
        public static final int Gravel_btn_tool = 0x7f0d0410;
        public static final int LAY_FLDNAME_DESCRIBE = 0x7f0d0411;
        public static final int LAY_FLDNAME_DIP = 0x7f0d0412;
        public static final int LAY_FLDNAME_DIP_ANGLE = 0x7f0d0413;
        public static final int LAY_FLDNAME_GRADE = 0x7f0d0414;
        public static final int LAY_FLDNAME_LAYCODE = 0x7f0d0415;
        public static final int LAY_FLDNAME_LAYCODE_ = 0x7f0d0416;
        public static final int LAY_FLDNAME_SECCODE = 0x7f0d0417;
        public static final int LAY_FLDNAME_SECPOINT = 0x7f0d0418;
        public static final int LAY_FLDNAME_SIGN = 0x7f0d0419;
        public static final int LAY_FLDNAME_SLOPE_L = 0x7f0d041a;
        public static final int LAY_FLDNAME_SLOPE_L_ = 0x7f0d041b;
        public static final int LAY_FLDNAME_TREND = 0x7f0d041c;
        public static final int LAY_FLDNAME_TREND_PM = 0x7f0d041d;
        public static final int LAY_FLDNAME_T_THICK = 0x7f0d041e;
        public static final int LAY_FLDNAME_V_THICK = 0x7f0d041f;
        public static final int MAIN_PROMPT_TYPE0 = 0x7f0d0420;
        public static final int MAIN_PROMPT_TYPE1 = 0x7f0d0421;
        public static final int MAIN_PROMPT_TYPE2 = 0x7f0d0422;
        public static final int MENU_DATASERVICE_BIGDATA = 0x7f0d0423;
        public static final int MENU_DATASERVICE_ML = 0x7f0d0424;
        public static final int MENU_DATASERVICE_UPLOAD = 0x7f0d0425;
        public static final int PHO_FLDNAME_CODE = 0x7f0d0426;
        public static final int PHO_FLDNAME_DESCRIBE = 0x7f0d0427;
        public static final int PHO_FLDNAME_DIRECTION = 0x7f0d0428;
        public static final int PHO_FLDNAME_GBCODE = 0x7f0d0429;
        public static final int PHO_FLDNAME_LAYCODE = 0x7f0d042a;
        public static final int PHO_FLDNAME_LAYCODE_ = 0x7f0d042b;
        public static final int PHO_FLDNAME_MPG_ID = 0x7f0d042c;
        public static final int PHO_FLDNAME_NUMBER = 0x7f0d042d;
        public static final int PHO_FLDNAME_SECCODE = 0x7f0d042e;
        public static final int PHO_FLDNAME_SECPOINT = 0x7f0d042f;
        public static final int PHO_FLDNAME_SLOPE_L = 0x7f0d0430;
        public static final int PHO_FLDNAME_SOUND_ID = 0x7f0d0431;
        public static final int REM_CHECKER = 0x7f0d0432;
        public static final int REM_CODE = 0x7f0d0433;
        public static final int REM_DESCRIPTION1 = 0x7f0d0434;
        public static final int REM_DESCRIPTION2 = 0x7f0d0435;
        public static final int REM_DESCRIPTION3 = 0x7f0d0436;
        public static final int REM_FLDNAME_CHECKER = 0x7f0d0437;
        public static final int REM_FLDNAME_CODE = 0x7f0d0438;
        public static final int REM_FLDNAME_DESCRIPTION1 = 0x7f0d0439;
        public static final int REM_FLDNAME_DESCRIPTION2 = 0x7f0d043a;
        public static final int REM_FLDNAME_DESCRIPTION3 = 0x7f0d043b;
        public static final int REM_FLDNAME_GROUP1 = 0x7f0d043c;
        public static final int REM_FLDNAME_GROUP2 = 0x7f0d043d;
        public static final int REM_FLDNAME_GROUP3 = 0x7f0d043e;
        public static final int REM_FLDNAME_GROUP4 = 0x7f0d043f;
        public static final int REM_FLDNAME_GROUP5 = 0x7f0d0440;
        public static final int REM_FLDNAME_IMAGE1 = 0x7f0d0441;
        public static final int REM_FLDNAME_IMAGE2 = 0x7f0d0442;
        public static final int REM_FLDNAME_IMAGE3 = 0x7f0d0443;
        public static final int REM_FLDNAME_INCODE = 0x7f0d0444;
        public static final int REM_FLDNAME_LOCATION = 0x7f0d0445;
        public static final int REM_FLDNAME_MAP1 = 0x7f0d0446;
        public static final int REM_FLDNAME_MAP2 = 0x7f0d0447;
        public static final int REM_FLDNAME_MAP3 = 0x7f0d0448;
        public static final int REM_FLDNAME_MAPCODE = 0x7f0d0449;
        public static final int REM_FLDNAME_MAPNAME = 0x7f0d044a;
        public static final int REM_FLDNAME_NAME = 0x7f0d044b;
        public static final int REM_FLDNAME_OUTCODE = 0x7f0d044c;
        public static final int REM_FLDNAME_PROTECT = 0x7f0d044d;
        public static final int REM_FLDNAME_ROUTECODE = 0x7f0d044e;
        public static final int REM_FLDNAME_RS1 = 0x7f0d044f;
        public static final int REM_FLDNAME_RS3 = 0x7f0d0450;
        public static final int REM_FLDNAME_SOUND1 = 0x7f0d0451;
        public static final int REM_FLDNAME_SOUND2 = 0x7f0d0452;
        public static final int REM_FLDNAME_SOUND3 = 0x7f0d0453;
        public static final int REM_FLDNAME_SUBTYPE = 0x7f0d0454;
        public static final int REM_FLDNAME_SURVERYER = 0x7f0d0455;
        public static final int REM_FLDNAME_TIME = 0x7f0d0456;
        public static final int REM_FLDNAME_TRAFFIC = 0x7f0d0457;
        public static final int REM_FLDNAME_TYPE = 0x7f0d0458;
        public static final int REM_FLDNAME_VALUE1 = 0x7f0d0459;
        public static final int REM_FLDNAME_VALUE2 = 0x7f0d045a;
        public static final int REM_FLDNAME_VEDIO1 = 0x7f0d045b;
        public static final int REM_FLDNAME_VEDIO2 = 0x7f0d045c;
        public static final int REM_FLDNAME_VEDIO3 = 0x7f0d045d;
        public static final int REM_FLDNAME_X = 0x7f0d045e;
        public static final int REM_FLDNAME_Y = 0x7f0d045f;
        public static final int REM_FLDNAME_Z = 0x7f0d0460;
        public static final int REM_IMAGE1 = 0x7f0d0461;
        public static final int REM_IMAGE2 = 0x7f0d0462;
        public static final int REM_IMAGE3 = 0x7f0d0463;
        public static final int REM_INCODE = 0x7f0d0464;
        public static final int REM_LOCATION = 0x7f0d0465;
        public static final int REM_MAP1 = 0x7f0d0466;
        public static final int REM_MAP2 = 0x7f0d0467;
        public static final int REM_MAP3 = 0x7f0d0468;
        public static final int REM_MAPCODE = 0x7f0d0469;
        public static final int REM_MAPNAME = 0x7f0d046a;
        public static final int REM_NAME = 0x7f0d046b;
        public static final int REM_OUTCODE = 0x7f0d046c;
        public static final int REM_PROTECT = 0x7f0d046d;
        public static final int REM_ROUTECODE = 0x7f0d046e;
        public static final int REM_RS1 = 0x7f0d046f;
        public static final int REM_RS3 = 0x7f0d0470;
        public static final int REM_SUBTYPE = 0x7f0d0471;
        public static final int REM_SURVERYER = 0x7f0d0472;
        public static final int REM_TIME = 0x7f0d0473;
        public static final int REM_TRAFFIC = 0x7f0d0474;
        public static final int REM_TYPE = 0x7f0d0475;
        public static final int REM_VALUE1 = 0x7f0d0476;
        public static final int REM_VALUE2 = 0x7f0d0477;
        public static final int REM_X = 0x7f0d0478;
        public static final int REM_Y = 0x7f0d0479;
        public static final int REM_Z = 0x7f0d047a;
        public static final int RGMAP_BIGDATASERVICE_FORDETAILS = 0x7f0d047b;
        public static final int RGMAP_BIGDATASERVICE_INPUTKEY = 0x7f0d047c;
        public static final int RGMAP_BITMAP_NODECEVICE = 0x7f0d047d;
        public static final int RGMAP_BITMAP_REDRAW = 0x7f0d047e;
        public static final int RGMAP_BITMAP_UNDO = 0x7f0d047f;
        public static final int RGMAP_CHKSTYLE_DEPART = 0x7f0d0480;
        public static final int RGMAP_CHKSTYLE_EACH = 0x7f0d0481;
        public static final int RGMAP_CHKSTYLE_GROUP = 0x7f0d0482;
        public static final int RGMAP_CHKSTYLE_ME = 0x7f0d0483;
        public static final int RGMAP_DBENGPOINT_ATTITJPOINT = 0x7f0d0484;
        public static final int RGMAP_DBENGPOINT_CODE = 0x7f0d0485;
        public static final int RGMAP_DBENGPOINT_DIP = 0x7f0d0486;
        public static final int RGMAP_DBENGPOINT_DIPANG = 0x7f0d0487;
        public static final int RGMAP_DBENGPOINT_FEATURE = 0x7f0d0488;
        public static final int RGMAP_DBENGPOINT_GAP = 0x7f0d0489;
        public static final int RGMAP_DBENGPOINT_GPOINT = 0x7f0d048a;
        public static final int RGMAP_DBENGPOINT_LENGTH = 0x7f0d048b;
        public static final int RGMAP_DBENGPOINT_LINECODE = 0x7f0d048c;
        public static final int RGMAP_DBENGPOINT_PACK = 0x7f0d048d;
        public static final int RGMAP_DBENGPOINT_PACKTYPE = 0x7f0d048e;
        public static final int RGMAP_DBENGPOINT_ROUGHNESS = 0x7f0d048f;
        public static final int RGMAP_DBENGPOINT_SPAN = 0x7f0d0490;
        public static final int RGMAP_DBENGPOINT_TREND = 0x7f0d0491;
        public static final int RGMAP_DBENGPOINT_TYPE = 0x7f0d0492;
        public static final int RGMAP_DBENGPOINT_WATER = 0x7f0d0493;
        public static final int RGMAP_DBENGPOINT_WIDTH = 0x7f0d0494;
        public static final int RGMAP_DBFLD_ENGPOINT_ATTITJPOINT = 0x7f0d0495;
        public static final int RGMAP_DBFLD_ENGPOINT_CODE = 0x7f0d0496;
        public static final int RGMAP_DBFLD_ENGPOINT_DIP = 0x7f0d0497;
        public static final int RGMAP_DBFLD_ENGPOINT_DIPANG = 0x7f0d0498;
        public static final int RGMAP_DBFLD_ENGPOINT_FEATURE = 0x7f0d0499;
        public static final int RGMAP_DBFLD_ENGPOINT_GAP = 0x7f0d049a;
        public static final int RGMAP_DBFLD_ENGPOINT_GPOINT = 0x7f0d049b;
        public static final int RGMAP_DBFLD_ENGPOINT_LENGTH = 0x7f0d049c;
        public static final int RGMAP_DBFLD_ENGPOINT_LINECODE = 0x7f0d049d;
        public static final int RGMAP_DBFLD_ENGPOINT_PACK = 0x7f0d049e;
        public static final int RGMAP_DBFLD_ENGPOINT_PACKTYPE = 0x7f0d049f;
        public static final int RGMAP_DBFLD_ENGPOINT_ROUGHNESS = 0x7f0d04a0;
        public static final int RGMAP_DBFLD_ENGPOINT_SPAN = 0x7f0d04a1;
        public static final int RGMAP_DBFLD_ENGPOINT_TREND = 0x7f0d04a2;
        public static final int RGMAP_DBFLD_ENGPOINT_TYPE = 0x7f0d04a3;
        public static final int RGMAP_DBFLD_ENGPOINT_WATER = 0x7f0d04a4;
        public static final int RGMAP_DBFLD_ENGPOINT_WIDTH = 0x7f0d04a5;
        public static final int RGMAP_DBFLD_HYDPOINT_AMOUNT = 0x7f0d04a6;
        public static final int RGMAP_DBFLD_HYDPOINT_CODE = 0x7f0d04a7;
        public static final int RGMAP_DBFLD_HYDPOINT_DEPTH = 0x7f0d04a8;
        public static final int RGMAP_DBFLD_HYDPOINT_GPOINT = 0x7f0d04a9;
        public static final int RGMAP_DBFLD_HYDPOINT_HYDROLOGY = 0x7f0d04aa;
        public static final int RGMAP_DBFLD_HYDPOINT_LINECODE = 0x7f0d04ab;
        public static final int RGMAP_DBFLD_HYDPOINT_METHOD = 0x7f0d04ac;
        public static final int RGMAP_DBFLD_HYDPOINT_PROTECTANT = 0x7f0d04ad;
        public static final int RGMAP_DBFLD_HYDPOINT_RECORDER = 0x7f0d04ae;
        public static final int RGMAP_DBFLD_HYDPOINT_TIME = 0x7f0d04af;
        public static final int RGMAP_DBFLD_HYDPOINT_X = 0x7f0d04b0;
        public static final int RGMAP_DBFLD_HYDPOINT_Y = 0x7f0d04b1;
        public static final int RGMAP_DBFLD_HYDPOINT_Z = 0x7f0d04b2;
        public static final int RGMAP_DBHYDPOINT_AMOUNT = 0x7f0d04b3;
        public static final int RGMAP_DBHYDPOINT_CODE = 0x7f0d04b4;
        public static final int RGMAP_DBHYDPOINT_DEPTH = 0x7f0d04b5;
        public static final int RGMAP_DBHYDPOINT_GPOINT = 0x7f0d04b6;
        public static final int RGMAP_DBHYDPOINT_HYDROLOGY = 0x7f0d04b7;
        public static final int RGMAP_DBHYDPOINT_LINECODE = 0x7f0d04b8;
        public static final int RGMAP_DBHYDPOINT_METHOD = 0x7f0d04b9;
        public static final int RGMAP_DBHYDPOINT_PROTECTANT = 0x7f0d04ba;
        public static final int RGMAP_DBHYDPOINT_RECORDER = 0x7f0d04bb;
        public static final int RGMAP_DBHYDPOINT_TIME = 0x7f0d04bc;
        public static final int RGMAP_DBHYDPOINT_X = 0x7f0d04bd;
        public static final int RGMAP_DBHYDPOINT_Y = 0x7f0d04be;
        public static final int RGMAP_DBHYDPOINT_Z = 0x7f0d04bf;
        public static final int RGMAP_EARTH_COMPANT1 = 0x7f0d04c0;
        public static final int RGMAP_EARTH_COMPANT2 = 0x7f0d04c1;
        public static final int RGMAP_EARTH_COMPANT3 = 0x7f0d04c2;
        public static final int RGMAP_ENGPOINT_AREA = 0x7f0d04c3;
        public static final int RGMAP_ENGPOINT_ATTITJPOINT = 0x7f0d04c4;
        public static final int RGMAP_ENGPOINT_CHECKER = 0x7f0d04c5;
        public static final int RGMAP_ENGPOINT_DIP = 0x7f0d04c6;
        public static final int RGMAP_ENGPOINT_DIPANG = 0x7f0d04c7;
        public static final int RGMAP_ENGPOINT_GPOINT = 0x7f0d04c8;
        public static final int RGMAP_ENGPOINT_LEN = 0x7f0d04c9;
        public static final int RGMAP_ENGPOINT_LINECODE = 0x7f0d04ca;
        public static final int RGMAP_ENGPOINT_POSITION = 0x7f0d04cb;
        public static final int RGMAP_ENGPOINT_RECORDER = 0x7f0d04cc;
        public static final int RGMAP_ENGPOINT_ROCKNAME = 0x7f0d04cd;
        public static final int RGMAP_ENGPOINT_STRAPH = 0x7f0d04ce;
        public static final int RGMAP_ENGPOINT_SURVERYER = 0x7f0d04cf;
        public static final int RGMAP_ENGPOINT_TIME = 0x7f0d04d0;
        public static final int RGMAP_ENGPOINT_TREND = 0x7f0d04d1;
        public static final int RGMAP_ENGPOINT_X = 0x7f0d04d2;
        public static final int RGMAP_ENGPOINT_Y = 0x7f0d04d3;
        public static final int RGMAP_ENGPOINT_Z = 0x7f0d04d4;
        public static final int RGMAP_FLDNAME_AALTITUDE = 0x7f0d04d5;
        public static final int RGMAP_FLDNAME_ABLELENTH = 0x7f0d04d6;
        public static final int RGMAP_FLDNAME_ACODE = 0x7f0d04d7;
        public static final int RGMAP_FLDNAME_ADATE = 0x7f0d04d8;
        public static final int RGMAP_FLDNAME_ADIP = 0x7f0d04d9;
        public static final int RGMAP_FLDNAME_ADIP_ANG = 0x7f0d04da;
        public static final int RGMAP_FLDNAME_AGBCODE = 0x7f0d04db;
        public static final int RGMAP_FLDNAME_AGE = 0x7f0d04dc;
        public static final int RGMAP_FLDNAME_AGEOPOINT = 0x7f0d04dd;
        public static final int RGMAP_FLDNAME_ALTITUDE = 0x7f0d04de;
        public static final int RGMAP_FLDNAME_AMAPCODE = 0x7f0d04df;
        public static final int RGMAP_FLDNAME_AMOUNT = 0x7f0d04e0;
        public static final int RGMAP_FLDNAME_ANGLE = 0x7f0d04e1;
        public static final int RGMAP_FLDNAME_APDESCRIBE = 0x7f0d04e2;
        public static final int RGMAP_FLDNAME_AREA = 0x7f0d04e3;
        public static final int RGMAP_FLDNAME_AREMARK = 0x7f0d04e4;
        public static final int RGMAP_FLDNAME_AROUTECODE = 0x7f0d04e5;
        public static final int RGMAP_FLDNAME_AR_CODE = 0x7f0d04e6;
        public static final int RGMAP_FLDNAME_ASTRAPH = 0x7f0d04e7;
        public static final int RGMAP_FLDNAME_ATREND = 0x7f0d04e8;
        public static final int RGMAP_FLDNAME_ATTITJPOINT = 0x7f0d04e9;
        public static final int RGMAP_FLDNAME_ATTITUDE_CODE = 0x7f0d04ea;
        public static final int RGMAP_FLDNAME_ATYPE = 0x7f0d04eb;
        public static final int RGMAP_FLDNAME_AXX = 0x7f0d04ec;
        public static final int RGMAP_FLDNAME_AYY = 0x7f0d04ed;
        public static final int RGMAP_FLDNAME_BAG_CODE = 0x7f0d04ee;
        public static final int RGMAP_FLDNAME_BCODE1 = 0x7f0d04ef;
        public static final int RGMAP_FLDNAME_BCODE2 = 0x7f0d04f0;
        public static final int RGMAP_FLDNAME_BDATE = 0x7f0d04f1;
        public static final int RGMAP_FLDNAME_BDIP = 0x7f0d04f2;
        public static final int RGMAP_FLDNAME_BDIP_ANG = 0x7f0d04f3;
        public static final int RGMAP_FLDNAME_BGBCODE = 0x7f0d04f4;
        public static final int RGMAP_FLDNAME_BGEOPOINT = 0x7f0d04f5;
        public static final int RGMAP_FLDNAME_BLEFT_BODY = 0x7f0d04f6;
        public static final int RGMAP_FLDNAME_BMAPCODE = 0x7f0d04f7;
        public static final int RGMAP_FLDNAME_BPGEOPOINT = 0x7f0d04f8;
        public static final int RGMAP_FLDNAME_BPLEFT_BODY = 0x7f0d04f9;
        public static final int RGMAP_FLDNAME_BPRELATION = 0x7f0d04fa;
        public static final int RGMAP_FLDNAME_BPRIGHT_BODY = 0x7f0d04fb;
        public static final int RGMAP_FLDNAME_BPTYPE = 0x7f0d04fc;
        public static final int RGMAP_FLDNAME_BRELATION = 0x7f0d04fd;
        public static final int RGMAP_FLDNAME_BREMARK = 0x7f0d04fe;
        public static final int RGMAP_FLDNAME_BRIGHT_BODY = 0x7f0d04ff;
        public static final int RGMAP_FLDNAME_BROUTECODE = 0x7f0d0500;
        public static final int RGMAP_FLDNAME_BR_CODE = 0x7f0d0501;
        public static final int RGMAP_FLDNAME_BSUBPOINT = 0x7f0d0502;
        public static final int RGMAP_FLDNAME_BTREND = 0x7f0d0503;
        public static final int RGMAP_FLDNAME_BTYPE = 0x7f0d0504;
        public static final int RGMAP_FLDNAME_CAUSE = 0x7f0d0505;
        public static final int RGMAP_FLDNAME_CHECHER = 0x7f0d0506;
        public static final int RGMAP_FLDNAME_CHECK = 0x7f0d0507;
        public static final int RGMAP_FLDNAME_CHECKDATE = 0x7f0d0508;
        public static final int RGMAP_FLDNAME_CHECKER = 0x7f0d0509;
        public static final int RGMAP_FLDNAME_CHECK_DATE = 0x7f0d050a;
        public static final int RGMAP_FLDNAME_CHECK_NAME = 0x7f0d050b;
        public static final int RGMAP_FLDNAME_CHK_CHECKER = 0x7f0d050c;
        public static final int RGMAP_FLDNAME_CHK_DATE = 0x7f0d050d;
        public static final int RGMAP_FLDNAME_CHK_RECORDER = 0x7f0d050e;
        public static final int RGMAP_FLDNAME_CHK_REMARK = 0x7f0d050f;
        public static final int RGMAP_FLDNAME_CHK_RESULT = 0x7f0d0510;
        public static final int RGMAP_FLDNAME_CHK_ROUTECODE = 0x7f0d0511;
        public static final int RGMAP_FLDNAME_CHK_SUMARY = 0x7f0d0512;
        public static final int RGMAP_FLDNAME_CHK_TYPE = 0x7f0d0513;
        public static final int RGMAP_FLDNAME_CODE = 0x7f0d0514;
        public static final int RGMAP_FLDNAME_COLOR = 0x7f0d0515;
        public static final int RGMAP_FLDNAME_COMBINATION = 0x7f0d0516;
        public static final int RGMAP_FLDNAME_COMPONENT = 0x7f0d0517;
        public static final int RGMAP_FLDNAME_DATE = 0x7f0d0518;
        public static final int RGMAP_FLDNAME_DATE_STA = 0x7f0d0519;
        public static final int RGMAP_FLDNAME_DEPTH = 0x7f0d051a;
        public static final int RGMAP_FLDNAME_DESCRIBE = 0x7f0d051b;
        public static final int RGMAP_FLDNAME_DEXPCRITION = 0x7f0d051c;
        public static final int RGMAP_FLDNAME_DIP = 0x7f0d051d;
        public static final int RGMAP_FLDNAME_DIPANG = 0x7f0d051e;
        public static final int RGMAP_FLDNAME_DIP_ANG = 0x7f0d051f;
        public static final int RGMAP_FLDNAME_ENGPOINT_AREA = 0x7f0d0520;
        public static final int RGMAP_FLDNAME_ENGPOINT_ATTITJPOINT = 0x7f0d0521;
        public static final int RGMAP_FLDNAME_ENGPOINT_CHECKER = 0x7f0d0522;
        public static final int RGMAP_FLDNAME_ENGPOINT_DIP = 0x7f0d0523;
        public static final int RGMAP_FLDNAME_ENGPOINT_DIPANG = 0x7f0d0524;
        public static final int RGMAP_FLDNAME_ENGPOINT_GPOINT = 0x7f0d0525;
        public static final int RGMAP_FLDNAME_ENGPOINT_LEN = 0x7f0d0526;
        public static final int RGMAP_FLDNAME_ENGPOINT_LINECODE = 0x7f0d0527;
        public static final int RGMAP_FLDNAME_ENGPOINT_POSITION = 0x7f0d0528;
        public static final int RGMAP_FLDNAME_ENGPOINT_RECORDER = 0x7f0d0529;
        public static final int RGMAP_FLDNAME_ENGPOINT_ROCKNAME = 0x7f0d052a;
        public static final int RGMAP_FLDNAME_ENGPOINT_STRAPH = 0x7f0d052b;
        public static final int RGMAP_FLDNAME_ENGPOINT_SURVERYER = 0x7f0d052c;
        public static final int RGMAP_FLDNAME_ENGPOINT_TIME = 0x7f0d052d;
        public static final int RGMAP_FLDNAME_ENGPOINT_TREND = 0x7f0d052e;
        public static final int RGMAP_FLDNAME_ENGPOINT_X = 0x7f0d052f;
        public static final int RGMAP_FLDNAME_ENGPOINT_Y = 0x7f0d0530;
        public static final int RGMAP_FLDNAME_ENGPOINT_Z = 0x7f0d0531;
        public static final int RGMAP_FLDNAME_FAGE = 0x7f0d0532;
        public static final int RGMAP_FLDNAME_FCODE = 0x7f0d0533;
        public static final int RGMAP_FLDNAME_FDATE = 0x7f0d0534;
        public static final int RGMAP_FLDNAME_FEATURE = 0x7f0d0535;
        public static final int RGMAP_FLDNAME_FGBCODE = 0x7f0d0536;
        public static final int RGMAP_FLDNAME_FGEOPOINT = 0x7f0d0537;
        public static final int RGMAP_FLDNAME_FIDENTIFY = 0x7f0d0538;
        public static final int RGMAP_FLDNAME_FLDATE = 0x7f0d0539;
        public static final int RGMAP_FLDNAME_FLMAPCODE = 0x7f0d053a;
        public static final int RGMAP_FLDNAME_FLNOTE = 0x7f0d053b;
        public static final int RGMAP_FLDNAME_FLOW = 0x7f0d053c;
        public static final int RGMAP_FLDNAME_FMAPCODE = 0x7f0d053d;
        public static final int RGMAP_FLDNAME_FNAME = 0x7f0d053e;
        public static final int RGMAP_FLDNAME_FOSSIL_CODE = 0x7f0d053f;
        public static final int RGMAP_FLDNAME_FPCODE = 0x7f0d0540;
        public static final int RGMAP_FLDNAME_FPDESCRIBE = 0x7f0d0541;
        public static final int RGMAP_FLDNAME_FPNAME = 0x7f0d0542;
        public static final int RGMAP_FLDNAME_FREMARK = 0x7f0d0543;
        public static final int RGMAP_FLDNAME_FROUTECODE = 0x7f0d0544;
        public static final int RGMAP_FLDNAME_FR_CODE = 0x7f0d0545;
        public static final int RGMAP_FLDNAME_FXX = 0x7f0d0546;
        public static final int RGMAP_FLDNAME_FYY = 0x7f0d0547;
        public static final int RGMAP_FLDNAME_GAEROMAP = 0x7f0d0548;
        public static final int RGMAP_FLDNAME_GAP = 0x7f0d0549;
        public static final int RGMAP_FLDNAME_GCAMERAMAN = 0x7f0d054a;
        public static final int RGMAP_FLDNAME_GDATE = 0x7f0d054b;
        public static final int RGMAP_FLDNAME_GDESCRIBE = 0x7f0d054c;
        public static final int RGMAP_FLDNAME_GEOMORPH = 0x7f0d054d;
        public static final int RGMAP_FLDNAME_GEOPOINT = 0x7f0d054e;
        public static final int RGMAP_FLDNAME_GFELLOW = 0x7f0d054f;
        public static final int RGMAP_FLDNAME_GHANDMAP = 0x7f0d0550;
        public static final int RGMAP_FLDNAME_GMAPCODE = 0x7f0d0551;
        public static final int RGMAP_FLDNAME_GMAPNAME = 0x7f0d0552;
        public static final int RGMAP_FLDNAME_GNOTE = 0x7f0d0553;
        public static final int RGMAP_FLDNAME_GPHOTO_ER = 0x7f0d0554;
        public static final int RGMAP_FLDNAME_GPOINT = 0x7f0d0555;
        public static final int RGMAP_FLDNAME_GRECORDER = 0x7f0d0556;
        public static final int RGMAP_FLDNAME_GROUTECODE = 0x7f0d0557;
        public static final int RGMAP_FLDNAME_GTASK = 0x7f0d0558;
        public static final int RGMAP_FLDNAME_GWEATHER = 0x7f0d0559;
        public static final int RGMAP_FLDNAME_HEIGHT = 0x7f0d055a;
        public static final int RGMAP_FLDNAME_HYDPOINT_CHECKER = 0x7f0d055b;
        public static final int RGMAP_FLDNAME_HYDPOINT_COLOR = 0x7f0d055c;
        public static final int RGMAP_FLDNAME_HYDPOINT_DESPCRITION = 0x7f0d055d;
        public static final int RGMAP_FLDNAME_HYDPOINT_FLOW = 0x7f0d055e;
        public static final int RGMAP_FLDNAME_HYDPOINT_GPOINT = 0x7f0d055f;
        public static final int RGMAP_FLDNAME_HYDPOINT_HYDROLOGY = 0x7f0d0560;
        public static final int RGMAP_FLDNAME_HYDPOINT_LINECODE = 0x7f0d0561;
        public static final int RGMAP_FLDNAME_HYDPOINT_MICROBE = 0x7f0d0562;
        public static final int RGMAP_FLDNAME_HYDPOINT_PELLUCIDITY = 0x7f0d0563;
        public static final int RGMAP_FLDNAME_HYDPOINT_POSITION = 0x7f0d0564;
        public static final int RGMAP_FLDNAME_HYDPOINT_RECORDER = 0x7f0d0565;
        public static final int RGMAP_FLDNAME_HYDPOINT_ROCKNAME = 0x7f0d0566;
        public static final int RGMAP_FLDNAME_HYDPOINT_SMACK = 0x7f0d0567;
        public static final int RGMAP_FLDNAME_HYDPOINT_STRAPH = 0x7f0d0568;
        public static final int RGMAP_FLDNAME_HYDPOINT_SUBSIDENCE = 0x7f0d0569;
        public static final int RGMAP_FLDNAME_HYDPOINT_SURVERYER = 0x7f0d056a;
        public static final int RGMAP_FLDNAME_HYDPOINT_TEMPERATURE = 0x7f0d056b;
        public static final int RGMAP_FLDNAME_HYDPOINT_TIME = 0x7f0d056c;
        public static final int RGMAP_FLDNAME_HYDPOINT_TYPE = 0x7f0d056d;
        public static final int RGMAP_FLDNAME_HYDPOINT_X = 0x7f0d056e;
        public static final int RGMAP_FLDNAME_HYDPOINT_Y = 0x7f0d056f;
        public static final int RGMAP_FLDNAME_HYDPOINT_Z = 0x7f0d0570;
        public static final int RGMAP_FLDNAME_HYDROLOGY = 0x7f0d0571;
        public static final int RGMAP_FLDNAME_INDUSTRY_TYPE = 0x7f0d0572;
        public static final int RGMAP_FLDNAME_KCANB_CODE = 0x7f0d0573;
        public static final int RGMAP_FLDNAME_KCANB_NAME = 0x7f0d0574;
        public static final int RGMAP_FLDNAME_KCODE = 0x7f0d0575;
        public static final int RGMAP_FLDNAME_KDATE = 0x7f0d0576;
        public static final int RGMAP_FLDNAME_KDESCRIBE = 0x7f0d0577;
        public static final int RGMAP_FLDNAME_KFILENAME = 0x7f0d0578;
        public static final int RGMAP_FLDNAME_KGBCODE = 0x7f0d0579;
        public static final int RGMAP_FLDNAME_KGEOPOINT = 0x7f0d057a;
        public static final int RGMAP_FLDNAME_KMAPCODE = 0x7f0d057b;
        public static final int RGMAP_FLDNAME_KPCODE = 0x7f0d057c;
        public static final int RGMAP_FLDNAME_KPDESCRIBE = 0x7f0d057d;
        public static final int RGMAP_FLDNAME_KROUTECODE = 0x7f0d057e;
        public static final int RGMAP_FLDNAME_KR_CODE = 0x7f0d057f;
        public static final int RGMAP_FLDNAME_KSCALE = 0x7f0d0580;
        public static final int RGMAP_FLDNAME_KTITLE = 0x7f0d0581;
        public static final int RGMAP_FLDNAME_KTX_CODE = 0x7f0d0582;
        public static final int RGMAP_FLDNAME_KXX = 0x7f0d0583;
        public static final int RGMAP_FLDNAME_KYY = 0x7f0d0584;
        public static final int RGMAP_FLDNAME_LARGESCALE_AREACODE = 0x7f0d0585;
        public static final int RGMAP_FLDNAME_LARGESCALE_AREANAME = 0x7f0d0586;
        public static final int RGMAP_FLDNAME_LARGESCALE_AREASCALE = 0x7f0d0587;
        public static final int RGMAP_FLDNAME_LARGESCALE_CHEMICALNOTE = 0x7f0d0588;
        public static final int RGMAP_FLDNAME_LARGESCALE_GEOLOGICALNOTE = 0x7f0d0589;
        public static final int RGMAP_FLDNAME_LARGESCALE_GEOPHYSICALNOTE = 0x7f0d058a;
        public static final int RGMAP_FLDNAME_LARGESCALE_MAPCODE = 0x7f0d058b;
        public static final int RGMAP_FLDNAME_LARGESCALE_X1 = 0x7f0d058c;
        public static final int RGMAP_FLDNAME_LARGESCALE_X2 = 0x7f0d058d;
        public static final int RGMAP_FLDNAME_LARGESCALE_Y1 = 0x7f0d058e;
        public static final int RGMAP_FLDNAME_LARGESCALE_Y2 = 0x7f0d058f;
        public static final int RGMAP_FLDNAME_LATITUDE = 0x7f0d0590;
        public static final int RGMAP_FLDNAME_LAYER = 0x7f0d0591;
        public static final int RGMAP_FLDNAME_LEN = 0x7f0d0592;
        public static final int RGMAP_FLDNAME_LINECODE = 0x7f0d0593;
        public static final int RGMAP_FLDNAME_LINE_CODE = 0x7f0d0594;
        public static final int RGMAP_FLDNAME_LITHO = 0x7f0d0595;
        public static final int RGMAP_FLDNAME_LITHOFACIES = 0x7f0d0596;
        public static final int RGMAP_FLDNAME_LITHOLOGY = 0x7f0d0597;
        public static final int RGMAP_FLDNAME_LITHO_A = 0x7f0d0598;
        public static final int RGMAP_FLDNAME_LITHO_B = 0x7f0d0599;
        public static final int RGMAP_FLDNAME_LITHO_C = 0x7f0d059a;
        public static final int RGMAP_FLDNAME_LITHO_CODE = 0x7f0d059b;
        public static final int RGMAP_FLDNAME_LITHO_DEPTH = 0x7f0d059c;
        public static final int RGMAP_FLDNAME_LITHO_ERA = 0x7f0d059d;
        public static final int RGMAP_FLDNAME_LOCATION = 0x7f0d059e;
        public static final int RGMAP_FLDNAME_LONGITUDE = 0x7f0d059f;
        public static final int RGMAP_FLDNAME_MAGMA = 0x7f0d05a0;
        public static final int RGMAP_FLDNAME_MAPCODE = 0x7f0d05a1;
        public static final int RGMAP_FLDNAME_MAX_THICK = 0x7f0d05a2;
        public static final int RGMAP_FLDNAME_MEANING = 0x7f0d05a3;
        public static final int RGMAP_FLDNAME_MEAN_THICK = 0x7f0d05a4;
        public static final int RGMAP_FLDNAME_METHOD = 0x7f0d05a5;
        public static final int RGMAP_FLDNAME_MICROBE = 0x7f0d05a6;
        public static final int RGMAP_FLDNAME_MINEPNT_CODE = 0x7f0d05a7;
        public static final int RGMAP_FLDNAME_MINE_CODE = 0x7f0d05a8;
        public static final int RGMAP_FLDNAME_MINE_TYPE = 0x7f0d05a9;
        public static final int RGMAP_FLDNAME_MIN_LENTH = 0x7f0d05aa;
        public static final int RGMAP_FLDNAME_MORPHOLOGY = 0x7f0d05ab;
        public static final int RGMAP_FLDNAME_MPG = 0x7f0d05ac;
        public static final int RGMAP_FLDNAME_NUM = 0x7f0d05ad;
        public static final int RGMAP_FLDNAME_OPINION = 0x7f0d05ae;
        public static final int RGMAP_FLDNAME_ORDER = 0x7f0d05af;
        public static final int RGMAP_FLDNAME_ORE_MINE_DATE = 0x7f0d05b0;
        public static final int RGMAP_FLDNAME_ORI_WEIGHT = 0x7f0d05b1;
        public static final int RGMAP_FLDNAME_OUTCROP = 0x7f0d05b2;
        public static final int RGMAP_FLDNAME_OXIDATION = 0x7f0d05b3;
        public static final int RGMAP_FLDNAME_PACK = 0x7f0d05b4;
        public static final int RGMAP_FLDNAME_PACKTYPE = 0x7f0d05b5;
        public static final int RGMAP_FLDNAME_PAMOUNT = 0x7f0d05b6;
        public static final int RGMAP_FLDNAME_PCODE = 0x7f0d05b7;
        public static final int RGMAP_FLDNAME_PDATE = 0x7f0d05b8;
        public static final int RGMAP_FLDNAME_PDESCRIBE = 0x7f0d05b9;
        public static final int RGMAP_FLDNAME_PDIRECTION = 0x7f0d05ba;
        public static final int RGMAP_FLDNAME_PELLUCIDITY = 0x7f0d05bb;
        public static final int RGMAP_FLDNAME_PGBCODE = 0x7f0d05bc;
        public static final int RGMAP_FLDNAME_PGEOPOINT = 0x7f0d05bd;
        public static final int RGMAP_FLDNAME_PGGEOPOINT = 0x7f0d05be;
        public static final int RGMAP_FLDNAME_PH = 0x7f0d05bf;
        public static final int RGMAP_FLDNAME_PHOTO_CODE = 0x7f0d05c0;
        public static final int RGMAP_FLDNAME_PHOTO_NUM = 0x7f0d05c1;
        public static final int RGMAP_FLDNAME_PLACE = 0x7f0d05c2;
        public static final int RGMAP_FLDNAME_PLITHO_A = 0x7f0d05c3;
        public static final int RGMAP_FLDNAME_PLITHO_B = 0x7f0d05c4;
        public static final int RGMAP_FLDNAME_PLITHO_C = 0x7f0d05c5;
        public static final int RGMAP_FLDNAME_PMAPCODE = 0x7f0d05c6;
        public static final int RGMAP_FLDNAME_PMPG_ID = 0x7f0d05c7;
        public static final int RGMAP_FLDNAME_PNT_CODE = 0x7f0d05c8;
        public static final int RGMAP_FLDNAME_PNUMBER = 0x7f0d05c9;
        public static final int RGMAP_FLDNAME_POINT_CODE = 0x7f0d05ca;
        public static final int RGMAP_FLDNAME_POSITION = 0x7f0d05cb;
        public static final int RGMAP_FLDNAME_PPCODE = 0x7f0d05cc;
        public static final int RGMAP_FLDNAME_PPDESCRIBE = 0x7f0d05cd;
        public static final int RGMAP_FLDNAME_PPMAPCODE = 0x7f0d05ce;
        public static final int RGMAP_FLDNAME_PROECTANT = 0x7f0d05cf;
        public static final int RGMAP_FLDNAME_PROUTECODE = 0x7f0d05d0;
        public static final int RGMAP_FLDNAME_PR_CODE = 0x7f0d05d1;
        public static final int RGMAP_FLDNAME_PSOUND_ID = 0x7f0d05d2;
        public static final int RGMAP_FLDNAME_PSTRAPHA = 0x7f0d05d3;
        public static final int RGMAP_FLDNAME_PSTRAPHB = 0x7f0d05d4;
        public static final int RGMAP_FLDNAME_PSTRAPHC = 0x7f0d05d5;
        public static final int RGMAP_FLDNAME_PSTRARAB = 0x7f0d05d6;
        public static final int RGMAP_FLDNAME_PSTRARAC = 0x7f0d05d7;
        public static final int RGMAP_FLDNAME_PSTRARBC = 0x7f0d05d8;
        public static final int RGMAP_FLDNAME_PX = 0x7f0d05d9;
        public static final int RGMAP_FLDNAME_PXX = 0x7f0d05da;
        public static final int RGMAP_FLDNAME_PY = 0x7f0d05db;
        public static final int RGMAP_FLDNAME_PYY = 0x7f0d05dc;
        public static final int RGMAP_FLDNAME_RCODE1 = 0x7f0d05dd;
        public static final int RGMAP_FLDNAME_RCODE2 = 0x7f0d05de;
        public static final int RGMAP_FLDNAME_RDATE = 0x7f0d05df;
        public static final int RGMAP_FLDNAME_RDIRECTION = 0x7f0d05e0;
        public static final int RGMAP_FLDNAME_RDISTANCE = 0x7f0d05e1;
        public static final int RGMAP_FLDNAME_RECORDER = 0x7f0d05e2;
        public static final int RGMAP_FLDNAME_RELATION = 0x7f0d05e3;
        public static final int RGMAP_FLDNAME_REMARK = 0x7f0d05e4;
        public static final int RGMAP_FLDNAME_RGEOPOINT = 0x7f0d05e5;
        public static final int RGMAP_FLDNAME_RLITHO = 0x7f0d05e6;
        public static final int RGMAP_FLDNAME_RMAPCODE = 0x7f0d05e7;
        public static final int RGMAP_FLDNAME_ROCKNAME = 0x7f0d05e8;
        public static final int RGMAP_FLDNAME_ROCKSY = 0x7f0d05e9;
        public static final int RGMAP_FLDNAME_RORDER = 0x7f0d05ea;
        public static final int RGMAP_FLDNAME_ROUGHNESS = 0x7f0d05eb;
        public static final int RGMAP_FLDNAME_ROUTECODE = 0x7f0d05ec;
        public static final int RGMAP_FLDNAME_ROUTE_CODE = 0x7f0d05ed;
        public static final int RGMAP_FLDNAME_ROUTING_DIRECTION = 0x7f0d05ee;
        public static final int RGMAP_FLDNAME_ROUTING_DISTANCE = 0x7f0d05ef;
        public static final int RGMAP_FLDNAME_ROUTING_RCODE = 0x7f0d05f0;
        public static final int RGMAP_FLDNAME_ROUTING_SUML = 0x7f0d05f1;
        public static final int RGMAP_FLDNAME_RPGEOPOINT = 0x7f0d05f2;
        public static final int RGMAP_FLDNAME_RPLITHO = 0x7f0d05f3;
        public static final int RGMAP_FLDNAME_RPNOTE = 0x7f0d05f4;
        public static final int RGMAP_FLDNAME_RPSTRAPHA = 0x7f0d05f5;
        public static final int RGMAP_FLDNAME_RREMARK = 0x7f0d05f6;
        public static final int RGMAP_FLDNAME_RROUTECODE = 0x7f0d05f7;
        public static final int RGMAP_FLDNAME_RR_CODE = 0x7f0d05f8;
        public static final int RGMAP_FLDNAME_RSTRAPHA = 0x7f0d05f9;
        public static final int RGMAP_FLDNAME_RSUM_L = 0x7f0d05fa;
        public static final int RGMAP_FLDNAME_R_CODE = 0x7f0d05fb;
        public static final int RGMAP_FLDNAME_SAMDATE = 0x7f0d05fc;
        public static final int RGMAP_FLDNAME_SAMPLE = 0x7f0d05fd;
        public static final int RGMAP_FLDNAME_SAMPLEING = 0x7f0d05fe;
        public static final int RGMAP_FLDNAME_SAMPLER = 0x7f0d05ff;
        public static final int RGMAP_FLDNAME_SAMPLE_CODE = 0x7f0d0600;
        public static final int RGMAP_FLDNAME_SAM_DATE = 0x7f0d0601;
        public static final int RGMAP_FLDNAME_SAPN = 0x7f0d0602;
        public static final int RGMAP_FLDNAME_SECCODE = 0x7f0d0603;
        public static final int RGMAP_FLDNAME_SEMI_TYPE = 0x7f0d0604;
        public static final int RGMAP_FLDNAME_SHAPE = 0x7f0d0605;
        public static final int RGMAP_FLDNAME_SKETCH_CODE = 0x7f0d0606;
        public static final int RGMAP_FLDNAME_SMACK = 0x7f0d0607;
        public static final int RGMAP_FLDNAME_SND = 0x7f0d0608;
        public static final int RGMAP_FLDNAME_SPNAME = 0x7f0d0609;
        public static final int RGMAP_FLDNAME_STONE_IN = 0x7f0d060a;
        public static final int RGMAP_FLDNAME_STRAPH = 0x7f0d060b;
        public static final int RGMAP_FLDNAME_STRAPHA = 0x7f0d060c;
        public static final int RGMAP_FLDNAME_STRAPHB = 0x7f0d060d;
        public static final int RGMAP_FLDNAME_STRAPHC = 0x7f0d060e;
        public static final int RGMAP_FLDNAME_STRARAB = 0x7f0d060f;
        public static final int RGMAP_FLDNAME_STRARAC = 0x7f0d0610;
        public static final int RGMAP_FLDNAME_STRARBC = 0x7f0d0611;
        public static final int RGMAP_FLDNAME_STRUCTURE = 0x7f0d0612;
        public static final int RGMAP_FLDNAME_STR_GEO = 0x7f0d0613;
        public static final int RGMAP_FLDNAME_SUBSIDENCE = 0x7f0d0614;
        public static final int RGMAP_FLDNAME_SURVERYER = 0x7f0d0615;
        public static final int RGMAP_FLDNAME_SWEIGHT = 0x7f0d0616;
        public static final int RGMAP_FLDNAME_TASK = 0x7f0d0617;
        public static final int RGMAP_FLDNAME_TECTONICS = 0x7f0d0618;
        public static final int RGMAP_FLDNAME_TECT_CHARACTER = 0x7f0d0619;
        public static final int RGMAP_FLDNAME_TEMPERATURE = 0x7f0d061a;
        public static final int RGMAP_FLDNAME_TIME = 0x7f0d061b;
        public static final int RGMAP_FLDNAME_TREND = 0x7f0d061c;
        public static final int RGMAP_FLDNAME_TYPE = 0x7f0d061d;
        public static final int RGMAP_FLDNAME_VIEW_MINERAL = 0x7f0d061e;
        public static final int RGMAP_FLDNAME_WATER = 0x7f0d061f;
        public static final int RGMAP_FLDNAME_WATERSYS_CODE = 0x7f0d0620;
        public static final int RGMAP_FLDNAME_WEATHER = 0x7f0d0621;
        public static final int RGMAP_FLDNAME_WEATHERING = 0x7f0d0622;
        public static final int RGMAP_FLDNAME_WEATHING = 0x7f0d0623;
        public static final int RGMAP_FLDNAME_WEIGHT = 0x7f0d0624;
        public static final int RGMAP_FLDNAME_WIDTH = 0x7f0d0625;
        public static final int RGMAP_FLDNAME_WORKPLACE = 0x7f0d0626;
        public static final int RGMAP_FLDNAME_X = 0x7f0d0627;
        public static final int RGMAP_FLDNAME_XX = 0x7f0d0628;
        public static final int RGMAP_FLDNAME_XY = 0x7f0d0629;
        public static final int RGMAP_FLDNAME_Y = 0x7f0d062a;
        public static final int RGMAP_FLDNAME_YY = 0x7f0d062b;
        public static final int RGMAP_FLDNAME_Z = 0x7f0d062c;
        public static final int RGMAP_FLDNAME_ZK_ENG_CODE = 0x7f0d062d;
        public static final int RGMAP_FLDNAME_ZK_SECT_CODE = 0x7f0d062e;
        public static final int RGMAP_HISRESOURCE_PROMPT1 = 0x7f0d062f;
        public static final int RGMAP_HISRESOURCE_PROMPT2 = 0x7f0d0630;
        public static final int RGMAP_HISRESOURCE_PROMPT3 = 0x7f0d0631;
        public static final int RGMAP_HYDPOINT_CHECKER = 0x7f0d0632;
        public static final int RGMAP_HYDPOINT_COLOR = 0x7f0d0633;
        public static final int RGMAP_HYDPOINT_DESPCRITION = 0x7f0d0634;
        public static final int RGMAP_HYDPOINT_FLOW = 0x7f0d0635;
        public static final int RGMAP_HYDPOINT_GPOINT = 0x7f0d0636;
        public static final int RGMAP_HYDPOINT_HYDROLOGY = 0x7f0d0637;
        public static final int RGMAP_HYDPOINT_LINECODE = 0x7f0d0638;
        public static final int RGMAP_HYDPOINT_MICROBE = 0x7f0d0639;
        public static final int RGMAP_HYDPOINT_PELLUCIDITY = 0x7f0d063a;
        public static final int RGMAP_HYDPOINT_POSITION = 0x7f0d063b;
        public static final int RGMAP_HYDPOINT_RECORDER = 0x7f0d063c;
        public static final int RGMAP_HYDPOINT_ROCKNAME = 0x7f0d063d;
        public static final int RGMAP_HYDPOINT_SMACK = 0x7f0d063e;
        public static final int RGMAP_HYDPOINT_STRAPH = 0x7f0d063f;
        public static final int RGMAP_HYDPOINT_SUBSIDENCE = 0x7f0d0640;
        public static final int RGMAP_HYDPOINT_SURVERYER = 0x7f0d0641;
        public static final int RGMAP_HYDPOINT_TEMPERATURE = 0x7f0d0642;
        public static final int RGMAP_HYDPOINT_TIME = 0x7f0d0643;
        public static final int RGMAP_HYDPOINT_TYPE = 0x7f0d0644;
        public static final int RGMAP_HYDPOINT_X = 0x7f0d0645;
        public static final int RGMAP_HYDPOINT_Y = 0x7f0d0646;
        public static final int RGMAP_HYDPOINT_Z = 0x7f0d0647;
        public static final int RGMAP_LAYOUT_AUTOVALUE = 0x7f0d0648;
        public static final int RGMAP_LAYOUT_SECCODE = 0x7f0d0649;
        public static final int RGMAP_LAYOUT_SECPROMPT = 0x7f0d064a;
        public static final int RGMAP_MAPTYPE_IMAGE = 0x7f0d064b;
        public static final int RGMAP_MAPTYPE_NOMAP = 0x7f0d064c;
        public static final int RGMAP_MAPTYPE_ROAD = 0x7f0d064d;
        public static final int RGMAP_MAPTYPE_TERRAIN = 0x7f0d064e;
        public static final int RGMAP_MAPTYPE_VECTOR = 0x7f0d064f;
        public static final int RGMAP_ORIENTATION_QFJ = 0x7f0d0650;
        public static final int RGMAP_PROMPT_ANGLE = 0x7f0d0651;
        public static final int RGMAP_PROMPT_ANIMAL = 0x7f0d0652;
        public static final int RGMAP_PROMPT_AOLANGUAGE = 0x7f0d0653;
        public static final int RGMAP_PROMPT_AOSELLANG = 0x7f0d0654;
        public static final int RGMAP_PROMPT_ATTINUM = 0x7f0d0655;
        public static final int RGMAP_PROMPT_ATTITJ = 0x7f0d0656;
        public static final int RGMAP_PROMPT_ATTSTRUADD = 0x7f0d0657;
        public static final int RGMAP_PROMPT_ATTSUBGRAPH = 0x7f0d0658;
        public static final int RGMAP_PROMPT_AUDIO = 0x7f0d0659;
        public static final int RGMAP_PROMPT_AUTOLOC = 0x7f0d065a;
        public static final int RGMAP_PROMPT_AoGISINFO = 0x7f0d065b;
        public static final int RGMAP_PROMPT_BCACHE = 0x7f0d065c;
        public static final int RGMAP_PROMPT_BOUNDARYNUM = 0x7f0d065d;
        public static final int RGMAP_PROMPT_BTARTID = 0x7f0d065e;
        public static final int RGMAP_PROMPT_BUTTONCANCEL = 0x7f0d065f;
        public static final int RGMAP_PROMPT_BUTTONOK = 0x7f0d0660;
        public static final int RGMAP_PROMPT_CACHE = 0x7f0d0661;
        public static final int RGMAP_PROMPT_CALPOS = 0x7f0d0662;
        public static final int RGMAP_PROMPT_CAMERATYPE = 0x7f0d0663;
        public static final int RGMAP_PROMPT_CHINESE = 0x7f0d0664;
        public static final int RGMAP_PROMPT_CHU = 0x7f0d0665;
        public static final int RGMAP_PROMPT_CLAOSE = 0x7f0d0666;
        public static final int RGMAP_PROMPT_CLEAR = 0x7f0d0667;
        public static final int RGMAP_PROMPT_CLOSENAVIGATION = 0x7f0d0668;
        public static final int RGMAP_PROMPT_COMPANT1 = 0x7f0d0669;
        public static final int RGMAP_PROMPT_COMPANT2 = 0x7f0d066a;
        public static final int RGMAP_PROMPT_COMPANT3 = 0x7f0d066b;
        public static final int RGMAP_PROMPT_COMPASS = 0x7f0d066c;
        public static final int RGMAP_PROMPT_COMPASSTYPE = 0x7f0d066d;
        public static final int RGMAP_PROMPT_COMPASSTYPE1 = 0x7f0d066e;
        public static final int RGMAP_PROMPT_COMPASSTYPE2 = 0x7f0d066f;
        public static final int RGMAP_PROMPT_COPYORGFILE = 0x7f0d0670;
        public static final int RGMAP_PROMPT_COUNTERANDFIELD = 0x7f0d0671;
        public static final int RGMAP_PROMPT_CPJ = 0x7f0d0672;
        public static final int RGMAP_PROMPT_CREATEFLODER = 0x7f0d0673;
        public static final int RGMAP_PROMPT_CREATESKETCH = 0x7f0d0674;
        public static final int RGMAP_PROMPT_DATAEDIT = 0x7f0d0675;
        public static final int RGMAP_PROMPT_DATAQUERYLIST = 0x7f0d0676;
        public static final int RGMAP_PROMPT_DATASOURCE = 0x7f0d0677;
        public static final int RGMAP_PROMPT_DECINFO = 0x7f0d0678;
        public static final int RGMAP_PROMPT_DELETEPOS = 0x7f0d0679;
        public static final int RGMAP_PROMPT_DELPROMPT = 0x7f0d067a;
        public static final int RGMAP_PROMPT_DELSEC = 0x7f0d067b;
        public static final int RGMAP_PROMPT_DESCHANDMAP = 0x7f0d067c;
        public static final int RGMAP_PROMPT_DESCM = 0x7f0d067d;
        public static final int RGMAP_PROMPT_DESCPHOTO = 0x7f0d067e;
        public static final int RGMAP_PROMPT_DESCROUTE = 0x7f0d067f;
        public static final int RGMAP_PROMPT_DESCSEC = 0x7f0d0680;
        public static final int RGMAP_PROMPT_DICLIB = 0x7f0d0681;
        public static final int RGMAP_PROMPT_DICLIBPATH = 0x7f0d0682;
        public static final int RGMAP_PROMPT_DICNAME = 0x7f0d0683;
        public static final int RGMAP_PROMPT_DIRECTION = 0x7f0d0684;
        public static final int RGMAP_PROMPT_DRAWROUTING = 0x7f0d0685;
        public static final int RGMAP_PROMPT_EDITMODEL = 0x7f0d0686;
        public static final int RGMAP_PROMPT_EN = 0x7f0d0687;
        public static final int RGMAP_PROMPT_ENGLISH = 0x7f0d0688;
        public static final int RGMAP_PROMPT_ENGPOINT = 0x7f0d0689;
        public static final int RGMAP_PROMPT_ERRPATH = 0x7f0d068a;
        public static final int RGMAP_PROMPT_ES = 0x7f0d068b;
        public static final int RGMAP_PROMPT_EXIT = 0x7f0d068c;
        public static final int RGMAP_PROMPT_FILE = 0x7f0d068d;
        public static final int RGMAP_PROMPT_FINDCLEAR = 0x7f0d068e;
        public static final int RGMAP_PROMPT_FINDCONTENT = 0x7f0d068f;
        public static final int RGMAP_PROMPT_FINDGPOINT = 0x7f0d0690;
        public static final int RGMAP_PROMPT_FINDLIB = 0x7f0d0691;
        public static final int RGMAP_PROMPT_FINSHGPS = 0x7f0d0692;
        public static final int RGMAP_PROMPT_FLLOWER = 0x7f0d0693;
        public static final int RGMAP_PROMPT_FOSSILE = 0x7f0d0694;
        public static final int RGMAP_PROMPT_FOSSILECODE = 0x7f0d0695;
        public static final int RGMAP_PROMPT_FOSSILELAYE = 0x7f0d0696;
        public static final int RGMAP_PROMPT_FREELAYERPARAM = 0x7f0d0697;
        public static final int RGMAP_PROMPT_GATHER = 0x7f0d0698;
        public static final int RGMAP_PROMPT_GATHERDEC = 0x7f0d0699;
        public static final int RGMAP_PROMPT_GATHERLEN = 0x7f0d069a;
        public static final int RGMAP_PROMPT_GBEMPTY = 0x7f0d069b;
        public static final int RGMAP_PROMPT_GEOCHEMSPEC = 0x7f0d069c;
        public static final int RGMAP_PROMPT_GEODATA = 0x7f0d069d;
        public static final int RGMAP_PROMPT_GOOGLELIB = 0x7f0d069e;
        public static final int RGMAP_PROMPT_GOOGLELIBPATH = 0x7f0d069f;
        public static final int RGMAP_PROMPT_GPEMPTY = 0x7f0d06a0;
        public static final int RGMAP_PROMPT_GPOINTSTYLE = 0x7f0d06a1;
        public static final int RGMAP_PROMPT_GPS = 0x7f0d06a2;
        public static final int RGMAP_PROMPT_GPSINFO = 0x7f0d06a3;
        public static final int RGMAP_PROMPT_GPSPOINT = 0x7f0d06a4;
        public static final int RGMAP_PROMPT_GPSPOINTTYPE = 0x7f0d06a5;
        public static final int RGMAP_PROMPT_GRANTJ = 0x7f0d06a6;
        public static final int RGMAP_PROMPT_GREMPTY = 0x7f0d06a7;
        public static final int RGMAP_PROMPT_HYDPOINT = 0x7f0d06a8;
        public static final int RGMAP_PROMPT_IMGDESC = 0x7f0d06a9;
        public static final int RGMAP_PROMPT_IMGSAVEFAilURE = 0x7f0d06aa;
        public static final int RGMAP_PROMPT_IMGSAVEFAilURE1 = 0x7f0d06ab;
        public static final int RGMAP_PROMPT_IMGSAVETO = 0x7f0d06ac;
        public static final int RGMAP_PROMPT_INPUTDESC = 0x7f0d06ad;
        public static final int RGMAP_PROMPT_INPUTSTR = 0x7f0d06ae;
        public static final int RGMAP_PROMPT_INSERTPOS = 0x7f0d06af;
        public static final int RGMAP_PROMPT_JWCOORD = 0x7f0d06b0;
        public static final int RGMAP_PROMPT_LABLEGRAPH = 0x7f0d06b1;
        public static final int RGMAP_PROMPT_LARGESCALE_AREACODE = 0x7f0d06b2;
        public static final int RGMAP_PROMPT_LARGESCALE_AREANAME = 0x7f0d06b3;
        public static final int RGMAP_PROMPT_LARGESCALE_AREASCALE = 0x7f0d06b4;
        public static final int RGMAP_PROMPT_LARGESCALE_CHEMICALNOTE = 0x7f0d06b5;
        public static final int RGMAP_PROMPT_LARGESCALE_GEOLOGICALNOTE = 0x7f0d06b6;
        public static final int RGMAP_PROMPT_LARGESCALE_GEOPHYSICALNOTE = 0x7f0d06b7;
        public static final int RGMAP_PROMPT_LARGESCALE_MAPCODE = 0x7f0d06b8;
        public static final int RGMAP_PROMPT_LARGESCALE_X1 = 0x7f0d06b9;
        public static final int RGMAP_PROMPT_LARGESCALE_X2 = 0x7f0d06ba;
        public static final int RGMAP_PROMPT_LARGESCALE_Y1 = 0x7f0d06bb;
        public static final int RGMAP_PROMPT_LARGESCALE_Y2 = 0x7f0d06bc;
        public static final int RGMAP_PROMPT_LAYER = 0x7f0d06bd;
        public static final int RGMAP_PROMPT_LINE = 0x7f0d06be;
        public static final int RGMAP_PROMPT_LINEARAM = 0x7f0d06bf;
        public static final int RGMAP_PROMPT_LINESTARTPOS = 0x7f0d06c0;
        public static final int RGMAP_PROMPT_LOCATIONNAG = 0x7f0d06c1;
        public static final int RGMAP_PROMPT_LONGPRESS = 0x7f0d06c2;
        public static final int RGMAP_PROMPT_MAPSOURCE1 = 0x7f0d06c3;
        public static final int RGMAP_PROMPT_MAPSOURCE2 = 0x7f0d06c4;
        public static final int RGMAP_PROMPT_MEDIATYPE = 0x7f0d06c5;
        public static final int RGMAP_PROMPT_MENUENG = 0x7f0d06c6;
        public static final int RGMAP_PROMPT_MENUGEOCHEM1 = 0x7f0d06c7;
        public static final int RGMAP_PROMPT_MENUGEOCHEM2 = 0x7f0d06c8;
        public static final int RGMAP_PROMPT_MENUGEOCHEM3 = 0x7f0d06c9;
        public static final int RGMAP_PROMPT_MENUGEOCHEM4 = 0x7f0d06ca;
        public static final int RGMAP_PROMPT_MENUGEOCHEM5 = 0x7f0d06cb;
        public static final int RGMAP_PROMPT_MENUGEOCHEM6 = 0x7f0d06cc;
        public static final int RGMAP_PROMPT_MENUGEOCHEM7 = 0x7f0d06cd;
        public static final int RGMAP_PROMPT_MENUGEOCHEM8 = 0x7f0d06ce;
        public static final int RGMAP_PROMPT_MENURESCIL = 0x7f0d06cf;
        public static final int RGMAP_PROMPT_MENUSET1 = 0x7f0d06d0;
        public static final int RGMAP_PROMPT_MENUSET2 = 0x7f0d06d1;
        public static final int RGMAP_PROMPT_MENUSET3 = 0x7f0d06d2;
        public static final int RGMAP_PROMPT_MODIDYTIME = 0x7f0d06d3;
        public static final int RGMAP_PROMPT_NAVIGATION = 0x7f0d06d4;
        public static final int RGMAP_PROMPT_NET_GETINFOFAIL = 0x7f0d06d5;
        public static final int RGMAP_PROMPT_NEXT = 0x7f0d06d6;
        public static final int RGMAP_PROMPT_NEXTPAGE = 0x7f0d06d7;
        public static final int RGMAP_PROMPT_NOCACHE = 0x7f0d06d8;
        public static final int RGMAP_PROMPT_NOCURGPSPOS = 0x7f0d06d9;
        public static final int RGMAP_PROMPT_NOCURLAYER = 0x7f0d06da;
        public static final int RGMAP_PROMPT_NOGEOMETRY = 0x7f0d06db;
        public static final int RGMAP_PROMPT_NOORIENTATION = 0x7f0d06dc;
        public static final int RGMAP_PROMPT_NOSERVICE = 0x7f0d06dd;
        public static final int RGMAP_PROMPT_NOSHOW = 0x7f0d06de;
        public static final int RGMAP_PROMPT_NOTEPARAM = 0x7f0d06df;
        public static final int RGMAP_PROMPT_OPENMAP = 0x7f0d06e0;
        public static final int RGMAP_PROMPT_OPENNAVIGATION = 0x7f0d06e1;
        public static final int RGMAP_PROMPT_OPENNETWORK = 0x7f0d06e2;
        public static final int RGMAP_PROMPT_OUTPHOTO = 0x7f0d06e3;
        public static final int RGMAP_PROMPT_PAGE = 0x7f0d06e4;
        public static final int RGMAP_PROMPT_PATHERR = 0x7f0d06e5;
        public static final int RGMAP_PROMPT_PAUSE = 0x7f0d06e6;
        public static final int RGMAP_PROMPT_PHOTO = 0x7f0d06e7;
        public static final int RGMAP_PROMPT_PHOTONUM = 0x7f0d06e8;
        public static final int RGMAP_PROMPT_PLANT = 0x7f0d06e9;
        public static final int RGMAP_PROMPT_PLAY = 0x7f0d06ea;
        public static final int RGMAP_PROMPT_PLAYAUTO = 0x7f0d06eb;
        public static final int RGMAP_PROMPT_PLAYVIDEO = 0x7f0d06ec;
        public static final int RGMAP_PROMPT_PMTJ = 0x7f0d06ed;
        public static final int RGMAP_PROMPT_PMXJ = 0x7f0d06ee;
        public static final int RGMAP_PROMPT_POINT = 0x7f0d06ef;
        public static final int RGMAP_PROMPT_POINTATTI = 0x7f0d06f0;
        public static final int RGMAP_PROMPT_POINTBOUNDARY = 0x7f0d06f1;
        public static final int RGMAP_PROMPT_POINTBYLENG = 0x7f0d06f2;
        public static final int RGMAP_PROMPT_POINTBYTIME = 0x7f0d06f3;
        public static final int RGMAP_PROMPT_POINTFOS = 0x7f0d06f4;
        public static final int RGMAP_PROMPT_POINTNUM = 0x7f0d06f5;
        public static final int RGMAP_PROMPT_POINTPHOTO = 0x7f0d06f6;
        public static final int RGMAP_PROMPT_POINTSAMPLE = 0x7f0d06f7;
        public static final int RGMAP_PROMPT_POINTSKETCH = 0x7f0d06f8;
        public static final int RGMAP_PROMPT_POSINFO = 0x7f0d06f9;
        public static final int RGMAP_PROMPT_PRBATTSHOW = 0x7f0d06fa;
        public static final int RGMAP_PROMPT_PRBSUBGRAPH = 0x7f0d06fb;
        public static final int RGMAP_PROMPT_PRDESC = 0x7f0d06fc;
        public static final int RGMAP_PROMPT_PRE = 0x7f0d06fd;
        public static final int RGMAP_PROMPT_PREPAGE = 0x7f0d06fe;
        public static final int RGMAP_PROMPT_PRJCONTINS = 0x7f0d06ff;
        public static final int RGMAP_PROMPT_PROCOORD = 0x7f0d0700;
        public static final int RGMAP_PROMPT_RECORDER = 0x7f0d0701;
        public static final int RGMAP_PROMPT_RECORDERTYPE = 0x7f0d0702;
        public static final int RGMAP_PROMPT_REMAINCODE = 0x7f0d0703;
        public static final int RGMAP_PROMPT_RESET = 0x7f0d0704;
        public static final int RGMAP_PROMPT_RESETENDPOINT = 0x7f0d0705;
        public static final int RGMAP_PROMPT_RESETLAYER = 0x7f0d0706;
        public static final int RGMAP_PROMPT_ROUTEANGLE = 0x7f0d0707;
        public static final int RGMAP_PROMPT_ROUTEDATA = 0x7f0d0708;
        public static final int RGMAP_PROMPT_ROUTEDESC = 0x7f0d0709;
        public static final int RGMAP_PROMPT_ROUTELEN = 0x7f0d070a;
        public static final int RGMAP_PROMPT_ROUTETASK = 0x7f0d070b;
        public static final int RGMAP_PROMPT_ROUTINGFRISTDESC = 0x7f0d070c;
        public static final int RGMAP_PROMPT_ROUTING_MIN = 0x7f0d070d;
        public static final int RGMAP_PROMPT_RSTARTID = 0x7f0d070e;
        public static final int RGMAP_PROMPT_SAMPLE = 0x7f0d070f;
        public static final int RGMAP_PROMPT_SAMPLECODE = 0x7f0d0710;
        public static final int RGMAP_PROMPT_SAMPLELAYE = 0x7f0d0711;
        public static final int RGMAP_PROMPT_SAMPLENUM = 0x7f0d0712;
        public static final int RGMAP_PROMPT_SAVEOK = 0x7f0d0713;
        public static final int RGMAP_PROMPT_SCALE = 0x7f0d0714;
        public static final int RGMAP_PROMPT_SECBASEINFO = 0x7f0d0715;
        public static final int RGMAP_PROMPT_SECCURPATH = 0x7f0d0716;
        public static final int RGMAP_PROMPT_SECESIT = 0x7f0d0717;
        public static final int RGMAP_PROMPT_SECMSG = 0x7f0d0718;
        public static final int RGMAP_PROMPT_SECNAME = 0x7f0d0719;
        public static final int RGMAP_PROMPT_SELATTI = 0x7f0d071a;
        public static final int RGMAP_PROMPT_SELELEVDIELD = 0x7f0d071b;
        public static final int RGMAP_PROMPT_SELENGPOINT = 0x7f0d071c;
        public static final int RGMAP_PROMPT_SELGPOINT = 0x7f0d071d;
        public static final int RGMAP_PROMPT_SELGRAN = 0x7f0d071e;
        public static final int RGMAP_PROMPT_SELHYDPOINT = 0x7f0d071f;
        public static final int RGMAP_PROMPT_SELLAYER = 0x7f0d0720;
        public static final int RGMAP_PROMPT_SELLAYER1 = 0x7f0d0721;
        public static final int RGMAP_PROMPT_SELSEC = 0x7f0d0722;
        public static final int RGMAP_PROMPT_SELSECLINE = 0x7f0d0723;
        public static final int RGMAP_PROMPT_SELWORKAREA = 0x7f0d0724;
        public static final int RGMAP_PROMPT_SERIAL = 0x7f0d0725;
        public static final int RGMAP_PROMPT_SETCPJ = 0x7f0d0726;
        public static final int RGMAP_PROMPT_SETDATE = 0x7f0d0727;
        public static final int RGMAP_PROMPT_SETSYSPATH = 0x7f0d0728;
        public static final int RGMAP_PROMPT_SETTIME = 0x7f0d0729;
        public static final int RGMAP_PROMPT_SHORTPRESS = 0x7f0d072a;
        public static final int RGMAP_PROMPT_SHOW = 0x7f0d072b;
        public static final int RGMAP_PROMPT_SHOWYESNO = 0x7f0d072c;
        public static final int RGMAP_PROMPT_SKETCH = 0x7f0d072d;
        public static final int RGMAP_PROMPT_SKETCHNUM = 0x7f0d072e;
        public static final int RGMAP_PROMPT_SKETCHSCAN = 0x7f0d072f;
        public static final int RGMAP_PROMPT_SOUNDNUM = 0x7f0d0730;
        public static final int RGMAP_PROMPT_SPANISH = 0x7f0d0731;
        public static final int RGMAP_PROMPT_STARTRBID = 0x7f0d0732;
        public static final int RGMAP_PROMPT_STOP = 0x7f0d0733;
        public static final int RGMAP_PROMPT_SUBGRAPH = 0x7f0d0734;
        public static final int RGMAP_PROMPT_SUBGRAPHPARAM = 0x7f0d0735;
        public static final int RGMAP_PROMPT_SUM = 0x7f0d0736;
        public static final int RGMAP_PROMPT_SYSBASEINFO = 0x7f0d0737;
        public static final int RGMAP_PROMPT_SYSLIB = 0x7f0d0738;
        public static final int RGMAP_PROMPT_SYSLIBPATH = 0x7f0d0739;
        public static final int RGMAP_PROMPT_SYSTEMCAMERA = 0x7f0d073a;
        public static final int RGMAP_PROMPT_SYSTEMRECORDER = 0x7f0d073b;
        public static final int RGMAP_PROMPT_TO = 0x7f0d073c;
        public static final int RGMAP_PROMPT_UPDATEPOS = 0x7f0d073d;
        public static final int RGMAP_PROMPT_USERCAMERA = 0x7f0d073e;
        public static final int RGMAP_PROMPT_USERRECORDER = 0x7f0d073f;
        public static final int RGMAP_PROMPT_VEDIO = 0x7f0d0740;
        public static final int RGMAP_PROMPT_VERSION = 0x7f0d0741;
        public static final int RGMAP_PROMPT_VIDEONUM = 0x7f0d0742;
        public static final int RGMAP_PROMPT_WN = 0x7f0d0743;
        public static final int RGMAP_PROMPT_WORKSUM = 0x7f0d0744;
        public static final int RGMAP_PROMPT_WS = 0x7f0d0745;
        public static final int RGMAP_SECTABLE_ANG = 0x7f0d0746;
        public static final int RGMAP_SECTABLE_ATTI = 0x7f0d0747;
        public static final int RGMAP_SECTABLE_CH = 0x7f0d0748;
        public static final int RGMAP_SECTABLE_CODE = 0x7f0d0749;
        public static final int RGMAP_SECTABLE_DESC = 0x7f0d074a;
        public static final int RGMAP_SECTABLE_DIP = 0x7f0d074b;
        public static final int RGMAP_SECTABLE_DIPANG = 0x7f0d074c;
        public static final int RGMAP_SECTABLE_DXH = 0x7f0d074d;
        public static final int RGMAP_SECTABLE_FWJ = 0x7f0d074e;
        public static final int RGMAP_SECTABLE_GC = 0x7f0d074f;
        public static final int RGMAP_SECTABLE_HD = 0x7f0d0750;
        public static final int RGMAP_SECTABLE_JCGX = 0x7f0d0751;
        public static final int RGMAP_SECTABLE_LAYER = 0x7f0d0752;
        public static final int RGMAP_SECTABLE_LINE = 0x7f0d0753;
        public static final int RGMAP_SECTABLE_LJGC = 0x7f0d0754;
        public static final int RGMAP_SECTABLE_LJHD = 0x7f0d0755;
        public static final int RGMAP_SECTABLE_LJPJ = 0x7f0d0756;
        public static final int RGMAP_SECTABLE_PJ = 0x7f0d0757;
        public static final int RGMAP_SECTABLE_POS = 0x7f0d0758;
        public static final int RGMAP_SECTABLE_SAMPLE = 0x7f0d0759;
        public static final int RGMAP_SECTABLE_TITLE = 0x7f0d075a;
        public static final int RGMAP_SECTABLE_XJ = 0x7f0d075b;
        public static final int RGMAP_SECTABLE_ZHD = 0x7f0d075c;
        public static final int RGMAP_TYPE_SUB = 0x7f0d075d;
        public static final int RGMAP_TYPE_TEXT = 0x7f0d075e;
        public static final int SAM_FLDNAME_ANALYSE = 0x7f0d075f;
        public static final int SAM_FLDNAME_CODE = 0x7f0d0760;
        public static final int SAM_FLDNAME_GEOUNIT = 0x7f0d0761;
        public static final int SAM_FLDNAME_LAYCODE = 0x7f0d0762;
        public static final int SAM_FLDNAME_LOCATION = 0x7f0d0763;
        public static final int SAM_FLDNAME_NAME = 0x7f0d0764;
        public static final int SAM_FLDNAME_SAMPLING = 0x7f0d0765;
        public static final int SAM_FLDNAME_SECCODE = 0x7f0d0766;
        public static final int SAM_FLDNAME_SECPOINT = 0x7f0d0767;
        public static final int SAM_FLDNAME_SLOPE_L = 0x7f0d0768;
        public static final int SAM_FLDNAME_TYPE = 0x7f0d0769;
        public static final int SECA_FLDNAME_CODE = 0x7f0d076a;
        public static final int SECA_FLDNAME_DIP = 0x7f0d076b;
        public static final int SECA_FLDNAME_DIP_ANG = 0x7f0d076c;
        public static final int SECA_FLDNAME_GBCODE = 0x7f0d076d;
        public static final int SECA_FLDNAME_LAYCODE = 0x7f0d076e;
        public static final int SECA_FLDNAME_SECCODE = 0x7f0d076f;
        public static final int SECA_FLDNAME_SECPOINT = 0x7f0d0770;
        public static final int SECA_FLDNAME_SLOPE_L = 0x7f0d0771;
        public static final int SECA_FLDNAME_TREND = 0x7f0d0772;
        public static final int SECA_FLDNAME_TYPE = 0x7f0d0773;
        public static final int SECTION_MENU_FOSSIL = 0x7f0d0774;
        public static final int SECTION_MENU_GPOINT = 0x7f0d0775;
        public static final int SECTION_MENU_PHOTO = 0x7f0d0776;
        public static final int SECTION_MENU_SAMPLE = 0x7f0d0777;
        public static final int SECTION_MENU_SECATT = 0x7f0d0778;
        public static final int SECTION_MENU_SKETCH = 0x7f0d0779;
        public static final int SECTION_MENU_SLAYER = 0x7f0d077a;
        public static final int SECTION_MENU_SUERY = 0x7f0d077b;
        public static final int SEC_FLDNAME_BEHIND = 0x7f0d077c;
        public static final int SEC_FLDNAME_CAMERA = 0x7f0d077d;
        public static final int SEC_FLDNAME_DATE = 0x7f0d077e;
        public static final int SEC_FLDNAME_DATE_END = 0x7f0d077f;
        public static final int SEC_FLDNAME_DATE_STA = 0x7f0d0780;
        public static final int SEC_FLDNAME_DEVIDER = 0x7f0d0781;
        public static final int SEC_FLDNAME_DIRECTION = 0x7f0d0782;
        public static final int SEC_FLDNAME_FORMER = 0x7f0d0783;
        public static final int SEC_FLDNAME_GASURVEY = 0x7f0d0784;
        public static final int SEC_FLDNAME_HEIGHT1 = 0x7f0d0785;
        public static final int SEC_FLDNAME_HEIGHT2 = 0x7f0d0786;
        public static final int SEC_FLDNAME_LATITUDE1 = 0x7f0d0787;
        public static final int SEC_FLDNAME_LATITUDE2 = 0x7f0d0788;
        public static final int SEC_FLDNAME_LENGTH = 0x7f0d0789;
        public static final int SEC_FLDNAME_LONGITUDE1 = 0x7f0d078a;
        public static final int SEC_FLDNAME_LONGITUDE2 = 0x7f0d078b;
        public static final int SEC_FLDNAME_MAPCODE = 0x7f0d078c;
        public static final int SEC_FLDNAME_MAPNAME = 0x7f0d078d;
        public static final int SEC_FLDNAME_PHOTOER = 0x7f0d078e;
        public static final int SEC_FLDNAME_RECORDER = 0x7f0d078f;
        public static final int SEC_FLDNAME_SAMPLING = 0x7f0d0790;
        public static final int SEC_FLDNAME_SCALE = 0x7f0d0791;
        public static final int SEC_FLDNAME_SECCODE = 0x7f0d0792;
        public static final int SEC_FLDNAME_SECNAME = 0x7f0d0793;
        public static final int SEC_FLDNAME_VERIFY = 0x7f0d0794;
        public static final int SEC_FLDNAME_XX1 = 0x7f0d0795;
        public static final int SEC_FLDNAME_XX2 = 0x7f0d0796;
        public static final int SEC_FLDNAME_YY1 = 0x7f0d0797;
        public static final int SEC_FLDNAME_YY2 = 0x7f0d0798;
        public static final int SKETCH_MENU_BK = 0x7f0d0799;
        public static final int SKETCH_MENU_COLOR = 0x7f0d079a;
        public static final int SKETCH_MENU_EASER = 0x7f0d079b;
        public static final int SKETCH_MENU_FILE = 0x7f0d079c;
        public static final int SKETCH_MENU_PEN = 0x7f0d079d;
        public static final int SKETCH_MENU_WIDTH = 0x7f0d079e;
        public static final int SKE_FLDNAME_CODE = 0x7f0d079f;
        public static final int SKE_FLDNAME_DATE = 0x7f0d07a0;
        public static final int SKE_FLDNAME_DESCRIBE = 0x7f0d07a1;
        public static final int SKE_FLDNAME_GEOPOINT = 0x7f0d07a2;
        public static final int SKE_FLDNAME_LAYCODE = 0x7f0d07a3;
        public static final int SKE_FLDNAME_SCALE = 0x7f0d07a4;
        public static final int SKE_FLDNAME_SECCODE = 0x7f0d07a5;
        public static final int SKE_FLDNAME_SECPOINT = 0x7f0d07a6;
        public static final int SKE_FLDNAME_SLOPE_L = 0x7f0d07a7;
        public static final int SKE_FLDNAME_TITLE = 0x7f0d07a8;
        public static final int SOFTWARE_UPDATE_CANCEL = 0x7f0d07a9;
        public static final int SOFTWARE_UPDATE_MESSAGE = 0x7f0d07aa;
        public static final int SOFTWARE_UPDATE_NO = 0x7f0d07ab;
        public static final int SOFTWARE_UPDATE_TITLE = 0x7f0d07ac;
        public static final int SOFTWARE_UPDATE_YES = 0x7f0d07ad;
        public static final int SUR_FLDNAME_AZIMUTH = 0x7f0d07ae;
        public static final int SUR_FLDNAME_FROM_X = 0x7f0d07af;
        public static final int SUR_FLDNAME_FROM_Y = 0x7f0d07b0;
        public static final int SUR_FLDNAME_FROM_Z = 0x7f0d07b1;
        public static final int SUR_FLDNAME_GRADE = 0x7f0d07b2;
        public static final int SUR_FLDNAME_HIGH = 0x7f0d07b3;
        public static final int SUR_FLDNAME_SECCODE = 0x7f0d07b4;
        public static final int SUR_FLDNAME_SECPOINT = 0x7f0d07b5;
        public static final int SUR_FLDNAME_SLOPE_L = 0x7f0d07b6;
        public static final int SUR_FLDNAME_TOTAL_HIGH = 0x7f0d07b7;
        public static final int SUR_FLDNAME_TO_X = 0x7f0d07b8;
        public static final int SUR_FLDNAME_TO_Y = 0x7f0d07b9;
        public static final int SUR_FLDNAME_TO_Z = 0x7f0d07ba;
        public static final int _model_default = 0x7f0d07bb;
        public static final int abc_action_bar_home_description = 0x7f0d07bc;
        public static final int abc_action_bar_home_description_format = 0x7f0d07bd;
        public static final int abc_action_bar_home_subtitle_description_format = 0x7f0d07be;
        public static final int abc_action_bar_up_description = 0x7f0d07bf;
        public static final int abc_action_menu_overflow_description = 0x7f0d07c0;
        public static final int abc_action_mode_done = 0x7f0d07c1;
        public static final int abc_activity_chooser_view_see_all = 0x7f0d07c2;
        public static final int abc_activitychooserview_choose_application = 0x7f0d07c3;
        public static final int abc_capital_off = 0x7f0d07c4;
        public static final int abc_capital_on = 0x7f0d07c5;
        public static final int abc_search_hint = 0x7f0d07c6;
        public static final int abc_searchview_description_clear = 0x7f0d07c7;
        public static final int abc_searchview_description_query = 0x7f0d07c8;
        public static final int abc_searchview_description_search = 0x7f0d07c9;
        public static final int abc_searchview_description_submit = 0x7f0d07ca;
        public static final int abc_searchview_description_voice = 0x7f0d07cb;
        public static final int abc_shareactionprovider_share_with = 0x7f0d07cc;
        public static final int abc_shareactionprovider_share_with_application = 0x7f0d07cd;
        public static final int abc_toolbar_collapse_description = 0x7f0d07ce;
        public static final int ao_GPSDilalog_autovalue = 0x7f0d07cf;
        public static final int ao_GPSDilalog_close = 0x7f0d07d0;
        public static final int ao_attlinkact_nosel = 0x7f0d07d1;
        public static final int ao_attlinkact_selcol = 0x7f0d07d2;
        public static final int ao_attribute_back_toast = 0x7f0d07d3;
        public static final int ao_btnCancel = 0x7f0d07d4;
        public static final int ao_btnInsert = 0x7f0d07d5;
        public static final int ao_btnOK = 0x7f0d07d6;
        public static final int ao_btnReplace = 0x7f0d07d7;
        public static final int ao_btnReturn = 0x7f0d07d8;
        public static final int ao_filelistact_createflord = 0x7f0d07d9;
        public static final int ao_filelistact_inputname = 0x7f0d07da;
        public static final int ao_filelistact_nosel = 0x7f0d07db;
        public static final int ao_filelistact_returnroot = 0x7f0d07dc;
        public static final int ao_filelistact_sel = 0x7f0d07dd;
        public static final int ao_filelistact_selflord = 0x7f0d07de;
        public static final int ao_gps_btnCenter = 0x7f0d07df;
        public static final int ao_gps_btnDrawPoint = 0x7f0d07e0;
        public static final int ao_gps_btnOnOff = 0x7f0d07e1;
        public static final int ao_gps_btnSwitchDisp = 0x7f0d07e2;
        public static final int ao_mapparam_centerlat = 0x7f0d07e3;
        public static final int ao_mapparam_coodtype = 0x7f0d07e4;
        public static final int ao_mapparam_coodunit = 0x7f0d07e5;
        public static final int ao_mapparam_paramscale = 0x7f0d07e6;
        public static final int ao_mapparam_projectiontype = 0x7f0d07e7;
        public static final int ao_mapparam_scale = 0x7f0d07e8;
        public static final int ao_mapparam_set = 0x7f0d07e9;
        public static final int ao_mapparam_title = 0x7f0d07ea;
        public static final int ao_menu_main_file = 0x7f0d07eb;
        public static final int ao_menu_main_file_layerrestore = 0x7f0d07ec;
        public static final int ao_menu_main_file_lyrmgr = 0x7f0d07ed;
        public static final int ao_menu_main_file_resetview = 0x7f0d07ee;
        public static final int ao_menu_main_file_save = 0x7f0d07ef;
        public static final int ao_menu_sketch_exit = 0x7f0d07f0;
        public static final int ao_menu_sketch_line = 0x7f0d07f1;
        public static final int ao_menu_sketch_line_copy = 0x7f0d07f2;
        public static final int ao_menu_sketch_line_del = 0x7f0d07f3;
        public static final int ao_menu_sketch_line_info = 0x7f0d07f4;
        public static final int ao_menu_sketch_line_move = 0x7f0d07f5;
        public static final int ao_menu_sketch_line_new1 = 0x7f0d07f6;
        public static final int ao_menu_sketch_line_new2 = 0x7f0d07f7;
        public static final int ao_menu_sketch_line_new3 = 0x7f0d07f8;
        public static final int ao_menu_sketch_line_node = 0x7f0d07f9;
        public static final int ao_menu_sketch_point = 0x7f0d07fa;
        public static final int ao_menu_sketch_point_copy = 0x7f0d07fb;
        public static final int ao_menu_sketch_point_del = 0x7f0d07fc;
        public static final int ao_menu_sketch_point_info = 0x7f0d07fd;
        public static final int ao_menu_sketch_point_move = 0x7f0d07fe;
        public static final int ao_menu_sketch_point_newnote = 0x7f0d07ff;
        public static final int ao_menu_sketch_point_newsym = 0x7f0d0800;
        public static final int ao_nodata = 0x7f0d0801;
        public static final int ao_textCompass = 0x7f0d0802;
        public static final int ao_textCompassClickHint = 0x7f0d0803;
        public static final int ao_textCompassDeclination = 0x7f0d0804;
        public static final int ao_textCompassDip = 0x7f0d0805;
        public static final int ao_textCompassDipangle = 0x7f0d0806;
        public static final int ao_textCompassHint = 0x7f0d0807;
        public static final int ao_textCompassPitchangle = 0x7f0d0808;
        public static final int ao_textCompassRollangle = 0x7f0d0809;
        public static final int ao_tool_func_backpoint = 0x7f0d080a;
        public static final int app_name = 0x7f0d080b;
        public static final int app_version = 0x7f0d080c;
        public static final int att_btn_tool = 0x7f0d080d;
        public static final int attitude_ang = 0x7f0d080e;
        public static final int attitude_btn_jieli = 0x7f0d080f;
        public static final int attitude_btn_luopan = 0x7f0d0810;
        public static final int attitude_direction = 0x7f0d0811;
        public static final int attitude_group_basic = 0x7f0d0812;
        public static final int attitude_group_other = 0x7f0d0813;
        public static final int attitude_no = 0x7f0d0814;
        public static final int attitude_radio_qzcal = 0x7f0d0815;
        public static final int attitude_radio_zqcal = 0x7f0d0816;
        public static final int attitude_rcode = 0x7f0d0817;
        public static final int attitude_tend = 0x7f0d0818;
        public static final int attitude_type = 0x7f0d0819;
        public static final int attitude_unit = 0x7f0d081a;
        public static final int attitude_xy = 0x7f0d081b;
        public static final int attitude_z = 0x7f0d081c;
        public static final int attribute_back_toast = 0x7f0d081d;
        public static final int bd_BTOK = 0x7f0d081e;
        public static final int bd_BTcancel = 0x7f0d081f;
        public static final int bd_bluetooth = 0x7f0d0820;
        public static final int bd_info = 0x7f0d0821;
        public static final int bd_prompt_Startlink = 0x7f0d0822;
        public static final int bd_prompt_bdtime = 0x7f0d0823;
        public static final int bd_prompt_electric = 0x7f0d0824;
        public static final int bd_prompt_find = 0x7f0d0825;
        public static final int bd_prompt_find1 = 0x7f0d0826;
        public static final int bd_prompt_find2 = 0x7f0d0827;
        public static final int bd_prompt_find3 = 0x7f0d0828;
        public static final int bd_prompt_find4 = 0x7f0d0829;
        public static final int bd_prompt_find5 = 0x7f0d082a;
        public static final int bd_prompt_find6 = 0x7f0d082b;
        public static final int bd_prompt_la = 0x7f0d082c;
        public static final int bd_prompt_linkdevice = 0x7f0d082d;
        public static final int bd_prompt_linking = 0x7f0d082e;
        public static final int bd_prompt_loc = 0x7f0d082f;
        public static final int bd_prompt_location = 0x7f0d0830;
        public static final int bd_prompt_longi = 0x7f0d0831;
        public static final int bd_prompt_sendcommandstate = 0x7f0d0832;
        public static final int bd_prompt_signal = 0x7f0d0833;
        public static final int bd_prompt_signal1 = 0x7f0d0834;
        public static final int bd_prompt_signal2 = 0x7f0d0835;
        public static final int bd_prompt_signal3 = 0x7f0d0836;
        public static final int bd_prompt_signal4 = 0x7f0d0837;
        public static final int bd_prompt_signal5 = 0x7f0d0838;
        public static final int bd_prompt_signal6 = 0x7f0d0839;
        public static final int bd_prompt_sms = 0x7f0d083a;
        public static final int bd_receivemsg = 0x7f0d083b;
        public static final int bd_sendmsg = 0x7f0d083c;
        public static final int bdinfo_name = 0x7f0d083d;
        public static final int bitmap_btn_tool = 0x7f0d083e;
        public static final int btnCancel = 0x7f0d083f;
        public static final int btnInsert = 0x7f0d0840;
        public static final int btnOK = 0x7f0d0841;
        public static final int btnReplace = 0x7f0d0842;
        public static final int btnReturn = 0x7f0d0843;
        public static final int btnSave = 0x7f0d0844;
        public static final int btn_test = 0x7f0d0845;
        public static final int cancel = 0x7f0d0846;
        public static final int card_num = 0x7f0d0847;
        public static final int connect_bt = 0x7f0d0848;
        public static final int connect_name = 0x7f0d0849;
        public static final int data_btn_tool = 0x7f0d084a;
        public static final int decoder_default = 0x7f0d084b;
        public static final int devicelist_tv = 0x7f0d084c;
        public static final int dic_AttitudeType = 0x7f0d084d;
        public static final int dic_FossilType = 0x7f0d084e;
        public static final int dic_SampleType = 0x7f0d084f;
        public static final int dic_boundary_type = 0x7f0d0850;
        public static final int dic_contact_relationship = 0x7f0d0851;
        public static final int dic_gushengwu = 0x7f0d0852;
        public static final int dic_mapcode = 0x7f0d0853;
        public static final int dic_mapname = 0x7f0d0854;
        public static final int dic_mine_era = 0x7f0d0855;
        public static final int dic_mine_name = 0x7f0d0856;
        public static final int dic_mulbjwz = 0x7f0d0857;
        public static final int dic_mulcy = 0x7f0d0858;
        public static final int dic_muldjw = 0x7f0d0859;
        public static final int dic_muldm = 0x7f0d085a;
        public static final int dic_muljs = 0x7f0d085b;
        public static final int dic_mulmtycp = 0x7f0d085c;
        public static final int dic_mulnzwzl = 0x7f0d085d;
        public static final int dic_mulqs = 0x7f0d085e;
        public static final int dic_mulsam = 0x7f0d085f;
        public static final int dic_mulsc = 0x7f0d0860;
        public static final int dic_mulsfblx = 0x7f0d0861;
        public static final int dic_mulsm = 0x7f0d0862;
        public static final int dic_mulsswfb = 0x7f0d0863;
        public static final int dic_mulswch = 0x7f0d0864;
        public static final int dic_mulsyt = 0x7f0d0865;
        public static final int dic_multdly = 0x7f0d0866;
        public static final int dic_multfh = 0x7f0d0867;
        public static final int dic_multfmc = 0x7f0d0868;
        public static final int dic_mulwr = 0x7f0d0869;
        public static final int dic_mulypzf = 0x7f0d086a;
        public static final int dic_mulys = 0x7f0d086b;
        public static final int dic_mulyz = 0x7f0d086c;
        public static final int dic_mulyzgk = 0x7f0d086d;
        public static final int dic_mulyzylx = 0x7f0d086e;
        public static final int dic_mulzd = 0x7f0d086f;
        public static final int dic_name = 0x7f0d0870;
        public static final int dic_place_name = 0x7f0d0871;
        public static final int dic_prjname = 0x7f0d0872;
        public static final int dic_remainsubtype = 0x7f0d0873;
        public static final int dic_remaintype = 0x7f0d0874;
        public static final int dic_stone_name = 0x7f0d0875;
        public static final int dic_tiantu_pclass = 0x7f0d0876;
        public static final int dic_tiantu_unit = 0x7f0d0877;
        public static final int dic_tiantu_weidimao = 0x7f0d0878;
        public static final int dic_weather = 0x7f0d0879;
        public static final int dic_yanxing = 0x7f0d087a;
        public static final int dlg_gps_rectify_dx = 0x7f0d087b;
        public static final int dlg_gps_rectify_dy = 0x7f0d087c;
        public static final int dlg_gps_rectify_dz = 0x7f0d087d;
        public static final int fos_age = 0x7f0d087e;
        public static final int fos_analyse = 0x7f0d087f;
        public static final int fos_code = 0x7f0d0880;
        public static final int fos_date = 0x7f0d0881;
        public static final int fos_fossil = 0x7f0d0882;
        public static final int fos_gbcode = 0x7f0d0883;
        public static final int fos_geounit = 0x7f0d0884;
        public static final int fos_laycode = 0x7f0d0885;
        public static final int fos_name = 0x7f0d0886;
        public static final int fos_remark = 0x7f0d0887;
        public static final int fos_sampling = 0x7f0d0888;
        public static final int fos_seccode = 0x7f0d0889;
        public static final int fos_secpoint = 0x7f0d088a;
        public static final int fos_slope_l = 0x7f0d088b;
        public static final int fos_table_age = 0x7f0d088c;
        public static final int fos_table_analyse = 0x7f0d088d;
        public static final int fos_table_code = 0x7f0d088e;
        public static final int fos_table_date = 0x7f0d088f;
        public static final int fos_table_geounit = 0x7f0d0890;
        public static final int fos_table_laycode = 0x7f0d0891;
        public static final int fos_table_name = 0x7f0d0892;
        public static final int fos_table_remark = 0x7f0d0893;
        public static final int fos_table_sampling = 0x7f0d0894;
        public static final int fos_table_secpoint = 0x7f0d0895;
        public static final int fos_table_slope_l = 0x7f0d0896;
        public static final int fos_table_type = 0x7f0d0897;
        public static final int fos_type = 0x7f0d0898;
        public static final int fossil_animal = 0x7f0d0899;
        public static final int fossil_class = 0x7f0d089a;
        public static final int fossil_date = 0x7f0d089b;
        public static final int fossil_layer = 0x7f0d089c;
        public static final int fossil_person = 0x7f0d089d;
        public static final int fossil_place = 0x7f0d089e;
        public static final int fossil_plant = 0x7f0d089f;
        public static final int fossil_rcode = 0x7f0d08a0;
        public static final int fossil_sample_no = 0x7f0d08a1;
        public static final int fossil_type = 0x7f0d08a2;
        public static final int freeline_date = 0x7f0d08a3;
        public static final int freeline_mapcode = 0x7f0d08a4;
        public static final int freeline_note = 0x7f0d08a5;
        public static final int gboundary_angle = 0x7f0d08a6;
        public static final int gboundary_bcode = 0x7f0d08a7;
        public static final int gboundary_btype = 0x7f0d08a8;
        public static final int gboundary_direction = 0x7f0d08a9;
        public static final int gboundary_distance = 0x7f0d08aa;
        public static final int gboundary_group_basic = 0x7f0d08ab;
        public static final int gboundary_leftbody = 0x7f0d08ac;
        public static final int gboundary_rcode = 0x7f0d08ad;
        public static final int gboundary_relation = 0x7f0d08ae;
        public static final int gboundary_rightbody = 0x7f0d08af;
        public static final int gboundary_trend = 0x7f0d08b0;
        public static final int gpo_altitude = 0x7f0d08b1;
        public static final int gpo_date = 0x7f0d08b2;
        public static final int gpo_geomorph = 0x7f0d08b3;
        public static final int gpo_geopoint = 0x7f0d08b4;
        public static final int gpo_gpodescribe = 0x7f0d08b5;
        public static final int gpo_gpoint = 0x7f0d08b6;
        public static final int gpo_latitude = 0x7f0d08b7;
        public static final int gpo_laycode = 0x7f0d08b8;
        public static final int gpo_lith_a = 0x7f0d08b9;
        public static final int gpo_lith_b = 0x7f0d08ba;
        public static final int gpo_lith_c = 0x7f0d08bb;
        public static final int gpo_litho = 0x7f0d08bc;
        public static final int gpo_litho_1 = 0x7f0d08bd;
        public static final int gpo_litho_2 = 0x7f0d08be;
        public static final int gpo_litho_3 = 0x7f0d08bf;
        public static final int gpo_location = 0x7f0d08c0;
        public static final int gpo_longitude = 0x7f0d08c1;
        public static final int gpo_outcrop = 0x7f0d08c2;
        public static final int gpo_seccode = 0x7f0d08c3;
        public static final int gpo_secpoint = 0x7f0d08c4;
        public static final int gpo_slope_l = 0x7f0d08c5;
        public static final int gpo_straph = 0x7f0d08c6;
        public static final int gpo_straph_a = 0x7f0d08c7;
        public static final int gpo_straph_b = 0x7f0d08c8;
        public static final int gpo_straph_c = 0x7f0d08c9;
        public static final int gpo_strar = 0x7f0d08ca;
        public static final int gpo_strarab = 0x7f0d08cb;
        public static final int gpo_strarac = 0x7f0d08cc;
        public static final int gpo_strarbc = 0x7f0d08cd;
        public static final int gpo_table_geomorph = 0x7f0d08ce;
        public static final int gpo_table_geopoint = 0x7f0d08cf;
        public static final int gpo_table_laycode = 0x7f0d08d0;
        public static final int gpo_table_lith_a = 0x7f0d08d1;
        public static final int gpo_table_lith_b = 0x7f0d08d2;
        public static final int gpo_table_lith_c = 0x7f0d08d3;
        public static final int gpo_table_outcrop = 0x7f0d08d4;
        public static final int gpo_table_secpoint = 0x7f0d08d5;
        public static final int gpo_table_slope_l = 0x7f0d08d6;
        public static final int gpo_table_straph_a = 0x7f0d08d7;
        public static final int gpo_table_straph_b = 0x7f0d08d8;
        public static final int gpo_table_straph_c = 0x7f0d08d9;
        public static final int gpo_table_strarab = 0x7f0d08da;
        public static final int gpo_table_strarac = 0x7f0d08db;
        public static final int gpo_table_strarbc = 0x7f0d08dc;
        public static final int gpo_table_type = 0x7f0d08dd;
        public static final int gpo_table_weathing = 0x7f0d08de;
        public static final int gpo_type = 0x7f0d08df;
        public static final int gpo_weathing = 0x7f0d08e0;
        public static final int gpo_xx = 0x7f0d08e1;
        public static final int gpo_yy = 0x7f0d08e2;
        public static final int gpoint_dianxing = 0x7f0d08e3;
        public static final int gpoint_editdizhi = 0x7f0d08e4;
        public static final int gpoint_edityanshi = 0x7f0d08e5;
        public static final int gpoint_fenghua = 0x7f0d08e6;
        public static final int gpoint_location = 0x7f0d08e7;
        public static final int gpoint_lutou = 0x7f0d08e8;
        public static final int gpoint_rAB = 0x7f0d08e9;
        public static final int gpoint_rAC = 0x7f0d08ea;
        public static final int gpoint_rBC = 0x7f0d08eb;
        public static final int gpoint_tiantuA = 0x7f0d08ec;
        public static final int gpoint_tiantuB = 0x7f0d08ed;
        public static final int gpoint_tiantuC = 0x7f0d08ee;
        public static final int gpoint_weidimao = 0x7f0d08ef;
        public static final int gpoint_xy = 0x7f0d08f0;
        public static final int gpoint_yanshiA = 0x7f0d08f1;
        public static final int gpoint_yanshiB = 0x7f0d08f2;
        public static final int gpoint_yanshiC = 0x7f0d08f3;
        public static final int gpoint_z = 0x7f0d08f4;
        public static final int gps_btnCenter = 0x7f0d08f5;
        public static final int gps_btnDrawPoint = 0x7f0d08f6;
        public static final int gps_btnOnOff = 0x7f0d08f7;
        public static final int gps_btnSwitchDisp = 0x7f0d08f8;
        public static final int hello = 0x7f0d08f9;
        public static final int infile_default = 0x7f0d08fa;
        public static final int language_default = 0x7f0d08fb;
        public static final int lay_describe = 0x7f0d08fc;
        public static final int lay_dip = 0x7f0d08fd;
        public static final int lay_dip_angle = 0x7f0d08fe;
        public static final int lay_grade = 0x7f0d08ff;
        public static final int lay_laycode = 0x7f0d0900;
        public static final int lay_laycode_ = 0x7f0d0901;
        public static final int lay_seccode = 0x7f0d0902;
        public static final int lay_secpoint = 0x7f0d0903;
        public static final int lay_sign = 0x7f0d0904;
        public static final int lay_slayer = 0x7f0d0905;
        public static final int lay_slope_l = 0x7f0d0906;
        public static final int lay_slope_l_ = 0x7f0d0907;
        public static final int lay_t_thick = 0x7f0d0908;
        public static final int lay_table_laycode = 0x7f0d0909;
        public static final int lay_table_secpoint = 0x7f0d090a;
        public static final int lay_table_slope_l = 0x7f0d090b;
        public static final int lay_trend = 0x7f0d090c;
        public static final int lay_trend_pm = 0x7f0d090d;
        public static final int lay_v_thick = 0x7f0d090e;
        public static final int menu = 0x7f0d090f;
        public static final int menu_addpoint_bygps = 0x7f0d0910;
        public static final int menu_att_link = 0x7f0d0911;
        public static final int menu_lineedit_att = 0x7f0d0912;
        public static final int menu_lineedit_copy = 0x7f0d0913;
        public static final int menu_lineedit_del = 0x7f0d0914;
        public static final int menu_lineedit_edit = 0x7f0d0915;
        public static final int menu_lineedit_info = 0x7f0d0916;
        public static final int menu_lineedit_move = 0x7f0d0917;
        public static final int menu_lineedit_new = 0x7f0d0918;
        public static final int menu_lineedit_new1 = 0x7f0d0919;
        public static final int menu_lineedit_new2 = 0x7f0d091a;
        public static final int menu_lineedit_new3 = 0x7f0d091b;
        public static final int menu_link_att = 0x7f0d091c;
        public static final int menu_main_earth = 0x7f0d091d;
        public static final int menu_main_earth_25georock = 0x7f0d091e;
        public static final int menu_main_earth_25geosoil = 0x7f0d091f;
        public static final int menu_main_earth_25geowater = 0x7f0d0920;
        public static final int menu_main_earth_25rock = 0x7f0d0921;
        public static final int menu_main_earth_25rock_r = 0x7f0d0922;
        public static final int menu_main_earth_25soil = 0x7f0d0923;
        public static final int menu_main_earth_25soil_r = 0x7f0d0924;
        public static final int menu_main_earth_25stream = 0x7f0d0925;
        public static final int menu_main_earth_25stream_r = 0x7f0d0926;
        public static final int menu_main_earth_25water = 0x7f0d0927;
        public static final int menu_main_earth_25water_r = 0x7f0d0928;
        public static final int menu_main_earth_5georock = 0x7f0d0929;
        public static final int menu_main_earth_5geosoil = 0x7f0d092a;
        public static final int menu_main_earth_5geowater = 0x7f0d092b;
        public static final int menu_main_earth_georock = 0x7f0d092c;
        public static final int menu_main_earth_georock_r = 0x7f0d092d;
        public static final int menu_main_earth_geosoil = 0x7f0d092e;
        public static final int menu_main_earth_geosoil_r = 0x7f0d092f;
        public static final int menu_main_earth_geowater = 0x7f0d0930;
        public static final int menu_main_earth_geowater_r = 0x7f0d0931;
        public static final int menu_main_earth_mulsedimentnew = 0x7f0d0932;
        public static final int menu_main_earth_mulsoil = 0x7f0d0933;
        public static final int menu_main_earth_mulsoil_r = 0x7f0d0934;
        public static final int menu_main_earth_mulsoilnew = 0x7f0d0935;
        public static final int menu_main_earth_mulstrem = 0x7f0d0936;
        public static final int menu_main_earth_mulstrem_r = 0x7f0d0937;
        public static final int menu_main_earth_mulwater = 0x7f0d0938;
        public static final int menu_main_earth_mulwater_r = 0x7f0d0939;
        public static final int menu_main_earth_mulwaternew = 0x7f0d093a;
        public static final int menu_main_earth_sand_sedi = 0x7f0d093b;
        public static final int menu_main_earth_sand_sedi_r = 0x7f0d093c;
        public static final int menu_main_earth_soil_sedi = 0x7f0d093d;
        public static final int menu_main_earth_soil_sedi_r = 0x7f0d093e;
        public static final int menu_main_earth_strem_sedi = 0x7f0d093f;
        public static final int menu_main_earth_strem_sedi_r = 0x7f0d0940;
        public static final int menu_main_file = 0x7f0d0941;
        public static final int menu_main_file_close = 0x7f0d0942;
        public static final int menu_main_file_config = 0x7f0d0943;
        public static final int menu_main_file_exit = 0x7f0d0944;
        public static final int menu_main_file_layerrestore = 0x7f0d0945;
        public static final int menu_main_file_lyrmgr = 0x7f0d0946;
        public static final int menu_main_file_mapparam = 0x7f0d0947;
        public static final int menu_main_file_menuconfig = 0x7f0d0948;
        public static final int menu_main_file_open = 0x7f0d0949;
        public static final int menu_main_file_resetview = 0x7f0d094a;
        public static final int menu_main_file_routesum = 0x7f0d094b;
        public static final int menu_main_file_save = 0x7f0d094c;
        public static final int menu_main_file_version = 0x7f0d094d;
        public static final int menu_main_gps = 0x7f0d094e;
        public static final int menu_main_gps_close = 0x7f0d094f;
        public static final int menu_main_gps_open = 0x7f0d0950;
        public static final int menu_main_prb = 0x7f0d0951;
        public static final int menu_main_prb_3dmodel = 0x7f0d0952;
        public static final int menu_main_prb_Imageparency = 0x7f0d0953;
        public static final int menu_main_prb_att = 0x7f0d0954;
        public static final int menu_main_prb_b = 0x7f0d0955;
        public static final int menu_main_prb_copyroute = 0x7f0d0956;
        public static final int menu_main_prb_dataquery = 0x7f0d0957;
        public static final int menu_main_prb_engpoint = 0x7f0d0958;
        public static final int menu_main_prb_fossil = 0x7f0d0959;
        public static final int menu_main_prb_freeline = 0x7f0d095a;
        public static final int menu_main_prb_gpsroute = 0x7f0d095b;
        public static final int menu_main_prb_hydpoint = 0x7f0d095c;
        public static final int menu_main_prb_lfree = 0x7f0d095d;
        public static final int menu_main_prb_location = 0x7f0d095e;
        public static final int menu_main_prb_measurearea = 0x7f0d095f;
        public static final int menu_main_prb_measurelin = 0x7f0d0960;
        public static final int menu_main_prb_modifypara = 0x7f0d0961;
        public static final int menu_main_prb_orecheck = 0x7f0d0962;
        public static final int menu_main_prb_p = 0x7f0d0963;
        public static final int menu_main_prb_pfree = 0x7f0d0964;
        public static final int menu_main_prb_photo = 0x7f0d0965;
        public static final int menu_main_prb_r = 0x7f0d0966;
        public static final int menu_main_prb_remain = 0x7f0d0967;
        public static final int menu_main_prb_remainLin = 0x7f0d0968;
        public static final int menu_main_prb_route = 0x7f0d0969;
        public static final int menu_main_prb_sample = 0x7f0d096a;
        public static final int menu_main_prb_showcompass = 0x7f0d096b;
        public static final int menu_main_prb_sketch = 0x7f0d096c;
        public static final int menu_main_prb_smartservice = 0x7f0d096d;
        public static final int menu_main_section = 0x7f0d096e;
        public static final int menu_main_section_editAtt = 0x7f0d096f;
        public static final int menu_main_section_editLib = 0x7f0d0970;
        public static final int menu_main_section_new = 0x7f0d0971;
        public static final int menu_main_section_sectionlist = 0x7f0d0972;
        public static final int menu_main_section_sectionmap = 0x7f0d0973;
        public static final int menu_main_section_sectionpath = 0x7f0d0974;
        public static final int menu_more = 0x7f0d0975;
        public static final int menu_pointedit_att = 0x7f0d0976;
        public static final int menu_pointedit_copy = 0x7f0d0977;
        public static final int menu_pointedit_del = 0x7f0d0978;
        public static final int menu_pointedit_info = 0x7f0d0979;
        public static final int menu_pointedit_link_att = 0x7f0d097a;
        public static final int menu_pointedit_move = 0x7f0d097b;
        public static final int menu_pointedit_new = 0x7f0d097c;
        public static final int menu_sectionlist_del = 0x7f0d097d;
        public static final int menu_sectionlist_editatt = 0x7f0d097e;
        public static final int menu_sectionlist_editlib = 0x7f0d097f;
        public static final int menu_sectionlist_new = 0x7f0d0980;
        public static final int menu_sectionmap_del = 0x7f0d0981;
        public static final int menu_sectionmap_editatt = 0x7f0d0982;
        public static final int menu_sectionmap_editlib = 0x7f0d0983;
        public static final int menu_sectionmap_move = 0x7f0d0984;
        public static final int menu_sectionmap_new = 0x7f0d0985;
        public static final int menu_sectionmap_path = 0x7f0d0986;
        public static final int menu_sectionmap_projection = 0x7f0d0987;
        public static final int menu_sectionmap_search = 0x7f0d0988;
        public static final int menu_sketch_background = 0x7f0d0989;
        public static final int menu_sketch_exit = 0x7f0d098a;
        public static final int menu_sketch_line = 0x7f0d098b;
        public static final int menu_sketch_line_copy = 0x7f0d098c;
        public static final int menu_sketch_line_del = 0x7f0d098d;
        public static final int menu_sketch_line_info = 0x7f0d098e;
        public static final int menu_sketch_line_move = 0x7f0d098f;
        public static final int menu_sketch_line_new1 = 0x7f0d0990;
        public static final int menu_sketch_line_new2 = 0x7f0d0991;
        public static final int menu_sketch_line_new3 = 0x7f0d0992;
        public static final int menu_sketch_line_node = 0x7f0d0993;
        public static final int menu_sketch_point = 0x7f0d0994;
        public static final int menu_sketch_point_copy = 0x7f0d0995;
        public static final int menu_sketch_point_del = 0x7f0d0996;
        public static final int menu_sketch_point_info = 0x7f0d0997;
        public static final int menu_sketch_point_move = 0x7f0d0998;
        public static final int menu_sketch_point_newnote = 0x7f0d0999;
        public static final int menu_sketch_point_newsym = 0x7f0d099a;
        public static final int menu_sketch_width = 0x7f0d099b;
        public static final int nlu_default = 0x7f0d099c;
        public static final int nodata = 0x7f0d099d;
        public static final int orecheck_ablelenth = 0x7f0d099e;
        public static final int orecheck_angle = 0x7f0d099f;
        public static final int orecheck_basicGeo = 0x7f0d09a0;
        public static final int orecheck_basicInfo = 0x7f0d09a1;
        public static final int orecheck_cause = 0x7f0d09a2;
        public static final int orecheck_character = 0x7f0d09a3;
        public static final int orecheck_checher = 0x7f0d09a4;
        public static final int orecheck_combination = 0x7f0d09a5;
        public static final int orecheck_conclusion = 0x7f0d09a6;
        public static final int orecheck_conponent = 0x7f0d09a7;
        public static final int orecheck_dip = 0x7f0d09a8;
        public static final int orecheck_industry_type = 0x7f0d09a9;
        public static final int orecheck_kcanb_code = 0x7f0d09aa;
        public static final int orecheck_kcanb_name = 0x7f0d09ab;
        public static final int orecheck_ktx_code = 0x7f0d09ac;
        public static final int orecheck_litho_character = 0x7f0d09ad;
        public static final int orecheck_litho_depth = 0x7f0d09ae;
        public static final int orecheck_litho_era = 0x7f0d09af;
        public static final int orecheck_lithofacies = 0x7f0d09b0;
        public static final int orecheck_lithology = 0x7f0d09b1;
        public static final int orecheck_magma = 0x7f0d09b2;
        public static final int orecheck_max_thick = 0x7f0d09b3;
        public static final int orecheck_mean_thick = 0x7f0d09b4;
        public static final int orecheck_meaning = 0x7f0d09b5;
        public static final int orecheck_min_lenth = 0x7f0d09b6;
        public static final int orecheck_mine_code = 0x7f0d09b7;
        public static final int orecheck_mine_type = 0x7f0d09b8;
        public static final int orecheck_minepnt_code = 0x7f0d09b9;
        public static final int orecheck_num = 0x7f0d09ba;
        public static final int orecheck_opinion = 0x7f0d09bb;
        public static final int orecheck_ore_mine_date = 0x7f0d09bc;
        public static final int orecheck_oxidation = 0x7f0d09bd;
        public static final int orecheck_place = 0x7f0d09be;
        public static final int orecheck_position = 0x7f0d09bf;
        public static final int orecheck_recorder = 0x7f0d09c0;
        public static final int orecheck_relation = 0x7f0d09c1;
        public static final int orecheck_rockname = 0x7f0d09c2;
        public static final int orecheck_sample = 0x7f0d09c3;
        public static final int orecheck_shape = 0x7f0d09c4;
        public static final int orecheck_stone_in = 0x7f0d09c5;
        public static final int orecheck_stone_rock = 0x7f0d09c6;
        public static final int orecheck_str_geo = 0x7f0d09c7;
        public static final int orecheck_structure = 0x7f0d09c8;
        public static final int orecheck_tect_character = 0x7f0d09c9;
        public static final int orecheck_tectonics = 0x7f0d09ca;
        public static final int orecheck_trend = 0x7f0d09cb;
        public static final int orecheck_xy = 0x7f0d09cc;
        public static final int org_name = 0x7f0d09cd;
        public static final int org_name_eng = 0x7f0d09ce;
        public static final int pho_code = 0x7f0d09cf;
        public static final int pho_describe = 0x7f0d09d0;
        public static final int pho_direction = 0x7f0d09d1;
        public static final int pho_gbcode = 0x7f0d09d2;
        public static final int pho_laycode = 0x7f0d09d3;
        public static final int pho_laycode_ = 0x7f0d09d4;
        public static final int pho_layout_code = 0x7f0d09d5;
        public static final int pho_layout_describe = 0x7f0d09d6;
        public static final int pho_layout_direction = 0x7f0d09d7;
        public static final int pho_layout_laycode = 0x7f0d09d8;
        public static final int pho_layout_number = 0x7f0d09d9;
        public static final int pho_layout_secpoint = 0x7f0d09da;
        public static final int pho_layout_slope_l = 0x7f0d09db;
        public static final int pho_mpg_id = 0x7f0d09dc;
        public static final int pho_number = 0x7f0d09dd;
        public static final int pho_photo = 0x7f0d09de;
        public static final int pho_seccode = 0x7f0d09df;
        public static final int pho_secpoint = 0x7f0d09e0;
        public static final int pho_slope_l = 0x7f0d09e1;
        public static final int pho_sound_id = 0x7f0d09e2;
        public static final int photo_btn_desc = 0x7f0d09e3;
        public static final int photo_content = 0x7f0d09e4;
        public static final int photo_direction = 0x7f0d09e5;
        public static final int photo_identity = 0x7f0d09e6;
        public static final int photo_no = 0x7f0d09e7;
        public static final int photo_num = 0x7f0d09e8;
        public static final int photo_rcode = 0x7f0d09e9;
        public static final int photo_seq = 0x7f0d09ea;
        public static final int photo_seq_desc = 0x7f0d09eb;
        public static final int photo_sound = 0x7f0d09ec;
        public static final int please_speak = 0x7f0d09ed;
        public static final int prop_default = 0x7f0d09ee;
        public static final int rg_dizhi = 0x7f0d09ef;
        public static final int rg_geo_desc = 0x7f0d09f0;
        public static final int rg_gpoint_no = 0x7f0d09f1;
        public static final int rg_route_no = 0x7f0d09f2;
        public static final int rg_xy = 0x7f0d09f3;
        public static final int routing_direction = 0x7f0d09f4;
        public static final int routing_distance = 0x7f0d09f5;
        public static final int routing_dizhi = 0x7f0d09f6;
        public static final int routing_rcode = 0x7f0d09f7;
        public static final int routing_suml = 0x7f0d09f8;
        public static final int routing_tiantu = 0x7f0d09f9;
        public static final int routing_yanshi = 0x7f0d09fa;
        public static final int sam_analyse = 0x7f0d09fb;
        public static final int sam_code = 0x7f0d09fc;
        public static final int sam_geounit = 0x7f0d09fd;
        public static final int sam_laycode = 0x7f0d09fe;
        public static final int sam_layout_analyse = 0x7f0d09ff;
        public static final int sam_layout_code = 0x7f0d0a00;
        public static final int sam_layout_geounit = 0x7f0d0a01;
        public static final int sam_layout_laycode = 0x7f0d0a02;
        public static final int sam_layout_name = 0x7f0d0a03;
        public static final int sam_layout_sampling = 0x7f0d0a04;
        public static final int sam_layout_secpoint = 0x7f0d0a05;
        public static final int sam_layout_slope_l = 0x7f0d0a06;
        public static final int sam_layout_type = 0x7f0d0a07;
        public static final int sam_location = 0x7f0d0a08;
        public static final int sam_name = 0x7f0d0a09;
        public static final int sam_sample = 0x7f0d0a0a;
        public static final int sam_sampling = 0x7f0d0a0b;
        public static final int sam_seccode = 0x7f0d0a0c;
        public static final int sam_secpoint = 0x7f0d0a0d;
        public static final int sam_slope_l = 0x7f0d0a0e;
        public static final int sam_type = 0x7f0d0a0f;
        public static final int sample_class = 0x7f0d0a10;
        public static final int sample_date = 0x7f0d0a11;
        public static final int sample_deepth = 0x7f0d0a12;
        public static final int sample_layer = 0x7f0d0a13;
        public static final int sample_no = 0x7f0d0a14;
        public static final int sample_num = 0x7f0d0a15;
        public static final int sample_person = 0x7f0d0a16;
        public static final int sample_place = 0x7f0d0a17;
        public static final int sample_rcode = 0x7f0d0a18;
        public static final int sample_weight = 0x7f0d0a19;
        public static final int sample_yanxing = 0x7f0d0a1a;
        public static final int scan_bt_restart = 0x7f0d0a1b;
        public static final int scan_bt_start = 0x7f0d0a1c;
        public static final int scan_bt_stop = 0x7f0d0a1d;
        public static final int search_text = 0x7f0d0a1e;
        public static final int sec_behind = 0x7f0d0a1f;
        public static final int sec_camera = 0x7f0d0a20;
        public static final int sec_date = 0x7f0d0a21;
        public static final int sec_date_end = 0x7f0d0a22;
        public static final int sec_date_sta = 0x7f0d0a23;
        public static final int sec_devider = 0x7f0d0a24;
        public static final int sec_direction = 0x7f0d0a25;
        public static final int sec_end = 0x7f0d0a26;
        public static final int sec_former = 0x7f0d0a27;
        public static final int sec_gasurvey = 0x7f0d0a28;
        public static final int sec_height = 0x7f0d0a29;
        public static final int sec_latitude = 0x7f0d0a2a;
        public static final int sec_layout_behind = 0x7f0d0a2b;
        public static final int sec_layout_camera = 0x7f0d0a2c;
        public static final int sec_layout_date = 0x7f0d0a2d;
        public static final int sec_layout_date_end = 0x7f0d0a2e;
        public static final int sec_layout_date_sta = 0x7f0d0a2f;
        public static final int sec_layout_devider = 0x7f0d0a30;
        public static final int sec_layout_direction = 0x7f0d0a31;
        public static final int sec_layout_former = 0x7f0d0a32;
        public static final int sec_layout_gasurvey = 0x7f0d0a33;
        public static final int sec_layout_height = 0x7f0d0a34;
        public static final int sec_layout_latitude = 0x7f0d0a35;
        public static final int sec_layout_length = 0x7f0d0a36;
        public static final int sec_layout_longitude = 0x7f0d0a37;
        public static final int sec_layout_mapcode = 0x7f0d0a38;
        public static final int sec_layout_mapname = 0x7f0d0a39;
        public static final int sec_layout_photoer = 0x7f0d0a3a;
        public static final int sec_layout_recorder = 0x7f0d0a3b;
        public static final int sec_layout_sampling = 0x7f0d0a3c;
        public static final int sec_layout_scale = 0x7f0d0a3d;
        public static final int sec_layout_seccode = 0x7f0d0a3e;
        public static final int sec_layout_secname = 0x7f0d0a3f;
        public static final int sec_layout_verify = 0x7f0d0a40;
        public static final int sec_layout_x = 0x7f0d0a41;
        public static final int sec_layout_y = 0x7f0d0a42;
        public static final int sec_length = 0x7f0d0a43;
        public static final int sec_longitude = 0x7f0d0a44;
        public static final int sec_mapcode = 0x7f0d0a45;
        public static final int sec_mapname = 0x7f0d0a46;
        public static final int sec_measure = 0x7f0d0a47;
        public static final int sec_photoer = 0x7f0d0a48;
        public static final int sec_recorder = 0x7f0d0a49;
        public static final int sec_sampling = 0x7f0d0a4a;
        public static final int sec_scale = 0x7f0d0a4b;
        public static final int sec_seccode = 0x7f0d0a4c;
        public static final int sec_secname = 0x7f0d0a4d;
        public static final int sec_section = 0x7f0d0a4e;
        public static final int sec_sta = 0x7f0d0a4f;
        public static final int sec_verify = 0x7f0d0a50;
        public static final int sec_x = 0x7f0d0a51;
        public static final int sec_y = 0x7f0d0a52;
        public static final int seca_code = 0x7f0d0a53;
        public static final int seca_dip = 0x7f0d0a54;
        public static final int seca_dip_ang = 0x7f0d0a55;
        public static final int seca_gbcode = 0x7f0d0a56;
        public static final int seca_laycode = 0x7f0d0a57;
        public static final int seca_layout_code = 0x7f0d0a58;
        public static final int seca_layout_dip = 0x7f0d0a59;
        public static final int seca_layout_dip_ang = 0x7f0d0a5a;
        public static final int seca_layout_laycode = 0x7f0d0a5b;
        public static final int seca_layout_secpoint = 0x7f0d0a5c;
        public static final int seca_layout_slope_l = 0x7f0d0a5d;
        public static final int seca_layout_trend = 0x7f0d0a5e;
        public static final int seca_layout_type = 0x7f0d0a5f;
        public static final int seca_secatt = 0x7f0d0a60;
        public static final int seca_seccode = 0x7f0d0a61;
        public static final int seca_secpoint = 0x7f0d0a62;
        public static final int seca_slope_l = 0x7f0d0a63;
        public static final int seca_trend = 0x7f0d0a64;
        public static final int seca_type = 0x7f0d0a65;
        public static final int section_layout_edit = 0x7f0d0a66;
        public static final int section_layout_info = 0x7f0d0a67;
        public static final int section_layout_update = 0x7f0d0a68;
        public static final int section_prompt = 0x7f0d0a69;
        public static final int select_search_record = 0x7f0d0a6a;
        public static final int sendmessage_title = 0x7f0d0a6b;
        public static final int setting = 0x7f0d0a6c;
        public static final int ske_code = 0x7f0d0a6d;
        public static final int ske_date = 0x7f0d0a6e;
        public static final int ske_describe = 0x7f0d0a6f;
        public static final int ske_geopoint = 0x7f0d0a70;
        public static final int ske_laycode = 0x7f0d0a71;
        public static final int ske_layout_code = 0x7f0d0a72;
        public static final int ske_layout_describe = 0x7f0d0a73;
        public static final int ske_layout_geopoint = 0x7f0d0a74;
        public static final int ske_layout_laycode = 0x7f0d0a75;
        public static final int ske_layout_scale = 0x7f0d0a76;
        public static final int ske_layout_secpoint = 0x7f0d0a77;
        public static final int ske_layout_slope_l = 0x7f0d0a78;
        public static final int ske_layout_title = 0x7f0d0a79;
        public static final int ske_scale = 0x7f0d0a7a;
        public static final int ske_seccode = 0x7f0d0a7b;
        public static final int ske_secpoint = 0x7f0d0a7c;
        public static final int ske_sketch = 0x7f0d0a7d;
        public static final int ske_slope_l = 0x7f0d0a7e;
        public static final int ske_title = 0x7f0d0a7f;
        public static final int sketch_btn_tool = 0x7f0d0a80;
        public static final int sketch_name = 0x7f0d0a81;
        public static final int sketch_no = 0x7f0d0a82;
        public static final int sketch_rcode = 0x7f0d0a83;
        public static final int sketch_strech = 0x7f0d0a84;
        public static final int speaking = 0x7f0d0a85;
        public static final int start = 0x7f0d0a86;
        public static final int startup_dot_desc = 0x7f0d0a87;
        public static final int startup_dot_label = 0x7f0d0a88;
        public static final int startup_gps_button = 0x7f0d0a89;
        public static final int startup_gps_desc = 0x7f0d0a8a;
        public static final int startup_gps_dx = 0x7f0d0a8b;
        public static final int startup_gps_dy = 0x7f0d0a8c;
        public static final int startup_gps_title = 0x7f0d0a8d;
        public static final int startup_line_desc = 0x7f0d0a8e;
        public static final int startup_line_label = 0x7f0d0a8f;
        public static final int startup_logo_desc = 0x7f0d0a90;
        public static final int startup_sampTime = 0x7f0d0a91;
        public static final int startup_title = 0x7f0d0a92;
        public static final int startup_welcome = 0x7f0d0a93;
        public static final int state_tv_connect = 0x7f0d0a94;
        public static final int state_tv_connect_failed = 0x7f0d0a95;
        public static final int state_tv_connect_success = 0x7f0d0a96;
        public static final int state_tv_disable = 0x7f0d0a97;
        public static final int state_tv_disconnect = 0x7f0d0a98;
        public static final int state_tv_enable = 0x7f0d0a99;
        public static final int state_tv_scan = 0x7f0d0a9a;
        public static final int state_tv_scan_over = 0x7f0d0a9b;
        public static final int status_bar_notification_info_overflow = 0x7f0d0a9c;
        public static final int sur_azimuth = 0x7f0d0a9d;
        public static final int sur_from = 0x7f0d0a9e;
        public static final int sur_grade = 0x7f0d0a9f;
        public static final int sur_high = 0x7f0d0aa0;
        public static final int sur_layout_azimuth = 0x7f0d0aa1;
        public static final int sur_layout_fromX = 0x7f0d0aa2;
        public static final int sur_layout_fromY = 0x7f0d0aa3;
        public static final int sur_layout_fromZ = 0x7f0d0aa4;
        public static final int sur_layout_grade = 0x7f0d0aa5;
        public static final int sur_layout_secpoint = 0x7f0d0aa6;
        public static final int sur_layout_slope_l = 0x7f0d0aa7;
        public static final int sur_layout_toX = 0x7f0d0aa8;
        public static final int sur_layout_toY = 0x7f0d0aa9;
        public static final int sur_layout_toZ = 0x7f0d0aaa;
        public static final int sur_seccode = 0x7f0d0aab;
        public static final int sur_secpoint = 0x7f0d0aac;
        public static final int sur_slope_l = 0x7f0d0aad;
        public static final int sur_survey = 0x7f0d0aae;
        public static final int sur_to = 0x7f0d0aaf;
        public static final int sur_total_high = 0x7f0d0ab0;
        public static final int sur_x = 0x7f0d0ab1;
        public static final int sur_y = 0x7f0d0ab2;
        public static final int sur_z = 0x7f0d0ab3;
        public static final int switch_bt_close = 0x7f0d0ab4;
        public static final int switch_bt_open = 0x7f0d0ab5;
        public static final int tool_func_backpoint = 0x7f0d0ab6;
        public static final int tool_layout_add = 0x7f0d0ab7;
        public static final int tool_layout_att = 0x7f0d0ab8;
        public static final int tool_layout_btadd = 0x7f0d0ab9;
        public static final int tool_layout_btdel = 0x7f0d0aba;
        public static final int tool_layout_btedit = 0x7f0d0abb;
        public static final int tool_layout_btinsert = 0x7f0d0abc;
        public static final int tool_layout_btlook = 0x7f0d0abd;
        public static final int tool_layout_btsave = 0x7f0d0abe;
        public static final int tool_layout_bttable = 0x7f0d0abf;
        public static final int tool_layout_cancel = 0x7f0d0ac0;
        public static final int tool_layout_del = 0x7f0d0ac1;
        public static final int tool_layout_new = 0x7f0d0ac2;
        public static final int tool_layout_ok = 0x7f0d0ac3;
        public static final int tool_layout_par = 0x7f0d0ac4;
        public static final int tool_layout_return = 0x7f0d0ac5;
        public static final int tool_layout_update = 0x7f0d0ac6;
        public static final int vad_default = 0x7f0d0ac7;
        public static final int vad_timeout_default = 0x7f0d0ac8;
        public static final int xcoord = 0x7f0d0ac9;
        public static final int ycoord = 0x7f0d0aca;
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 0x7f0e0000;
        public static final int AlertDialog_AppCompat_Light = 0x7f0e0001;
        public static final int Animation_AppCompat_Dialog = 0x7f0e0002;
        public static final int Animation_AppCompat_DropDownUp = 0x7f0e0003;
        public static final int AppTheme = 0x7f0e0004;
        public static final int Base_AlertDialog_AppCompat = 0x7f0e0005;
        public static final int Base_AlertDialog_AppCompat_Light = 0x7f0e0006;
        public static final int Base_Animation_AppCompat_Dialog = 0x7f0e0007;
        public static final int Base_Animation_AppCompat_DropDownUp = 0x7f0e0008;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 0x7f0e000a;
        public static final int Base_DialogWindowTitle_AppCompat = 0x7f0e0009;
        public static final int Base_TextAppearance_AppCompat = 0x7f0e000b;
        public static final int Base_TextAppearance_AppCompat_Body1 = 0x7f0e000c;
        public static final int Base_TextAppearance_AppCompat_Body2 = 0x7f0e000d;
        public static final int Base_TextAppearance_AppCompat_Button = 0x7f0e000e;
        public static final int Base_TextAppearance_AppCompat_Caption = 0x7f0e000f;
        public static final int Base_TextAppearance_AppCompat_Display1 = 0x7f0e0010;
        public static final int Base_TextAppearance_AppCompat_Display2 = 0x7f0e0011;
        public static final int Base_TextAppearance_AppCompat_Display3 = 0x7f0e0012;
        public static final int Base_TextAppearance_AppCompat_Display4 = 0x7f0e0013;
        public static final int Base_TextAppearance_AppCompat_Headline = 0x7f0e0014;
        public static final int Base_TextAppearance_AppCompat_Inverse = 0x7f0e0015;
        public static final int Base_TextAppearance_AppCompat_Large = 0x7f0e0016;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f0e0017;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0e0018;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0e0019;
        public static final int Base_TextAppearance_AppCompat_Medium = 0x7f0e001a;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f0e001b;
        public static final int Base_TextAppearance_AppCompat_Menu = 0x7f0e001c;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 0x7f0e001d;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0e001e;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f0e001f;
        public static final int Base_TextAppearance_AppCompat_Small = 0x7f0e0020;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f0e0021;
        public static final int Base_TextAppearance_AppCompat_Subhead = 0x7f0e0022;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f0e0023;
        public static final int Base_TextAppearance_AppCompat_Title = 0x7f0e0024;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f0e0025;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0e0026;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0e0027;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0e0028;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0e0029;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0e002a;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0e002b;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0e002c;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 0x7f0e002d;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0e002e;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0e002f;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0e0030;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0e0031;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f0e0032;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0e0033;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0e0034;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0e0035;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0e0036;
        public static final int Base_ThemeOverlay_AppCompat = 0x7f0e0045;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f0e0046;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 0x7f0e0047;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0e0048;
        public static final int Base_ThemeOverlay_AppCompat_Light = 0x7f0e0049;
        public static final int Base_Theme_AppCompat = 0x7f0e0037;
        public static final int Base_Theme_AppCompat_CompactMenu = 0x7f0e0038;
        public static final int Base_Theme_AppCompat_Dialog = 0x7f0e0039;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 0x7f0e003d;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 0x7f0e003a;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f0e003b;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f0e003c;
        public static final int Base_Theme_AppCompat_Light = 0x7f0e003e;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f0e003f;
        public static final int Base_Theme_AppCompat_Light_Dialog = 0x7f0e0040;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f0e0044;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f0e0041;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f0e0042;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0e0043;
        public static final int Base_V11_Theme_AppCompat_Dialog = 0x7f0e004a;
        public static final int Base_V11_Theme_AppCompat_Light_Dialog = 0x7f0e004b;
        public static final int Base_V12_Widget_AppCompat_AutoCompleteTextView = 0x7f0e004c;
        public static final int Base_V12_Widget_AppCompat_EditText = 0x7f0e004d;
        public static final int Base_V21_Theme_AppCompat = 0x7f0e004e;
        public static final int Base_V21_Theme_AppCompat_Dialog = 0x7f0e004f;
        public static final int Base_V21_Theme_AppCompat_Light = 0x7f0e0050;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f0e0051;
        public static final int Base_V22_Theme_AppCompat = 0x7f0e0052;
        public static final int Base_V22_Theme_AppCompat_Light = 0x7f0e0053;
        public static final int Base_V23_Theme_AppCompat = 0x7f0e0054;
        public static final int Base_V23_Theme_AppCompat_Light = 0x7f0e0055;
        public static final int Base_V7_Theme_AppCompat = 0x7f0e0056;
        public static final int Base_V7_Theme_AppCompat_Dialog = 0x7f0e0057;
        public static final int Base_V7_Theme_AppCompat_Light = 0x7f0e0058;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f0e0059;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f0e005a;
        public static final int Base_V7_Widget_AppCompat_EditText = 0x7f0e005b;
        public static final int Base_Widget_AppCompat_ActionBar = 0x7f0e005c;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 0x7f0e005d;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f0e005e;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 0x7f0e005f;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 0x7f0e0060;
        public static final int Base_Widget_AppCompat_ActionButton = 0x7f0e0061;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f0e0062;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f0e0063;
        public static final int Base_Widget_AppCompat_ActionMode = 0x7f0e0064;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 0x7f0e0065;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f0e0066;
        public static final int Base_Widget_AppCompat_Button = 0x7f0e0067;
        public static final int Base_Widget_AppCompat_ButtonBar = 0x7f0e006d;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0e006e;
        public static final int Base_Widget_AppCompat_Button_Borderless = 0x7f0e0068;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f0e0069;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0e006a;
        public static final int Base_Widget_AppCompat_Button_Colored = 0x7f0e006b;
        public static final int Base_Widget_AppCompat_Button_Small = 0x7f0e006c;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f0e006f;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f0e0070;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f0e0071;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f0e0072;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f0e0073;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f0e0074;
        public static final int Base_Widget_AppCompat_EditText = 0x7f0e0075;
        public static final int Base_Widget_AppCompat_ImageButton = 0x7f0e0076;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 0x7f0e0077;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f0e0078;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0e0079;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f0e007a;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0e007b;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f0e007c;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 0x7f0e007d;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0e007e;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 0x7f0e007f;
        public static final int Base_Widget_AppCompat_ListView = 0x7f0e0080;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 0x7f0e0081;
        public static final int Base_Widget_AppCompat_ListView_Menu = 0x7f0e0082;
        public static final int Base_Widget_AppCompat_PopupMenu = 0x7f0e0083;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f0e0084;
        public static final int Base_Widget_AppCompat_PopupWindow = 0x7f0e0085;
        public static final int Base_Widget_AppCompat_ProgressBar = 0x7f0e0086;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f0e0087;
        public static final int Base_Widget_AppCompat_RatingBar = 0x7f0e0088;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f0e0089;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 0x7f0e008a;
        public static final int Base_Widget_AppCompat_SearchView = 0x7f0e008b;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f0e008c;
        public static final int Base_Widget_AppCompat_SeekBar = 0x7f0e008d;
        public static final int Base_Widget_AppCompat_Spinner = 0x7f0e008e;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 0x7f0e008f;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f0e0090;
        public static final int Base_Widget_AppCompat_Toolbar = 0x7f0e0091;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0e0092;
        public static final int Platform_AppCompat = 0x7f0e0093;
        public static final int Platform_AppCompat_Light = 0x7f0e0094;
        public static final int Platform_ThemeOverlay_AppCompat = 0x7f0e0095;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 0x7f0e0096;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 0x7f0e0097;
        public static final int Platform_V11_AppCompat = 0x7f0e0098;
        public static final int Platform_V11_AppCompat_Light = 0x7f0e0099;
        public static final int Platform_V14_AppCompat = 0x7f0e009a;
        public static final int Platform_V14_AppCompat_Light = 0x7f0e009b;
        public static final int Platform_Widget_AppCompat_Spinner = 0x7f0e009c;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f0e009d;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f0e009e;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f0e009f;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f0e00a0;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f0e00a1;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f0e00a2;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f0e00a8;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f0e00a3;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f0e00a4;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f0e00a5;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f0e00a6;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f0e00a7;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f0e00a9;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f0e00aa;
        public static final int TextAppearance_AppCompat = 0x7f0e00ab;
        public static final int TextAppearance_AppCompat_Body1 = 0x7f0e00ac;
        public static final int TextAppearance_AppCompat_Body2 = 0x7f0e00ad;
        public static final int TextAppearance_AppCompat_Button = 0x7f0e00ae;
        public static final int TextAppearance_AppCompat_Caption = 0x7f0e00af;
        public static final int TextAppearance_AppCompat_Display1 = 0x7f0e00b0;
        public static final int TextAppearance_AppCompat_Display2 = 0x7f0e00b1;
        public static final int TextAppearance_AppCompat_Display3 = 0x7f0e00b2;
        public static final int TextAppearance_AppCompat_Display4 = 0x7f0e00b3;
        public static final int TextAppearance_AppCompat_Headline = 0x7f0e00b4;
        public static final int TextAppearance_AppCompat_Inverse = 0x7f0e00b5;
        public static final int TextAppearance_AppCompat_Large = 0x7f0e00b6;
        public static final int TextAppearance_AppCompat_Large_Inverse = 0x7f0e00b7;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f0e00b8;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f0e00b9;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f0e00ba;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f0e00bb;
        public static final int TextAppearance_AppCompat_Medium = 0x7f0e00bc;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 0x7f0e00bd;
        public static final int TextAppearance_AppCompat_Menu = 0x7f0e00be;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f0e00bf;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 0x7f0e00c0;
        public static final int TextAppearance_AppCompat_Small = 0x7f0e00c1;
        public static final int TextAppearance_AppCompat_Small_Inverse = 0x7f0e00c2;
        public static final int TextAppearance_AppCompat_Subhead = 0x7f0e00c3;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 0x7f0e00c4;
        public static final int TextAppearance_AppCompat_Title = 0x7f0e00c5;
        public static final int TextAppearance_AppCompat_Title_Inverse = 0x7f0e00c6;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f0e00c7;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f0e00c8;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f0e00c9;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f0e00ca;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f0e00cb;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f0e00cc;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f0e00cd;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f0e00ce;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f0e00cf;
        public static final int TextAppearance_AppCompat_Widget_Button = 0x7f0e00d0;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f0e00d1;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f0e00d2;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f0e00d3;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f0e00d4;
        public static final int TextAppearance_AppCompat_Widget_Switch = 0x7f0e00d5;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f0e00d6;
        public static final int TextAppearance_StatusBar_EventContent = 0x7f0e00d7;
        public static final int TextAppearance_StatusBar_EventContent_Info = 0x7f0e00d8;
        public static final int TextAppearance_StatusBar_EventContent_Line2 = 0x7f0e00d9;
        public static final int TextAppearance_StatusBar_EventContent_Time = 0x7f0e00da;
        public static final int TextAppearance_StatusBar_EventContent_Title = 0x7f0e00db;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f0e00dc;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f0e00dd;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f0e00de;
        public static final int ThemeOverlay_AppCompat = 0x7f0e00f4;
        public static final int ThemeOverlay_AppCompat_ActionBar = 0x7f0e00f5;
        public static final int ThemeOverlay_AppCompat_Dark = 0x7f0e00f6;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f0e00f7;
        public static final int ThemeOverlay_AppCompat_Light = 0x7f0e00f8;
        public static final int Theme_AppCompat = 0x7f0e00df;
        public static final int Theme_AppCompat_CompactMenu = 0x7f0e00e0;
        public static final int Theme_AppCompat_DayNight = 0x7f0e00e1;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 0x7f0e00e2;
        public static final int Theme_AppCompat_DayNight_Dialog = 0x7f0e00e3;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f0e00e6;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f0e00e4;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f0e00e5;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 0x7f0e00e7;
        public static final int Theme_AppCompat_Dialog = 0x7f0e00e8;
        public static final int Theme_AppCompat_DialogWhenLarge = 0x7f0e00eb;
        public static final int Theme_AppCompat_Dialog_Alert = 0x7f0e00e9;
        public static final int Theme_AppCompat_Dialog_MinWidth = 0x7f0e00ea;
        public static final int Theme_AppCompat_Light = 0x7f0e00ec;
        public static final int Theme_AppCompat_Light_DarkActionBar = 0x7f0e00ed;
        public static final int Theme_AppCompat_Light_Dialog = 0x7f0e00ee;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 0x7f0e00f1;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 0x7f0e00ef;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f0e00f0;
        public static final int Theme_AppCompat_Light_NoActionBar = 0x7f0e00f2;
        public static final int Theme_AppCompat_NoActionBar = 0x7f0e00f3;
        public static final int Widget_AppCompat_ActionBar = 0x7f0e00f9;
        public static final int Widget_AppCompat_ActionBar_Solid = 0x7f0e00fa;
        public static final int Widget_AppCompat_ActionBar_TabBar = 0x7f0e00fb;
        public static final int Widget_AppCompat_ActionBar_TabText = 0x7f0e00fc;
        public static final int Widget_AppCompat_ActionBar_TabView = 0x7f0e00fd;
        public static final int Widget_AppCompat_ActionButton = 0x7f0e00fe;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 0x7f0e00ff;
        public static final int Widget_AppCompat_ActionButton_Overflow = 0x7f0e0100;
        public static final int Widget_AppCompat_ActionMode = 0x7f0e0101;
        public static final int Widget_AppCompat_ActivityChooserView = 0x7f0e0102;
        public static final int Widget_AppCompat_AutoCompleteTextView = 0x7f0e0103;
        public static final int Widget_AppCompat_Button = 0x7f0e0104;
        public static final int Widget_AppCompat_ButtonBar = 0x7f0e010a;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f0e010b;
        public static final int Widget_AppCompat_Button_Borderless = 0x7f0e0105;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 0x7f0e0106;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f0e0107;
        public static final int Widget_AppCompat_Button_Colored = 0x7f0e0108;
        public static final int Widget_AppCompat_Button_Small = 0x7f0e0109;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 0x7f0e010c;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 0x7f0e010d;
        public static final int Widget_AppCompat_CompoundButton_Switch = 0x7f0e010e;
        public static final int Widget_AppCompat_DrawerArrowToggle = 0x7f0e010f;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 0x7f0e0110;
        public static final int Widget_AppCompat_EditText = 0x7f0e0111;
        public static final int Widget_AppCompat_ImageButton = 0x7f0e0112;
        public static final int Widget_AppCompat_Light_ActionBar = 0x7f0e0113;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 0x7f0e0114;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f0e0115;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f0e0116;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f0e0117;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 0x7f0e0118;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f0e0119;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 0x7f0e011a;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f0e011b;
        public static final int Widget_AppCompat_Light_ActionButton = 0x7f0e011c;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f0e011d;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f0e011e;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f0e011f;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 0x7f0e0120;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f0e0121;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f0e0122;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 0x7f0e0123;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 0x7f0e0124;
        public static final int Widget_AppCompat_Light_PopupMenu = 0x7f0e0125;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f0e0126;
        public static final int Widget_AppCompat_Light_SearchView = 0x7f0e0127;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f0e0128;
        public static final int Widget_AppCompat_ListPopupWindow = 0x7f0e0129;
        public static final int Widget_AppCompat_ListView = 0x7f0e012a;
        public static final int Widget_AppCompat_ListView_DropDown = 0x7f0e012b;
        public static final int Widget_AppCompat_ListView_Menu = 0x7f0e012c;
        public static final int Widget_AppCompat_PopupMenu = 0x7f0e012d;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 0x7f0e012e;
        public static final int Widget_AppCompat_PopupWindow = 0x7f0e012f;
        public static final int Widget_AppCompat_ProgressBar = 0x7f0e0130;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 0x7f0e0131;
        public static final int Widget_AppCompat_RatingBar = 0x7f0e0132;
        public static final int Widget_AppCompat_RatingBar_Indicator = 0x7f0e0133;
        public static final int Widget_AppCompat_RatingBar_Small = 0x7f0e0134;
        public static final int Widget_AppCompat_SearchView = 0x7f0e0135;
        public static final int Widget_AppCompat_SearchView_ActionBar = 0x7f0e0136;
        public static final int Widget_AppCompat_SeekBar = 0x7f0e0137;
        public static final int Widget_AppCompat_Spinner = 0x7f0e0138;
        public static final int Widget_AppCompat_Spinner_DropDown = 0x7f0e0139;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f0e013a;
        public static final int Widget_AppCompat_Spinner_Underlined = 0x7f0e013b;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 0x7f0e013c;
        public static final int Widget_AppCompat_Toolbar = 0x7f0e013d;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f0e013e;
        public static final int ao_AppTheme = 0x7f0e013f;
        public static final int ao_input_btn_item = 0x7f0e0140;
        public static final int ao_input_group = 0x7f0e0141;
        public static final int ao_input_group_desc = 0x7f0e0142;
        public static final int ao_input_group_title = 0x7f0e0143;
        public static final int ao_input_item = 0x7f0e0144;
        public static final int ao_input_item_btn = 0x7f0e0145;
        public static final int ao_input_item_btns = 0x7f0e0146;
        public static final int ao_input_item_content = 0x7f0e0147;
        public static final int ao_input_item_desc = 0x7f0e0148;
        public static final int ao_input_item_editLine = 0x7f0e0149;
        public static final int ao_input_item_editbox = 0x7f0e014a;
        public static final int ao_input_item_label = 0x7f0e014b;
        public static final int ao_input_item_seperator = 0x7f0e014c;
        public static final int ao_input_item_value = 0x7f0e014d;
        public static final int ao_popup_theme = 0x7f0e014e;
        public static final int bottom_menu_style = 0x7f0e014f;
        public static final int popup_theme = 0x7f0e0150;
    }

    public static final class styleable {
        public static final int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static final int ActionBar_background = 0x00000000;
        public static final int ActionBar_backgroundSplit = 0x00000001;
        public static final int ActionBar_backgroundStacked = 0x00000002;
        public static final int ActionBar_contentInsetEnd = 0x00000003;
        public static final int ActionBar_contentInsetLeft = 0x00000004;
        public static final int ActionBar_contentInsetRight = 0x00000005;
        public static final int ActionBar_contentInsetStart = 0x00000006;
        public static final int ActionBar_customNavigationLayout = 0x00000007;
        public static final int ActionBar_displayOptions = 0x00000008;
        public static final int ActionBar_divider = 0x00000009;
        public static final int ActionBar_elevation = 0x0000000a;
        public static final int ActionBar_height = 0x0000000b;
        public static final int ActionBar_hideOnContentScroll = 0x0000000c;
        public static final int ActionBar_homeAsUpIndicator = 0x0000000d;
        public static final int ActionBar_homeLayout = 0x0000000e;
        public static final int ActionBar_icon = 0x0000000f;
        public static final int ActionBar_indeterminateProgressStyle = 0x00000010;
        public static final int ActionBar_itemPadding = 0x00000011;
        public static final int ActionBar_logo = 0x00000012;
        public static final int ActionBar_navigationMode = 0x00000013;
        public static final int ActionBar_popupTheme = 0x00000014;
        public static final int ActionBar_progressBarPadding = 0x00000015;
        public static final int ActionBar_progressBarStyle = 0x00000016;
        public static final int ActionBar_subtitle = 0x00000017;
        public static final int ActionBar_subtitleTextStyle = 0x00000018;
        public static final int ActionBar_title = 0x00000019;
        public static final int ActionBar_titleTextStyle = 0x0000001a;
        public static final int ActionMenuItemView_android_minWidth = 0x00000000;
        public static final int ActionMode_background = 0x00000000;
        public static final int ActionMode_backgroundSplit = 0x00000001;
        public static final int ActionMode_closeItemLayout = 0x00000002;
        public static final int ActionMode_height = 0x00000003;
        public static final int ActionMode_subtitleTextStyle = 0x00000004;
        public static final int ActionMode_titleTextStyle = 0x00000005;
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static final int ActivityChooserView_initialActivityCount = 0x00000001;
        public static final int AlertDialog_android_layout = 0x00000000;
        public static final int AlertDialog_buttonPanelSideLayout = 0x00000001;
        public static final int AlertDialog_listItemLayout = 0x00000002;
        public static final int AlertDialog_listLayout = 0x00000003;
        public static final int AlertDialog_multiChoiceItemLayout = 0x00000004;
        public static final int AlertDialog_singleChoiceItemLayout = 0x00000005;
        public static final int AppCompatImageView_android_src = 0x00000000;
        public static final int AppCompatImageView_srcCompat = 0x00000001;
        public static final int AppCompatTextView_android_textAppearance = 0x00000000;
        public static final int AppCompatTextView_textAllCaps = 0x00000001;
        public static final int AppCompatTheme_actionBarDivider = 0x00000002;
        public static final int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static final int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static final int AppCompatTheme_actionBarSize = 0x00000005;
        public static final int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static final int AppCompatTheme_actionBarStyle = 0x00000007;
        public static final int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static final int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static final int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static final int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static final int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static final int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static final int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static final int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static final int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static final int AppCompatTheme_actionModeBackground = 0x00000011;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static final int AppCompatTheme_actionModeCloseDrawable = 0x00000013;
        public static final int AppCompatTheme_actionModeCopyDrawable = 0x00000014;
        public static final int AppCompatTheme_actionModeCutDrawable = 0x00000015;
        public static final int AppCompatTheme_actionModeFindDrawable = 0x00000016;
        public static final int AppCompatTheme_actionModePasteDrawable = 0x00000017;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 0x00000018;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 0x00000019;
        public static final int AppCompatTheme_actionModeShareDrawable = 0x0000001a;
        public static final int AppCompatTheme_actionModeSplitBackground = 0x0000001b;
        public static final int AppCompatTheme_actionModeStyle = 0x0000001c;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001d;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 0x0000001e;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 0x0000001f;
        public static final int AppCompatTheme_activityChooserViewStyle = 0x00000020;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000021;
        public static final int AppCompatTheme_alertDialogCenterButtons = 0x00000022;
        public static final int AppCompatTheme_alertDialogStyle = 0x00000023;
        public static final int AppCompatTheme_alertDialogTheme = 0x00000024;
        public static final int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static final int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 0x00000025;
        public static final int AppCompatTheme_borderlessButtonStyle = 0x00000026;
        public static final int AppCompatTheme_buttonBarButtonStyle = 0x00000027;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 0x00000028;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 0x00000029;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002a;
        public static final int AppCompatTheme_buttonBarStyle = 0x0000002b;
        public static final int AppCompatTheme_buttonStyle = 0x0000002c;
        public static final int AppCompatTheme_buttonStyleSmall = 0x0000002d;
        public static final int AppCompatTheme_checkboxStyle = 0x0000002e;
        public static final int AppCompatTheme_checkedTextViewStyle = 0x0000002f;
        public static final int AppCompatTheme_colorAccent = 0x00000030;
        public static final int AppCompatTheme_colorButtonNormal = 0x00000031;
        public static final int AppCompatTheme_colorControlActivated = 0x00000032;
        public static final int AppCompatTheme_colorControlHighlight = 0x00000033;
        public static final int AppCompatTheme_colorControlNormal = 0x00000034;
        public static final int AppCompatTheme_colorPrimary = 0x00000035;
        public static final int AppCompatTheme_colorPrimaryDark = 0x00000036;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 0x00000037;
        public static final int AppCompatTheme_controlBackground = 0x00000038;
        public static final int AppCompatTheme_dialogPreferredPadding = 0x00000039;
        public static final int AppCompatTheme_dialogTheme = 0x0000003a;
        public static final int AppCompatTheme_dividerHorizontal = 0x0000003b;
        public static final int AppCompatTheme_dividerVertical = 0x0000003c;
        public static final int AppCompatTheme_dropDownListViewStyle = 0x0000003d;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 0x0000003e;
        public static final int AppCompatTheme_editTextBackground = 0x0000003f;
        public static final int AppCompatTheme_editTextColor = 0x00000040;
        public static final int AppCompatTheme_editTextStyle = 0x00000041;
        public static final int AppCompatTheme_homeAsUpIndicator = 0x00000042;
        public static final int AppCompatTheme_imageButtonStyle = 0x00000043;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000044;
        public static final int AppCompatTheme_listDividerAlertDialog = 0x00000045;
        public static final int AppCompatTheme_listPopupWindowStyle = 0x00000046;
        public static final int AppCompatTheme_listPreferredItemHeight = 0x00000047;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 0x00000048;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 0x00000049;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 0x0000004a;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 0x0000004b;
        public static final int AppCompatTheme_panelBackground = 0x0000004c;
        public static final int AppCompatTheme_panelMenuListTheme = 0x0000004d;
        public static final int AppCompatTheme_panelMenuListWidth = 0x0000004e;
        public static final int AppCompatTheme_popupMenuStyle = 0x0000004f;
        public static final int AppCompatTheme_popupWindowStyle = 0x00000050;
        public static final int AppCompatTheme_radioButtonStyle = 0x00000051;
        public static final int AppCompatTheme_ratingBarStyle = 0x00000052;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 0x00000053;
        public static final int AppCompatTheme_ratingBarStyleSmall = 0x00000054;
        public static final int AppCompatTheme_searchViewStyle = 0x00000055;
        public static final int AppCompatTheme_seekBarStyle = 0x00000056;
        public static final int AppCompatTheme_selectableItemBackground = 0x00000057;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000058;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 0x00000059;
        public static final int AppCompatTheme_spinnerStyle = 0x0000005a;
        public static final int AppCompatTheme_switchStyle = 0x0000005b;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 0x0000005c;
        public static final int AppCompatTheme_textAppearanceListItem = 0x0000005d;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 0x0000005e;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x0000005f;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 0x00000060;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 0x00000061;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 0x00000062;
        public static final int AppCompatTheme_textColorSearchUrl = 0x00000063;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 0x00000064;
        public static final int AppCompatTheme_toolbarStyle = 0x00000065;
        public static final int AppCompatTheme_windowActionBar = 0x00000066;
        public static final int AppCompatTheme_windowActionBarOverlay = 0x00000067;
        public static final int AppCompatTheme_windowActionModeOverlay = 0x00000068;
        public static final int AppCompatTheme_windowFixedHeightMajor = 0x00000069;
        public static final int AppCompatTheme_windowFixedHeightMinor = 0x0000006a;
        public static final int AppCompatTheme_windowFixedWidthMajor = 0x0000006b;
        public static final int AppCompatTheme_windowFixedWidthMinor = 0x0000006c;
        public static final int AppCompatTheme_windowMinWidthMajor = 0x0000006d;
        public static final int AppCompatTheme_windowMinWidthMinor = 0x0000006e;
        public static final int AppCompatTheme_windowNoTitle = 0x0000006f;
        public static final int ArcMenu_position = 0x00000000;
        public static final int ArcMenu_radius = 0x00000001;
        public static final int ButtonBarLayout_allowStacking = 0x00000000;
        public static final int CompoundButton_android_button = 0x00000000;
        public static final int CompoundButton_buttonTint = 0x00000001;
        public static final int CompoundButton_buttonTintMode = 0x00000002;
        public static final int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static final int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static final int DrawerArrowToggle_barLength = 0x00000002;
        public static final int DrawerArrowToggle_color = 0x00000003;
        public static final int DrawerArrowToggle_drawableSize = 0x00000004;
        public static final int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static final int DrawerArrowToggle_spinBars = 0x00000006;
        public static final int DrawerArrowToggle_thickness = 0x00000007;
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static final int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static final int LinearLayoutCompat_android_gravity = 0x00000000;
        public static final int LinearLayoutCompat_android_orientation = 0x00000001;
        public static final int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static final int LinearLayoutCompat_divider = 0x00000005;
        public static final int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static final int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static final int LinearLayoutCompat_showDividers = 0x00000008;
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static final int MenuGroup_android_checkableBehavior = 0x00000005;
        public static final int MenuGroup_android_enabled = 0x00000000;
        public static final int MenuGroup_android_id = 0x00000001;
        public static final int MenuGroup_android_menuCategory = 0x00000003;
        public static final int MenuGroup_android_orderInCategory = 0x00000004;
        public static final int MenuGroup_android_visible = 0x00000002;
        public static final int MenuItem_actionLayout = 0x0000000d;
        public static final int MenuItem_actionProviderClass = 0x0000000e;
        public static final int MenuItem_actionViewClass = 0x0000000f;
        public static final int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static final int MenuItem_android_checkable = 0x0000000b;
        public static final int MenuItem_android_checked = 0x00000003;
        public static final int MenuItem_android_enabled = 0x00000001;
        public static final int MenuItem_android_icon = 0x00000000;
        public static final int MenuItem_android_id = 0x00000002;
        public static final int MenuItem_android_menuCategory = 0x00000005;
        public static final int MenuItem_android_numericShortcut = 0x0000000a;
        public static final int MenuItem_android_onClick = 0x0000000c;
        public static final int MenuItem_android_orderInCategory = 0x00000006;
        public static final int MenuItem_android_title = 0x00000007;
        public static final int MenuItem_android_titleCondensed = 0x00000008;
        public static final int MenuItem_android_visible = 0x00000004;
        public static final int MenuItem_showAsAction = 0x00000010;
        public static final int MenuView_android_headerBackground = 0x00000004;
        public static final int MenuView_android_horizontalDivider = 0x00000002;
        public static final int MenuView_android_itemBackground = 0x00000005;
        public static final int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static final int MenuView_android_itemTextAppearance = 0x00000001;
        public static final int MenuView_android_verticalDivider = 0x00000003;
        public static final int MenuView_android_windowAnimationStyle = 0x00000000;
        public static final int MenuView_preserveIconSpacing = 0x00000007;
        public static final int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static final int PopupWindow_android_popupBackground = 0x00000000;
        public static final int PopupWindow_overlapAnchor = 0x00000001;
        public static final int SearchView_android_focusable = 0x00000000;
        public static final int SearchView_android_imeOptions = 0x00000003;
        public static final int SearchView_android_inputType = 0x00000002;
        public static final int SearchView_android_maxWidth = 0x00000001;
        public static final int SearchView_closeIcon = 0x00000004;
        public static final int SearchView_commitIcon = 0x00000005;
        public static final int SearchView_defaultQueryHint = 0x00000006;
        public static final int SearchView_goIcon = 0x00000007;
        public static final int SearchView_iconifiedByDefault = 0x00000008;
        public static final int SearchView_layout = 0x00000009;
        public static final int SearchView_queryBackground = 0x0000000a;
        public static final int SearchView_queryHint = 0x0000000b;
        public static final int SearchView_searchHintIcon = 0x0000000c;
        public static final int SearchView_searchIcon = 0x0000000d;
        public static final int SearchView_submitBackground = 0x0000000e;
        public static final int SearchView_suggestionRowLayout = 0x0000000f;
        public static final int SearchView_voiceIcon = 0x00000010;
        public static final int Spinner_android_dropDownWidth = 0x00000003;
        public static final int Spinner_android_entries = 0x00000000;
        public static final int Spinner_android_popupBackground = 0x00000001;
        public static final int Spinner_android_prompt = 0x00000002;
        public static final int Spinner_popupTheme = 0x00000004;
        public static final int SwitchCompat_android_textOff = 0x00000001;
        public static final int SwitchCompat_android_textOn = 0x00000000;
        public static final int SwitchCompat_android_thumb = 0x00000002;
        public static final int SwitchCompat_showText = 0x00000003;
        public static final int SwitchCompat_splitTrack = 0x00000004;
        public static final int SwitchCompat_switchMinWidth = 0x00000005;
        public static final int SwitchCompat_switchPadding = 0x00000006;
        public static final int SwitchCompat_switchTextAppearance = 0x00000007;
        public static final int SwitchCompat_thumbTextPadding = 0x00000008;
        public static final int SwitchCompat_track = 0x00000009;
        public static final int TextAppearance_android_shadowColor = 0x00000004;
        public static final int TextAppearance_android_shadowDx = 0x00000005;
        public static final int TextAppearance_android_shadowDy = 0x00000006;
        public static final int TextAppearance_android_shadowRadius = 0x00000007;
        public static final int TextAppearance_android_textColor = 0x00000003;
        public static final int TextAppearance_android_textSize = 0x00000000;
        public static final int TextAppearance_android_textStyle = 0x00000002;
        public static final int TextAppearance_android_typeface = 0x00000001;
        public static final int TextAppearance_textAllCaps = 0x00000008;
        public static final int Toolbar_android_gravity = 0x00000000;
        public static final int Toolbar_android_minHeight = 0x00000001;
        public static final int Toolbar_collapseContentDescription = 0x00000002;
        public static final int Toolbar_collapseIcon = 0x00000003;
        public static final int Toolbar_contentInsetEnd = 0x00000004;
        public static final int Toolbar_contentInsetLeft = 0x00000005;
        public static final int Toolbar_contentInsetRight = 0x00000006;
        public static final int Toolbar_contentInsetStart = 0x00000007;
        public static final int Toolbar_logo = 0x00000008;
        public static final int Toolbar_logoDescription = 0x00000009;
        public static final int Toolbar_maxButtonHeight = 0x0000000a;
        public static final int Toolbar_navigationContentDescription = 0x0000000b;
        public static final int Toolbar_navigationIcon = 0x0000000c;
        public static final int Toolbar_popupTheme = 0x0000000d;
        public static final int Toolbar_subtitle = 0x0000000e;
        public static final int Toolbar_subtitleTextAppearance = 0x0000000f;
        public static final int Toolbar_subtitleTextColor = 0x00000010;
        public static final int Toolbar_title = 0x00000011;
        public static final int Toolbar_titleMarginBottom = 0x00000012;
        public static final int Toolbar_titleMarginEnd = 0x00000013;
        public static final int Toolbar_titleMarginStart = 0x00000014;
        public static final int Toolbar_titleMarginTop = 0x00000015;
        public static final int Toolbar_titleMargins = 0x00000016;
        public static final int Toolbar_titleTextAppearance = 0x00000017;
        public static final int Toolbar_titleTextColor = 0x00000018;
        public static final int ViewBackgroundHelper_android_background = 0x00000000;
        public static final int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static final int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static final int ViewStubCompat_android_id = 0x00000000;
        public static final int ViewStubCompat_android_inflatedId = 0x00000002;
        public static final int ViewStubCompat_android_layout = 0x00000001;
        public static final int View_android_focusable = 0x00000001;
        public static final int View_android_theme = 0x00000000;
        public static final int View_paddingEnd = 0x00000002;
        public static final int View_paddingStart = 0x00000003;
        public static final int View_theme = 0x00000004;
        public static final int ao_ScrollScreen_default_screen = 0;
        public static final int[] ActionBar = {R.attr.background, R.attr.backgroundSplit, R.attr.backgroundStacked, R.attr.contentInsetEnd, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.customNavigationLayout, R.attr.displayOptions, R.attr.divider, R.attr.elevation, R.attr.height, R.attr.hideOnContentScroll, R.attr.homeAsUpIndicator, R.attr.homeLayout, R.attr.icon, R.attr.indeterminateProgressStyle, R.attr.itemPadding, R.attr.logo, R.attr.navigationMode, R.attr.popupTheme, R.attr.progressBarPadding, R.attr.progressBarStyle, R.attr.subtitle, R.attr.subtitleTextStyle, R.attr.title, R.attr.titleTextStyle};
        public static final int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static final int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {R.attr.background, R.attr.backgroundSplit, R.attr.closeItemLayout, R.attr.height, R.attr.subtitleTextStyle, R.attr.titleTextStyle};
        public static final int[] ActivityChooserView = {R.attr.expandActivityOverflowButtonDrawable, R.attr.initialActivityCount};
        public static final int[] AlertDialog = {android.R.attr.layout, R.attr.buttonPanelSideLayout, R.attr.listItemLayout, R.attr.listLayout, R.attr.multiChoiceItemLayout, R.attr.singleChoiceItemLayout};
        public static final int[] AppCompatImageView = {android.R.attr.src, R.attr.srcCompat};
        public static final int[] AppCompatTextView = {android.R.attr.textAppearance, R.attr.textAllCaps};
        public static final int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, R.attr.actionBarDivider, R.attr.actionBarItemBackground, R.attr.actionBarPopupTheme, R.attr.actionBarSize, R.attr.actionBarSplitStyle, R.attr.actionBarStyle, R.attr.actionBarTabBarStyle, R.attr.actionBarTabStyle, R.attr.actionBarTabTextStyle, R.attr.actionBarTheme, R.attr.actionBarWidgetTheme, R.attr.actionButtonStyle, R.attr.actionDropDownStyle, R.attr.actionMenuTextAppearance, R.attr.actionMenuTextColor, R.attr.actionModeBackground, R.attr.actionModeCloseButtonStyle, R.attr.actionModeCloseDrawable, R.attr.actionModeCopyDrawable, R.attr.actionModeCutDrawable, R.attr.actionModeFindDrawable, R.attr.actionModePasteDrawable, R.attr.actionModePopupWindowStyle, R.attr.actionModeSelectAllDrawable, R.attr.actionModeShareDrawable, R.attr.actionModeSplitBackground, R.attr.actionModeStyle, R.attr.actionModeWebSearchDrawable, R.attr.actionOverflowButtonStyle, R.attr.actionOverflowMenuStyle, R.attr.activityChooserViewStyle, R.attr.alertDialogButtonGroupStyle, R.attr.alertDialogCenterButtons, R.attr.alertDialogStyle, R.attr.alertDialogTheme, R.attr.autoCompleteTextViewStyle, R.attr.borderlessButtonStyle, R.attr.buttonBarButtonStyle, R.attr.buttonBarNegativeButtonStyle, R.attr.buttonBarNeutralButtonStyle, R.attr.buttonBarPositiveButtonStyle, R.attr.buttonBarStyle, R.attr.buttonStyle, R.attr.buttonStyleSmall, R.attr.checkboxStyle, R.attr.checkedTextViewStyle, R.attr.colorAccent, R.attr.colorButtonNormal, R.attr.colorControlActivated, R.attr.colorControlHighlight, R.attr.colorControlNormal, R.attr.colorPrimary, R.attr.colorPrimaryDark, R.attr.colorSwitchThumbNormal, R.attr.controlBackground, R.attr.dialogPreferredPadding, R.attr.dialogTheme, R.attr.dividerHorizontal, R.attr.dividerVertical, R.attr.dropDownListViewStyle, R.attr.dropdownListPreferredItemHeight, R.attr.editTextBackground, R.attr.editTextColor, R.attr.editTextStyle, R.attr.homeAsUpIndicator, R.attr.imageButtonStyle, R.attr.listChoiceBackgroundIndicator, R.attr.listDividerAlertDialog, R.attr.listPopupWindowStyle, R.attr.listPreferredItemHeight, R.attr.listPreferredItemHeightLarge, R.attr.listPreferredItemHeightSmall, R.attr.listPreferredItemPaddingLeft, R.attr.listPreferredItemPaddingRight, R.attr.panelBackground, R.attr.panelMenuListTheme, R.attr.panelMenuListWidth, R.attr.popupMenuStyle, R.attr.popupWindowStyle, R.attr.radioButtonStyle, R.attr.ratingBarStyle, R.attr.ratingBarStyleIndicator, R.attr.ratingBarStyleSmall, R.attr.searchViewStyle, R.attr.seekBarStyle, R.attr.selectableItemBackground, R.attr.selectableItemBackgroundBorderless, R.attr.spinnerDropDownItemStyle, R.attr.spinnerStyle, R.attr.switchStyle, R.attr.textAppearanceLargePopupMenu, R.attr.textAppearanceListItem, R.attr.textAppearanceListItemSmall, R.attr.textAppearanceSearchResultSubtitle, R.attr.textAppearanceSearchResultTitle, R.attr.textAppearanceSmallPopupMenu, R.attr.textColorAlertDialogListItem, R.attr.textColorSearchUrl, R.attr.toolbarNavigationButtonStyle, R.attr.toolbarStyle, R.attr.windowActionBar, R.attr.windowActionBarOverlay, R.attr.windowActionModeOverlay, R.attr.windowFixedHeightMajor, R.attr.windowFixedHeightMinor, R.attr.windowFixedWidthMajor, R.attr.windowFixedWidthMinor, R.attr.windowMinWidthMajor, R.attr.windowMinWidthMinor, R.attr.windowNoTitle};
        public static final int[] ArcMenu = {R.attr.position, R.attr.radius};
        public static final int[] ButtonBarLayout = {R.attr.allowStacking};
        public static final int[] CompoundButton = {android.R.attr.button, R.attr.buttonTint, R.attr.buttonTintMode};
        public static final int[] DrawerArrowToggle = {R.attr.arrowHeadLength, R.attr.arrowShaftLength, R.attr.barLength, R.attr.color, R.attr.drawableSize, R.attr.gapBetweenBars, R.attr.spinBars, R.attr.thickness};
        public static final int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, R.attr.divider, R.attr.dividerPadding, R.attr.measureWithLargestChild, R.attr.showDividers};
        public static final int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static final int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static final int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static final int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, R.attr.actionLayout, R.attr.actionProviderClass, R.attr.actionViewClass, R.attr.showAsAction};
        public static final int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, R.attr.preserveIconSpacing};
        public static final int[] PopupWindow = {android.R.attr.popupBackground, R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {R.attr.state_above_anchor};
        public static final int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, R.attr.closeIcon, R.attr.commitIcon, R.attr.defaultQueryHint, R.attr.goIcon, R.attr.iconifiedByDefault, R.attr.layout, R.attr.queryBackground, R.attr.queryHint, R.attr.searchHintIcon, R.attr.searchIcon, R.attr.submitBackground, R.attr.suggestionRowLayout, R.attr.voiceIcon};
        public static final int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, R.attr.popupTheme};
        public static final int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, R.attr.showText, R.attr.splitTrack, R.attr.switchMinWidth, R.attr.switchPadding, R.attr.switchTextAppearance, R.attr.thumbTextPadding, R.attr.track};
        public static final int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, R.attr.textAllCaps};
        public static final int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, R.attr.collapseContentDescription, R.attr.collapseIcon, R.attr.contentInsetEnd, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStart, R.attr.logo, R.attr.logoDescription, R.attr.maxButtonHeight, R.attr.navigationContentDescription, R.attr.navigationIcon, R.attr.popupTheme, R.attr.subtitle, R.attr.subtitleTextAppearance, R.attr.subtitleTextColor, R.attr.title, R.attr.titleMarginBottom, R.attr.titleMarginEnd, R.attr.titleMarginStart, R.attr.titleMarginTop, R.attr.titleMargins, R.attr.titleTextAppearance, R.attr.titleTextColor};
        public static final int[] View = {android.R.attr.theme, android.R.attr.focusable, R.attr.paddingEnd, R.attr.paddingStart, R.attr.theme};
        public static final int[] ViewBackgroundHelper = {android.R.attr.background, R.attr.backgroundTint, R.attr.backgroundTintMode};
        public static final int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};
        public static final int[] ao_ScrollScreen = {R.attr.default_screen};
    }

    public static final class xml {
        public static final int setting_all = 0x7f100000;
        public static final int setting_nlu = 0x7f100001;
        public static final int setting_offline = 0x7f100002;
        public static final int setting_online = 0x7f100003;
    }
}
