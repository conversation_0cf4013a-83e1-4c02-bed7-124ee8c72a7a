<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="1"
    android:versionName="3.0"
    package="com.AoRGMap">
    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="23"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-feature android:name="android.hardware.camera"/>
    <uses-feature android:name="android.hardware.camera.autofocus"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <application
        android:theme="@android:style/Theme.Light"
        android:label="@string/app_name"
        android:icon="@drawable/app_icon"
        android:name="com.AoRGMap.RGMapApplication"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:supportsRtl="true">
        <uses-library android:name="android.test.runner"/>
        <meta-data
            android:name="com.baidu.speech.APP_ID"
            android:value="10127572"/>
        <meta-data
            android:name="com.baidu.speech.API_KEY"
            android:value="rZh0DLQKcqMzYjNSf2mwVByA"/>
        <meta-data
            android:name="com.baidu.speech.SECRET_KEY"
            android:value="3137fd3bec47ff381c37505583a044c4"/>
        <service
            android:name="com.baidu.speech.VoiceRecognitionService"
            android:exported="false"/>
        <activity
            android:label="@string/app_name"
            android:name="com.AoRGMap.AoRGMapActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.AoRGMap.GisLibPathActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:label="@string/startup_title"
            android:name="com.AoRGMap.StartupActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.MenuDemo"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.ColorSelectActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.SubnoSelectActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.NoteInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.SubInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.LineInfoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.LayerManagerActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoGIS.ui.FileListActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoDevBase.ui.filebrowser.FileListActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoDevBase.ui.AttributeLinkActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoDevBase.config.MapParamActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoDevBase.ui.AoCompassActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SysConfigActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttGPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttRoutingActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttGBoundaryActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttAttitudeActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttFossilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttPhotoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttOrecheckActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttFreeLineActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttSampleActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttSketchActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttGravelViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttGravelActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttAttiActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttRemainActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttEngPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttHydPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.EngPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.HydPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttLibEngPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttLibHydPointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.DrawSketchActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.DrawSecSketchActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.drawbitmap.DrawBitmapActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.SectionActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSectionActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSurveyActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSlayerActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSsampleActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSecattActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSphotoActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSfossilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSsketchActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.AttSgpointActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.PMDicListActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttSandSediActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoRockActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoSoilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoWaterActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25RockActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25SoilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25WaterActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25StreamActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttSandSediRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoRockRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoSoilRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoWaterRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25RockRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25SoilRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25WaterRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.Att25StreamRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttStremSediActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttSoilSediActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulsoilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulwaterActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulstremActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttStremSediRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttSoilSediRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulsoilRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulstremRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttMulwaterRActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemStream5wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSoil5wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemRock5wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemStream25wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSoil25wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemRock25wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulMedia25wActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemSamNationalActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilLayActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilLayerActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSoilActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulSedimentActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.AttGeoChemMulWaterActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.earth.MulActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.camera.CameraTakerActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.camera.CameraTaker2Activity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.OrientationActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.AoRGMap.ExtOrientationActivity"
            android:screenOrientation="portrait"/>
        <activity
            android:name="com.AoRGMap.prb.ViewVideo"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.ViewRouteActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.ImageViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.SoundViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.RouteSumActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.ViewSectionActivity"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.ViewSectionDocActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttRemainLinActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.pm.PmXjActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.MyImageView1Activity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.MySoundViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.MyVideoViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.net.WebServiceHtmlActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.net.WebServiceViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttGPointDescActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.MenuConfigActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.Util.ExtAudioRecorderActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.Util.ExtDrawBitmapActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.AoDlgRGMapActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.ExtLayer.AttLargeScaleActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.DataQueryConfigActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.ExtLayer.DataQueryListActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.BigDataQueryActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.ExtListFileServiceActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.SecExtListFileServiceActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.BigDataWebServiceViewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.SmartSpaceServiceMapActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.ExtStorageActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttExtMediaDataActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.AttExtFileDataActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.InnerTableListActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.prb.InnerTableAttributeActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.HisDataListFileServiceActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SmartService.WebBigDataQueryActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.threeDmodel.DmodelActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.threeDmodel.Model3DMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.yksb.CameraActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.yksb.YksbReslutActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:name="com.AoRGMap.SplitScreenActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|locale"/>
        <activity
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:name="com.AoRGMap.baidu.activity.ActivityMain"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:label="在线识别"
            android:name="com.AoRGMap.baidu.activity.ActivityOnline"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="离线语法识别"
            android:name="com.AoRGMap.baidu.activity.ActivityOffline"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="语义解析（本地和在线）"
            android:name="com.AoRGMap.baidu.activity.ActivityNlu"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="全部识别功能"
            android:name="com.AoRGMap.baidu.activity.ActivityAllRecog"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="唤醒词"
            android:name="com.AoRGMap.baidu.activity.ActivityWakeUp"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="精简版识别"
            android:name="com.AoRGMap.baidu.activity.mini.ActivityMiniRecog"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:label="精简版唤醒词"
            android:name="com.AoRGMap.baidu.activity.mini.ActivityMiniWakeUp"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden">
            <intent-filter>
                <category android:name="com.baidu.speech.recognizerdemo.intent.category.SAMPLE_CODE"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:label="在线识别设置"
            android:name="com.AoRGMap.baidu.activity.setting.OnlineSetting"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden"/>
        <activity
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:label="离线语法设置"
            android:name="com.AoRGMap.baidu.activity.setting.OfflineSetting"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden"/>
        <activity
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:label="语义设置"
            android:name="com.AoRGMap.baidu.activity.setting.NluSetting"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden"/>
        <activity
            android:theme="@android:style/Theme.DeviceDefault.Light"
            android:label="全部识别设置"
            android:name="com.AoRGMap.baidu.activity.setting.AllSetting"
            android:screenOrientation="portrait"
            android:configChanges="screenLayout|orientation|keyboardHidden"/>
        <activity android:name="com.AoDevBase.ui.AttributeExtActivity"/>
    </application>
</manifest>
