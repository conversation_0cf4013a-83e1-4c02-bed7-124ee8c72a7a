<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:id="@+id/point_layout"
    android:background="@color/back_color"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="horizontal"
        android:background="#6e8fb8"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <Spinner
            android:id="@+id/point_spinner"
            android:layout_width="95dp"
            android:layout_height="wrap_content"/>
        <Button
            android:id="@+id/point_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tool_layout_btadd"/>
        <Button
            android:id="@+id/point_del"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tool_layout_btdel"/>
        <Button
            android:id="@+id/point_update"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tool_layout_btedit"/>
        <Button
            android:id="@+id/point_insert"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tool_layout_btsave"/>
    </LinearLayout>
</LinearLayout>
