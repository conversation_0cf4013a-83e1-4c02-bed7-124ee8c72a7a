<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/mainRelativeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.AoRGMap.RGMapView
            android:id="@+id/main_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:gravity="center"
            android:id="@+id/id_menu_viewtypelayout"
            android:background="@android:drawable/editbox_background"
            android:layout_width="250dp"
            android:layout_height="45dp"
            android:layout_marginTop="5dp"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true">
            <Button
                android:id="@+id/id_menu_gsView"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/MAIN_PROMPT_TYPE0"
                android:layout_weight="1"/>
            <ImageView
                android:background="#d3d3d3"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>
            <Button
                android:id="@+id/id_menu_tdtView"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/MAIN_PROMPT_TYPE1"
                android:layout_weight="1"/>
            <ImageView
                android:background="#d3d3d3"
                android:layout_width="1dp"
                android:layout_height="match_parent"/>
            <Button
                android:id="@+id/id_menu_googleView"
                android:background="@null"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/MAIN_PROMPT_TYPE2"
                android:layout_weight="1"/>
        </LinearLayout>
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="55dp"
            android:layout_marginRight="50dp">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/floatBtnAddGPoint"
                    android:background="@drawable/p"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddRoute"
                    android:background="@drawable/r"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddBound"
                    android:background="@drawable/b"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE1"
                    android:background="@drawable/e1"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE2"
                    android:background="@drawable/e2"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE3"
                    android:background="@drawable/e3"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE4"
                    android:background="@drawable/e4"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE5"
                    android:background="@drawable/e5"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE6"
                    android:background="@drawable/e6"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
                <ImageView
                    android:layout_width="3dp"
                    android:layout_height="match_parent"/>
                <Button
                    android:id="@+id/floatBtnAddE7"
                    android:background="@drawable/e7"
                    android:layout_width="40dp"
                    android:layout_height="40dp"/>
            </LinearLayout>
        </HorizontalScrollView>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/id_googlemap_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="55dp"
        android:layout_marginRight="5dp"
        android:layout_alignParentRight="true">
        <ImageView
            android:id="@+id/id_setgooglemaptype"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/maptype0"
            android:scaleType="fitCenter"/>
    </RelativeLayout>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/id_hisresource"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_below="@+id/id_googlemap_type"
        android:layout_alignRight="@+id/id_googlemap_type">
        <ImageView
            android:id="@+id/id_showhisresource"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/image_zyml2"
            android:scaleType="fitCenter"/>
    </RelativeLayout>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/id_layout_showatt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_below="@+id/id_hisresource"
        android:layout_alignRight="@+id/id_hisresource">
        <ImageView
            android:id="@+id/id_button_showbkatt"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/info2"
            android:scaleType="fitCenter"/>
    </RelativeLayout>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/id_measureline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_below="@+id/id_layout_showatt"
        android:layout_alignRight="@+id/id_layout_showatt">
        <ImageButton
            android:id="@+id/id_measureline_close"
            android:background="@drawable/close"
            android:layout_width="40dp"
            android:layout_height="40dp"/>
    </RelativeLayout>
    <RelativeLayout
        android:orientation="vertical"
        android:id="@+id/id_map_orientationzoom"
        android:background="@drawable/compass2"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_centerInParent="true">
        <ImageView
            android:id="@+id/id_image_orientationshow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/close_compass"
            android:layout_centerHorizontal="true"/>
        <ImageView
            android:id="@+id/id_image_orientationzoom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/compass1"/>
        <TextView
            android:textSize="40dp"
            android:textStyle="bold"
            android:textColor="#8f11ff22"
            android:id="@+id/id_image_orientationtext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <com.AoDevBase.ui.MainFloatView
        android:orientation="vertical"
        android:id="@+id/gpsFloat"
        android:background="#77808080"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_alignParentLeft="true">
        <TextView
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:id="@+id/ao_gps_bar_dragger"
            android:background="#88808080"
            android:clickable="true"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="GPS"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/float_panel"
            android:background="#00000000"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:background="#00000000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="X:"/>
                <EditText
                    android:id="@+id/gps_editor_x"
                    android:layout_width="150sp"
                    android:layout_height="wrap_content"
                    android:text="N/A"
                    android:maxLines="1"
                    android:editable="false"
                    android:inputType="none"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Y:"/>
                <EditText
                    android:id="@+id/gps_editor_y"
                    android:layout_width="150sp"
                    android:layout_height="wrap_content"
                    android:text="N/A"
                    android:maxLines="1"
                    android:editable="false"
                    android:inputType="none"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:background="#00000000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="H:"/>
                <EditText
                    android:id="@+id/gps_editor_z"
                    android:layout_width="150sp"
                    android:layout_height="wrap_content"
                    android:text="N/A"
                    android:maxLines="1"
                    android:editable="false"
                    android:inputType="none"/>
                <CheckBox
                    android:id="@+id/gps_btnnavigation"
                    android:layout_width="75sp"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_NAVIGATION"/>
                <CheckBox
                    android:id="@+id/gps_btbgpslocation"
                    android:layout_width="75sp"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_AUTOLOC"/>
            </LinearLayout>
            <LinearLayout
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:background="#00000000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <Button
                    android:gravity="center"
                    android:id="@+id/gps_btnOnOff"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gps_btnOnOff"
                    android:layout_weight="1"/>
                <Button
                    android:gravity="center"
                    android:id="@+id/gps_btnSwitchDisp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gps_btnSwitchDisp"
                    android:layout_weight="1"/>
                <Button
                    android:gravity="center"
                    android:id="@+id/gps_btnCenter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gps_btnCenter"
                    android:layout_weight="1"/>
                <Button
                    android:gravity="center"
                    android:id="@+id/gps_btnDrawPoint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gps_btnDrawPoint"
                    android:layout_weight="1"/>
            </LinearLayout>
            <LinearLayout
                android:layout_gravity="center"
                android:orientation="horizontal"
                android:background="#00000000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:textColor="#ff0000"
                    android:gravity="center"
                    android:id="@+id/gps_nnavigation_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_CLOSENAVIGATION"/>
            </LinearLayout>
        </LinearLayout>
    </com.AoDevBase.ui.MainFloatView>
    <com.AoRGMap.arcmenu.ArcMenu
        android:id="@+id/id_arcmenu"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:ns1="http://schemas.android.com/apk/res-auto" 
        ns1:position="3"
        xmlns:ns2="http://schemas.android.com/apk/res-auto" 
        ns2:radius="150dp">
        <RelativeLayout
            android:background="@drawable/composer_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/id_arcmenu_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/composer_icn_plus"
                android:layout_centerInParent="true"/>
        </RelativeLayout>
        <ImageView
            android:id="@+id/id_arcmenu_button1"
            android:tag="ywsb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_ywsb"/>
        <ImageView
            android:id="@+id/id_arcmenu_button2"
            android:tag="xspm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_xspm"
            android:layout_centerInParent="true"/>
        <ImageView
            android:id="@+id/id_arcmenu_button3"
            android:tag="place"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_place"
            android:layout_centerInParent="true"/>
        <ImageView
            android:id="@+id/id_arcmenu_button4"
            android:tag="sjwj"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_sjwj"
            android:layout_centerInParent="true"/>
        <ImageView
            android:id="@+id/id_arcmenu_button5"
            android:tag="lssc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_lssc"
            android:layout_centerInParent="true"/>
        <ImageView
            android:id="@+id/id_arcmenu_button6"
            android:tag="yksb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_yksb"
            android:layout_centerInParent="true"/>
        <ImageView
            android:id="@+id/id_arcmenu_button7"
            android:tag="sjjm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/composer_sjjm"
            android:layout_centerInParent="true"/>
    </com.AoRGMap.arcmenu.ArcMenu>
    <SeekBar
        android:id="@+id/id_image_seekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="60dp"
        android:layout_marginRight="30dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"/>
</RelativeLayout>
