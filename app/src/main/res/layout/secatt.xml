<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:scrollbars="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="horizontal"
                android:background="#5f9ea0"
                android:layout_width="match_parent"
                android:layout_height="47dp">
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_secpoint"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_secpoint"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_laycode"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_laycode"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_code"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_code"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_slope_l"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_slope_l"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_type"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_type"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_trend"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_trend"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_dip"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_dip"/>
                <TextView
                    android:textSize="20sp"
                    android:textColor="#556570"
                    android:gravity="center"
                    android:id="@+id/seca_dip_ang"
                    android:background="#f0ffff"
                    android:layout_width="80pt"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1pt"
                    android:layout_marginRight="1pt"
                    android:layout_marginBottom="1pt"
                    android:text="@string/seca_layout_dip_ang"/>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:background="#f0ffff"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ListView
                    android:id="@+id/listSecatt"
                    android:fadingEdge="none"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:listSelector="#00000000"
                    android:scrollingCache="false"
                    android:divider="#00000000"
                    android:dividerHeight="1px"/>
            </LinearLayout>
        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>
