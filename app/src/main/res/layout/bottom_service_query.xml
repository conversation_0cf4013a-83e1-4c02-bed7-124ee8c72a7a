<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/service_query_radiogroup"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_service_bigdata"
            android:layout_marginTop="2dp"
            android:text="@string/MENU_DATASERVICE_BIGDATA"
            android:drawableTop="@android:drawable/ic_menu_search"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_service_load"
            android:layout_marginTop="2dp"
            android:text="@string/MENU_DATASERVICE_UPLOAD"
            android:drawableTop="@android:drawable/ic_menu_upload"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_service_list"
            android:layout_marginTop="2dp"
            android:text="@string/MENU_DATASERVICE_ML"
            android:drawableTop="@android:drawable/ic_menu_sort_by_size"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
