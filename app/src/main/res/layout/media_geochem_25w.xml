<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/id_type_selector"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true">
        <RadioButton
            android:id="@+id/id_media_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="@string/RGMAP_PROMPT_DESCPHOTO"
            android:drawableTop="@android:drawable/presence_video_online"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/id_media_audio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="@string/RGMAP_PROMPT_AUDIO"
            android:drawableTop="@android:drawable/presence_audio_online"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/id_media_vedio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="@string/RGMAP_PROMPT_VEDIO"
            android:drawableTop="@android:drawable/presence_video_online"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/id_fgx"
        android:layout_below="@+id/id_type_selector">
        <ListView
            android:id="@+id/id_listview_mulmedia"
            android:fadingEdge="none"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:listSelector="#00000000"
            android:scrollingCache="false"
            android:divider="#00000000"
            android:dividerHeight="1px"
            android:layout_centerHorizontal="true"/>
    </HorizontalScrollView>
    <ImageView
        android:id="@+id/id_fgx"
        android:background="#cccccc"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/id_photo_info"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/id_photo_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/id_radioGroup_menu1">
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="40dp">
            <TextView
                android:textSize="16sp"
                android:gravity="center"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:text="@string/GEOCHEM25W_MULMEDIA__PROMPT_MEDIAID"/>
            <EditText
                android:gravity="center"
                android:layout_gravity="center"
                android:id="@+id/id_edit_code"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
        </LinearLayout>
        <ImageView
            android:background="#cccccc"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="40dp">
            <TextView
                android:textSize="16sp"
                android:gravity="center"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:text="@string/GEOCHEM25W_MULMEDIA__PROMPT_DIRECTION"/>
            <EditText
                android:gravity="center_vertical"
                android:layout_gravity="center"
                android:id="@+id/id_edit_direction"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <ImageView
                android:id="@+id/id_edit_compass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@android:drawable/ic_menu_compass"/>
        </LinearLayout>
        <ImageView
            android:background="#cccccc"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="40dp">
            <TextView
                android:textSize="16sp"
                android:gravity="center"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:text="@string/GEOCHEM25W_MULMEDIA__PROMPT_FILENO"/>
            <EditText
                android:gravity="center_vertical"
                android:layout_gravity="center"
                android:id="@+id/id_edit_fileno"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <ImageView
                android:id="@+id/id_bt_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@android:drawable/ic_menu_slideshow"/>
            <ImageView
                android:id="@+id/id_bt_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@android:drawable/ic_media_ff"/>
        </LinearLayout>
        <ImageView
            android:background="#cccccc"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="40dp">
            <TextView
                android:textSize="16sp"
                android:gravity="center"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:text="@string/GEOCHEM25W_MULMEDIA__PROMPT_NOTE"/>
            <EditText
                android:gravity="center_vertical"
                android:layout_gravity="center"
                android:id="@+id/id_edit_note"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
        </LinearLayout>
    </LinearLayout>
    <RadioGroup
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/id_radioGroup_menu1"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true">
        <RadioButton
            android:id="@+id/id_menu_add"
            android:layout_marginTop="2dp"
            android:text="@string/tool_layout_add"
            android:drawableTop="@android:drawable/ic_menu_add"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/id_menu_update"
            android:layout_marginTop="2dp"
            android:text="@string/tool_layout_update"
            android:drawableTop="@android:drawable/stat_notify_sync"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/id_menu_delete"
            android:layout_marginTop="2dp"
            android:text="@string/tool_layout_del"
            android:drawableTop="@android:drawable/ic_menu_delete"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</RelativeLayout>
