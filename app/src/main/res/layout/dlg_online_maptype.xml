<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:id="@+id/id_dlgmaptype"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <ImageView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/id_google_type0"
        android:background="@drawable/ao_bkcolor"
        android:paddingLeft="3dp"
        android:paddingTop="3dp"
        android:paddingRight="3dp"
        android:paddingBottom="3dp"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/maptype0"
        android:scaleType="fitCenter"/>
    <ImageView
        android:id="@+id/id_google_type1"
        android:paddingLeft="3dp"
        android:paddingTop="3dp"
        android:paddingRight="3dp"
        android:paddingBottom="3dp"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/maptype1"
        android:scaleType="fitCenter"/>
    <ImageView
        android:id="@+id/id_google_type2"
        android:paddingLeft="3dp"
        android:paddingTop="3dp"
        android:paddingRight="3dp"
        android:paddingBottom="3dp"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/maptype2"
        android:scaleType="fitCenter"/>
</LinearLayout>
