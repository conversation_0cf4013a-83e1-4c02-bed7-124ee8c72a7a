<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="20sp"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="蓝牙连接"/>
        <Button
            android:id="@+id/bluetoothBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bd_bluetooth"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="20sp"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="北斗状态"/>
        <Button
            android:id="@+id/bdinfoBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bd_info"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="20sp"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="发送短报文"/>
        <Button
            android:id="@+id/sendmsgBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bd_sendmsg"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="20sp"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="接收短报文"/>
        <Button
            android:id="@+id/receivemsgBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bd_receivemsg"/>
    </LinearLayout>
</LinearLayout>
