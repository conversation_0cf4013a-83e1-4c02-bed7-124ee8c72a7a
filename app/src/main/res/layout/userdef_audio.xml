<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:background="@drawable/userdef_audio_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/id_save_layout"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginTop="2dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true">
        <ImageView
            android:id="@+id/id_audio_erase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:src="@drawable/erase"/>
        <ImageView
            android:id="@+id/id_audio_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="15dp"
            android:src="@drawable/apply"
            android:layout_alignParentRight="true"/>
    </RelativeLayout>
    <TextView
        android:textSize="20dp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:id="@+id/id_audio_text"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="录音机"
        android:layout_below="@+id/id_save_layout"/>
    <Chronometer
        android:textSize="32dp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:id="@+id/id_audio_Time"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_below="@+id/id_audio_text"
        android:layout_centerHorizontal="true"/>
    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true">
        <ImageView
            android:id="@+id/imageView1"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="5dp"
            android:paddingBottom="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/voice_rcd_hint"/>
        <ImageView
            android:layout_gravity="bottom"
            android:id="@+id/id_imageView_amp"
            android:paddingLeft="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/amp1"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center"
        android:id="@+id/id_button_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true">
        <ImageView
            android:id="@+id/id_audio_stop"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="5dp"
            android:paddingBottom="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/userdef_audio_stop"/>
        <ImageView
            android:id="@+id/id_audio_record"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="5dp"
            android:paddingBottom="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/userdef_audio_rekam"/>
        <ImageView
            android:id="@+id/id_audio_pause"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="5dp"
            android:paddingBottom="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/userdef_audio_return"/>
    </LinearLayout>
</RelativeLayout>
