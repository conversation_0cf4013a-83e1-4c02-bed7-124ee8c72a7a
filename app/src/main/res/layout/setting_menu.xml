<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:textSize="16dp"
        android:gravity="center_vertical"
        android:id="@+id/relativeLayout1"
        android:background="@drawable/ao_bkcolor"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentTop="true">
        <TextView
            android:textSize="16dp"
            android:id="@+id/textView14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_main_file_menuconfig"
            android:layout_centerInParent="true"/>
        <Button
            android:textSize="16dp"
            android:gravity="center"
            android:id="@+id/menuset_button_cancel"
            android:background="@drawable/ao_titlebutton"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="2dp"
            android:text="@string/ao_btnCancel"
            android:layout_alignParentTop="true"/>
        <Button
            android:textSize="16dp"
            android:gravity="center"
            android:id="@+id/menuset_button_ok"
            android:background="@drawable/ao_titlebutton"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_marginTop="2dp"
            android:layout_marginRight="5dp"
            android:text="@string/ao_btnOK"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"/>
    </RelativeLayout>
    <ScrollView
        android:id="@+id/scrollView1"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:gravity="center_vertical"
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="14sp"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    android:id="@+id/textView1"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_MENUSET1"/>
            </LinearLayout>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView2"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENURESCIL"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox1"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView3"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUENG"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox2"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="14sp"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    android:id="@+id/textView4"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_MENUSET2"/>
            </LinearLayout>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView5"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM1"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox3"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView6"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM2"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox4"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView7"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM3"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox5"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView8"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM4"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox6"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView9"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM5"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox7"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView10"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM6"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox8"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView111"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM7"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox13"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView16"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/RGMAP_PROMPT_MENUGEOCHEM8"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox14"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <LinearLayout
                android:gravity="center_vertical"
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="14sp"
                    android:gravity="center_vertical"
                    android:layout_gravity="center_vertical"
                    android:id="@+id/textView11"
                    android:paddingLeft="10dp"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/RGMAP_PROMPT_MENUSET3"/>
            </LinearLayout>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView12"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/menu_main_prb_location"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox9"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView13"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/menu_main_prb_measurelin"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox10"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView141"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/menu_main_prb_modifypara"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox11"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="45dp">
                <TextView
                    android:textSize="16sp"
                    android:gravity="center_vertical"
                    android:id="@+id/textView15"
                    android:paddingLeft="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/menu_main_prb_Imageparency"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"/>
                <CheckBox
                    android:gravity="center_vertical"
                    android:id="@+id/id_checkBox12"
                    android:paddingRight="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"/>
            </RelativeLayout>
            <ImageView
                android:background="#cccccc"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
