<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/point_radiogroup1"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_point_new"
            android:layout_marginTop="2dp"
            android:text="@string/menu_pointedit_new"
            android:drawableTop="@drawable/bottom_point_add"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_point_del"
            android:layout_marginTop="2dp"
            android:text="@string/menu_pointedit_del"
            android:drawableTop="@drawable/bottom_point_del"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_point_modi_att"
            android:layout_marginTop="2dp"
            android:text="@string/menu_pointedit_att"
            android:drawableTop="@drawable/bottom_edit_att"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_point_modi_param"
            android:layout_marginTop="2dp"
            android:text="@string/menu_pointedit_info"
            android:drawableTop="@drawable/bottom_edit_info"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_point_more_1"
            android:layout_marginTop="2dp"
            android:text="@string/menu_more"
            android:drawableTop="@drawable/menu_more"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
