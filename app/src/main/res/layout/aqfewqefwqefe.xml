<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:id="@+id/content"
        android:background="#e9e9e9"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true">
        <RelativeLayout
            android:background="#336699"
            android:layout_width="match_parent"
            android:layout_height="60dp">
            <ImageView
                android:id="@+id/imageView_menu"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginLeft="5dp"
                android:src="@android:drawable/ic_menu_sort_by_size"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"/>
            <ImageView
                android:id="@+id/imageView_return"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginRight="5dp"
                android:src="@android:drawable/ic_menu_revert"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"/>
            <TextView
                android:textSize="20dp"
                android:textStyle="bold"
                android:gravity="center"
                android:id="@+id/textView_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"/>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp">
            <Gallery
                android:id="@+id/gallery1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:spacing="5dp"/>
        </RelativeLayout>
        <ListView
            android:id="@+id/listView2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
</RelativeLayout>
