<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:background="@android:drawable/editbox_background_normal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/id_dataquery_headType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">
        <LinearLayout
            android:id="@+id/id_dataquery_Type"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="45dp"/>
    </HorizontalScrollView>
    <ListView
        android:id="@+id/id_dataquery_poplist"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:divider="#aaa"
        android:dividerHeight="1px"
        android:layout_weight="1"
        android:layout_below="@+id/id_dataquery_headType"
        android:scrollbarFadeDuration="0"
        android:fadeScrollbars="false"/>
</RelativeLayout>
