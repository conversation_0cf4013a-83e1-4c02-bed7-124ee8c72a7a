<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:id="@+id/status_bar_latest_event_content"
    android:layout_width="match_parent"
    android:layout_height="128dp">
    <ImageView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/icon"
        android:layout_width="@dimen/notification_large_icon_width"
        android:layout_height="@dimen/notification_large_icon_height"
        android:scaleType="centerCrop"/>
    <include
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:layout_alignParentRight="true"
        layout="@layout/notification_media_cancel_action"/>
    <include
        android:layout_gravity="fill_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/notification_large_icon_width"
        android:layout_toLeftOf="@+id/cancel_action"
        android:layout_marginStart="@dimen/notification_large_icon_width"
        android:layout_toStartOf="@+id/cancel_action"
        layout="@layout/notification_template_lines"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/media_actions"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_alignParentBottom="true"
        android:layoutDirection="ltr"/>
    <ImageView
        android:id="@+id/action_divider"
        android:background="?android:attr/dividerHorizontal"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/media_actions"/>
</RelativeLayout>
