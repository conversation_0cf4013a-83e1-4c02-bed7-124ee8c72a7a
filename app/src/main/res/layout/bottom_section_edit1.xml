<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/section_radiogroup1"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_section_new"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sectionmap_new"
            android:drawableTop="@drawable/bottom_new_section"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_section_modi_att"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sectionmap_editatt"
            android:drawableTop="@drawable/bottom_edit_base"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_section_modi_lib"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sectionmap_editlib"
            android:drawableTop="@drawable/bottom_edit_lib"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_section_proj"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sectionmap_projection"
            android:drawableTop="@drawable/bottom_proj"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_section_more_1"
            android:layout_marginTop="2dp"
            android:text="@string/menu_more"
            android:drawableTop="@drawable/menu_more"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
