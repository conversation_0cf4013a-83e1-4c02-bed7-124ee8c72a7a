<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:scrollbars="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TableLayout
                    android:orientation="horizontal"
                    android:background="#5f9ea0"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TableRow>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_linecode"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="LINECODE"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_gpoint"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="GPOINT"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_attitjpoint"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="ATTITJPOINT"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_code"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="CODE"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_type"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="TYPE"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_trend"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="TREND"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_dip"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="DIP"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_dip_ang"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="DIP_ANG"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_length"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="WIDTH"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_width"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="WIDTH"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_feature"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="FEATURE"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_pack"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="PACK"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_packtype"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="PACKTYPE"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_span"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="SPAN"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_roughness"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="ROUGHNESS"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_gap"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="GAP"/>
                        <TextView
                            android:textSize="20sp"
                            android:textColor="#556570"
                            android:id="@+id/ep_water"
                            android:background="#f0ffff"
                            android:paddingLeft="5pt"
                            android:layout_width="80pt"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="1pt"
                            android:layout_marginRight="1pt"
                            android:layout_marginBottom="1pt"
                            android:text="WATER"/>
                    </TableRow>
                </TableLayout>
            </LinearLayout>
            <LinearLayout
                android:orientation="horizontal"
                android:background="#f0ffff"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ListView
                    android:id="@+id/listEngPoint"
                    android:fadingEdge="none"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:listSelector="#00000000"
                    android:scrollingCache="false"
                    android:divider="#00000000"
                    android:dividerHeight="1px"/>
            </LinearLayout>
        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>
