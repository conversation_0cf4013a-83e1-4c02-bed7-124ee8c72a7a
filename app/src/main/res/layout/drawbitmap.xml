<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:id="@+id/mainRelativeLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <com.AoRGMap.Util.DrawView
            android:id="@+id/main_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </RelativeLayout>
    <RadioGroup
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/main_sketch_menu"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_sketch_setpen"
            android:layout_marginTop="2dp"
            android:text="@string/SKETCH_MENU_PEN"
            android:drawableTop="@drawable/sketchpen"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_sketch_Eraser"
            android:layout_marginTop="2dp"
            android:text="@string/SKETCH_MENU_EASER"
            android:drawableTop="@drawable/sketcheraser"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_sketch_setwidth"
            android:layout_marginTop="2dp"
            android:text="@string/SKETCH_MENU_WIDTH"
            android:drawableTop="@drawable/sketchlinewidth"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_sketch_setcolor"
            android:layout_marginTop="2dp"
            android:text="@string/SKETCH_MENU_COLOR"
            android:drawableTop="@drawable/sketchcolor"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_sketch_setbk"
            android:layout_marginTop="2dp"
            android:text="@string/SKETCH_MENU_BK"
            android:drawableTop="@drawable/sketchbk"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_sketch_save"
            android:layout_marginTop="2dp"
            android:text="@string/menu_main_file_save"
            android:drawableTop="@drawable/sketchsave"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
