<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#556570"
        android:id="@+id/id_Layer_id"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="20pt"
        android:layout_height="wrap_content"
        android:text="ID"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#556570"
        android:id="@+id/id_Layer_start"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:text="起始位置"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#556570"
        android:id="@+id/id_Layer_end"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:text="终止位置"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#556570"
        android:id="@+id/id_Layer_soiltype"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:text="土壤类型"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#556570"
        android:id="@+id/id_Layer_soildesc"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="50pt"
        android:layout_height="wrap_content"
        android:text="简述"
        android:singleLine="true"/>
</LinearLayout>
