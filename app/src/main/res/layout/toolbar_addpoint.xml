<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:id="@+id/toolbar_edit_point"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="P"/>
    <Button
        android:id="@+id/btn_toolbar_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_ok"/>
    <Button
        android:id="@+id/btn_toolbar_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_cancel"/>
    <Button
        android:id="@+id/btn_toolbar_return"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_return"/>
    <Button
        android:id="@+id/btn_toolbar_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_new"/>
    <Button
        android:id="@+id/btn_toolbar_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_par"/>
    <Button
        android:id="@+id/btn_toolbar_att"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tool_layout_att"/>
</LinearLayout>
