<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:textSize="18sp"
        android:textColor="#556570"
        android:id="@+id/id_Layer_id"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="20pt"
        android:layout_height="wrap_content"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textColor="#556570"
        android:id="@+id/id_Layer_start"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textColor="#556570"
        android:id="@+id/id_Layer_end"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textColor="#556570"
        android:id="@+id/id_Layer_soiltype"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="40pt"
        android:layout_height="wrap_content"
        android:singleLine="true"/>
    <TextView
        android:textSize="18sp"
        android:textColor="#556570"
        android:id="@+id/id_Layer_soildesc"
        android:background="@drawable/bkframe"
        android:paddingLeft="6pt"
        android:layout_width="50pt"
        android:layout_height="wrap_content"
        android:singleLine="true"/>
</LinearLayout>
