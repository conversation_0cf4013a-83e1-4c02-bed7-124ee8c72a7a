<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.AoRGMap.DlgRgMapView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/id_dlg_mapview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <ImageView
        android:id="@+id/id_image_cancel"
        android:background="@drawable/camera_mode_button"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="15dp"
        android:src="@drawable/erase"
        android:scaleType="center"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"/>
    <ImageView
        android:id="@+id/id_image_apply"
        android:background="@drawable/camera_mode_button"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="15dp"
        android:src="@drawable/apply"
        android:scaleType="center"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"/>
</RelativeLayout>
