<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/line_radiogroup3"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_line_copy"
            android:layout_marginTop="2dp"
            android:text="@string/menu_lineedit_copy"
            android:drawableTop="@drawable/bottom_copy_point"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_line_link_att"
            android:layout_marginTop="2dp"
            android:text="@string/menu_att_link"
            android:drawableTop="@drawable/bottom_att_link"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:clickable="false"
            android:layout_marginTop="2dp"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:clickable="false"
            android:layout_marginTop="2dp"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_line_more_3"
            android:layout_marginTop="2dp"
            android:text="@string/menu_more"
            android:drawableTop="@drawable/menu_more"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
