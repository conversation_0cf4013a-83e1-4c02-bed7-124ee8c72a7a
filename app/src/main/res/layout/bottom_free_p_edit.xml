<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" 
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
        android:gravity="center_vertical"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:id="@+id/free_p_radiogroup"
        android:background="@drawable/maintab_toolbar_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RadioButton
            android:id="@+id/menu_free_p_new_sub"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sketch_point_newsym"
            android:drawableTop="@drawable/bottom_new"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_free_p_new_note"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sketch_point_newnote"
            android:drawableTop="@drawable/bottom_new_note"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_free_p_del"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sketch_point_del"
            android:drawableTop="@drawable/bottom_delete"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_free_p_modi_param"
            android:layout_marginTop="2dp"
            android:text="@string/menu_pointedit_info"
            android:drawableTop="@drawable/bottom_edit_info"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_free_p_move"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sketch_point_move"
            android:drawableTop="@drawable/bottom_move"
            style="@style/bottom_menu_style"/>
        <RadioButton
            android:id="@+id/menu_free_p_copy"
            android:layout_marginTop="2dp"
            android:text="@string/menu_sketch_point_copy"
            android:drawableTop="@drawable/bottom_copy_point"
            style="@style/bottom_menu_style"/>
    </RadioGroup>
</LinearLayout>
