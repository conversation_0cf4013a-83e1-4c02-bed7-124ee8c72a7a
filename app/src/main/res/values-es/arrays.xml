<?xml version="1.0" encoding="utf-8"?>
<resources>
    <array name="AxLenTypes">
        <item>0:MM</item>
        <item>1:MilliMeter</item>
        <item>2:M</item>
        <item>3:S</item>
        <item>4:D</item>
        <item>5:DSM</item>
        <item>6:F</item>
        <item>7:Min</item>
        <item>8:radian</item>
        <item>9:grad</item>
        <item>10:km</item>
        <item>11:dm</item>
        <item>12:cm</item>
    </array>
    <array name="AxProjTypes">
        <item>0:Geographic coordinate system</item>
        <item>1:Universal transverse Mercator projection (UTM)</item>
        <item>3:Lambert Conformal Conic Projection</item>
        <item>5:Gauss-Kruger projection</item>
        <item>5:Web Mercator projection</item>
    </array>
    <array name="AxTypes">
        <item>0:User coordinate system</item>
        <item>1:Geographic coordinate system</item>
        <item>2:Geodetic Coordinate System</item>
        <item>3:Projection plane rectangular coordinate system</item>
        <item>4:Geocentric geodetic coordinate system</item>
    </array>
    <array name="Component_array">
        <item>NO</item>
        <item>30%</item>
        <item>30%-70%</item>
        <item>70%</item>
    </array>
    <array name="_model_list">
        <item>@string/vad_default</item>
        <item>search, 搜索模型，默认， 适用于短句， 无逗号，可以有语义</item>
        <item>input, 输入法模型，适用于长句及长语音，有逗号分割， 无语义</item>
    </array>
    <array name="decoder_list">
        <item>@string/decoder_default</item>
        <item>0, 纯在线</item>
        <item>2, 离线语法及在线识别混合, 在线优先</item>
    </array>
    <array name="engpoint_array">
        <item>engineering point</item>
    </array>
    <array name="gravel_array">
        <item>Gravel</item>
        <item>Joint</item>
    </array>
    <array name="hydpoint_array">
        <item>hydrological point</item>
    </array>
    <array name="infile_list">
        <item>@string/infile_default</item>
        <item>res:///com/baidu/android/voicedemo/16k_test.pcm, 16k采样的测试音频</item>
        <item>#com.baidu.android.voicedemo.recognization.online.InFileStream.create16kStream()</item>
    </array>
    <array name="language">
        <item>@string/language_default</item>
        <item>cmn-Hans-CN, 普通话(默认)</item>
        <item>en-GB, 英语(美国)</item>
        <item>sichuan-Hans-CN, 四川话</item>
        <item>yue-Hans-CN, 粤语</item>
    </array>
    <array name="nlu_list">
        <item>@string/nlu_default</item>
        <item>enable, 开启本地语义解析（不支持英语）</item>
        <item>enable-all, 在enable参数基础上,临时解析结果也做本地语义分析</item>
        <item>disable, 不适用语义解析</item>
    </array>
    <array name="prop_list">
        <item>@string/prop_default</item>
        <item>10005, 热词（默认）</item>
        <item>10060, 地图</item>
        <item>10001, 音乐</item>
        <item>10002, 视频</item>
        <item>10003, 应用</item>
        <item>10004, 网页</item>
        <item>10006, 购物</item>
        <item>10007, 健康</item>
        <item>10008, 打电话</item>
        <item>10008, 超级电话</item>
        <item>10052, 医疗</item>
        <item>10053, 汽车</item>
        <item>10054, 娱乐餐饮</item>
        <item>10055, 财经</item>
        <item>10056, 游戏</item>
        <item>10057, 菜谱</item>
        <item>10058, 助手</item>
        <item>20000, 输入</item>
    </array>
    <array name="section_array">
        <item>lead</item>
        <item>layer</item>
        <item>photo</item>
        <item>attitide</item>
        <item>sketch</item>
        <item>sampleing</item>
        <item>fossil</item>
        <item>geological point</item>
    </array>
    <array name="vad_list">
        <item>@string/vad_default</item>
        <item>dnn, 推荐模型，新版本</item>
        <item>model-vad, 默认，旧版模型</item>
        <item>touch, 关闭静音断句功能。用戶手动停止录音</item>
        <item>mfe, 算法实现vad，不推荐</item>
    </array>
    <array name="vad_timeout_list">
        <item>@string/vad_timeout_default</item>
        <item>0, 开启长语音（离线不支持）。建议长句输入模型。</item>
        <item>800, 毫秒，静音800ms后断句，适用于输入短语</item>
        <item>2000, 毫秒，静音2000ms后断句，适用于输入长句</item>
        <item>2230, 毫秒，请修改代码中的VAD_ENDPOINT_TIMEOUT，自定义时长（800ms-3000ms之间）</item>
    </array>
</resources>
