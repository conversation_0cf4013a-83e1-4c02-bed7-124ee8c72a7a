<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"  android:key="root_screen">
    <PreferenceCategory xmlns:android="http://schemas.android.com/apk/res/android" android:title="常用">
        <ListPreference
            android:entries="@array/language"
            android:title="语种, 默认普通话"
            android:key="_language"
            android:summary=""
            android:defaultValue="@string/language_default"
            android:dialogTitle="语种"
            android:entryValues="@array/language"/>
        <ListPreference
            android:entries="@array/_model_list"
            android:title="短句输入或者长句输入"
            android:key="_model"
            android:summary=""
            android:defaultValue="@string/_model_default"
            android:dialogTitle="长短句"
            android:entryValues="@array/_model_list"/>
        <ListPreference
            android:entries="@array/decoder_list"
            android:title="纯在线 或 在线+离线语法"
            android:key="decoder"
            android:summary=""
            android:defaultValue="@string/decoder_default"
            android:dialogTitle="纯在线/在线和离线语法"
            android:entryValues="@array/decoder_list"/>
    </PreferenceCategory>
    <PreferenceCategory android:title="高级">
        <ListPreference
            android:entries="@array/vad_list"
            android:title="VAD算法"
            android:key="vad"
            android:summary=""
            android:defaultValue="@string/vad_default"
            android:dialogTitle="VAD"
            android:entryValues="@array/vad_list"/>
        <ListPreference
            android:entries="@array/vad_timeout_list"
            android:title="VAD时长设置，请勿选长语音"
            android:key="vad.endpoint-timeout"
            android:summary=""
            android:defaultValue="@string/vad_timeout_default"
            android:dialogTitle="VAD"
            android:entryValues="@array/vad_timeout_list"/>
        <ListPreference
            android:entries="@array/infile_list"
            android:title="外部音频"
            android:key="infile"
            android:summary=""
            android:defaultValue="@string/infile_default"
            android:dialogTitle="外部音频"
            android:entryValues="@array/infile_list"/>
        <CheckBoxPreference
            android:title="保存录音"
            android:key="_outfile"
            android:summary="同时开启&apos;音频回调CALLBACK_EVENT_ASR_AUDIO参数&apos;才生效，默认保存路径 /sdcard/baiduAsr/outfile.pcm 见代码中OUT_FILE参数"
            android:defaultValue="false"/>
        <CheckBoxPreference
            android:title="是否开启在线语义解析"
            android:key="_nlu_online"
            android:summary=""
            android:defaultValue="false"/>
        <ListPreference
            android:entries="@array/prop_list"
            android:title="垂直领域（在线功能）"
            android:key="prop"
            android:summary=""
            android:defaultValue="@string/prop_default"
            android:dialogTitle="垂直领域（离线不生效）"
            android:entryValues="@array/prop_list"/>
        <CheckBoxPreference
            android:title="禁用标点"
            android:key="disable-punctuation"
            android:summary="在选择长句（输入法模型）的前提下生效。如用于本地语义解析"
            android:defaultValue="false"/>
        <CheckBoxPreference
            android:title="开启代码中音频回调事件"
            android:key="accept-audio-data"
            android:summary="CALLBACK_EVENT_ASR_AUDIO参数。不需要音频二进制数据的回调，请勿开启，否则影响性能"
            android:defaultValue="false"/>
        <CheckBoxPreference
            android:title="开启代码中音量回调事件"
            android:key="accept-audio-volume"
            android:summary="CALLBACK_EVENT_ASR_VOLUME参数。是否需要音频的音量回调。正常使用请勿开启"
            android:defaultValue="false"/>
    </PreferenceCategory>
    <PreferenceCategory android:title="语义解析-本地及离线语法">
        <ListPreference
            android:entries="@array/nlu_list"
            android:title="本地语义解析，请同时勾选下方的语法文件"
            android:key="nlu"
            android:summary=""
            android:defaultValue="@string/decoder_default"
            android:dialogTitle="本地语义解析"
            android:entryValues="@array/nlu_list"/>
        <CheckBoxPreference
            android:title="离线语法及本地语义bsg文件"
            android:key="grammer"
            android:summary="在线时及离线时都可以使用本地语义功能。"
            android:defaultValue="false"/>
        <CheckBoxPreference
            android:title="扩展词条，需代码中设置bsg文件生效"
            android:key="_slot_data"
            android:summary="如GRAMMER的bsg文件中，只设置了王五。勾选后会覆盖原来的词条，可以测&apos;打电话给赵六&apos;，但是&apos;打电话给张三&apos;失效"
            android:defaultValue="false"/>
    </PreferenceCategory>
    <PreferenceCategory android:title="不常用">
        <CheckBoxPreference
            android:title="提示音"
            android:key="_tips_sound"
            android:summary=""
            android:defaultValue="false"/>
    </PreferenceCategory>
</PreferenceScreen>
