//index.js
const app = getApp()

Page({
  data: {
    mealType: '早餐', // 当前餐次
    mealCount: 0, // 就餐人数
    departments: [], // 部门数据
    totalPeople: 0, // 总人数
    canClick: true, // 是否可以点击按钮
    clickedPeople: [], // 已点击的人员记录
    currentTime: '', // 当前时间显示
    timeCheckInterval: null, // 时间检查定时器
    lastResetCheck: null, // 上次重置检查时间
    nextResetTime: '', // 下次重置时间
    showExportBtn: false, // 是否显示导出按钮
    visitorCount: 0, // 访客就餐人数
    // 新入职员工功能相关
    departmentOptions: ['公司领导', '综合部', '供销部', '财务部', '法律外联部', '科技工程（安环）部'],
    selectedDeptIndex: -1 // 选中的部门索引
  },

  onLoad: function () {
    console.log('页面加载完成')
    this.loadPeopleData()
    this.initTimeLogic()
    this.startTimeCheck()
    this.loadStoredData() // 在loadPeopleData之后调用，会恢复存储的状态
    this.calculateNextResetTime()
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    if (this.data.timeCheckInterval) {
      clearInterval(this.data.timeCheckInterval)
    }
  },

  onShow: function() {
    // 页面显示时重新检查状态
    this.initTimeLogic()
    this.calculateNextResetTime()
  },

  // 初始化时间逻辑
  initTimeLogic: function() {
    this.updateTimeAndMealType()
    this.checkClickPermission()
    this.checkAutoReset()
  },

  // 开始时间检查（每10秒检查一次，更精确）
  startTimeCheck: function() {
    const interval = setInterval(() => {
      this.updateTimeAndMealType()
      this.checkClickPermission()
      this.checkAutoReset()
      this.calculateNextResetTime()
    }, 10000) // 每10秒检查一次

    this.setData({ timeCheckInterval: interval })
  },

  // 计算下次重置时间
  calculateNextResetTime: function() {
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes()

    let nextResetHour, nextResetMinute

    // 找到下一个重置时间点
    if (hour < 9 || (hour === 9 && minute < 30)) {
      nextResetHour = 9
      nextResetMinute = 30
    } else if (hour < 14 || (hour === 14 && minute < 30)) {
      nextResetHour = 14
      nextResetMinute = 30
    } else if (hour < 23 || (hour === 23 && minute < 0)) {
      nextResetHour = 23
      nextResetMinute = 0
    } else {
      // 明天的9:30
      nextResetHour = 9
      nextResetMinute = 30
    }

    const nextResetTime = `${nextResetHour.toString().padStart(2, '0')}:${nextResetMinute.toString().padStart(2, '0')}`
    this.setData({ nextResetTime })
  },

  // 获取下次重置时间的时间戳
  getNextResetTimestamp: function() {
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes()

    let nextResetDate = new Date(now)

    // 找到下一个重置时间点
    if (hour < 9 || (hour === 9 && minute < 30)) {
      nextResetDate.setHours(9, 30, 1, 0)
    } else if (hour < 14 || (hour === 14 && minute < 30)) {
      nextResetDate.setHours(14, 30, 1, 0)
    } else if (hour < 23 || (hour === 23 && minute < 0)) {
      nextResetDate.setHours(23, 0, 1, 0)
    } else {
      // 明天的9:30
      nextResetDate.setDate(nextResetDate.getDate() + 1)
      nextResetDate.setHours(9, 30, 1, 0)
    }

    return nextResetDate.getTime()
  },

  // 更新时间和餐次类型
  updateTimeAndMealType: function() {
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes()
    const second = now.getSeconds()
    const currentTime = hour * 100 + minute

    // 格式化当前时间显示（包含秒）
    const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`

    let mealType = ''

    // 根据需求的复杂时间段判断餐次
    if (currentTime >= 1430 && currentTime < 2300) {
      mealType = '早餐'
    } else if (currentTime >= 2300 || currentTime < 930) {
      mealType = '午餐'
    } else if (currentTime >= 930 && currentTime < 1430) {
      mealType = '晚餐'
    }

    this.setData({
      mealType: mealType,
      currentTime: timeStr
    })
  },

  // 检查是否可以点击按钮
  checkClickPermission: function() {
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes()
    const currentTime = hour * 100 + minute

    let canClick = false
    const mealType = this.data.mealType

    // 根据餐次和时间段判断是否可以点击
    if (mealType === '早餐' && currentTime >= 1430 && currentTime < 2300) {
      canClick = true
    } else if (mealType === '午餐' && (currentTime >= 2300 || currentTime < 930)) {
      canClick = true
    } else if (mealType === '晚餐' && currentTime >= 930 && currentTime < 1430) {
      canClick = true
    }

    // 检查是否有点击记录，决定是否显示导出按钮
    const hasClickedPeople = this.data.departments.some(dept =>
      dept.people.some(person => person.clicked)
    )

    this.setData({
      canClick,
      showExportBtn: hasClickedPeople
    })
  },

  // 检查是否需要自动重置（精确到秒）
  checkAutoReset: function() {
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes()
    const second = now.getSeconds()

    // 检查是否到了重置时间点：14:30:01、23:00:01、9:30:01
    const isResetTime = (
      (hour === 14 && minute === 30 && second === 1) ||
      (hour === 23 && minute === 0 && second === 1) ||
      (hour === 9 && minute === 30 && second === 1)
    )

    const currentResetKey = `${hour}-${minute}-${second}`

    if (isResetTime && this.data.lastResetCheck !== currentResetKey) {
      console.log('到达重置时间，执行自动重置')
      this.setData({ lastResetCheck: currentResetKey })
      this.resetAllButtons()
    }
  },

  // 重置所有按钮状态
  resetAllButtons: function() {
    const departments = this.data.departments.map(dept => {
      const people = dept.people.map(person => ({
        ...person,
        clicked: false,
        lastClickTime: null,
        clickCount: 0
      }))

      return {
        ...dept,
        people: people,
        totalCount: people.length,
        eatingCount: people.length // 重置后所有人都吃饭
      }
    })

    this.setData({
      departments: departments,
      mealCount: this.data.totalPeople,
      clickedPeople: [],
      showExportBtn: false,
      visitorCount: 0 // 重置访客人数
    })

    // 清除本地存储
    wx.removeStorageSync('mealData')

    console.log('所有按钮状态已重置，访客人数已清零')

    // 显示重置提示
    wx.showToast({
      title: '状态已重置',
      icon: 'success',
      duration: 2000
    })
  },

  // 手动重置按钮
  onManualReset: function() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有人员状态吗？此操作不可恢复。',
      confirmText: '确认重置',
      cancelText: '取消',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.resetAllButtons()
        }
      }
    })
  },

  // 访客就餐人数点击事件
  onVisitorClick: function() {
    if (!this.data.canClick) {
      wx.showToast({
        title: '当前时间不可操作',
        icon: 'none',
        duration: 2000
      })
      return
    }

    wx.showModal({
      title: '设置访客就餐人数',
      content: '',
      editable: true,
      placeholderText: '请输入数字',
      success: (res) => {
        if (res.confirm && res.content) {
          const inputValue = res.content.trim()
          const visitorNum = parseInt(inputValue)

          if (isNaN(visitorNum) || visitorNum < 0) {
            wx.showToast({
              title: '请输入有效的数字',
              icon: 'none',
              duration: 2000
            })
            return
          }

          // 更新访客人数
          this.setData({
            visitorCount: visitorNum
          })

          // 保存到本地存储
          this.saveDataToStorage()

          wx.showToast({
            title: `访客人数已设置为${visitorNum}人`,
            icon: 'success',
            duration: 2000
          })

          console.log('访客人数更新为：', visitorNum)
        }
      }
    })
  },

  // 导出数据功能
  onExportData: function() {
    try {
      const exportData = this.generateExportData()

      // 显示导出数据
      wx.showModal({
        title: '导出数据',
        content: exportData,
        showCancel: false,
        confirmText: '复制到剪贴板',
        success: (res) => {
          if (res.confirm) {
            wx.setClipboardData({
              data: exportData,
              success: () => {
                wx.showToast({
                  title: '已复制到剪贴板',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    } catch (e) {
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      })
      console.error('导出数据失败：', e)
    }
  },

  // 生成导出数据
  generateExportData: function() {
    const now = new Date()
    const dateStr = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

    const totalMealCount = this.data.mealCount + this.data.visitorCount

    let exportText = `就餐统计报告\n`
    exportText += `日期：${dateStr} ${timeStr}\n`
    exportText += `餐次：${this.data.mealType}\n`
    exportText += `员工总人数：${this.data.totalPeople}人\n`
    exportText += `员工就餐人数：${this.data.mealCount}人\n`
    exportText += `访客就餐人数：${this.data.visitorCount}人\n`
    exportText += `总就餐人数：${totalMealCount}人\n`
    exportText += `员工不就餐人数：${this.data.totalPeople - this.data.mealCount}人\n\n`

    // 按部门统计
    this.data.departments.forEach(dept => {
      const deptTotal = dept.people.length
      const deptNotEating = dept.people.filter(p => p.clicked).length
      const deptEating = deptTotal - deptNotEating

      exportText += `${dept.department}：\n`
      exportText += `  总人数：${deptTotal}人\n`
      exportText += `  就餐：${deptEating}人\n`
      exportText += `  不就餐：${deptNotEating}人\n`

      if (deptNotEating > 0) {
        const notEatingNames = dept.people.filter(p => p.clicked).map(p => p.name)
        exportText += `  不就餐人员：${notEatingNames.join('、')}\n`
      }
      exportText += '\n'
    })

    return exportText
  },

  // 更新部门统计信息
  updateDepartmentStats: function() {
    const departments = this.data.departments.map(dept => {
      const totalCount = dept.people.length
      const notEatingCount = dept.people.filter(p => p.clicked).length
      const eatingCount = totalCount - notEatingCount

      return {
        ...dept,
        totalCount: totalCount,
        eatingCount: eatingCount
      }
    })

    this.setData({ departments })
  },

  // 加载人员数据
  loadPeopleData: function() {
    this.parseNameData()
  },

  // 解析name.txt格式的数据
  parseNameData: function() {
    try {
      // 首先尝试从本地存储加载已保存的部门数据（包括新添加的员工）
      const storedDepartments = wx.getStorageSync('departmentsData')

      if (storedDepartments && storedDepartments.length > 0) {
        console.log('从本地存储加载部门数据（包含新添加的员工）')

        let totalCount = 0
        const departments = storedDepartments.map(dept => {
          const currentDeptStartIndex = totalCount
          const people = dept.people.map((person, index) => ({
            ...person,
            clicked: false,
            index: currentDeptStartIndex + index,
            lastClickTime: null,
            clickCount: 0
          }))

          totalCount += people.length

          return {
            ...dept,
            people: people,
            totalCount: people.length,
            eatingCount: people.length
          }
        })

        this.setData({
          departments: departments,
          totalPeople: totalCount,
          mealCount: totalCount
        })

        console.log('从存储加载完成，总人数：', totalCount)
        return
      }

      // 如果没有存储数据，使用默认的公司人员数据
      console.log('使用默认的公司人员数据')
      const nameData = `公司领导：
朱景和、邵胜军、张晓峰、孟彦林。
综合部：
葛冰心、殷恒辉、孙翔、郑子熠、安涛、宋雨竹、陈行、孙元荣、纪文东、郑泽润。
供销部：
陈贤林、郝琪、贾文、魏鑫宇、陈姬君、房晓敏。
财务部：
赵启祥、李紫燕、邓蓉、杨光玲。
法律外联部：
秦熙杰。
科技工程（安环）部：
田春友、李佳奇、邓显通、边豪。`

      const lines = nameData.trim().split('\n')
      const departments = []
      let totalCount = 0

      for (let i = 0; i < lines.length; i += 2) {
        if (i + 1 < lines.length) {
          const deptName = lines[i].trim()
          const peopleStr = lines[i + 1].trim()

          // 解析人员名单，去掉最后的"。"并按"、"分割
          const peopleNames = peopleStr.replace(/。$/, '').split('、').filter(name => name.trim())

          const people = peopleNames.map((name, index) => ({
            name: name.trim(),
            clicked: false,
            index: totalCount + index,
            lastClickTime: null,
            clickCount: 0
          }))

          departments.push({
            department: deptName,
            people: people,
            totalCount: people.length,
            eatingCount: people.length // 初始时所有人都吃饭
          })

          totalCount += people.length
        }
      }

      this.setData({
        departments: departments,
        totalPeople: totalCount,
        mealCount: totalCount // 初始时就餐人数等于总人数
      })

      // 保存初始数据到本地存储
      wx.setStorageSync('departmentsData', departments)

      console.log('解析完成，总人数：', totalCount)
      console.log('初始就餐人数：', totalCount)
      console.log('部门数据：', departments)
    } catch (e) {
      console.error('解析人员数据失败：', e)
      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    }
  },

  // 加载存储的数据
  loadStoredData: function() {
    try {
      const storedData = wx.getStorageSync('mealData')
      if (storedData && storedData.date === this.getTodayKey()) {
        console.log('发现当天存储的数据，正在恢复...')

        // 恢复当天的数据
        const departments = this.data.departments
        let actualMealCount = this.data.totalPeople // 从总人数开始计算

        storedData.clickedPeople.forEach(clickedPerson => {
          const deptIndex = departments.findIndex(d => d.department === clickedPerson.dept)
          const pIndex = departments[deptIndex]?.people.findIndex(p => p.name === clickedPerson.person)

          if (deptIndex !== -1 && pIndex !== -1) {
            departments[deptIndex].people[pIndex].clicked = clickedPerson.clicked
            departments[deptIndex].people[pIndex].lastClickTime = clickedPerson.lastClickTime
            departments[deptIndex].people[pIndex].clickCount = clickedPerson.clickCount || 0

            // 如果这个人点击了（不吃饭），就餐人数减1
            if (clickedPerson.clicked) {
              actualMealCount -= 1
            }
          }
        })

        this.setData({
          departments: departments,
          mealCount: actualMealCount, // 使用重新计算的就餐人数
          clickedPeople: storedData.clickedPeople,
          visitorCount: storedData.visitorCount || 0 // 恢复访客人数
        })

        // 更新部门统计
        this.updateDepartmentStats()

        console.log('已恢复存储的数据，实际就餐人数：', actualMealCount, '访客人数：', storedData.visitorCount || 0)
      } else {
        console.log('没有找到当天的存储数据或数据已过期')
      }
    } catch (e) {
      console.error('加载存储数据失败：', e)
    }
  },

  // 保存数据到本地存储
  saveDataToStorage: function() {
    try {
      const clickedPeople = []
      this.data.departments.forEach(dept => {
        dept.people.forEach(person => {
          if (person.clicked || person.lastClickTime) {
            clickedPeople.push({
              dept: dept.department,
              person: person.name,
              clicked: person.clicked,
              lastClickTime: person.lastClickTime,
              clickCount: person.clickCount || 0
            })
          }
        })
      })

      const dataToSave = {
        date: this.getTodayKey(),
        mealCount: this.data.mealCount,
        clickedPeople: clickedPeople,
        lastUpdate: new Date().getTime(),
        mealType: this.data.mealType,
        visitorCount: this.data.visitorCount // 保存访客人数
      }

      wx.setStorageSync('mealData', dataToSave)
      console.log('数据已保存到本地存储，就餐人数：', this.data.mealCount, '访客人数：', this.data.visitorCount)
    } catch (e) {
      console.error('保存数据失败：', e)
    }
  },

  // 获取今天的日期键
  getTodayKey: function() {
    const now = new Date()
    return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`
  },

  // 人员按钮点击事件
  onPersonClick: function(e) {
    if (!this.data.canClick) {
      wx.showToast({
        title: '当前时间不可操作',
        icon: 'none',
        duration: 2000
      })
      return
    }

    const { dept, person } = e.currentTarget.dataset

    console.log('点击了：', dept, person)

    const departments = this.data.departments
    const deptIndex = departments.findIndex(d => d.department === dept)
    const pIndex = departments[deptIndex].people.findIndex(p => p.name === person)

    if (deptIndex !== -1 && pIndex !== -1) {
      const personData = departments[deptIndex].people[pIndex]
      const now = new Date()

      // 检查是否是在下次重置时间前的再次点击
      if (personData.clicked && personData.lastClickTime) {
        const nextResetTimestamp = this.getNextResetTimestamp()
        const currentTimestamp = now.getTime()

        if (currentTimestamp < nextResetTimestamp) {
          // 在下次重置时间前再次点击，显示确认对话框
          this.showReconfirmDialog(dept, person, deptIndex, pIndex)
          return
        }
      }

      // 首次点击或已过重置时间的点击，显示确认对话框
      this.showConfirmDialog(dept, person, deptIndex, pIndex)
    }
  },

  // 显示确认对话框（首次点击）
  showConfirmDialog: function(dept, person, deptIndex, pIndex) {
    const mealType = this.data.mealType
    wx.showModal({
      title: '确认操作',
      content: `你确认不吃${mealType}？`,
      confirmText: '确认不吃',
      cancelText: '取消',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.updatePersonStatus(deptIndex, pIndex, true, '不吃饭')
        } else {
          console.log('用户取消了操作')
        }
      }
    })
  },

  // 显示再次确认对话框（在下次重置时间前再次点击）
  showReconfirmDialog: function(dept, person, deptIndex, pIndex) {
    wx.showModal({
      title: '确认操作',
      content: '你确定要吃饭？',
      confirmText: '确定要吃',
      cancelText: '取消',
      confirmColor: '#4caf50',
      success: (res) => {
        if (res.confirm) {
          this.updatePersonStatus(deptIndex, pIndex, false, '要吃饭')
        } else {
          console.log('用户取消了恢复操作')
        }
      }
    })
  },

  // 更新人员状态
  updatePersonStatus: function(deptIndex, pIndex, clicked, action) {
    const departments = this.data.departments
    const now = new Date()

    const wasClicked = departments[deptIndex].people[pIndex].clicked
    const personName = departments[deptIndex].people[pIndex].name

    // 更新人员状态
    departments[deptIndex].people[pIndex].clicked = clicked
    departments[deptIndex].people[pIndex].lastClickTime = now.getTime()
    departments[deptIndex].people[pIndex].clickCount = (departments[deptIndex].people[pIndex].clickCount || 0) + 1

    // 更新就餐人数
    let newMealCount = this.data.mealCount
    if (clicked && !wasClicked) {
      newMealCount -= 1 // 点击表示不吃饭，人数减1
    } else if (!clicked && wasClicked) {
      newMealCount += 1 // 取消点击表示要吃饭，人数加1
    }

    this.setData({
      departments: departments,
      mealCount: newMealCount
    })

    // 更新部门统计
    this.updateDepartmentStats()

    // 保存到本地存储
    this.saveDataToStorage()

    // 显示操作结果
    wx.showToast({
      title: `${personName}已确认${action}`,
      icon: 'success',
      duration: 2000
    })

    console.log(`${personName}状态更新：${action}，当前就餐人数：${newMealCount}`)
  },

  // 部门选择器变化事件
  onDepartmentChange: function(e) {
    const selectedIndex = parseInt(e.detail.value)
    this.setData({
      selectedDeptIndex: selectedIndex
    })
    console.log('选择了部门：', this.data.departmentOptions[selectedIndex])
  },

  // 添加新入职员工
  onAddNewEmployee: function() {
    if (this.data.selectedDeptIndex < 0) {
      wx.showToast({
        title: '请先选择部门',
        icon: 'none',
        duration: 2000
      })
      return
    }

    const selectedDept = this.data.departmentOptions[this.data.selectedDeptIndex]

    wx.showModal({
      title: '新入职员工',
      content: `请输入加入"${selectedDept}"的新员工姓名：`,
      editable: true,
      placeholderText: '请输入姓名',
      success: (res) => {
        if (res.confirm && res.content) {
          const newEmployeeName = res.content.trim()

          if (!newEmployeeName) {
            wx.showToast({
              title: '请输入有效的姓名',
              icon: 'none',
              duration: 2000
            })
            return
          }

          // 检查姓名是否已存在
          const nameExists = this.data.departments.some(dept =>
            dept.people.some(person => person.name === newEmployeeName)
          )

          if (nameExists) {
            wx.showToast({
              title: '该姓名已存在',
              icon: 'none',
              duration: 2000
            })
            return
          }

          // 添加新员工
          this.addNewEmployeeToDepartment(selectedDept, newEmployeeName)
        }
      }
    })
  },

  // 将新员工添加到指定部门
  addNewEmployeeToDepartment: function(deptName, employeeName) {
    const departments = [...this.data.departments]
    const deptIndex = departments.findIndex(dept => dept.department === deptName)

    if (deptIndex === -1) {
      wx.showToast({
        title: '部门不存在',
        icon: 'error',
        duration: 2000
      })
      return
    }

    // 创建新员工对象
    const newEmployee = {
      name: employeeName,
      clicked: false,
      index: this.data.totalPeople,
      lastClickTime: null,
      clickCount: 0
    }

    // 添加到部门
    departments[deptIndex].people.push(newEmployee)
    departments[deptIndex].totalCount = departments[deptIndex].people.length
    departments[deptIndex].eatingCount = departments[deptIndex].people.length

    // 更新总人数和就餐人数
    const newTotalPeople = this.data.totalPeople + 1
    const newMealCount = this.data.mealCount + 1

    this.setData({
      departments: departments,
      totalPeople: newTotalPeople,
      mealCount: newMealCount
    })

    // 更新部门统计信息
    this.updateDepartmentStats()

    // 更新name.txt文件内容
    this.updateNameFile()

    // 保存到本地存储
    this.saveDataToStorage()

    wx.showToast({
      title: `${employeeName}已加入${deptName}`,
      icon: 'success',
      duration: 2000
    })

    console.log(`新员工${employeeName}已添加到${deptName}，总人数：${newTotalPeople}`)
  },

  // 更新name.txt文件内容（模拟文件更新）
  updateNameFile: function() {
    try {
      // 生成新的name.txt格式内容
      let nameFileContent = ''

      this.data.departments.forEach(dept => {
        nameFileContent += `${dept.department}：\n`
        const peopleNames = dept.people.map(person => person.name).join('、')
        nameFileContent += `${peopleNames}。\n`
      })

      // 在实际应用中，这里应该将内容写入到name.txt文件
      // 由于微信小程序的限制，我们只能在控制台输出或存储到本地存储
      console.log('更新后的name.txt内容：')
      console.log(nameFileContent)

      // 将更新后的人员数据保存到本地存储，作为持久化存储
      wx.setStorageSync('nameFileContent', nameFileContent)
      wx.setStorageSync('departmentsData', this.data.departments)

    } catch (e) {
      console.error('更新name.txt文件失败：', e)
    }
  }
})