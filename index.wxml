<!--index.wxml-->
<view class="container">
  <!-- 标题和时间 -->
  <view class="header">
    <view class="title">就餐统计</view>
    <view class="time-info">当前时间：{{currentTime}}</view>
    <view class="meal-type">当前可报餐次：{{mealType}}</view>
    <view class="click-status {{canClick ? 'can-click' : 'cannot-click'}}">
      {{canClick ? '可以操作' : '当前时间不可操作'}}
    </view>
    <view class="next-reset" wx:if="{{nextResetTime}}">
      下次重置：{{nextResetTime}}
    </view>
  </view>

  <!-- 功能按钮区域 -->
  <view class="function-buttons">
    <button
      class="function-btn export-btn"
      wx:if="{{showExportBtn}}"
      bindtap="onExportData">
      📊 导出数据
    </button>
    <button
      class="function-btn reset-btn"
      bindtap="onManualReset">
      🔄 手动重置
    </button>
  </view>

  <!-- 操作说明 -->
  <view class="instructions" wx:if="{{canClick}}">
    <text class="instruction-text">💡 点击姓名按钮表示不吃{{mealType}}，红色按钮在下次重置前可恢复</text>
  </view>

  <!-- 访客就餐统计区域 -->
  <view class="visitor-section">
    <view class="visitor-header">
      <text class="visitor-title">访客就餐统计</text>
    </view>
    <view class="visitor-content">
      <button
        class="visitor-btn {{!canClick ? 'disabled' : ''}}"
        bindtap="onVisitorClick"
        disabled="{{!canClick}}">
        访客就餐人数
      </button>
      <view class="visitor-count">{{visitorCount}}人</view>
    </view>
  </view>

  <!-- 部门和人员区域 -->
  <view class="content">
    <block wx:for="{{departments}}" wx:key="department">
      <!-- 部门名称 -->
      <view class="department-header">
        <view class="department-name">{{item.department}}</view>
      </view>

      <!-- 人员按钮 -->
      <view class="people-buttons">
        <button
          wx:for="{{item.people}}"
          wx:for-item="person"
          wx:key="name"
          class="person-btn {{person.clicked ? 'clicked' : ''}} {{!canClick ? 'disabled' : ''}}"
          data-dept="{{item.department}}"
          data-person="{{person.name}}"
          bindtap="onPersonClick"
          disabled="{{!canClick}}">
          {{person.name}}
        </button>
      </view>
    </block>
  </view>

  <!-- 新入职员工功能区域 -->
  <view class="new-employee-section">
    <view class="new-employee-header">
      <text class="new-employee-title">新入职员工管理</text>
    </view>
    <view class="new-employee-content">
      <view class="department-selector">
        <picker
          bindchange="onDepartmentChange"
          value="{{selectedDeptIndex}}"
          range="{{departmentOptions}}">
          <view class="picker-display">
            {{selectedDeptIndex >= 0 ? departmentOptions[selectedDeptIndex] : '请选择部门'}}
          </view>
        </picker>
      </view>
      <button
        class="new-employee-btn {{selectedDeptIndex < 0 ? 'disabled' : ''}}"
        bindtap="onAddNewEmployee"
        disabled="{{selectedDeptIndex < 0}}">
        👤 新入职员工
      </button>
    </view>
  </view>
</view>

<!-- 底部统计信息 - 移到container外面，确保固定定位 -->
<view class="footer">
  <view class="meal-info">{{mealType}}就餐人数：{{mealCount + visitorCount}}人</view>
  <view class="total-info">员工：{{mealCount}}人 | 访客：{{visitorCount}}人 | 员工不就餐：{{totalPeople - mealCount}}人</view>
</view>